import { GameData } from "../../../game/GameData";
import data from "../../../lib/data/data";
import { DailyChallengeApi } from "./DailyChallengeApi";
import { DailyChallengeConfig } from "./DailyChallengeConfig";
import { DailyChallengeData } from "./DailyChallengeData";
import { DailyChallengeService } from "./DailyChallengeService";
import { DailyChallengeSubscriber } from "./DailyChallengeSubscriber";
import { DailyChallengeViewModel } from "./DailyChallengeViewModel";

export class DailyChallengeModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): DailyChallengeModule {
    if (!GameData.instance.DailyChallengeModule) {
      GameData.instance.DailyChallengeModule = new DailyChallengeModule();
    }
    return GameData.instance.DailyChallengeModule;
  }
  private _data = new DailyChallengeData();
  private _api = new DailyChallengeApi();
  private _service = new DailyChallengeService();
  private _subscriber = new DailyChallengeSubscriber();
  private _viewModel = new DailyChallengeViewModel();
  private _config = new DailyChallengeConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new DailyChallengeData();
    this._api = new DailyChallengeApi();
    this._service = new DailyChallengeService();
    this._subscriber = new DailyChallengeSubscriber();
    this._viewModel = new DailyChallengeViewModel();
    this._config = new DailyChallengeConfig();
    this._api.info(() => {
      completedCallback && completedCallback();
    });

    // 模块初始化
    this._subscriber.register();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
