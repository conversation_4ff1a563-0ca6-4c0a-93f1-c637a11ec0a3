import { _decorator, EventTouch, instantiate, Label, Node, sp, Sprite, tween, UITransform, Widget } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { HeroModule } from "../../../module/hero/HeroModule";
import { Star } from "../../common/Star";
import { JsonMgr } from "../../mgr/JsonMgr";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { HeroMessage } from "../../net/protocol/Hero";
import TipMgr from "../../../lib/tips/TipMgr";
import { HeroRouteItem } from "../../../module/hero/HeroRoute";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { HeroEvent } from "../../../module/hero/HeroConstant";
import Formate from "../../../lib/utils/Formate";
import { ExpandScrollView } from "../../../../platform/src/core/ui/components/ExpandScrollView";
import { HeroSkillAdapter } from "./adapter/HeroDetailAdapter";
import { FriendModule } from "../../../module/friend/FriendModule";
import { ItemCost } from "../../common/ItemCost";
import { AudioItem, AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import ResMgr from "../../../lib/common/ResMgr";
import { IConfigHeroBreak } from "../../JsonDefine";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { HeroAudioName } from "../../../module/hero/HeroConfig";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "../../../ext_guide/GuideMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Thu Sep 26 2024 17:39:44 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/hero/UIHeroPage.ts
 *
 */
enum LEVEL_STATE {
  NORMAL,
  BREAK,
  FULL,
}
export enum PageState {
  HERO = 1, //技能
  FATE, //缘分
  HALO, //光环
}
@ccclass("UIHeroPage")
export class UIHeroPage extends BaseCtrl {
  @property(Node)
  private nodeHeroImg: Node;
  @property(Node)
  private nodeHeroYinji: Node;
  @property(Node)
  private nodeHeroInfo: Node;
  @property(Node)
  private nodeHaloInfo: Node;
  @property(Node)
  private nodeHeroAttrs: Node;
  @property(Label)
  private lblLvel: Label;
  @property(Label)
  private lblQuality: Label;
  @property(Sprite)
  private bgRace;
  @property(Node)
  private nodeOp: Node; //用户交互区域
  @property(Sprite)
  private bgColor: Sprite;
  @property(Node)
  private starHero: Node;
  @property(Label)
  private lblName: Label;
  @property(Node)
  private nodeUnowned: Node;
  @property(Node)
  private itemCost: Node;
  @property(Node)
  private skillList: Node;
  @property(Node)
  private heroSkillViewHolder: Node;
  @property(Node)
  private heroExpandViewHolder: Node;
  @property(Node)
  private friendViewHolder: Node;
  @property(Node)
  private friendExpandViewHolder: Node;
  @property(Node)
  private btnAttrMore: Node;

  private _adapter: HeroSkillAdapter;
  private _currentPage: PageState;
  private _audioItem: AudioItem;

  private _nodeCache: { [key: number]: Node } = {};
  //=================================================
  protected onLoad(): void {
    this._adapter = new HeroSkillAdapter(
      this.heroSkillViewHolder,
      this.heroExpandViewHolder,
      this.friendViewHolder,
      this.friendExpandViewHolder
    );
  }
  protected start(): void {
    this.refreshUI();

    this.skillList.getComponent(ExpandScrollView).setAdapter(this._adapter);

    this.playVoice();
    this.setTab(PageState.HERO);

    this.nodeHeroImg.parent
      .getChildByName("levelupSpine")
      .getComponent(sp.Skeleton)
      .setCompleteListener((e) => {
        this.nodeHeroImg.parent.getChildByName("levelupSpine").active = false;
      });
  }
  protected onEnable(): void {
    MsgMgr.on(MsgEnum.ON_HERO_UPDATE, this.onHeroMsgUpgrade, this);
    MsgMgr.on(HeroEvent.HERO_VIEWMODEL, this.onViewModelChange, this);
    MsgMgr.on(MsgEnum.ON_PET_UPDATE, this.onHeroMsgUpgrade, this);
  }
  protected onDisable(): void {
    MsgMgr.off(MsgEnum.ON_HERO_UPDATE, this.onHeroMsgUpgrade, this);
    MsgMgr.off(MsgEnum.ON_PET_UPDATE, this.onHeroMsgUpgrade, this);
    MsgMgr.off(HeroEvent.HERO_VIEWMODEL, this.onViewModelChange, this);
  }

  protected onDestroy(): void {
    if (this._audioItem) {
      this._audioItem.release();
    }
  }
  public setTab(tab: PageState) {
    this._currentPage = tab;
    let heroInfo = HeroModule.viewModel.currentHero;
    this.nodeHaloInfo.active = false;
    this.skillList.active = false;
    let width = 0;
    switch (tab) {
      case PageState.HERO:
        this.skillList.active = true;
        width = this.heroSkillViewHolder.getComponent(UITransform).width;
        let skillDatas = [];
        skillDatas = skillDatas.concat(heroInfo.skill1List);
        skillDatas = skillDatas.concat(heroInfo.talentList);
        // log.log(heroInfo);
        this._adapter?.setHeroData(skillDatas, heroInfo.id);

        // 跳到最小的那个并且展开 --START
        let minIndex = 0;
        let minValue = 1000;
        for (let i = 0; i < skillDatas.length; i++) {
          let skillInfo = JsonMgr.instance.jsonList.c_heroSkill[skillDatas[i]];
          let heroMessage = HeroModule.data.getHeroMessage(heroInfo.id);
          let level = heroMessage?.skillMap[skillInfo.id] ?? 0;
          if (level > 0 && level < minValue) {
            minValue = level;
            minIndex = i;
          }
        }
        this.skillList.getComponent(ExpandScrollView).expandIndex(minIndex);
        // 跳到最小的那个并且展开 --END
        break;
      case PageState.FATE:
        this.skillList.active = true;
        width = this.friendViewHolder.getComponent(UITransform).width;
        let friendIds = FriendModule.data.getHeroFriends(heroInfo.id);
        // log.log(friendIds);
        this._adapter?.setFriendData(friendIds, heroInfo.id);
        break;
      case PageState.HALO:
        this.nodeHaloInfo.active = true;
        break;
    }
    this.skillList.getComponent(ExpandScrollView).setColumnWidth(width);
  }

  private playVoice() {
    if (this._audioItem) {
      this._audioItem.release();
    }
    let heroInfo = HeroModule.viewModel.currentHero;
    if (heroInfo.voice && heroInfo.voice.length > 0) {
      this._audioItem = AudioMgr.instance.playVoice(heroInfo.voice[0]);
    }
  }
  private refreshAttrs() {
    let attrList = [2, 1, 3, 4, 21, 22, 23, 24, 25, 26];
    let heroInfo = HeroModule.viewModel.currentHero;
    let attr = HeroModule.service.getHeroAttrMap(heroInfo.id);
    for (let i = 0; i < attrList.length; i++) {
      let config = JsonMgr.instance.jsonList.c_attribute[attrList[i]];
      let item = this.nodeHeroAttrs.getChildByName(`lbl_attr${i}`);
      if (!item.getComponent(Label)) {
        continue;
      }
      if (attrList[i] > 4) {
        item.getComponent(Label).string = `${config.name}+${Formate.formatAttr(attrList[i], attr[attrList[i]])}`;
      } else {
        item.getComponent(Label).string = `${config.name}+${Formate.format(attr[attrList[i]])}`;
      }
    }
  }
  private refreshData() {
    let heroInfo = HeroModule.viewModel.currentHero;
    let heroMessage = HeroModule.data.getHeroMessage(heroInfo.id);
    if (heroMessage) {
      // HeroModule.service.updateHeroColorBg(this.bgColor, heroInfo.id);
      this.lblLvel.string = `等级:${heroMessage.level}`;
      this.nodeUnowned.active = false;
      let yinjiLevel = HeroModule.data.getHeroImprintsLv(heroInfo.id);
      this.starHero.getComponent(Star).setStar(yinjiLevel);
      this.setUpLevelState();

      let zizhi = HeroModule.service.getHeroQuality(heroInfo.id);
      this.lblQuality.string = `总资质：${zizhi}`;
      let costTotal = 0;
      if (this.nodeOp.getChildByPath("btn_ten_combo/check_box/check").active) {
        costTotal = HeroModule.service.getNextTenLevelUpCost(heroMessage.heroId);
      } else {
        costTotal = HeroModule.service.getNextLevelUpCost(heroMessage.heroId);
      }
      this.itemCost.getComponent(ItemCost).setItemId(ItemEnum.阅历_3, costTotal);
      this.refreshAttrs();
      this.skillList.getComponent(UITransform).height = 415;
    } else {
      this.skillList.getComponent(UITransform).height = 415;
      this.nodeOp.active = false;
      this.starHero.active = false;
      this.lblLvel.node.active = false;
      this.nodeUnowned.active = true;
      this.nodeHeroAttrs.active = false;
      let zizhi = HeroModule.service.getHeroQuality(heroInfo.id);
      this.lblQuality.string = `资质：${zizhi}`;
    }
  }
  private loadHeroPrefab(position: number) {
    let heroInfo = HeroModule.viewModel.currentHeros[position];
    ResMgr.setNodePrefab(
      BundleEnum.BUNDLE_COMMON_HERO_FULL,
      `prefabs/hero_${heroInfo.id}`,
      this.nodeHeroImg,
      (prefab: Node) => {
        this._nodeCache[position] = prefab;
        let maxWidth = this.nodeHeroImg.parent.getComponent(UITransform).width;
        let maxHeight = this.nodeHeroImg.parent.getComponent(UITransform).height;
        if (maxHeight < maxWidth) {
          let scale = maxWidth / prefab.getComponent(UITransform).width;
          this.nodeHeroImg.setScale(scale, scale);
        } else {
          let scale = maxHeight / prefab.getComponent(UITransform).height;
          this.nodeHeroImg.setScale(scale, scale);
        }
        if (position == HeroModule.viewModel.currentPosition) {
          this.nodeHeroImg.removeAllChildren();
          return false;
        }
        return true;
      }
    );
  }
  private setHeroPrefab() {
    let currentPosition = HeroModule.viewModel.currentPosition;
    let curNode = this._nodeCache[currentPosition];
    if (!curNode) {
      this.loadHeroPrefab(currentPosition);
    } else {
      this.nodeHeroImg.removeAllChildren();
      this.nodeHeroImg.addChild(curNode);
    }
    let lastPosition = HeroModule.viewModel.currentPosition - 1;
    if (lastPosition < 0) {
      lastPosition = HeroModule.viewModel.currentHeros.length - 1;
    }
    let nextPosition = HeroModule.viewModel.currentPosition + 1;
    if (nextPosition >= HeroModule.viewModel.currentHeros.length) {
      nextPosition = 0;
    }
    if (!this._nodeCache[lastPosition]) {
      this.loadHeroPrefab(lastPosition);
    }
    if (!this._nodeCache[nextPosition]) {
      this.loadHeroPrefab(nextPosition);
    }
    const keys = Object.keys(this._nodeCache).map((key) => parseInt(key, 10));
    keys.forEach((key) => {
      if (key != currentPosition && key != lastPosition && key != nextPosition) {
        let node = this._nodeCache[key];
        node && node.destroy();
        delete this._nodeCache[key];
      }
    });

    // this._nodeCache
  }
  private refreshUI() {
    let heroInfo = HeroModule.viewModel.currentHero;
    this.lblName.string = heroInfo.name;
    HeroModule.service.updateHeroRaceIcon(this.bgRace, heroInfo.id);
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_UI, `atlas_imgs/pingzhi_${heroInfo.color}`, this.bgColor);
    this.setHeroPrefab();

    if (heroInfo.talentList.length > 0) {
      this.nodeHeroYinji.active = true;
    } else {
      this.nodeHeroYinji.active = false;
    }

    // 设置十连升级
    this.nodeOp.getChildByPath("btn_ten_combo/check_box/check").active =
      HeroModule.viewModel.setting_hero_lvup_ten_combo;
    this.refreshData();
  }
  private heroUpResponse(hero: HeroMessage) {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.升级成功);
    MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "HERO_LEVEL_UP");
    TipsMgr.setEnableTouch(true);

    let levelupSpine = this.nodeHeroImg.parent.getChildByName("levelupSpine");
    levelupSpine.active = true;
    levelupSpine.getComponent(sp.Skeleton).setAnimation(0, "action", false);
  }
  private setUpLevelState(): LEVEL_STATE {
    let heroInfo = HeroModule.viewModel.currentHero;
    // log.log("============setUpLevelState============");
    //突破等级==突破次数+1
    let message = HeroModule.data.getHeroMessage(heroInfo.id);
    let config_heroBreak: IConfigHeroBreak = JsonMgr.instance.jsonList.c_heroBreak[message.breakTopLevel + 1];
    //突破后的等级 = 突破等级+2
    // let next_heroBeak: IConfigHeroBreak = JsonMgr.instance.jsonList.c_heroBreak[message.breakTopLevel + 2];
    // log.log(config_heroBreak);
    //&& next_heroBeak
    if (message.level < heroInfo.maxLv) {
      // log.log("config_heroBreak");
      if (config_heroBreak && config_heroBreak.levelMax <= message.level) {
        //否则显示执行突破
        this.nodeOp.getChildByName("btn_break").active = true;
        this.nodeOp.getChildByName("bg_level_full").active = false;
        this.nodeOp.getChildByName("btn_level_up").active = false;
        this.nodeOp.getChildByName("btn_ten_combo").active = false;
        this.nodeOp.getChildByName("node_cost").active = false;
        return LEVEL_STATE.BREAK;
      } else {
        //如果小于突破等级或没有可以突破的条件则显示执行升级，升级到最大等级为止
        this.nodeOp.getChildByName("btn_break").active = false;
        this.nodeOp.getChildByName("bg_level_full").active = false;
        this.nodeOp.getChildByName("btn_level_up").active = true;
        this.nodeOp.getChildByName("btn_ten_combo").active = true;
        this.nodeOp.getChildByName("node_cost").active = true;
        return LEVEL_STATE.NORMAL;
      }
    } else {
      // log.log("config_heroBreak else");
      //满级
      this.nodeOp.getChildByName("btn_break").active = false;
      this.nodeOp.getChildByName("bg_level_full").active = true;
      this.nodeOp.getChildByName("btn_level_up").active = false;
      this.nodeOp.getChildByName("btn_ten_combo").active = false;
      this.nodeOp.getChildByName("node_cost").active = false;
      return LEVEL_STATE.FULL;
    }
  }
  private onHeroMsgUpgrade() {
    this.refreshData();
    this._adapter.setDataOnly();
  }

  private onViewModelChange(data: any) {
    this.refreshUI();
    this.setTab(this._currentPage);
    this.playVoice();
  }

  private onClickAttrMore(e: EventTouch) {
    let attrMore = this.btnAttrMore;
    if (attrMore.scale.y < 0) {
      // this.node.getChildByName("btn_hero_attrs_dialog").active = false;
      tween(this.nodeHeroAttrs.getComponent(UITransform))
        .to(
          0.15,
          { height: 63 },
          {
            onUpdate: (target: any, ratio: number) => {
              // this.nodeHeroAttrs.getChildByName("background").getComponent(UITransform).height = target.height;
              attrMore.getComponent(Widget).updateAlignment();
            },
          }
        )
        .start();
    } else {
      // this.node.getChildByName("btn_hero_attrs_dialog").active = true;
      let node = instantiate(this.node.getChildByName("btn_hero_attrs_dialog"));
      node.active = true;
      node.once(Node.EventType.TOUCH_END, () => {
        this.onClickAttrMore(e);
        node.destroy();
      });
      TipsMgr.showTipNode(node);
      tween(this.nodeHeroAttrs.getComponent(UITransform))
        .to(
          0.15,
          { height: 210 },
          {
            onUpdate: (target: any, ratio: number) => {
              // this.nodeHeroAttrs.getChildByName("background").getComponent(UITransform).height = target.height;
              attrMore.getComponent(Widget).updateAlignment();
            },
          }
        )
        .start();
    }
    attrMore.setScale(attrMore.scale.x, attrMore.scale.y * -1, attrMore.scale.z);

    // HeroModule.viewModel.heroDetailAttrMore = !HeroModule.viewModel.heroDetailAttrMore;
  }
  private onClickUpgrade() {
    TipsMgr.setEnableTouch(false, 3);
    let hero = HeroModule.viewModel.currentHero;
    AudioMgr.instance.playEffect(AudioName.Effect.武将升级);
    if (this.setUpLevelState() == LEVEL_STATE.NORMAL) {
      if (this.itemCost.getComponent(ItemCost).isEnough()) {
        if (this.nodeOp.getChildByPath("btn_ten_combo/check_box/check").active) {
          HeroModule.api.levelUpTen(hero.id, this.heroUpResponse.bind(this));
        } else {
          HeroModule.api.levelUp(hero.id, this.heroUpResponse.bind(this));
        }
      } else {
        TipsMgr.setEnableTouch(true);
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: this.itemCost.getComponent(ItemCost).itemId });
        //弹窗礼包事件
        MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, this.itemCost.getComponent(ItemCost).itemId);
      }
    }
  }
  private onClickBreak() {
    UIMgr.instance.showDialog(HeroRouteItem.UIHeroBreak, { heroInfo: HeroModule.viewModel.currentHero });
  }
  private onClickCheckBox() {
    if (!HeroModule.data.canLevelTen()) {
      TipMgr.showTip("拥有10个战将或战将总等级达到100级时开启");
      this.nodeOp.getChildByPath("btn_ten_combo/check_box/check").active = false;
      return;
    }

    this.nodeOp.getChildByPath("btn_ten_combo/check_box/check").active = !this.nodeOp.getChildByPath(
      "btn_ten_combo/check_box/check"
    ).active;
    HeroModule.viewModel.setting_hero_lvup_ten_combo = this.nodeOp.getChildByPath(
      "btn_ten_combo/check_box/check"
    ).active;
    this.refreshData();
  }
  private onClickImprint(e: EventTouch) {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击印记按钮);
    let hero = HeroModule.viewModel.currentHero;
    if (!HeroModule.data.getHeroMessage(hero.id)) {
      return;
    }
    let level = HeroModule.data.getHeroImprintsLv(hero.id);
    if (level < 10) {
      UIMgr.instance.showDialog(HeroRouteItem.UIHeroImprint, { heroInfo: hero });
    } else {
      UIMgr.instance.showDialog(HeroRouteItem.UIHeroImprintFullLevel, { heroId: hero.id });
    }
  }
  private onClickGetPath(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let hero = HeroModule.viewModel.currentHero;
    switch (hero.getWay) {
      case 1: //主角升级
        GuideMgr.startGuide({ stepId: 25 });
        break;
      case 2: //幸运商店
        GuideMgr.startGuide({ stepId: 36 });
        break;
      case 3: //活动投放
        TipMgr.showTip("请关注活动开启");
        break;
      case 4: //天荒古境
        GuideMgr.startGuide({ stepId: 37 });
        break;
      case 5: //四大天王
      case 6: //师徒一行
        GuideMgr.startGuide({ stepId: 63 });
        break;
      case 7: //充值豪礼
        TipMgr.showTip("请关注活动开启");
        break;
    }
  }
}
