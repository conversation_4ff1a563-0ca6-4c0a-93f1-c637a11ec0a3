import {
  _decorator,
  assetManager,
  CapsuleCollider,
  Component,
  EventTouch,
  Node,
  tween,
  UIOpacity,
  v3,
  Widget,
} from "cc";
import { AssetMgr } from "../ResHelper";
import { isValid } from "cc";
import { NodeTool } from "../../../GameScrpit/lib/utils/NodeTool";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

// 向上级发送准备完毕消息
export const MSG_BASE_CTRL_READY = "MSG_BASE_CTRL_READY";

export const MSG_BASE_CTRL_CLOSE = "MSG_BASE_CTRL_CLOSE";

@ccclass("BaseCtrl")
export class BaseCtrl extends Component {
  // 资源管理类
  private _assetMgr: AssetMgr;
  protected get assetMgr(): AssetMgr {
    if (!this._assetMgr) {
      this._assetMgr = AssetMgr.create();
    }
    return this._assetMgr;
  }

  // 路由管理节点
  private _nodeRoute: Node;
  public set nodeRoute(value: Node) {
    this._nodeRoute = value;
  }
  public get nodeRoute(): Node {
    if (!this._nodeRoute) {
      this._nodeRoute = this.node.getParent();
    }
    return this._nodeRoute;
  }

  // 是否已关注/显示最前
  private _isFocus: boolean = false;

  // 调用者关闭本窗口后的回调方法
  public onCloseBack: Function;

  // 调用者ui准备完毕的回调方法
  public onUIReady: Function;

  /** ======== 保证加载完成后执行的方法 ======== */
  // 是否准备完毕，准备完毕才可以响应点击事件
  public isReady: boolean = false;
  // 加载完成后调用的方法列表
  public funcOnReadyList: Function[] = [];
  /** cocoscreator 生命周期函数 内部调用开始方法 */
  protected async onStart() {}

  // 显示背景阴影
  public showBgShadow: boolean = false;

  // 是否弹窗动画
  public playShowAni: boolean = false;

  // 启用自动绑定
  protected autoBind: boolean = true;

  // 存在的节点
  protected nodeMap: Map<string, Node> = null;

  // 自动设置层级
  protected autoSetLayer: boolean = true;

  // 兼容UINode
  private _deps = [];

  /** 普初始化 */
  init(args: any): void {}

  /** cocoscreator 生命周期函数 */
  protected onLoad(): void {}

  protected start() {
    if (this.playShowAni) {
      this.node.setScale(v3(0.5, 0.5, 1));

      let uiOpaticy = this.node.getComponent(UIOpacity);
      if (uiOpaticy == null) {
        uiOpaticy = this.node.addComponent(UIOpacity);
      }

      uiOpaticy.opacity = 0;
      const t1 = tween(uiOpaticy).to(0.25, { opacity: 255 });

      const t2 = tween(this.node)
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "backOut" })
        .call(() => {
          this.node.getComponent(Widget)?.updateAlignment();
        });

      tween(this.node).parallel(t1, t2).start();

      this.focus = true;
      // 异步统一的 入场动画
    }

    if (this.autoBind) {
      NodeTool.addClickEvent(this.node, this);
    }

    if (this.node.parent) {
      this.node.getParent().emit(MSG_BASE_CTRL_READY, this.node.uuid);
    }

    if (this.autoSetLayer) {
      this.node.walk((child) => (child.layer = this.node.parent.layer));
    }

    // 回调页面准备完成
    if (this.onUIReady) {
      try {
        this.onUIReady(this.node);
      } catch (error) {
        log.error(error);
      }
    }

    if (this.onStart) {
      new Promise(async (resolve) => {
        // 子类重写onStart方法
        await this.onStart();

        // 加载完成后执行的方法
        await this.onStartComplete();

        resolve(null);
      });
    }
  }

  // 兼容UINODE
  public addDeps(uuid: string) {
    this._deps.push(uuid);
  }

  protected onDestroy() {
    if (this._assetMgr) {
      this.assetMgr.release();
    }
    // 兼容UINODE
    for (let i = 0; i < this._deps.length; i++) {
      let uuid = this._deps[i];
      let res = assetManager.assets.get(uuid);
      res.decRef();
      res = null;
    }
  }

  /**
   * 关闭并销毁当前节点
   * @param args 关闭回调参数
   */
  public closeBack(args: any = {}) {
    if (isValid(this.node) == false) {
      log.info("已经释放，不在执行closeBack");
      return;
    }

    if (isValid(this.node)) {
      if (this.playShowAni) {
        let uiOpaticy = this.node.getComponent(UIOpacity);
        if (uiOpaticy == null) {
          uiOpaticy = this.node.addComponent(UIOpacity);
        }
        const baseCtrlCloseEvent = new EventTouch(null, true, MSG_BASE_CTRL_CLOSE);
        // cancelEvent.
        this.node.dispatchEvent(baseCtrlCloseEvent);

        const t1 = tween(uiOpaticy).to(0.2, { opacity: 0 });

        const t2 = tween(this.node)
          .to(0.2, { scale: v3(0.5, 0.5, 1) })
          .call(() => {
            if (this.onCloseBack) {
              try {
                this.onCloseBack(args);
              } catch (error) {
                log.error(error);
              }
            }
            this.node.destroy();
          });

        tween(this.node).parallel(t1, t2).start();
      } else {
        if (this.onCloseBack) {
          try {
            this.onCloseBack(args);
          } catch (error) {
            log.error(error);
          }
        }
        this.node.destroy();
      }
    }
  }

  private setBgShadow(isShow: boolean) {}

  public set focus(value: boolean) {
    if (this._isFocus == value) {
      return;
    }
    this._isFocus = value;

    if (value) {
      this.onFocus();
    } else {
      this.onUnFocus();
    }
  }

  public get focus() {
    return this._isFocus;
  }

  // 路由调用组件的获取焦点事件
  public onFocus() {
    // 背景显示阴影
    this.setBgShadow(true);
  }

  // 路由调用组件的失去焦点事件
  public onUnFocus() {
    this.setBgShadow(false);
  }

  /**
   * 重新加载节点map,用于动态加载节点后的getNode
   * @param node 需要重新加的节点
   */
  public reloadNodeMap(node: Node) {
    if (!this.nodeMap) {
      this.nodeMap = new Map();
      this.node.walk((child) => {
        this.nodeMap.set(child.name, child);
      });
    } else {
      node.walk((child) => {
        this.nodeMap.set(child.name, child);
      });
    }
  }

  public getNode(name: string): Node {
    if (!this.nodeMap) {
      this.nodeMap = new Map();
      this.node.walk((child) => {
        this.nodeMap.set(child.name, child);
      });
    }

    if (!this.nodeMap.has(name)) {
      console.error(`${this.node.name} 没有找到节点 ${name}`);
    }

    return this.nodeMap.get(name);
  }

  /**
   * 检查节点是否存在
   * @param name 节点名
   * @returns 是否存在
   */
  public hasNode(name: string): Boolean {
    if (!this.nodeMap) {
      this.nodeMap = new Map();
      this.node.walk((child) => {
        this.nodeMap.set(child.name, child);
      });
    }

    return this.nodeMap.has(name);
  }

  /**
   * 节点 isReady 后执行，如果已经 isReady 则直接执行，执行需要在子类中调用
   * @param func 要执行的方法
   */
  public runAfterStart(func: Function) {
    if (this.isReady) {
      func();
    } else {
      this.funcOnReadyList.push(func);
    }
  }

  /** 调用 ready 方法 */
  private async onStartComplete() {
    this.isReady = true;
    for (let i = 0; i < this.funcOnReadyList.length; i++) {
      const func = this.funcOnReadyList[i];
      await func();
    }
  }
}
