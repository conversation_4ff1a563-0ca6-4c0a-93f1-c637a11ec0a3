import { _decorator, Node, UITransform } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { LayoutManager } from "../LayoutManager";
import { Rect } from "../../../../lib/utils/Rect";

const log = Logger.getLoger(LOG_LEVEL.STOP);
export enum ListLayoutGravity {
  BOTTOM,
  TOP,
}
const { ccclass, property } = _decorator;
@ccclass("ListLayoutManager")
export class ListLayoutManager extends LayoutManager {
  private _direction: ListLayoutGravity = ListLayoutGravity.BOTTOM;

  protected onLayout(changed: boolean, lastRect: Rect, offsetX: number, offsetY: number, isFling: boolean): Rect {
    if (!isFling) {
      if (offsetY < 0) {
        if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
          this.getNodeTop(this.children.peekRear()) + offsetY < this.getBorderTop() && (offsetY /= 3);
        }
      } else {
        if (this.viewholders.firstVisiblePosition == 0) {
          this.getNodeBottom(this.children.peekFront()) + offsetY > this.getBorderBottom() && (offsetY /= 3);
        }
      }
    }
    for (let i = 0; i < this.children.size(); i++) {
      let child = this.children.get(i);
      let width = child.getComponent(UITransform).width;
      let anchorX = child.getComponent(UITransform).anchorX;
      let x = this.getBorderLeft() + anchorX * width;
      let childY = child.position.y + offsetY;
      this.updateChildPosition(child, x, childY);
    }
    if (this.children.size() == 0) {
      let newNode = this.addNodeToTail();
      if (newNode) {
        let width = newNode.getComponent(UITransform).width;
        let height = newNode.getComponent(UITransform).height;
        let anchorX = newNode.getComponent(UITransform).anchorX;
        let anchorY = newNode.getComponent(UITransform).anchorY;
        let x = this.getBorderLeft() + anchorX * width;
        let y = this.getBorderBottom() + height * anchorY;
        this.updateChildPosition(newNode, x, y);
      } else {
        return;
      }
    }
    if (offsetY > 0) {
      // 向上滚动
      for (
        let first = this.children.peekFront();
        this.getNodeBottom(first) > this.getBorderBottom();
        first = this.children.peekFront()
      ) {
        let newNode = this.addNodeToHeader();
        if (newNode) {
          let width = newNode.getComponent(UITransform).width;
          let height = newNode.getComponent(UITransform).height;
          let anchorX = newNode.getComponent(UITransform).anchorX;
          let anchorY = newNode.getComponent(UITransform).anchorY;
          let x = this.getBorderLeft() + anchorX * width;
          let y = this.getNodeBottom(first) - (1 - anchorY) * height;
          this.updateChildPosition(newNode, x, y);
        } else {
          break;
        }
      }
    } else {
      // 向下滚动
      for (
        let last = this.children.peekRear();
        this.getNodeTop(last) < this.getBorderTop();
        last = this.children.peekRear()
      ) {
        let newNode = this.addNodeToTail();
        if (newNode) {
          let width = newNode.getComponent(UITransform).width;
          let height = newNode.getComponent(UITransform).height;
          let anchorX = newNode.getComponent(UITransform).anchorX;
          let anchorY = newNode.getComponent(UITransform).anchorY;
          let x = this.getBorderLeft() + anchorX * width;
          let y = this.getNodeTop(last) + height * anchorY;
          this.updateChildPosition(newNode, x, y);
        } else {
          break;
        }
      }
    }

    return new Rect(
      this.getBorderLeft(),
      this.getNodeTop(this.children.peekFront()),
      this.getBorderRight(),
      this.getNodeBottom(this.children.peekRear())
    );
  }

  protected onChildRecycled(child: Node, isFromHeader: boolean): boolean {
    let recycle = super.onChildRecycled(child, isFromHeader);
    let recycle2 = false;
    // let front = this.children.peekFront();
    // let rear = this.children.peekRear();
    if (this._direction == ListLayoutGravity.BOTTOM) {
      if (isFromHeader) {
        recycle2 = this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1;
      } else {
        recycle2 = this.viewholders.firstVisiblePosition == 0;
      }
    }
    return recycle && !recycle2;
  }
  protected onFling(xoffset: number, yoffset: number): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    if (this.getNodeBottom(front) > this.getBorderBottom() || this.getNodeTop(rear) < this.getBorderTop()) {
      this.scroller.setQuickStop();
      // log.log("fling complete");
    }
    // log.log("fling", xoffset, yoffset);
    super.onFling(xoffset, yoffset);
  }
  protected onScrollComplete(): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    let overStart = 0;
    let overEnd = 0;
    if (this.viewholders.firstVisiblePosition == 0) {
      overStart = this.getBorderBottom() - this.getNodeBottom(front);
    }
    if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
      overEnd = this.getBorderTop() - this.getNodeTop(rear);
    }
    // log.log(`overStart[${overStart}], overEnd[${overEnd}], first[${this.viewholders.firstVisiblePosition}]`);
    if (
      this.viewholders.firstVisiblePosition == 0 &&
      this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1
    ) {
      if (overStart < 0) {
        this.layoutChildrenOffset(0, overStart);
      } else if (overStart > 0 && overEnd > 0) {
        let deltaS = Math.abs(overStart) < Math.abs(overEnd) ? overStart : overEnd;
        this.layoutChildrenOffset(0, deltaS);
      }
    } else if (overStart < 0) {
      this.layoutChildrenOffset(0, overStart);
    } else if (overEnd > 0) {
      this.layoutChildrenOffset(0, overEnd);
    }
  }
  protected onFlingComplete(): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    let overStart = 0;
    let overEnd = 0;
    if (this.viewholders.firstVisiblePosition == 0) {
      overStart = this.getBorderBottom() - this.getNodeBottom(front);
    }
    if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
      overEnd = this.getBorderTop() - this.getNodeTop(rear);
    }
    // log.log(`overStart[${overStart}], overEnd[${overEnd}], first[${this.viewholders.firstVisiblePosition}]`);
    if (
      this.viewholders.firstVisiblePosition == 0 &&
      this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1
    ) {
      if (overStart < 0) {
        this.scroller.scrollBy(0, overStart);
      } else if (overStart > 0 && overEnd > 0) {
        let deltaS = Math.abs(overStart) < Math.abs(overEnd) ? overStart : overEnd;
        this.scroller.scrollBy(0, deltaS);
      }
    } else if (overStart < 0) {
      this.scroller.scrollBy(0, overStart);
    } else if (overEnd > 0) {
      this.scroller.scrollBy(0, overEnd);
    }
  }
}
