// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Item.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface GoodsMessage {
  /** 资源列表 */
  resMap: { [key: number]: number };
  /** 道具列表 */
  itemMap: { [key: number]: number };
  /** 英雄列表 */
  heroMap: { [key: number]: number };
  /** 珍兽列表 */
  petMap: { [key: number]: number };
  /** 挚友列表 */
  friendMap: { [key: number]: number };
  /** 皮肤列表 */
  skinMap: { [key: number]: number };
  /** 头像列表 */
  headShowMap: { [key: number]: number };
  /** 头像框列表 */
  headFrameMap: { [key: number]: number };
  /** 气泡列表 */
  bubbleMap: { [key: number]: number };
  /** 头像列表 */
  titleMap: { [key: number]: number };
}

export interface GoodsMessage_ResMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_ItemMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_HeroMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_PetMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_FriendMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_SkinMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_HeadShowMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_HeadFrameMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_BubbleMapEntry {
  key: number;
  value: number;
}

export interface GoodsMessage_TitleMapEntry {
  key: number;
  value: number;
}

/** 物品类别数量信息 */
export interface ItemMessage {
  /** 物品ID */
  id: number;
  /** 类别Id */
  itemId: number;
  /** 数量 */
  num: number;
}

/**  */
export interface ItemUseMessage {
  /** 使用的道具(合成时表示要合成的道具) */
  itemId: number;
  num: number;
  /** 指定英雄 */
  heroId: number;
  /** 指定据点 */
  cityId: number;
  /** 自选道具宝箱：用户从宝箱中选择道具的索引(从0开始)，以及选择的数量 */
  choiceItemMap: { [key: number]: number };
}

export interface ItemUseMessage_ChoiceItemMapEntry {
  key: number;
  value: number;
}

function createBaseGoodsMessage(): GoodsMessage {
  return {
    resMap: {},
    itemMap: {},
    heroMap: {},
    petMap: {},
    friendMap: {},
    skinMap: {},
    headShowMap: {},
    headFrameMap: {},
    bubbleMap: {},
    titleMap: {},
  };
}

export const GoodsMessage: MessageFns<GoodsMessage> = {
  encode(message: GoodsMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.resMap).forEach(([key, value]) => {
      GoodsMessage_ResMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    Object.entries(message.itemMap).forEach(([key, value]) => {
      GoodsMessage_ItemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.heroMap).forEach(([key, value]) => {
      GoodsMessage_HeroMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.petMap).forEach(([key, value]) => {
      GoodsMessage_PetMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.friendMap).forEach(([key, value]) => {
      GoodsMessage_FriendMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    Object.entries(message.skinMap).forEach(([key, value]) => {
      GoodsMessage_SkinMapEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    Object.entries(message.headShowMap).forEach(([key, value]) => {
      GoodsMessage_HeadShowMapEntry.encode({ key: key as any, value }, writer.uint32(58).fork()).join();
    });
    Object.entries(message.headFrameMap).forEach(([key, value]) => {
      GoodsMessage_HeadFrameMapEntry.encode({ key: key as any, value }, writer.uint32(66).fork()).join();
    });
    Object.entries(message.bubbleMap).forEach(([key, value]) => {
      GoodsMessage_BubbleMapEntry.encode({ key: key as any, value }, writer.uint32(74).fork()).join();
    });
    Object.entries(message.titleMap).forEach(([key, value]) => {
      GoodsMessage_TitleMapEntry.encode({ key: key as any, value }, writer.uint32(82).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = GoodsMessage_ResMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.resMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = GoodsMessage_ItemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.itemMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = GoodsMessage_HeroMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.heroMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = GoodsMessage_PetMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.petMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = GoodsMessage_FriendMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.friendMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = GoodsMessage_SkinMapEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.skinMap[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          const entry7 = GoodsMessage_HeadShowMapEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.headShowMap[entry7.key] = entry7.value;
          }
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          const entry8 = GoodsMessage_HeadFrameMapEntry.decode(reader, reader.uint32());
          if (entry8.value !== undefined) {
            message.headFrameMap[entry8.key] = entry8.value;
          }
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          const entry9 = GoodsMessage_BubbleMapEntry.decode(reader, reader.uint32());
          if (entry9.value !== undefined) {
            message.bubbleMap[entry9.key] = entry9.value;
          }
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          const entry10 = GoodsMessage_TitleMapEntry.decode(reader, reader.uint32());
          if (entry10.value !== undefined) {
            message.titleMap[entry10.key] = entry10.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage>, I>>(base?: I): GoodsMessage {
    return GoodsMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage>, I>>(object: I): GoodsMessage {
    const message = createBaseGoodsMessage();
    message.resMap = Object.entries(object.resMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.itemMap = Object.entries(object.itemMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.heroMap = Object.entries(object.heroMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.petMap = Object.entries(object.petMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.friendMap = Object.entries(object.friendMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.skinMap = Object.entries(object.skinMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.headShowMap = Object.entries(object.headShowMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.headFrameMap = Object.entries(object.headFrameMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.bubbleMap = Object.entries(object.bubbleMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.titleMap = Object.entries(object.titleMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseGoodsMessage_ResMapEntry(): GoodsMessage_ResMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_ResMapEntry: MessageFns<GoodsMessage_ResMapEntry> = {
  encode(message: GoodsMessage_ResMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_ResMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_ResMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_ResMapEntry>, I>>(base?: I): GoodsMessage_ResMapEntry {
    return GoodsMessage_ResMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_ResMapEntry>, I>>(object: I): GoodsMessage_ResMapEntry {
    const message = createBaseGoodsMessage_ResMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_ItemMapEntry(): GoodsMessage_ItemMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_ItemMapEntry: MessageFns<GoodsMessage_ItemMapEntry> = {
  encode(message: GoodsMessage_ItemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_ItemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_ItemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_ItemMapEntry>, I>>(base?: I): GoodsMessage_ItemMapEntry {
    return GoodsMessage_ItemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_ItemMapEntry>, I>>(object: I): GoodsMessage_ItemMapEntry {
    const message = createBaseGoodsMessage_ItemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_HeroMapEntry(): GoodsMessage_HeroMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_HeroMapEntry: MessageFns<GoodsMessage_HeroMapEntry> = {
  encode(message: GoodsMessage_HeroMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_HeroMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_HeroMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_HeroMapEntry>, I>>(base?: I): GoodsMessage_HeroMapEntry {
    return GoodsMessage_HeroMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_HeroMapEntry>, I>>(object: I): GoodsMessage_HeroMapEntry {
    const message = createBaseGoodsMessage_HeroMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_PetMapEntry(): GoodsMessage_PetMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_PetMapEntry: MessageFns<GoodsMessage_PetMapEntry> = {
  encode(message: GoodsMessage_PetMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_PetMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_PetMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_PetMapEntry>, I>>(base?: I): GoodsMessage_PetMapEntry {
    return GoodsMessage_PetMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_PetMapEntry>, I>>(object: I): GoodsMessage_PetMapEntry {
    const message = createBaseGoodsMessage_PetMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_FriendMapEntry(): GoodsMessage_FriendMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_FriendMapEntry: MessageFns<GoodsMessage_FriendMapEntry> = {
  encode(message: GoodsMessage_FriendMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_FriendMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_FriendMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_FriendMapEntry>, I>>(base?: I): GoodsMessage_FriendMapEntry {
    return GoodsMessage_FriendMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_FriendMapEntry>, I>>(object: I): GoodsMessage_FriendMapEntry {
    const message = createBaseGoodsMessage_FriendMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_SkinMapEntry(): GoodsMessage_SkinMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_SkinMapEntry: MessageFns<GoodsMessage_SkinMapEntry> = {
  encode(message: GoodsMessage_SkinMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_SkinMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_SkinMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_SkinMapEntry>, I>>(base?: I): GoodsMessage_SkinMapEntry {
    return GoodsMessage_SkinMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_SkinMapEntry>, I>>(object: I): GoodsMessage_SkinMapEntry {
    const message = createBaseGoodsMessage_SkinMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_HeadShowMapEntry(): GoodsMessage_HeadShowMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_HeadShowMapEntry: MessageFns<GoodsMessage_HeadShowMapEntry> = {
  encode(message: GoodsMessage_HeadShowMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_HeadShowMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_HeadShowMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_HeadShowMapEntry>, I>>(base?: I): GoodsMessage_HeadShowMapEntry {
    return GoodsMessage_HeadShowMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_HeadShowMapEntry>, I>>(
    object: I,
  ): GoodsMessage_HeadShowMapEntry {
    const message = createBaseGoodsMessage_HeadShowMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_HeadFrameMapEntry(): GoodsMessage_HeadFrameMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_HeadFrameMapEntry: MessageFns<GoodsMessage_HeadFrameMapEntry> = {
  encode(message: GoodsMessage_HeadFrameMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_HeadFrameMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_HeadFrameMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_HeadFrameMapEntry>, I>>(base?: I): GoodsMessage_HeadFrameMapEntry {
    return GoodsMessage_HeadFrameMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_HeadFrameMapEntry>, I>>(
    object: I,
  ): GoodsMessage_HeadFrameMapEntry {
    const message = createBaseGoodsMessage_HeadFrameMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_BubbleMapEntry(): GoodsMessage_BubbleMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_BubbleMapEntry: MessageFns<GoodsMessage_BubbleMapEntry> = {
  encode(message: GoodsMessage_BubbleMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_BubbleMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_BubbleMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_BubbleMapEntry>, I>>(base?: I): GoodsMessage_BubbleMapEntry {
    return GoodsMessage_BubbleMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_BubbleMapEntry>, I>>(object: I): GoodsMessage_BubbleMapEntry {
    const message = createBaseGoodsMessage_BubbleMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseGoodsMessage_TitleMapEntry(): GoodsMessage_TitleMapEntry {
  return { key: 0, value: 0 };
}

export const GoodsMessage_TitleMapEntry: MessageFns<GoodsMessage_TitleMapEntry> = {
  encode(message: GoodsMessage_TitleMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoodsMessage_TitleMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsMessage_TitleMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<GoodsMessage_TitleMapEntry>, I>>(base?: I): GoodsMessage_TitleMapEntry {
    return GoodsMessage_TitleMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsMessage_TitleMapEntry>, I>>(object: I): GoodsMessage_TitleMapEntry {
    const message = createBaseGoodsMessage_TitleMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseItemMessage(): ItemMessage {
  return { id: 0, itemId: 0, num: 0 };
}

export const ItemMessage: MessageFns<ItemMessage> = {
  encode(message: ItemMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.itemId !== 0) {
      writer.uint32(16).int64(message.itemId);
    }
    if (message.num !== 0) {
      writer.uint32(24).int32(message.num);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ItemMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseItemMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.itemId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.num = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ItemMessage>, I>>(base?: I): ItemMessage {
    return ItemMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ItemMessage>, I>>(object: I): ItemMessage {
    const message = createBaseItemMessage();
    message.id = object.id ?? 0;
    message.itemId = object.itemId ?? 0;
    message.num = object.num ?? 0;
    return message;
  },
};

function createBaseItemUseMessage(): ItemUseMessage {
  return { itemId: 0, num: 0, heroId: 0, cityId: 0, choiceItemMap: {} };
}

export const ItemUseMessage: MessageFns<ItemUseMessage> = {
  encode(message: ItemUseMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.itemId !== 0) {
      writer.uint32(8).int64(message.itemId);
    }
    if (message.num !== 0) {
      writer.uint32(16).int32(message.num);
    }
    if (message.heroId !== 0) {
      writer.uint32(24).int64(message.heroId);
    }
    if (message.cityId !== 0) {
      writer.uint32(32).int64(message.cityId);
    }
    Object.entries(message.choiceItemMap).forEach(([key, value]) => {
      ItemUseMessage_ChoiceItemMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ItemUseMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseItemUseMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.itemId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.num = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.heroId = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.cityId = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = ItemUseMessage_ChoiceItemMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.choiceItemMap[entry5.key] = entry5.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ItemUseMessage>, I>>(base?: I): ItemUseMessage {
    return ItemUseMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ItemUseMessage>, I>>(object: I): ItemUseMessage {
    const message = createBaseItemUseMessage();
    message.itemId = object.itemId ?? 0;
    message.num = object.num ?? 0;
    message.heroId = object.heroId ?? 0;
    message.cityId = object.cityId ?? 0;
    message.choiceItemMap = Object.entries(object.choiceItemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseItemUseMessage_ChoiceItemMapEntry(): ItemUseMessage_ChoiceItemMapEntry {
  return { key: 0, value: 0 };
}

export const ItemUseMessage_ChoiceItemMapEntry: MessageFns<ItemUseMessage_ChoiceItemMapEntry> = {
  encode(message: ItemUseMessage_ChoiceItemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ItemUseMessage_ChoiceItemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseItemUseMessage_ChoiceItemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ItemUseMessage_ChoiceItemMapEntry>, I>>(
    base?: I,
  ): ItemUseMessage_ChoiceItemMapEntry {
    return ItemUseMessage_ChoiceItemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ItemUseMessage_ChoiceItemMapEntry>, I>>(
    object: I,
  ): ItemUseMessage_ChoiceItemMapEntry {
    const message = createBaseItemUseMessage_ChoiceItemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
