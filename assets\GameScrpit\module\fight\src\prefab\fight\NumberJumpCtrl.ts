import { _decorator, Label, Node, tween, UIOpacity, v3 } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { ChangeValueType } from "../../FightConstant";
const { ccclass, property } = _decorator;

@ccclass("NumberJump")
export class NumberJump extends BaseCtrl {
  value = "";
  type: ChangeValueType;

  node_ani: Node;
  lbl_num: Label;
  lbl_num_吸血: Label;
  lbl_num_暴击: Label;

  async onStart() {
    this.node_ani = this.getNode("node_ani");
    this.lbl_num = this.getNode("lbl_num").getComponent(Label);
    this.lbl_num_吸血 = this.getNode("lbl_num_吸血").getComponent(Label);
    this.lbl_num_暴击 = this.getNode("lbl_num_暴击").getComponent(Label);
  }

  show() {
    this.lbl_num.node.active = false;
    this.lbl_num_吸血.node.active = false;
    this.lbl_num_暴击.node.active = false;

    this.node_ani.setScale(0, 0, 1);
    this.node_ani.setPosition(0, 0, 0);

    if (this.type == ChangeValueType.普攻) {
      this.lbl_num.node.active = true;
      this.lbl_num.string = this.value;

      const t1 = tween(this.node_ani)
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .delay(0.1)
        .to(0.2, { scale: v3(0, 0, 1) }, { easing: "sineIn" });

      const t2 = tween(this.node_ani).to(0.7, { position: v3(0, 200) });

      const t3 = tween(this.node_ani.getComponent(UIOpacity))
        .to(0.2, { opacity: 255 })
        .delay(0.2)
        .to(0.2, { opacity: 0 });

      tween(this.node_ani).parallel(t1, t2, t3).start();
    } else if (this.type == ChangeValueType.暴击) {
      this.lbl_num_暴击.node.active = true;
      this.lbl_num_暴击.string = `暴击${this.value}`;

      const t1 = tween(this.node_ani)
        .to(0.3, { scale: v3(1.2, 1.2, 1) }, { easing: "sineOut" })
        .delay(0.1);

      const t2 = tween(this.node_ani).to(0.7, { position: v3(0, 200) });

      const t3 = tween(this.node_ani.getComponent(UIOpacity))
        .to(0.2, { opacity: 255 })
        .delay(0.3)
        .to(0.1, { opacity: 0 });

      tween(this.node_ani).parallel(t1, t2, t3).start();
    } else if (this.type == ChangeValueType.吸血) {
      this.lbl_num_吸血.node.active = true;
      this.lbl_num_吸血.string = this.value;

      const t1 = tween(this.node_ani)
        .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
        .delay(0.1);

      const t2 = tween(this.node_ani).to(0.7, { position: v3(0, 200) });

      const t3 = tween(this.node_ani.getComponent(UIOpacity))
        .to(0.2, { opacity: 255 })
        .delay(0.3)
        .to(0.1, { opacity: 0 });

      tween(this.node_ani).parallel(t1, t2, t3).start();
    }
  }

  public init(args: { value: number; type: ChangeValueType }) {
    this.value = `${args.value}`;
    this.type = args.type;
  }
}
