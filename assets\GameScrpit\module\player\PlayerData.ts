import { IAttr } from "../../game/GameDefine";
import ToolExt from "../../game/common/ToolExt";
import MsgEnum from "../../game/event/MsgEnum";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import {
  BubbleMessage,
  HeadFrameMessage,
  HeadShowMessage,
  PlayerBattleAttrResponse,
  PlayerDataMessage,
  PlayerRenameResponse,
  SkinMessage,
  TitleMessage,
} from "../../game/net/protocol/Player";
import MsgMgr from "../../lib/event/MsgMgr";
import { CityModule } from "../city/CityModule";
import { HeroModule } from "../hero/HeroModule";
import { SoulModule } from "../soul/SoulModule";
import { HorseModule } from "../horse/HorseModule";
import { PlayerEvent } from "./PlayerEvent";
import { PupilModule } from "../pupil/src/PupilModule";
import { IConfigLeaderRecord } from "./PlayerConfig";
import { ItemEnum } from "../../lib/common/ItemEnum";
import StorageMgr, { StorageKeyEnum } from "../../../platform/src/StorageHelper";
import TipMgr from "../../lib/tips/TipMgr";
import { addAttrMap } from "../../lib/utils/AttrTool";
import TickerMgr from "../../lib/ticker/TickerMgr";
import { IConfigLeader } from "../../game/JsonDefine";

export enum ItemType2Enum {
  /**普通道具 */
  Com_Item = 1,
  /**活动道具 */
  Huo_Item = 2,
  /**合成道具 */
  Join_Item = 3,
}

export enum ItemType1Enum {
  /**代表只能查看 */
  Look_Item = 0,
  /**调用加成方式*/
  Add_Item = 1,
  /**可合成道具 */
  Join_Item = 2,
  /**使用获得固定道具*/
  Get_GD_Item = 3,
  /**使用获得随机道具 */
  Get_SJ_Item = 4,
  /**使用获得自选道具 */
  Get_ZX_Item = 5,
}

export class PlayerData {
  // 服务端战力结果
  public _playerBattleAttrResponse: PlayerBattleAttrResponse = {
    battleAttrMap: {},
    power: 0,
    speed: 0,
    citySpeed: 0,
    pupilSpeed: 0,
  };
  public get playerBattleAttrResponse() {
    return this._playerBattleAttrResponse;
  }
  public set playerBattleAttrResponse(value: PlayerBattleAttrResponse) {
    let oldPower = this._playerBattleAttrResponse.power;
    let newPower = value.power;
    // 消息通知
    if (oldPower != newPower) {
      MsgMgr.emit(PlayerEvent.ON_PLAYER_POWER_CHANGE, newPower, newPower - oldPower);
    }

    let oldSpeed = this._playerBattleAttrResponse.speed;
    let newSpeed = value.speed;
    if (oldSpeed != newSpeed) {
      this.setItemNum(ItemEnum.繁荣度_2, newSpeed);
      MsgMgr.emit(PlayerEvent.ON_PLAYER_BLOOM_CHANGE, newSpeed, newSpeed - oldSpeed);
    }

    this._playerBattleAttrResponse = value;
    MsgMgr.emit(PlayerEvent.ON_UIPLAYER_DATA_UPDATE);
  }

  private _fightSpeed: number = 0;
  public get fightSpeed() {
    return this._fightSpeed;
  }

  public set fightSpeed(val: number) {
    this._fightSpeed = val;
  }

  /**主角身份信息 */
  private _playerDataMsg: PlayerDataMessage = null;

  /**战斗属性 */
  private _playerAttrMap: IAttr = new IAttr();

  public initPlayerInfo(res: PlayerDataMessage) {
    /**主角身份信息 */
    this._playerDataMsg = res;

    // 战斗速度
    this._fightSpeed = StorageMgr.loadNum(StorageKeyEnum.GameSpeed);

    // 道具初始化
    let keys = Object.keys(res.itemMap);
    for (let i = 0; i < keys.length; i++) {
      this.setItemNum(Number(keys[i]), res.itemMap[keys[i]], false);
    }

    // 大数数据初始化
    let keys2 = Object.keys(res.bigNumItemMap);
    for (let i = 0; i < keys2.length; i++) {
      this.setItemNum(Number(keys2[i]), res.bigNumItemMap[keys2[i]], false);
    }

    MsgMgr.emit("PLAYER_INFO", res);
  }

  public clearItemMap() {
    this._itemMap.clear();
  }

  /** =================================== 事件处理 ============================================= */
  public dailyTreasureRes(lastDailyTreasureTs: number) {
    // 最近一次玩家领取每日宝箱的时间
    this._playerDataMsg.lastDailyTreasureTs = lastDailyTreasureTs;
    MsgMgr.emit(PlayerEvent.ON_UIPLAYER_DATA_UPDATE);
  }

  /**改名 rename */
  public renameRes(renameResponse: PlayerRenameResponse) {
    this._playerDataMsg.nickname = renameResponse.nickname;
    this._playerDataMsg.renameCount = renameResponse.renameCount;
    MsgMgr.emit(PlayerEvent.ON_UIPLAYER_DATA_UPDATE);
  }

  /**设置主角等级 */
  public setLevel(level: number) {
    this._playerDataMsg.level = level;
    MsgMgr.emit(PlayerEvent.ON_UIPLAYER_DATA_UPDATE);
    this.onPlayerAttrUpdate();
  }

  /**
   * 更新战力和繁荣度
   */
  onPlayerAttrUpdate() {
    MsgMgr.emit(PlayerEvent.ON_UIPLAYER_DATA_UPDATE);
  }

  /** =================================== 配置文件 ============================================= */
  /**
   * c_leader
   * @param playerLv
   */
  public getConfigLeaderData(playerLv: number): IConfigLeader {
    let list = Object.keys(JsonMgr.instance.jsonList.c_leader);
    let db = JsonMgr.instance.jsonList.c_leader[list[0]];
    let maxLv = db.maxLv;
    let lv = playerLv > maxLv ? maxLv : playerLv;
    return JsonMgr.instance.jsonList.c_leader[lv];
  }

  public getPlayerInfo() {
    return this._playerDataMsg;
  }

  /**性别 */
  public get sex() {
    return this._playerDataMsg.sex;
  }

  public get playerDataMsg(): PlayerDataMessage {
    return this._playerDataMsg;
  }

  public set playerDataMsg(val: PlayerDataMessage) {
    this._playerDataMsg = val;
  }

  private _headShowMap: { [key: number]: HeadShowMessage } = {};

  public get headShowMap() {
    return this._headShowMap;
  }

  public set headShowMap(val) {
    this._headShowMap = val;
  }

  public get head() {
    for (let i in this.headShowMap) {
      let data = this.headShowMap[i];
      if (data.chosen == true) {
        return data;
      }
    }
    let list = Object.keys(this.headShowMap);
    // 默认最低级皮肤
    let data: HeadShowMessage = this.headShowMap[list[0]] || {
      skinId: this.getPlayerInfo().sex == 1 ? 1701 : 1711,
      chosen: true,
    };
    data.chosen = true;
    return data;
  }

  public setUseHeadShow(id: number) {
    for (let i in this.headShowMap) {
      if (this.headShowMap[i].headShowId == id) {
        this.headShowMap[i].chosen = true;
      } else {
        this.headShowMap[i].chosen = false;
      }
    }
    MsgMgr.emit(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE);
  }

  private _skinMap: { [key: number]: SkinMessage } = {};
  public get skinMap() {
    return this._skinMap;
  }

  public set skinMap(data) {
    this._skinMap = data;
  }

  public get skin() {
    for (let i in this.skinMap) {
      let data = this.skinMap[i];
      if (data.chosen == true) {
        return data;
      }
    }

    let list = Object.keys(this.skinMap);
    // 默认最低级皮肤
    let data: SkinMessage = this.skinMap[list[0]] || {
      skinId: this.getPlayerInfo().sex == 1 ? 1701 : 1711,
      chosen: true,
    };
    data.chosen = true;
    return data;
  }

  public setUseSkin(skinId: number) {
    for (let i in this.skinMap) {
      if (this.skinMap[i].skinId == skinId) {
        this.skinMap[i].chosen = true;
      } else {
        this.skinMap[i].chosen = false;
      }
    }
    MsgMgr.emit(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE);
  }

  private _headFrameMap: { [key: number]: HeadFrameMessage } = {};
  public get headFrameMap() {
    return this._headFrameMap;
  }

  public set headFrameMap(data) {
    this.headFrameMap = data;
  }

  public get headFrame() {
    for (let i in this.headFrameMap) {
      let data = this.headFrameMap[i];
      if (data.chosen == true) {
        return data;
      }
    }

    return null;
  }

  public setUseHeadFrame(id: number) {
    for (let i in this.headFrameMap) {
      if (this.headFrameMap[i].headFrameId == id) {
        this.headFrameMap[i].chosen = true;
      } else {
        this.headFrameMap[i].chosen = false;
      }
    }
    MsgMgr.emit(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE);
  }

  private _titleMap: { [key: number]: TitleMessage } = {};
  public get titleMap() {
    return this._titleMap;
  }

  public set titleMap(data) {
    this._titleMap = data;
  }

  public get title() {
    for (let i in this.titleMap) {
      let data = this.titleMap[i];
      if (data.chosen == true) {
        return data;
      }
    }
    return null;
  }

  public setUseTitle(id: number) {
    for (let i in this.titleMap) {
      if (this.titleMap[i].titleId == id) {
        this.titleMap[i].chosen = true;
      } else {
        this.titleMap[i].chosen = false;
      }
    }
    MsgMgr.emit(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE);
  }

  private _bubbleMap: { [key: number]: BubbleMessage } = {};
  public get bubbleMap() {
    return this._bubbleMap;
  }

  public set bubbleMap(data) {
    this._bubbleMap = data;
  }

  public get bubble() {
    for (let i in this.bubbleMap) {
      let data = this.bubbleMap[i];
      if (data.chosen == true) {
        return data;
      }
    }
    return null;
  }

  public setUseBubble(id: number) {
    for (let i in this.bubbleMap) {
      if (this.bubbleMap[i].bubbleId == id) {
        this.bubbleMap[i].chosen = true;
      } else {
        this.bubbleMap[i].chosen = false;
      }
    }
    MsgMgr.emit(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE);
  }

  /** =================================== 本地数据 ======================================= */

  /**获取主角 id */
  public get playerId() {
    return this._playerDataMsg.id;
  }

  /**
   * 是否领取过每日奖励
   * @returns true 领取过
   */
  public ishadGetDailyTreasure() {
    /**每日奖励 */
    const dateStamp = new Date(new Date().toLocaleDateString()).getTime();
    return dateStamp < this._playerDataMsg.lastDailyTreasureTs;
  }

  private _rightMap;
  /**权益 */
  public get rightMap(): { [key: number]: number } {
    return this._rightMap;
  }

  /**权益 */
  public set rightMap(val: { [key: number]: number }) {
    this._rightMap = val;
  }

  /** =================================== 繁荣度 ============================================= */
  /** 更新主角繁荣度 */
  public updatePlayerBloom() {
    let bloom = 0;

    /**据点繁荣度 */
    bloom += CityModule.data.getTotalBloom();

    // 弟子繁荣度
    bloom += PupilModule.data.getTotalBloom();

    this.setItemNum(ItemEnum.繁荣度_2, bloom);
  }

  /** =================================== 属性 ============================================= */
  /** 更新 Player 属性  */
  public updatePlayerAttrMap() {
    let attrTotal = new IAttr();

    let configPlayerLv = this.getConfigLeaderData(1);
    for (let i = 0; i < configPlayerLv.fistAttrList.length; i++) {
      attrTotal[configPlayerLv.fistAttrList[i][0]] = configPlayerLv.fistAttrList[i][1];
    }

    /**坐骑 */
    let horseAttr = HorseModule.data.getHorseAttrMap();
    addAttrMap(attrTotal, horseAttr);

    /**据点 */
    let cityAttr = CityModule.data.getCityTotalAttrMap();
    addAttrMap(attrTotal, cityAttr);

    /**hero */
    let heroAttr = HeroModule.service.getAllHeroAttrMap();
    addAttrMap(attrTotal, heroAttr);

    /**武魂 */
    let soulAttrMap = SoulModule.data.getSpulAttrMap();
    addAttrMap(attrTotal, soulAttrMap);

    /**弟子 */
    let pupilAttrMap = PupilModule.data.getPupilTotalAttrMap();
    addAttrMap(attrTotal, pupilAttrMap);

    /** 五族荣耀 */
    let wzryAttrMap = CityModule.service.getWuZuAddAttr();
    addAttrMap(attrTotal, wzryAttrMap);

    this._playerAttrMap = attrTotal;
    this.setItemNum(ItemEnum.战力_8, ToolExt.computePowerByAttr(this._playerAttrMap));
  }

  /**获取战斗属性汇总 */
  public getPlayerAttrMap() {
    return this._playerAttrMap;
  }

  //==================================道具===================================================

  /**普通道具 */
  private _comItem: { [key: number]: number } = Object.create(null);
  /**活动道具 */
  private _huoItem: { [key: number]: number } = Object.create(null);
  /**合成道具 */
  private _joinItem: { [key: number]: number } = Object.create(null);
  /**道具汇总 */
  private _itemMap: Map<number, number> = new Map<number, number>();

  /**普通道具 */
  public get comItem() {
    return this._comItem;
  }

  /**活动道具 */
  public get huoItem() {
    return this._huoItem;
  }

  /**合成道具 */
  public get joinItem() {
    return this._joinItem;
  }

  /**设置道具剩余库存
   * @param id id
   * @param num 剩余库存
   */
  public setItemNum(id: number, num: number, isEmit = true) {
    this._itemMap.set(id, num);

    let info = JsonMgr.instance.getConfigItem(id);

    for (let i = 0; i < info.type2List.length; i++) {
      if (info.type2List[i] == ItemType2Enum.Com_Item) {
        this._comItem[id] = num;
      }

      if (info.type2List[i] == ItemType2Enum.Huo_Item) {
        this._huoItem[id] = num;
      }

      if (info.type2List[i] == ItemType2Enum.Join_Item) {
        this._joinItem[id] = num;
      }
    }

    if (isEmit) {
      if (id < 1000) {
        MsgMgr.emit(MsgEnum.ON_PLAYER_NUM_UPDATE, id);
      } else {
        MsgMgr.emit(MsgEnum.ON_EXIST_ITEM_CHANGE, id);
      }
    }
  }

  /**获取道具的数量 --- 普通，活动，合成都可以
   * @param id 道具的类型
   */
  public getItemNum(id: number, defaultValue: number = 0) {
    return this._itemMap.get(id) || defaultValue;
  }

  /** 顺序: 皮肤 头像 头像框 称号 气泡 */
  public getMyAvatarList() {
    return [
      this.skin?.skinId,
      this.head?.headShowId,
      this.headFrame?.headFrameId,
      this.title?.titleId,
      this.bubble?.bubbleId,
    ];
  }
}
