import StorageMgr, { StorageKeyEnum } from "../../platform/src/StorageHelper";
import MsgMgr from "../lib/event/MsgMgr";
import FmUtils from "../lib/utils/FmUtils";
import { ActivityModule } from "../module/activity/ActivityModule";
import { AzstModule } from "../module/azst/AzstModule";
import { ChatModule } from "../module/chat/ChatModule";
import { CityModule } from "../module/city/CityModule";
import { ClubModule } from "../module/club/ClubModule";
import { DailyChallengeApi } from "../module/daily_challenge/src/DailyChallengeApi";
import { DailyChallengeModule } from "../module/daily_challenge/src/DailyChallengeModule";
import { EventActionModule } from "../module/event_action/src/EventActionModule";
import { FarmModule } from "../module/farm/FarmModule";
import { FightModule } from "../module/fight/src/FightModule";
import { FriendModule } from "../module/friend/FriendModule";
import { GameHealthModule } from "../module/game_health/GameHealthModule";
import { GoodsModule } from "../module/goods/GoodsModule";
import { HeroModule } from "../module/hero/HeroModule";
import { HorseModule } from "../module/horse/HorseModule";
import { HuntModule } from "../module/hunt/HuntModule";
import { LuckDrawModule } from "../module/luck_draw/LuckDrawModule";
import { MailModule } from "../module/mail/MailModule";
import { MainRankModule } from "../module/main_rank/MainRankModule";
import { MainTaskModule } from "../module/mainTask/MainTaskModule";
import { PetModule } from "../module/pet/PetModule";
import { PlayerModule } from "../module/player/PlayerModule";
import { PostModule } from "../module/post/postModule";
import { PupilModule } from "../module/pupil/src/PupilModule";
import { ShengDianModule } from "../module/sheng_dian/ShengDianModule";
import { SoulModule } from "../module/soul/SoulModule";
import { TrainModule } from "../module/train/src/TrainModule";
import { TravelModule } from "../module/travel/TravelModule";
import MsgEnum from "./event/MsgEnum";
import { SystemOpenEnum } from "./GameDefine";
import { IConfigSystemOpen } from "./JsonDefine";
import { JsonMgr } from "./mgr/JsonMgr";
import { LangMgr } from "./mgr/LangMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

export enum GameStatusEnum {
  // 游戏未运行
  GAME_NOT_RUNNING = 0,
  // 游戏运行中
  GAME_RUNNING = 1,
  // 游戏暂停
  GAME_PAUSE = 2,
  // 游戏结束
  GAME_END = 3,
}

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

export class GameDirector {
  private static _instance: GameDirector;

  public static get instance(): GameDirector {
    if (!this._instance) {
      this._instance = new GameDirector();

      // 负数代表白名单ID,不需要校验是否开启
      let idWhite = -1;

      this._instance._systemOpenStatusList = [
        // 直接加载模块
        { id: --idWhite, module: PlayerModule.instance },
        { id: --idWhite, module: MainTaskModule.instance },
        { id: --idWhite, module: CityModule.instance },
        { id: --idWhite, module: GoodsModule.instance },
        { id: --idWhite, module: ActivityModule.instance },
        { id: --idWhite, module: ChatModule.instance },
        { id: --idWhite, module: MailModule.instance },
        { id: --idWhite, module: GameHealthModule.instance },
        { id: --idWhite, module: MainRankModule.instance },
        // { id: --idWhite, module: FrbpModule.instance },

        // 需要权限开启加载的模块
        { id: SystemOpenEnum.HOURSE_至宝系统, module: HorseModule.instance },
        { id: SystemOpenEnum.SOUL_兽魂系统, module: SoulModule.instance },
        { id: SystemOpenEnum.PUPIL_弟子系统, module: PupilModule.instance },
        { id: SystemOpenEnum.HERO_战将系统, module: HeroModule.instance },
        { id: SystemOpenEnum.PET_灵兽系统, module: PetModule.instance },
        { id: SystemOpenEnum.FRIEND_仙友系统, module: FriendModule.instance },
        { id: SystemOpenEnum.FIGHT_关卡系统, module: FightModule.instance },
        { id: SystemOpenEnum.TRAVEL_游历系统, module: TravelModule.instance },
        { id: SystemOpenEnum.POST_驿站系统, module: PostModule.instance },
        { id: SystemOpenEnum.AZST_演武场, module: AzstModule.instance },
        { id: SystemOpenEnum.LUCKY_幸运抽奖, module: LuckDrawModule.instance },
        { id: SystemOpenEnum.FARM_福地洞天, module: FarmModule.instance },
        { id: SystemOpenEnum.HUNT_天荒古境, module: HuntModule.instance },
        { id: SystemOpenEnum.CLUB_战盟系统, module: ClubModule.instance },
        { id: SystemOpenEnum.SD_圣殿, module: ShengDianModule.instance },
        { id: SystemOpenEnum.SJSJ_三界事件, module: EventActionModule.instance },
        { id: SystemOpenEnum.TRAIN_三界试练, module: TrainModule.instance },
        { id: SystemOpenEnum.DAILY_每日挑战, module: DailyChallengeModule.instance },
        

        // 前端权限
        { id: SystemOpenEnum.TERRAIN_领地换装, module: null },
        { id: SystemOpenEnum.GOODS_英雄图鉴, module: null },
        { id: SystemOpenEnum.GOODS_商城, module: null },
        { id: SystemOpenEnum.POWER_特权卡, module: null },
        { id: SystemOpenEnum.FINGHT_三倍战斗加速, module: null },
        { id: SystemOpenEnum.HUNT_灵兽入侵, module: null },
        { id: SystemOpenEnum.HUNT_洪荒出现, module: null },
        { id: SystemOpenEnum.CLUB_战盟BOSS, module: null },
      ];
    }
    return GameDirector._instance;
  }

  public static clear() {
    GameDirector._instance = null;
  }

  private _status = GameStatusEnum.GAME_NOT_RUNNING;
  public get gameStatus(): GameStatusEnum {
    return this._status;
  }
  public set gameStatus(status: GameStatusEnum) {
    this._status = status;
  }

  public get isRunning() {
    return this._status == GameStatusEnum.GAME_RUNNING;
  }

  // 模块列表
  public systemOpenList: number[] = [];

  // 系统开启状态
  private _systemOpenStatusList: {
    id: number;
    module: any;
    isOpen?: boolean;
    isInit?: boolean;
  }[] = [];

  // 系统开启回调
  public saveWaitAniToStorage(systemId: number) {
    let configSystemOpen: IConfigSystemOpen = JsonMgr.instance.jsonList.c_systemOpen[systemId];
    if (configSystemOpen && configSystemOpen.openShow > 0) {
      let waitPlayList = [];
      let str = StorageMgr.loadStr(StorageKeyEnum.WaitSystemOpenPlayList);
      if (str) {
        waitPlayList = JSON.parse(str);
      }

      if (!waitPlayList.includes(systemId)) {
        waitPlayList.push(systemId);
        StorageMgr.saveItem(StorageKeyEnum.WaitSystemOpenPlayList, JSON.stringify(waitPlayList));
      }
    }
  }

  // 客户端自己判断系统开启
  public isSystemOpenClient(id: number) {
    let rs = false;
    let configSystemOpen = JsonMgr.instance.getConfigSystemOpen(id);
    let openConditionType = configSystemOpen.openList[0];

    if (openConditionType == 1) {
      // 主角身份等级
      rs = PlayerModule.data.getPlayerInfo().level >= configSystemOpen.openList[1];
    } else if (openConditionType == 2) {
      if (!this.isSystemOpen(SystemOpenEnum.FIGHT_关卡系统)) {
        return false;
      }

      // 通关关卡
      if (!FightModule.data.chapterMsg?.chapterId) {
        return false;
      }
      let index = JsonMgr.instance.getConfigCopyMain(FightModule.data.chapterMsg.chapterId)["index"];
      rs = index > configSystemOpen.openList[1];
    } else if (openConditionType == 3) {
      if (!this.isSystemOpen(SystemOpenEnum.HERO_战将系统)) {
        return false;
      }
      // 累计获得门客数量
      rs = HeroModule.data.getOwnedHeros().length >= configSystemOpen.openList[1];
    } else if (openConditionType == 4) {
      // 累计完成主线任务数量 服务端判断，客户端无法判断
      rs = false;
    } else if (openConditionType == 5) {
      if (!this.isSystemOpen(SystemOpenEnum.FRIEND_仙友系统)) {
        return false;
      }
      // 累计获得仙友的数量
      rs = FriendModule.data.getFriendIds(true).length >= configSystemOpen.openList[1];
    } else {
      log.warn("系统权限未配置解锁方式", id, configSystemOpen.openList);
      rs = true;
    }
    log.log("判断系统开启--", id, rs);
    return rs;
  }

  /** 判断系统是否开启某模块功能 */
  public isSystemOpen(id: number): boolean {
    return this.systemOpenList.includes(id);
  }

  // 返回待播放动画的新功能列表
  public getSystemOpenWaitAniList(): number[] {
    let waitPlayList = [];
    let str = StorageMgr.loadStr(StorageKeyEnum.WaitSystemOpenPlayList);
    if (str) {
      waitPlayList = JSON.parse(str);
    }
    return waitPlayList;
  }

  public saveSystemOpenWaitAniList(list: number[]) {
    StorageMgr.saveItem(StorageKeyEnum.WaitSystemOpenPlayList, JSON.stringify(list));
  }

  // 获取解锁条件提示
  public getUnLockMsg(systemId: number): string {
    let configSystemOpen: IConfigSystemOpen = JsonMgr.instance.jsonList.c_systemOpen[systemId];
    if (!configSystemOpen) {
      return "";
    }

    switch (configSystemOpen.openList[0]) {
      case 1:
        /** 主角身份等级 */
        return LangMgr.txMsgCode(115, [configSystemOpen.openList[1]]); // `主角${configSystemOpen.openList[1]}级解锁`;
      case 2:
        /** 通关%s关卡解锁 */
        const cfg = JsonMgr.instance.jsonList.c_copyMain[FightModule.data.chapterId];
        const idx = cfg?.index || 0;
        return LangMgr.txMsgCode(160, [idx, configSystemOpen.openList[1]]);
      case 3:
        /** 累计获得%s个门客解锁 */
        const heroCount = HeroModule.data.getOwnedHeros().length;
        return LangMgr.txMsgCode(159, [heroCount, configSystemOpen.openList[1]]);
      case 4:
        /** 累计完成%s个主线任务解锁 */
        const totalSuccessCount = MainTaskModule.data.mainTaskMsg?.totalSuccessCount || 0;
        return LangMgr.txMsgCode(114, [totalSuccessCount, configSystemOpen.openList[1]]); // `累计完成${configSystemOpen.openList[1]}个主线任务解锁`;
      case 5:
        /** 累计获得%s个仙友解锁 */
        const friendCount = FriendModule.data.ownedFriendNum;
        return LangMgr.txMsgCode(158, [friendCount, configSystemOpen.openList[1]]);
      default:
        return "未解锁";
    }
  }

  // 加载模块
  public async updateSystemOpenList(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      PlayerModule.api.systemOpen((list: number[]) => {
        if (FmUtils.isArrayDiff(this.systemOpenList, list)) {
          this.systemOpenList = list;
          MsgMgr.emit(MsgEnum.ON_RIGHT_UPDATE, list);
        }
        resolve(true);
      });
    });
  }

  // 客户端判断解锁
  public async checkAndLoadModule(playAni?: boolean, needUpdate = true) {
    if (needUpdate) {
      await this.updateSystemOpenList();
    }

    // if (this.isSystemOpenClient(SystemOpenEnum.POWER_特权卡)) {
    //   this.systemOpenList.includes(SystemOpenEnum.POWER_特权卡) ||
    //     this.systemOpenList.push(SystemOpenEnum.POWER_特权卡);
    // }
    // if (this.isSystemOpenClient(SystemOpenEnum.HD_首充)) {
    //   this.systemOpenList.includes(SystemOpenEnum.HD_首充) || this.systemOpenList.push(SystemOpenEnum.HD_首充);
    // }

    for (let idx in this._systemOpenStatusList) {
      let item = this._systemOpenStatusList[idx];

      // 前端权限
      if (!item.module) {
        if (this.isSystemOpen(item.id) && !item.isInit) {
          item.isInit = true;
          item.isOpen = true;
          playAni && this.saveWaitAniToStorage(item.id);
        }
        continue;
      }

      // 无需权限就要加载的模块
      if (item.id < 0 && !item.isOpen) {
        item.isOpen = true;

        item.isInit = false;
        // 有序加载
        await new Promise((resolve, reject) => {
          item.module.init({}, () => {
            item.isInit = true;

            playAni && this.saveWaitAniToStorage(item.id);
            resolve(true);
          });
        });

        continue;
      }

      // 需要权限开启加载的模块
      if (item.id > 0 && !item.isOpen) {
        if (this.isSystemOpen(item.id)) {
          item.isOpen = true;

          item.isInit = false;
          item.module.init({}, () => {
            item.isInit = true;

            playAni && this.saveWaitAniToStorage(item.id);
          });
        }

        continue;
      }
    }
  }

  // 版本号
  public getVersion(): { version: string; debugVersion: string } {
    let v = StorageMgr.loadStr(StorageKeyEnum.Version) || "{}";
    return JSON.parse(v);
  }
}
