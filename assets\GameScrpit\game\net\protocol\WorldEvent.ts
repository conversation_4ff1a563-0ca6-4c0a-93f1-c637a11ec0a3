// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: WorldEvent.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RewardMessage } from "./Comm";

export const protobufPackage = "sim";

/**  */
export interface EventRepelResponse {
  /** 进度 当大于等于总进度时就是完成 */
  progress: number;
  /** 奖励 */
  rewardMessage: RewardMessage | undefined;
}

/**  */
export interface EventTrainMessage {
  /** 事件集合 */
  eventMap: { [key: number]: ThiefEventMessage };
  /** 最近一次触发循环任务的时间戳 */
  lastUpdateTime: number;
}

export interface EventTrainMessage_EventMapEntry {
  key: number;
  value: ThiefEventMessage | undefined;
}

/**  */
export interface ThiefEventMessage {
  /** 当前进度 */
  progress: number;
  /** 总进度 */
  totalProgress: number;
  /** 完成时的奖励 */
  rewardList: number[];
}

function createBaseEventRepelResponse(): EventRepelResponse {
  return { progress: 0, rewardMessage: undefined };
}

export const EventRepelResponse: MessageFns<EventRepelResponse> = {
  encode(message: EventRepelResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.progress !== 0) {
      writer.uint32(8).int32(message.progress);
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EventRepelResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEventRepelResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.progress = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<EventRepelResponse>, I>>(base?: I): EventRepelResponse {
    return EventRepelResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EventRepelResponse>, I>>(object: I): EventRepelResponse {
    const message = createBaseEventRepelResponse();
    message.progress = object.progress ?? 0;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseEventTrainMessage(): EventTrainMessage {
  return { eventMap: {}, lastUpdateTime: 0 };
}

export const EventTrainMessage: MessageFns<EventTrainMessage> = {
  encode(message: EventTrainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.eventMap).forEach(([key, value]) => {
      EventTrainMessage_EventMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    if (message.lastUpdateTime !== 0) {
      writer.uint32(16).int64(message.lastUpdateTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EventTrainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEventTrainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = EventTrainMessage_EventMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.eventMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lastUpdateTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<EventTrainMessage>, I>>(base?: I): EventTrainMessage {
    return EventTrainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EventTrainMessage>, I>>(object: I): EventTrainMessage {
    const message = createBaseEventTrainMessage();
    message.eventMap = Object.entries(object.eventMap ?? {}).reduce<{ [key: number]: ThiefEventMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ThiefEventMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.lastUpdateTime = object.lastUpdateTime ?? 0;
    return message;
  },
};

function createBaseEventTrainMessage_EventMapEntry(): EventTrainMessage_EventMapEntry {
  return { key: 0, value: undefined };
}

export const EventTrainMessage_EventMapEntry: MessageFns<EventTrainMessage_EventMapEntry> = {
  encode(message: EventTrainMessage_EventMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      ThiefEventMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EventTrainMessage_EventMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEventTrainMessage_EventMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ThiefEventMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<EventTrainMessage_EventMapEntry>, I>>(base?: I): EventTrainMessage_EventMapEntry {
    return EventTrainMessage_EventMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EventTrainMessage_EventMapEntry>, I>>(
    object: I,
  ): EventTrainMessage_EventMapEntry {
    const message = createBaseEventTrainMessage_EventMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ThiefEventMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseThiefEventMessage(): ThiefEventMessage {
  return { progress: 0, totalProgress: 0, rewardList: [] };
}

export const ThiefEventMessage: MessageFns<ThiefEventMessage> = {
  encode(message: ThiefEventMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.progress !== 0) {
      writer.uint32(8).int32(message.progress);
    }
    if (message.totalProgress !== 0) {
      writer.uint32(16).int32(message.totalProgress);
    }
    writer.uint32(26).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ThiefEventMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseThiefEventMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.progress = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.totalProgress = reader.int32();
          continue;
        }
        case 3: {
          if (tag === 25) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ThiefEventMessage>, I>>(base?: I): ThiefEventMessage {
    return ThiefEventMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ThiefEventMessage>, I>>(object: I): ThiefEventMessage {
    const message = createBaseThiefEventMessage();
    message.progress = object.progress ?? 0;
    message.totalProgress = object.totalProgress ?? 0;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
