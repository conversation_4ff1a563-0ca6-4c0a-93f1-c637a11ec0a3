import { _decorator, Label, math, Node, sp, Sprite, tween, v3 } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { ClubRouteItem } from "../../../module/club/ClubRoute";
import { ClubModule } from "../../../module/club/ClubModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import TipMgr from "../../../lib/tips/TipMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import { CLUB_POSITION, ClubEvent } from "../../../module/club/ClubConstant";
import { UIClubAvatar } from "./UIClubAvatar";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { ClubAudioName } from "../../../module/club/ClubConfig";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import StorageMgr, { StorageKeyEnum } from "db://assets/platform/src/StorageHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIClubAssistList } from "./UIClubAssistList";
import FmUtils from "../../../lib/utils/FmUtils";
import { UIClubMembers } from "./UIClubMembers";
import { UIClubDonate } from "./UIClubDonate";
import { UIClubLog } from "./UIClubLog";
import { UIClubEditSlogan } from "./UIClubEditSlogan";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { UIExchangeShop } from "../../../module/common/src/prefab/ui/UIExchangeShop";
import { ShopTypeEnum } from "../../../module/common/src/CommonConfig";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Tue Aug 06 2024 10:59:53 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/club/UIClubMain.ts
 *
 */

@ccclass("UIClubMain")
export class UIClubMain extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_CLUB}?prefab/ui/UIClubMain`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this.refreshClubInfo();
    MsgMgr.on(ClubEvent.CLUB_DATA_CHANGE, this.refreshClubInfo, this);
    MsgMgr.on(ClubEvent.CLUB_POSITION_CHANGE, this.refreshClubInfo, this);
    //
    // MsgMgr.emit(MsgEnum.ON_UINAVIGATE_MAIN_CLOSE_GO_ZHAOGE, ClubRouteItem.UIClubMain);
    // /
    ClubModule.api.ownClub(); //每次进入战盟更新数据
    this.getNode("UIClubAvatar").on(
      Node.EventType.TOUCH_END,
      () => {
        AudioMgr.instance.playEffect(ClubAudioName.Effect.点击战盟旗帜图标);
        let avatar = ClubModule.data.clubMessage?.avatar;
        UIMgr.instance.showDialog(ClubRouteItem.UIClubEditAvatar, { avatar: avatar });
      },
      this
    );

    BadgeMgr.instance.setBadgeId(this.getNode("btn_boss"), BadgeType.UIClubMain.btn_boss.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_donate"), BadgeType.UIClubMain.btn_donate.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_task"), BadgeType.UIClubMain.btn_task.id);
    BadgeMgr.instance.setBadgeId(this.getNode("members"), BadgeType.UIClubMain.btn_apply.id);
  }
  protected onEvtHide(): void {
    super.onEvtHide();
    MsgMgr.off(ClubEvent.CLUB_DATA_CHANGE, this.refreshClubInfo, this);
    MsgMgr.off(ClubEvent.CLUB_POSITION_CHANGE, this.refreshClubInfo, this);
  }

  private refreshClubInfo() {
    //显示联盟信息
    // log.log("========================================");
    // log.log(ClubModule.data.clubFormMessage);
    // log.log("========================================");
    if (!this.node) {
      return;
    }
    if (!ClubModule.data.clubMessage) {
      return;
    }
    let avatar_color = 1;
    let avatar_pic = 1;
    if (ClubModule.data.clubMessage?.avatar) {
      let ava = ClubModule.data.clubMessage.avatar.split("_");
      if (ava.length == 2) {
        avatar_color = Number(ava[0]);
        avatar_pic = Number(ava[1]);
      } else {
        avatar_color = 1;
        avatar_pic = 1;
      }
      this.getNode("UIClubAvatar").getComponent(UIClubAvatar).setAvatar(`${avatar_color}_${avatar_pic}`);
    }
    this.getNode("name_value").getComponent(Label).string = `${ClubModule.data.clubMessage.name}`;
    this.getNode("level_value").getComponent(Label).string = `${ClubModule.data.clubMessage.level}`;
    this.getNode("lbl_notice").getComponent(Label).string = `${ClubModule.data.clubMessage.slogan}`;
    let nextExp = ClubModule.config.getUnionExp(ClubModule.data.clubMessage.level + 1);

    let process = 1;
    if (nextExp != 0) {
      process = ClubModule.data.clubMessage.exp / nextExp;
    }

    this.getNode("progress").getComponent(Sprite).fillRange = process;
    if (nextExp == 0) {
      this.getNode("club_experience").active = false;
    } else {
      this.getNode("club_experience").getComponent(Label).string = `${ClubModule.data.clubMessage.exp}/${nextExp}`; //
    }

    let ownner = "";
    for (let i = 0; i < ClubModule.data.clubMessage.memberList.length; i++) {
      //position 1盟主 2副盟主 3普通成员
      if (ClubModule.data.clubMessage.memberList[i].position == 1) {
        ownner = ClubModule.data.clubMessage.memberList[i].simpleMessage.nickname;
        if (ClubModule.data.clubMessage.memberList[i].simpleMessage.userId == PlayerModule.data.playerId) {
          ownner = `${ownner} (自己)`;
        }
      }
    }
    this.getNode("ownner_value").getComponent(Label).string = `${ownner}`;
    this.getNode("members_value").getComponent(Label).string = `${
      ClubModule.data.clubMessage.memberList.length
    }/${ClubModule.config.getUnionMaxNumber(ClubModule.data.clubMessage.level)}`;
    // if (ClubModule.data.position == 1 || ClubModule.data.position == 2) {
    //   this.getNode("btn_apply").active = true;
    //   // this.getNode("manager").active = true;
    // } else {
    //   this.getNode("btn_apply").active = false;
    //   // this.getNode("manager").active = false;
    // }

    // 默认不显示砍价
    this.getNode("btn_bargain").active = false;
    this.getNode("btn_bargain_disable").active = true;
    // 是否显示砍价
    if (ClubModule.data.clubMessage.bargain && ClubModule.data.clubMessage.bargain.bargainId >= 0) {
      if (StorageMgr.loadNum(StorageKeyEnum.BargainStartAni) !== ClubModule.data.clubMessage.bargain.endTime) {
        // 判断是否需要显示动画
        this.bargainAni();
      } else {
        // 否则直接显示
        this.getNode("btn_bargain").active = true;
        FmUtils.setCd(this.getNode("lbl_bargain_cd"), ClubModule.data.clubMessage.bargain.endTime, false, () => {
          this.getNode("btn_bargain").active = false;
        });
        this.getNode("btn_bargain_disable").active = false;
      }
    }

    if (ClubModule.data.position == CLUB_POSITION.盟主) {
      this.getNode("btn_notice_edit").active = true;
    } else {
      this.getNode("btn_notice_edit").active = false;
    }
  }

  private bargainAni() {
    this.getNode("btn_bargain_chuxian").active = true;
    this.getNode("node_bargain_head")
      .getComponent(sp.Skeleton)
      .setCompleteListener(() => {
        this.getNode("node_bargain_head").getComponent(sp.Skeleton).setAnimation(0, "juan_zhou_chi_xu", true);
      });
    this.getNode("node_bargain_head")
      .getComponent(sp.Skeleton)
      .setEventListener((animation, event) => {
        if (event["data"].name == "sj_zi_biaoti") {
          //
          this.getNode("node_bargain_title").getComponent(sp.Skeleton).setAnimation(0, "zi", false);
        }
        if (event["data"].name == "sj_guang_xuan_zhuan") {
          //
          this.getNode("node_bargain_tx").active = true;
        }
      });
  }

  private on_click_btn_bargain_chuxian() {
    let wordPos = this.getNode("icon_daji_touxiang").getWorldPosition();

    let endWord = this.getNode("btn_bargain_disable").getWorldPosition();

    tween(this.getNode("node_filght_skt"))
      .call(() => {
        tween(this.getNode("node_bargain_ani"))
          .to(0.3, { scale: v3(0, 0, 0), worldPosition: wordPos })
          .start();
      })
      .delay(0.3)
      .call(() => {
        this.getNode("node_bargain_ani").active = false;
        this.getNode("node_bargain_bg").active = false;
      })
      .set({ active: true })
      .call(() => {
        SpineUtil.playSpine(this.getNode("node_filght_skt").getComponentInChildren(sp.Skeleton), "tuowei", true);

        const angleRad = Math.atan2(endWord.y - wordPos.y, endWord.x - wordPos.x);

        this.getNode("node_filght_skt").angle = math.toDegree(angleRad);
      })
      .to(0.3, { worldPosition: endWord }, { easing: "sineIn" })
      .call(() => {
        // SpineUtil.playSpine(spine, "quan", false);
      })
      .call(() => {
        this.getNode("btn_bargain_chuxian").active = false;
        this.getNode("node_bargain_chuxian").active = true;
        this.getNode("node_bargain_chuxian")
          .getComponent(sp.Skeleton)
          .setCompleteListener(() => {
            this.getNode("btn_bargain_disable").active = false;
          });
        this.getNode("node_bargain_chuxian").getComponent(sp.Skeleton).setAnimation(0, "da_ji_chu_xian", false);
        setTimeout(() => {
          this.getNode("btn_bargain").active = true;
          FmUtils.setCd(this.getNode("lbl_bargain_cd"), ClubModule.data.clubMessage.bargain.endTime, false, () => {
            this.getNode("btn_bargain").active = false;
          });
          StorageMgr.saveItem(StorageKeyEnum.BargainStartAni, ClubModule.data.clubMessage.bargain.endTime + "");
        }, 700);
      })
      .start();
  }

  private on_click_btn_boss() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.点击战盟首领图标);
    UIMgr.instance.showDialog(ClubRouteItem.UIClubBossMain);
  }
  private on_click_btn_task() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.点击战盟任务图标);
    UIMgr.instance.showDialog(ClubRouteItem.UIClubTask);
  }
  private on_click_notice() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
  }
  private on_click_shop() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.点击战盟任务图标);
    // UIMgr.instance.showDialog(ClubRouteItem.UIClubShop);
    RouteManager.uiRouteCtrl.showRoute(UIExchangeShop, { payload: { type: ShopTypeEnum.战盟商店 } });
  }
  private on_click_rule() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
  }
  private on_click_members() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.点击左下方成员排行按钮);
    //
    RouteManager.uiRouteCtrl.showRoute(UIClubMembers);
  }

  private on_click_rank() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.点击左下方成员排行按钮);
    UIMgr.instance.showDialog(ClubRouteItem.UIClubRank);
  }

  private on_click_btn_donate() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.点击上方捐献日志管理按钮);
    RouteManager.uiRouteCtrl.showRoute(UIClubDonate);
  }
  private on_click_log() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.点击上方捐献日志管理按钮);
    RouteManager.uiRouteCtrl.showRoute(UIClubLog);
  }
  private on_click_btn_name_edit() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    if (ClubModule.data.position != 1) {
      TipMgr.showTip("只有盟主才可修改名称");
      return;
    }
    UIMgr.instance.showDialog(ClubRouteItem.UIClubNameFix);
  }

  // 联盟砍价
  private on_click_btn_bargain() {
    if (ClubModule.service.isInterceptOperation()) {
      return;
    }
    UIMgr.instance.showDialog(ClubRouteItem.UIClubBargain);
  }

  private on_click_btn_sklx_xiezhu() {
    AudioMgr.instance.playEffect(1537);
    RouteManager.uiRouteCtrl.showRoute(UIClubAssistList);
  }

  private on_click_btn_bargain_disable() {
    TipMgr.showTip("领取战盟任务有机率触发战盟砍价");
  }

  // 修改公告
  private on_click_btn_notice_edit() {
    let routeArgs: RouteShowArgs = {
      onCloseBack: () => {
        this.getNode("lbl_notice").getComponent(Label).string = `${ClubModule.data.clubMessage.slogan}`;
        // this.refreshClubInfo();
      },
    };
    RouteManager.uiRouteCtrl.showRoute(UIClubEditSlogan, routeArgs);
  }
}
