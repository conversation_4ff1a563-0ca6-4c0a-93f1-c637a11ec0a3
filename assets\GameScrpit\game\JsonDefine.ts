export interface IConfigAct {
  /** id */
  id: number;
  /** 活动名称 */
  mailTitle: string;
}

export interface IConfigActionEffect {
  /**  */
  id: number;
  /** 名称 */
  name: string;
  /** 前置条件 */
  first: number;
  /** 是否可叠加 */
  isAdd: number;
}

export interface IConfigActionSkill {
  /** ID */
  id: number;
  /** 描述 */
  name: string;
  /** 是否判断命中 */
  isHit: number;
  /** 技能效果 */
  skillList: number[];
}

export interface IConfigAttribute {
  /**  */
  id: number;
  /** 名称 */
  name: string;
  /** 属性类型 */
  type1: number;
  /** 技能叠加上限 */
  max: number;
  /** 英雄战力转换 */
  powerRate1List: number[];
  /** 怪物战力转换 */
  powerRate2List: number[];
}

export interface IConfigBackground {
  /** ID */
  id: number;
  /** 冲锋场景 */
  runBg: string[];
  /** 回合背景 */
  roundBg: string;
  /** 待机资源 */
  waiting: string;
  /** 胜利 */
  win: string;
  /** 失败 */
  lose: string;
}

export interface IConfigBlessLand {
  /** id */
  id: number;
  /** 葫芦名称 */
  name: string;
  /** 刷到葫芦再次刷新需要做提示 */
  tip: number;
  /** 是否限时消失-秒 */
  die: number;
  /** 品质 */
  color: number;
  /** 拉取随机时长-秒 */
  fetchTimeList: number[];
  /** 派遣上限 */
  send: number;
  /** 美术ID */
  iconId: number;
  /** 后台自动刷新权重 */
  refreshRate: number;
  /** 消耗仙玉刷新 */
  refreshRate2: number;
  /** 看广告刷新 */
  refreshRate3: number;
  /** 刷新出的个数上线 */
  max: number;
  /** 必得道具 */
  reward1List: number[][];
  /** 获得道具的概率 */
  itemRate: number;
  /** 概率获得道具 */
  reward2List: number[][];
  /** 雇佣时长-小时 */
  hireTime: number;
  /** 冷却时间~单位秒 */
  refreshColdTime: number;
  /** 雇佣消耗仙玉数量 */
  hireCostList: number[];
  /** 同时雇佣上限 */
  hireMax: number;
  /** 消耗仙玉刷新(手动刷新) */
  refreshCost1List: number[];
  /** 看广告刷新次数 */
  adTime: number;
  /** 探寻刷新时间冷却 */
  coldTime: number;
  /** 自动刷新时间点 */
  refreshFreeList: number[];
  /** 葫芦的数量 */
  fruitNumber: number;
  /** 周围福地最大数量 */
  nearMax: number;
  /** 敌对福地最大数量 */
  enemyMax: number;
  /** 每日采集次数上限 */
  fetchMax: number;
}

export interface IConfigBlessLandBee {
  /** id */
  id: number;
  /** 解锁采集次数 */
  unlock: number;
}

export interface IConfigBlessLandFund {
  /** id */
  id: number;
  /** 基金名称 */
  name: string;
  /** 解锁价格 */
  price: number;
  /** 采集类型 */
  type: number;
  /** 采集他人的次数 */
  fetchTime: number;
  /** 完成奖励 */
  rewardList: number[][];
}

export interface IConfigBloodPlace {
  /** id */
  id: number;
  /** 关卡高度 */
  place01: number;
  /** 演武场高度 */
  place02: number;
  /** 天荒古境狩猎 */
  place03: number;
  /** 古境BOSS */
  place04: number;
  /** 战盟首领 */
  place05: number;
  /** 时空裂隙 */
  place06: number;
}

export interface IConfigBubble {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 获取途径 */
  source: string;
  /** 称号类型 */
  type: number;
  /** 图标id */
  iconId: number;
  /** 是否隐藏 */
  isHide: number;
  /** 属性加成 */
  attrAdd: number[][];
  /** 加成描述 */
  addDesc: string;
}

export interface IConfigBuff {
  /** ID */
  id: number;
  /** 类型 */
  type: number;
  /** 表现类型  */
  showType: number;
  /** 名字 */
  name: string;
  /** 表现 */
  showRes: string;
  /** buff脚本名 */
  buffScrpit: string;
}

export interface IConfigBuild {
  /** id */
  id: number;
  /** 建筑名称 */
  name: string;
  /** 音效ID */
  musicId: number;
  /** 产金要求 */
  goldSpeed: string;
  /** 关卡通关开启 */
  unlockLv: number;
  /** 建造消耗金币 */
  buildCost: number;
  /** 每员工每秒产金数 */
  goldProduce: number;
  /** 描述 */
  des: string;
  /** 类型 */
  type: number;
  /** 美术ICON */
  icon: number;
  /** 是否有解锁引导 */
  isShow: number;
  /** 建筑皮肤解锁等级 */
  unlockLvList: number[];
  /** 入住战将ID */
  heroIdList: number[];
  /** 据点通关奖励宝箱 */
  rewardList: number[][];
  /** X，Y坐标 */
  position: number[][];
  /** 缩放比例 */
  scale: number[][];
  /** 最终建筑ID */
  lastId: number;
  /** 建筑初始伙计数量 */
  workerFirst: number;
  /** 入住战将上限 */
  workerMax: number;
}

export interface IConfigBuildBox {
  /** id */
  id: number;
  /** 宝箱道具奖励 */
  rewardList: number[];
  /** 获取概率 */
  rateList: number[];
  /** 宝箱掉落概率 */
  dropRateList: number[];
  /** 升级所需累计号召次数 */
  callTime: number[];
  /** 累计解锁建筑个数 */
  buildNum: number[];
  /** 自动号召解锁条件 */
  callAuto: number[];
  /** 宝箱等级上限 */
  lvMax: number;
  /** 宝箱个数上限 */
  boxMax: number;
  /** 自动号召时间间隔 */
  autoTime: number;
}

export interface IConfigBuildCrystal {
  /** id */
  id: number;
  /** 建筑等级 */
  level: number;
  /** 消耗(物品id,数量） */
  cost: number;
  /** 商铺加成倍数(万分比） */
  rateAdd: number;
  /** 每秒自动点击次数 */
  time: number;
  /** 点击收益 */
  reward: number;
  /** 升级消耗道具ID */
  cfg: number;
  /** 建筑最大等级 */
  lvMax: number;
  /** 水晶皮肤解锁等级 */
  unlockLvList: number[];
  /** 离线收益弹出持续时间 */
  popUpTime: number;
  /** 离线收益最大时长 */
  rewardMaxTime: number;
  /** 自动点击解锁条件 */
  unlock: number;
  /** 自动点击图标展示条件 */
  openIcon: number;
  /** 解锁自动点击奖励 */
  rewardItemsList: number[];
}

export interface IConfigBuildLv {
  /** id */
  id: number;
  /** 对应建筑id */
  buildId: number;
  /** 建筑等级 */
  level: number;
  /** 最大员工数量 */
  workerMax: number;
  /** 消耗(物品id,数量） */
  cost: number;
  /** 建筑升级加成倍数(万分比） */
  rateAdd: number;
  /** 美术ICON */
  icon: number;
  /** 战将基础繁荣度加成(万分比) */
  heroRate: number;
  /** 等级对应属性 */
  attrFit: number[][];
  /** 战将品质加成系数 */
  heroRateList: number[];
  /** 升级消耗道具ID */
  cfg: number;
  /** 建筑最大等级 */
  lvMax: number;
}

export interface IConfigBuildLvReward {
  /** id */
  id: number;
  /** 总等级数量 */
  buildId: number;
  /** 对应奖励 */
  rewardList: number[][];
  /** 成就描述 */
  des: string;
}

export interface IConfigBuildShow {
  /** id */
  id: number;
  /** 解锁对应建筑ID */
  buildId: number;
  /** 名称 */
  name: string;
  /** 预览建筑ID */
  buildShowId: number[];
  /** 对应装饰ID */
  trimId: number[];
  /** 全部解锁装饰奖励 */
  rewardList: number[][];
}

export interface IConfigBuildTrimReward {
  /** id */
  id: number;
  /** 建筑名称 */
  name: string;
  /** 音效ID */
  musicId: number;
  /** 建筑名称2 */
  name2: string;
  /** 是否有气泡 */
  bubble: number;
  /** 解锁条件 */
  unlockTypeList: number[][];
  /** 是否手动建造 */
  isManual: number;
  /** 手动建造获得奖励 */
  rewardList: number[][];
  /** 弃用的ID */
  hide: number;
  /** 解锁建筑总等级 */
  buildLv: number;
  /** spine文件名 */
  spineName: string;
  /** 动作名 */
  aniName: string;
  /** 过渡动画   */
  midName: string;
  /** 未解锁虚影 */
  xuying: string;
  /** 虚影坐标 */
  xuyingPlace: number[];
  /** 虚影大小 */
  xuyingBig: number[];
  /** 气泡文件名 */
  bubbleName: string;
  /** 气泡文字色号 */
  bubbleColor: string;
  /** 出场动画 */
  outShow: string;
  /** 窗口内spine坐标 */
  windowPlace: number[];
  /** 窗口内spine大小 */
  windowBig: number[];
  /** 窗口是否包含背景 */
  windowBg: string;
  /** 背景坐标 */
  windowBgPlace: number[];
  /** 背景大小 */
  bgSize: number[];
  /** 位置坐标 */
  pos: number[];
  /** spine位移 */
  spinePos: number[];
  /** 缩放 */
  scale: number[];
  /** 层级 */
  layer: number;
  /** 繁荣度增加 */
  prosperityAdd: number;
  /** 预览繁荣度显示 */
  prosperityShow: number;
  /** 预览装饰图片文件名 */
  spineShow: string;
  /** 成就描述 */
  des: string;
}

export interface IConfigBuildWorkerReward {
  /** id */
  id: number;
  /** 累计招募伙计数量 */
  workerNum: number;
  /** 对应属性加成 */
  attrAdd: number[][];
  /** 成就描述 */
  des: string;
}

export interface IConfigBulletShow {
  /** ID */
  id: number;
  /** name */
  name: string;
  /** scrpitName */
  scrpitName: string;
  /** 资源路径 */
  res: string;
  /** 其他子弹脚本 */
  heBulletId: number;
}

export interface IConfigBuyFirst {
  /** ID */
  id: number;
  /** 获取奖励 */
  rewardList: number[][];
  /** 价格ID1 */
  priceId: number;
  /** 价格ID2 */
  typeId: number;
}

export interface IConfigBuyId {
  /** 天数id */
  id: number;
  /** 对应国内价格-RMB */
  price: number;
}

export interface IConfigBuyType {
  /** id */
  id: number;
}

export interface IConfigCharacterArt {
  /** id */
  id: number;
  /** 世界中比例大小 */
  worldScale: number;
  /** 展示用的比例 */
  showScale: number;
}

export interface IConfigCityTransform {
  /** ID */
  id: number;
  /** X，Y坐标 */
  position: number[][];
  /** 缩放比例 */
  scale: number[][];
}

export interface IConfigColor {
  /** id */
  id: number;
  /** 字体颜色 */
  fontColor: string;
  /** 字体大小 */
  fontSize: string;
  /** 描边颜色 */
  outlineColor: string;
  /** 描边大小 */
  outlineWidth: string;
  /** 前字段 */
  start: string;
  /** 后字段 */
  end: string;
}

export interface IConfigCompete {
  /** 排行榜ID */
  id: number;
  /** 对应名次 */
  order: number;
  /** 排行榜奖励 */
  rewardList: number[][];
  /** 胜利奖励 */
  winReward: number[][];
  /** 初始积分 */
  firstScore: number;
  /** 每日免费刷新出机器人的概率 */
  rateList: number[];
  /** 排行榜匹配范围 */
  fitList: number[];
  /** 每日免费刷新次数上限 */
  freeMax: number;
  /** 次数恢复时间 */
  time: number;
  /** 钻石刷新消耗 */
  costList: number[];
  /** 系统解锁初始挑战券数量 */
  firstRewardList: number[];
  /** 战斗最大回合数 */
  round: number;
}

export interface IConfigCompeteRobot {
  /** 排行榜ID */
  id: number;
  /** 名字 */
  name: string;
  /** 性别 */
  sex: number;
  /** 机器人属性 */
  attrList: number[][];
  /** 头像 */
  headShow: number;
  /** 战斗形象 */
  leaderSkinId: number;
  /** 机器人积分 */
  score: number;
  /** 初始排行榜展示人数 */
  firstShow: number;
  /** 最大排行榜展示人数 */
  maxShow: number;
  /** 所有机器人等级 */
  lv: number;
}

export interface IConfigCopyMain {
  /** 关卡id */
  id: number;
  /** 关卡描述 */
  des: string;
  /** 关联建筑ID */
  buildId: number;
  /** 场景ID */
  bgId: number;
  /** 【万分比】气运初始消耗系数 */
  cost: number;
  /** 初始气运消耗固定值 */
  costNum: number;
  /** 气运消耗次数 */
  costTime: number;
  /** 关卡道具奖励 */
  reward1List: number[][];
  /** 进度初始阅历奖励 */
  reward2: number;
  /** BOSS表现 */
  monsterId: number;
  /** 怪物名称 */
  bossName: string;
  /** BOSS战力 */
  powerList: number[][];
  /** 鼓舞关卡系数【万分比】 */
  inspireCost1: number;
  /** 鼓舞气运消耗固定值 */
  inspireCostNum: number;
  /** 关卡事件 */
  eventIdList: number[][];
  /** 玩家属性 */
  roleAttr: number[][];
  /** 玩家皮肤ID */
  roleId: number;
  /** 进度阅历奖励系数 */
  reward2RateList: number[];
  /** 气运消耗系数 */
  costTimeList: number[];
  /** 鼓舞消耗递增系数 */
  inspireRateList: number[];
  /** 气运最大鼓舞次数 */
  inspireMax1: number;
  /** 每次气运鼓舞战力提升 */
  inspireAdd1: number;
  /** 每次道具鼓舞消耗 */
  inspireCost2List: number[];
  /** 最大道具鼓舞次数 */
  inspireMax2: number;
  /** 战斗最大回合数 */
  round: number;
  /** 每次气运鼓舞生命、攻击、防御、敏捷提升 */
  inspireAdd2: number;
  /** 最大事件数量 */
  maxEven: number;
  /** 每次进度气运损耗系数 */
  lossRateList: number[];
  /** 气运一键解锁条件 */
  finishedLv: number;
  /** 前端使用空字段 */
  index: number;
}

export interface IConfigDailyChallenge {
  /** id */
  id: number;
  /** 挑战对应伤害值 */
  harmNum: number;
  /** 挑战阶段奖励 */
  reward01List: number[][];
  /** 挑战BOSS */
  monsterId: number[];
  /** 每日免费挑战次数 */
  free: number;
  /** 挑战最大回合数 */
  roundMax: number;
  /** 概率掉落奖励道具显示 */
  showItemsList: number[];
}

export interface IConfigDailyChallengeRank {
  /** id */
  id: number;
  /** 排名 */
  taskList: number[];
  /** 排行榜奖励 */
  rewardList: number[][];
}

export interface IConfigEffect {
  /** id */
  id: number;
  /** bundle */
  bundle: string;
  /** filename */
  fileName: string;
  /** action */
  actionName: string;
  /** audioId */
  audioId: number;
}

export interface IConfigErrorcode {
  /** id */
  id: number;
  /** 计数 */
  count: number;
  /** 描述前端展示 */
  desc: string;
}

export interface IConfigEvent {
  /** id */
  id: number;
  /** 事件类型 */
  type: number;
  /** 关联建筑ID */
  buildId: number;
  /** 宝箱奖励 */
  rewardRateList: number[][];
  /** 奖励解锁1 */
  unlockRewardList: number[][];
  /** 奖励解锁2 */
  unlockList: number[];
  /** 事件名称 */
  name: string;
  /** 事件描述 */
  des: string;
  /** 答案1 */
  answer1: number;
  /** 答案2 */
  answer2: number;
  /** 派遣方案 */
  send1: number;
  /** 派遣子方案 */
  send2: number;
  /** 派遣数量 */
  send3: number;
  /** 事件星数 */
  star: number;
  /** 事件奖励1 */
  reward1List: number[][];
  /** 事件奖励2 */
  reward2List: number[][];
  /** 事件奖励3 */
  reward3List: number[][];
  /** 事件奖励4 */
  reward4List: number[][];
  /** 事件奖励5 */
  reward5List: number[][];
}

export interface IConfigEvent2 {
  /** id */
  id: number;
  /** 狩猎名字 */
  name: string;
  /** 事件类型 */
  type: number;
  /** 触发类型 */
  actType: number;
  /** 冷却时间 */
  coldTime: number;
  /** 事件解锁条件 */
  unlock: number[];
  /** 事件减益 */
  debuff: number[];
  /** 预制体 */
  prefabPath: string[];
  /** 形象ID */
  spineId: string;
  /** 小偷反馈文案ID */
  word: number[];
  /** 显示文案ID */
  word02: number[];
  /** 击退所需次数随机 */
  attackNum: number[];
  /** 完成奖励从池子中随机一个 */
  rewardRateList: number[][];
  /** 事件触发时间点 */
  time: number[];
  /** 最多同时存在事件数量 */
  max: number;
  /** 循环事件开启条件 */
  cycleOpen: number[];
}

export interface IConfigFightBullet {
  /** id */
  id: number;
  /** 出现spine文件 */
  startSpine: string;
  /** 出现动画 */
  startAni: string;
  /** 飞行spine文件 */
  flySpine: string;
  /** 飞行动画 */
  flyAni: string;
  /** 爆炸spine文件 */
  boomSpine: string;
  /** 爆炸动画 */
  boomAni: string;
  /** 飞行速度 */
  speed: number;
}

export interface IConfigFightCharacter {
  /** id */
  id: number;
  /** 形象ID */
  artId: number;
  /** 动效列表 */
  actionMap: any;
}

export interface IConfigFracture {
  /** id */
  id: number;
  /** 所属层级 */
  floor: number;
  /** 触发类型 */
  type: number;
  /** 触发权重 */
  rate: number;
  /** 活动期间可探索上限 */
  max: number;
  /** 活动期间累计探索次数解锁 */
  unlockNum: number;
  /** NPC备选怪物ID */
  monsterId: number[];
  /** 对应关联ID */
  linkId: number[];
  /** 关联宝箱ID */
  boxId: number;
  /** 搜索消耗 */
  cost01: number[];
  /** 第二次及以后挑战消耗 */
  cost02: number[];
  /** 是否可以绕过 */
  isSkip: number;
  /** 是否存日志 */
  isSave: number;
  /** 完成是否可以进入下一层 */
  isNext: number;
  /** 是否可以战盟互助 */
  isHelp: number;
  /** 冷却时间~单位秒 */
  coldTime: number;
  /** 触发必得 */
  reward01List: number[];
  /** 完成概率获得其中一种道具 */
  reward02List: number[][];
  /** 单龙爪转化 */
  transformList: number[][];
  /** 活动1期间~周几~时间 */
  act01TimeList: number[][];
  /** 活动2期间~周几~时间 */
  act02TimeList: number[][];
  /** 解锁顺序ID */
  unlock: number[];
}

export interface IConfigFracture_draw {
  /** ID */
  id: number;
  /** 所属层数 */
  floor: number;
  /** 奖励 */
  rewardList: number[];
  /** 权重 */
  rate: number;
  /** 解锁条件 */
  unlock: number;
  /** 排序 */
  order: number;
}

export interface IConfigFracture_even {
  /** 事件类型 */
  id: number;
  /** 事件描述文本 */
  desc: number;
  /** 冷却时间~单位秒 */
  coldTime: number;
  /** 是否可以战盟互助 */
  isHelp: number;
  /** 处理需要额外消耗 */
  costDo: number[];
  /** 回答1 */
  answer1: number;
  /** 回答2 */
  answer2: number;
  /** 恭喜获得界面文本1 */
  rewardMsg1: number;
  /** 恭喜获得界面文本2 */
  rewardMsg2: number;
  /** 奖励1 */
  reward01List: number[][];
  /** 奖励2 */
  reward02List: number[][];
}

export interface IConfigFriednFame {
  /** 美名效果ID */
  id: number;
  /** 美名描述 */
  des: string;
  /** 对应美名等级 */
  costList: number[];
  /** 加成属性ID */
  attrId: number;
  /** 对应加成属性数值 */
  addList: number[][];
}

export interface IConfigFriednFameLv {
  /** id */
  id: number;
  /** 名字 */
  name: string;
  /** 对应友好度-因果 */
  levelUpNeed1: number;
  /** 对应才华值-天命 */
  levelUpNeed2: number;
  /** 升级所需挚友数量 */
  levelUpNeed3: number;
}

export interface IConfigFriednReward {
  /** id */
  id: number;
  /** 累计获得仙友数量 */
  friendNum: number;
  /** 累计总因果值-友好 */
   yinguo: number;
  /** 累计总天命值-才华 */
  tianming: number;
  /** 对应属性加成 */
  attrAdd: number[][];
  /** 成就描述 */
  des: string;
}

export interface IConfigFriend {
  /** id */
  id: number;
  /** 名字 */
  name: string;
  /** 声音 */
  voice: number[];
  /** 种族类别 */
  type: number;
  /** 标签类别 */
  labelType: number[];
  /** 道具类型 */
  goodsType: number;
  /** 1男2女 */
  gender: number;
  /** 品质 */
  color: number;
  /** 排序 */
  order: number;
  /** 初始友好度-因果 */
  friendlyFirst: number;
  /** 初始才华值-天命 */
  talentFirst: number;
  /** 重复获得仙友转化 */
  changeList: number[];
  /** 挚友战将 */
  heroList: number[];
  /** 是否显示获取路径为知己目标 */
  isTarget: number;
  /** 解锁条件 */
  unlock: number;
  /** 解锁数值 */
  unlockNum: number;
  /** 是否关闭服务端自动解锁 */
  closeAuto: number;
  /** 美名效果ID */
  fameId: number;
  /** 美名效果ID */
  fameIdList: number[];
  /** 仙友互动对话文案 */
  chat: string;
  /** 一键谈心次数 */
  fast: number;
  /** 一键谈心解锁条件 */
  unlockFastList: number[];
  /** 增加友好和才华道具 */
  addItemList: number[][];
}

export interface IConfigFriendHeroSkill {
  /** id */
  id: number;
  /** 名字 */
  name1: string;
  /** 等级 */
  level: number;
  /** 挚友门客技能固定值 */
  value1: number[][];
  /** 升级消耗1 */
  cost1: number;
  /** 升级消耗道具1 */
  costItemId1: number;
  /** 名字 */
  name2: string;
  /** 挚友门客技能百分比 */
  value2: number;
  /** 升级消耗2 */
  cost2: number;
  /** 升级消耗道具2 */
  costItemId2: number;
}

export interface IConfigFriendPicture {
  /** id */
  id: number;
  /** 名字 */
  name: string;
  /** 图鉴知己ID */
  friendId: number[];
  /** 图鉴激活奖励 */
  rewardList: number[][];
}

export interface IConfigFriendSkill {
  /** id */
  id: number;
  /** 名字 */
  name: string;
  /** 种族类别 */
  type: number;
  /** 所需友好度 */
  friendlyNeed: number;
}

export interface IConfigFriendSkillLv {
  /** id */
  id: number;
  /** 技能值百分比 */
  value: number;
  /** 随机权重 */
  rate: number;
  /** 技能初始值 */
  valueFirst: number;
  /** 许愿石培养消耗 */
  costList: number[];
  /** 许愿石保底次数 */
  max: number;
}

export interface IConfigGain {
  /** id */
  id: number;
  /** 获取路径描述 */
  dec: string;
  /** 显示排序 */
  order: number;
}

export interface IConfigGuide {
  /** ID */
  id: number;
  /** 引导分类 */
  type: number;
  /** 表现类型 */
  showType: number;
  /** 表现类型参数 */
  args: string;
}

export interface IConfigGuideGameTalk {
  /** ID */
  id: number;
  /** 脚本分组 */
  type: number;
  /** 形象ID */
  skinId: number;
  /** 角色名称 */
  name: string;
  /** 位置 */
  position: number;
  /** 文字内容 */
  msg: string;
}

export interface IConfigGuideGameTast {
  /** id */
  id: number;
  /** 任务标题 */
  title: string;
  /** 任务描述 */
  des: string;
  /** 完成值 */
  doneNum: number;
  /** 点击一次女娲获取的气运值 */
  rewardNum: number;
}

export interface IConfigGuidePath {
  /** id */
  id: number;
  /** 系统名称 */
  name: string;
  /** 分类ID */
  typeId: number;
  /** 路由页面 */
  routeNameList: string[];
  /** 所在大节点 */
  parentName: string;
  /** 点击节点名 */
  nodeClickName: string;
  /** 重做 */
  redo: number;
  /** 确认方式 */
  checkType: number;
  /**  */
  msg: string;
  /** 隐藏阴影 */
  hideShadow: number;
  /** 不飞手指/手指直达 */
  noFly: number;
}

export interface IConfigGuideTalk {
  /** ID */
  id: number;
  /** 脚本分组 */
  type: number;
  /** 形象ID */
  skinId: number;
  /** 角色名称 */
  name: string;
  /** 位置 */
  position: number;
  /** 文字内容 */
  msg: string;
}

export interface IConfigGuideV2 {
  /** ID */
  id: number;
  /** 引导类型 */
  type: number;
  /** 参数 */
  args: string;
  /** 位置 */
  position: string;
  /** 皮肤ID */
  skinId: string;
  /** 名称 */
  name: string;
  /** 强制 */
  force: number;
  /** 自动下一步 */
  auto: number;
}

export interface IConfigHead {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 获取途径 */
  source: string;
  /** 时间类型 */
  timeType: number;
  /** 持续时长-天 */
  timeLat: number;
  /** 重复获得转化为道具 */
  changeItemList: number[][];
  /** 图标id */
  iconId: number;
  /** 是否隐藏 */
  isHide: number;
  /** 属性加成 */
  attrAdd: number[][];
}

export interface IConfigHeadShow {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 获取途径 */
  source: string;
  /** 性别--男1女2 */
  sex: number;
  /** 图标id */
  iconSpr: string;
  /** 是否隐藏 */
  isHide: number;
  /** 类型 */
  type: number;
  /** 属性加成 */
  attrAdd: number[][];
  /** 解锁条件 */
  unlock: number[];
}

export interface IConfigHelp {
  /** 编号 */
  id: number;
  /** 帮助内容 */
  helpText: string;
}

export interface IConfigHero {
  /** id */
  id: number;
  /** 名字 */
  name: string;
  /** 声音 */
  voice: number[];
  /** 解锁奖励 */
  unlockRewardList: number[][];
  /** 获取路径 */
  gain: number;
  /** 战斗预览 */
  skinId: number;
  /** 道具小图标 */
  itemIcon: string;
  /** 入住战将卡牌ID */
  cardId: string;
  /** 种族类别 */
  type: number;
  /** 1男2女 */
  gender: number;
  /** 道具类型 */
  goodsType: number;
  /** 品质 */
  color: number;
  /** 该伙伴是否db */
  isNaked: number;
  /** 初始资质 */
  quality: number;
  /** 排序 */
  order: number;
  /** 门客等级上限 */
  maxLv: number;
  /** 匹配灵兽ID */
  petId: number;
  /** 头衔类别 */
  headType: number;
  /** 获取途径 */
  getWay: number;
  /** 初始属性 */
  attrFirstAdd: number[][];
  /** 门客普通技能 */
  skill1List: number[];
  /** 光环 */
  lightList: number[];
  /** 精进之后的种族印记 */
  yinjiList: number[];
  /** 精进解锁的技能 */
  talentList: number[];
  /** 是否可以精进天赋 */
  isFine: number;
  /** 天赋精进突破条件 */
  breakList: number[];
  /** 上阵徒弟槽位解锁等级 */
  unlockLvList: number[];
  /** 战力生命系数 */
  hpRatio: number;
  /** 战力攻击系数 */
  attackRatio: number;
  /** 战力防御系数 */
  defendRatio: number;
  /** 战力敏捷系数 */
  speedRatio: number;
  /** 十连开启需要伙伴数量 */
  open1: number;
  /** 十连开启需要伙伴总等级 */
  open2: number;
  /** 技能点重置消耗 */
  resetCostList: number[][];
  /** 是否可觉醒 */
  isAwaken: number;
  /** 觉醒技能 */
  awakenList: number[];
  /** 觉醒普通技能 */
  awakeSkillList: number[];
  /** 缘分 */
  fateList: number[];
  /** 羁绊技能id */
  eltUnitIdList: number[];
  /** 自述 */
  des: string;
}

export interface IConfigHeroBreak {
  /** 突破等级 */
  id: number;
  /** 等级上限 */
  levelMax: number;
  /** 突破消耗(物品id,数量） */
  breakCostList: number[][];
  /** 增加资质 */
  qualityAdd: number;
  /** 属性增加 */
  attrAddList: number[][];
}

export interface IConfigHeroLv {
  /** 等级 */
  id: number;
  /** 消耗 */
  cost: number;
  /** 1资质增加战力 */
  powerAdd: number;
  /** 1资质对应基础属性 */
  attrAddList: number[][];
}

export interface IConfigHeroPicture {
  /** id */
  id: number;
  /** 名字 */
  name: string;
  /** 组合战将ID */
  heroId: number[];
  /** 对应等级 */
  level: number[];
  /** 对应属性加成 */
  attrAdd: number[][];
  /** 描述 */
  des: string;
}

export interface IConfigHeroSkill {
  /** 技能等级 */
  id: number;
  /** 美术ICON */
  iconId: number;
  /** 技能名称 */
  name: string;
  /** 技能描述 */
  des: string;
  /** 技能类型 */
  type: number;
  /** 解锁条件 */
  unlockList: number[];
  /** 星级数量 */
  star: number;
  /** 等级上限 */
  maxLv: number;
  /** 升级消耗 */
  costList: number[][];
  /** 初始技能属性值 */
  firstAttraList: number[][];
  /** 升级属性增加 */
  addList: number[][];
  /** 突破条件 */
  breakList: number[];
}

export interface IConfigHeroSkillLv {
  /** 技能等级 */
  id: number;
  /** 对应等级 */
  level: number;
  /** 升级消耗 */
  costList: number[];
  /** 是否去掉升级按钮 */
  isOrNot: number;
  /** 生命攻击防御增加固定值 */
  powerAdd1: number[][];
  /** 生命攻击防御增加百分比 */
  powerAdd2: number;
  /** 所有人族生命攻击防御增加百分比 */
  heroPowerAdd1: number;
  /** 所有神族生命攻击防御增加百分比 */
  heroPowerAdd2: number;
  /** 所有战将生命攻击防御增加百分比 */
  heroPowerAdd3: number;
}

export interface IConfigHeroTalentSkill {
  /** 天赋ID */
  id: number;
  /** 天赋名称 */
  name: string;
  /** 天赋描述 */
  des: string;
  /** 对应战将精进等级 */
  costList: number[];
  /** 加成属性ID */
  attrId: number;
  /** 对应加成属性数值 */
  addList: number[][];
}

export interface IConfigHome {
  /** ID */
  id: number;
  /** 建筑名称 */
  name: string;
  /** 废墟文件名 */
  spineName2: string;
  /** 废墟图标 */
  iconName2: string;
  /** 地图坐标 */
  place: number[];
  /** 图片文件名 */
  spineName: string;
  /** 小图标文件名 */
  iconName: string;
  /** 解锁繁荣度 */
  unlock: number;
  /** 成就奖励 */
  reward01List: number[][];
  /** 触发背景解锁 */
  bgOpen: string;
  /** 所有建筑全部建1级就奖励 */
  reward02List: number[][];
  /** 所有建筑全部建2级就奖励 */
  reward03List: number[][];
  /** 所有建筑全部建3级就奖励 */
  reward04List: number[][];
  /** 重复获得永久道具自动转化 */
  change: number[][];
}

export interface IConfigHorse {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 描述 */
  desc: string;
  /** 主属性ID */
  mainId: number;
  /** 价格 */
  costList: number[];
  /** 初始特殊属性 */
  firstList: number[][];
  /** 升阶特殊属性加成 */
  addList: number[][];
  /** 初始坐骑id */
  firstHorseId: number;
}

export interface IConfigHorseLv {
  /** ID */
  id: number;
  /** 升阶所需等级 */
  lvNeed: number;
  /** 升级所需经验 */
  expNeed: number;
  /** 点一次获得经验 */
  lvExpList: number[][];
  /** 点一次消耗 */
  lvCostList: number[][];
  /** 升阶消耗 */
  breakCostList: number[][];
  /** 一经验对应提升属性 */
  expAddList: number[][];
  /** 升阶基础属性加成 */
  breakAddList: number[][];
  /** 初始基础属性 */
  firstList: number[][];
}

export interface IConfigHunt {
  /** ID */
  id: number;
  /** 怪物名称 */
  name: string;
  /** 怪物美术ID */
  monsterId: number;
  /** 怪物战力 */
  monsterPower: number[][];
  /** 击杀奖励1 */
  reward1List: number[];
  /** 击杀奖励2 */
  reward2List: number[];
  /** 击杀奖励3 */
  reward3List: number[];
  /** 古境积分ID */
  coinId: number;
  /** 番天印ID */
  sealId: number;
  /** 结算时间-晚上10点 */
  timeEnd: number;
  /** 开启时间0点 */
  timeStart: number;
  /** 战斗最大回合数 */
  round: number;
}

export interface IConfigHuntBoss {
  /** ID */
  id: number;
  /** 对应掉血量 */
  harm: number;
  /** 对应奖励1 */
  reward1List: number[];
  /** 对应奖励2 */
  reward2List: number[];
  /** BOSS初始属性 */
  bossAttr: number[][];
  /** BOSS表现ID */
  monsterId: number;
  /** 每次挑战回合数 */
  round: number;
  /** BOSS生命值波动 */
  rate: number;
  /** 中午开启时间 */
  start1: number;
  /** 晚上开启时间 */
  start2: number;
  /** 中午结束时间 */
  over1: number;
  /** 晚上结束时间 */
  over2: number;
  /** 古境积分ID */
  coinId: number;
  /** 每日0点重置 */
  max: number;
}

export interface IConfigHuntBossRank {
  /** 排行榜ID */
  id: number;
  /** 对应名次 */
  order: number;
  /** 排行榜奖励 */
  reward1List: number[][];
  /** 击杀奖励 */
  reward2List: number[][];
}

export interface IConfigItem {
  /**  */
  id: number;
  /** 名称 */
  name: string;
  /** 描述 */
  des: string;
  /** 图标ID */
  iconId: string;
  /** 品质 */
  color: number;
  /** 去特效 */
  closeSpecial: number;
  /** 道具类型 */
  goodsType: number;
  /** 道具类型1 */
  type1: number;
  /** 道具类型1的子类型 */
  type1Son1List: number[][];
  /** 背包功能 */
  type2List: number[];
  /** 活动ID */
  type2Son: number;
  /** 跳转途径 */
  jumplist: number[];
}

export interface IConfigJump {
  /** id */
  id: number;
  /** 跳转描述 */
  des: string;
  /** 模块要求 */
  systemOpenId: number;
  /** 类型 */
  type: number;
  /** 页面名称 */
  pageName: string;
  /** 所在页面 */
  parentPageList: string[];
  /** 打开界面后的点击按钮 */
  nodeClick: string;
  /** 参数 */
  args: string;
}

export interface IConfigLand {
  /** 领地皮肤ID */
  id: number;
  /** 美术资源 */
  iconId: number;
}

export interface IConfigLeader {
  /** id */
  id: number;
  /** 主角身份 */
  name: string;
  /** 对应等阶 */
  jieji: number;
  /** 对应等级 */
  jingjie2: string;
  /** 对应境界 */
  jingjie: string;
  /** 境界对应的颜色Id */
  colorId: number;
  /** 对应境界图标 */
  jingjieIcon: string;
  /** 等级对应图标底 */
  jingjieBg: string;
  /** 进阶对应图标底 */
  jinjieBg: string;
  /** 所需运势 */
  speed: number;
  /** 所需功德 */
  virtue: number;
  /** 每日奖励仙玉 */
  dayReward: number;
  /** 境界进阶主角属性加成 */
  attrAdd: number[][];
  /** 获得资源 */
  unlockList: number[][];
  /** 谈心上限 */
  chatMax: number;
  /** 谈心恢复时间 */
  chatTime: number;
  /** 徒弟体力槽大小 */
  trainMax: number;
  /** 徒弟基础培养获得阅历 */
  trainReward: number;
  /** 徒弟培养气运消耗 */
  trainCost: number;
  /** 徒弟成年繁荣度加成 */
  trainAdd: number;
  /** 玩家初始资源 */
  fistItemList: number[][];
  /** 改名卡消耗道具数量 */
  nameCostList: number[];
  /** 初始基础属性 */
  fistAttrList: number[][];
  /** 最大等级 */
  maxLv: number;
  /** 默认进入府邸的等级 */
  fudiLv: number;
  /** 2倍战斗解锁所需主角等级 */
  fast2Battle: number;
  /** 3倍战斗解锁所需主角等级 */
  fast3Battle: number;
  /** 吸血概率 */
  xixueRate: number;
  /** 手指指引冷却时间 */
  guideColdTime: number;
}

export interface IConfigLeaderName {
  /** id */
  id: number;
  /** 品质描述 */
  des: string;
  /** 性别 */
  sex: number;
}

export interface IConfigLeaderSkin {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 血条位置 */
  placeId: number;
  /** 冲刺动画音效 */
  music: number;
  /** 皮肤标签类型 */
  type: number;
  /** 是否展示立绘 */
  lihui: number;
  /** 时间类型 */
  timeType: number;
  /** 持续时长-天 */
  timeLat: number;
  /** 性别--男1女2 */
  sex: number;
  /** 重复获得转化为道具 */
  changeItemList: number[][];
  /** 对应皮肤表现ID */
  spineId: number;
  /** 获取途径 */
  source: string;
  /** 解锁条件 */
  unlock: number[];
  /** 激活消耗 */
  activateCostList: number[];
  /** 是否隐藏 */
  isHide: number;
  /** 属性加成 */
  attrAdd: number[][];
  /** 府邸界面 */
  renderScale1: number[];
  /** 换装界面 */
  renderScale2: number[];
  /** 关卡界面 */
  renderScale3: number[];
  /** 战盟BOSS界面 */
  renderScale4: number[];
  /** 演武场排名界面 */
  renderScale5: number[];
  /** 游历界面 */
  renderScale6: number[];
  /** 天荒古境BOSS */
  renderScale7: number[];
  /** 天荒古境灵兽入侵 */
  renderScale8: number[];
  /** 关卡战斗 */
  renderScale9: number[];
  /** 演武场战斗 */
  renderScale10: number[];
  /** 狩猎灵兽战斗 */
  renderScale11: number[];
  /** 狩猎BOSS战斗 */
  renderScale12: number[];
  /** 战盟BOSS战斗 */
  renderScale13: number[];
  /** 查看他人信息展示界面 */
  renderScale14: number[];
  /** 至宝界面 */
  renderScale15: number[];
  /** 宫殿前方主角大小 */
  renderScale16: number[];
  /** 宫殿后方三个大小 */
  renderScale17: number[];
  /** 荣耀阁大小 */
  renderScale18: number[];
  /** 时空待机 */
  renderScale19: number[];
  /** 时空战斗场景 */
  renderScale20: number[];
  /** 时空排行榜 */
  renderScale21: number[];
  /** 繁荣度比拼排行榜 */
  renderScale22: number[];
  /** 繁荣度比拼界面 */
  renderScale23: number[];
}

export interface IConfigLuckDraw {
  /** ID */
  id: number;
  /** 抽中奖励道具 */
  reward1List: number[];
  /** 抽取道具的概率 */
  rate: number;
  /** 累计抽取N次必得大奖 */
  maxTime: number;
  /** 抽奖消耗道具ID */
  cardId: number;
  /** 十连抽消耗道具 */
  cost10: number[];
  /** 看1次视频抽取的次数 */
  draw: number;
  /** 每日看视频上限 */
  maxDay: number;
}

export interface IConfigMail {
  /** 编号 */
  id: number;
  /** 邮件标题 */
  mailTitle: string;
  /** 邮件内容 */
  mailText: string;
  /** 类别 */
  type: number;
  /** 特殊时长-天 */
  specialTime: number;
  /** 默认时长-天 */
  defaultTime: number;
}

export interface IConfigMessage {
  /** 编号 */
  id: number;
  /** 服务器消息码 */
  code: string;
  /** 标题 */
  title: string;
  /** 文本 */
  text: string;
}

export interface IConfigMonster {
  /** id */
  id: number;
  /** 怪物形象 */
  monsterShowId: number;
  /** 怪物属性 */
  attr: number[][];
}

export interface IConfigMonsterMatrix {
  /** id */
  id: number;
  /** 怪物数量 */
  num: number;
  /** 位置列表 */
  positionList: number[][];
  /** 形象id */
  skinId: number[];
  /** 填正就是向左的速度 */
  speed: number;
}

export interface IConfigMonsterShow {
  /** ID */
  id: number;
  /** 名字 */
  name: string;
  /** 血条位置 */
  placeId: number;
  /** 怪物头像 */
  iconId: string;
  /** spineId */
  spineId: number;
  /** 主线关卡待机 */
  renderScale1: number[];
  /** 主线关卡战斗 */
  renderScale2: number[];
  /** 天荒BOSS待机 */
  renderScale3: number[];
  /** 天荒BOSS战斗 */
  renderScale4: number[];
  /** 战盟待机 */
  renderScale5: number[];
  /** 战盟战斗 */
  renderScale6: number[];
  /** 天荒灵兽待机 */
  renderScale7: number[];
  /** 天荒灵兽战斗 */
  renderScale8: number[];
  /** 时空裂隙 */
  renderScale9: number[];
}

export interface IConfigMusic {
  /** ID */
  id: number;
  /** 音频文件 */
  musicName: string;
  /** 网络地址 */
  url: string;
  /** 音量 */
  volume: number;
  /** 播放时间间隔 */
  cd: number;
}

export interface IConfigPet {
  /** id */
  id: number;
  /** 默认皮肤 */
  firstSkin: number;
  /** 跳转途径 */
  jumplist: number[];
  /** 拓展皮肤 */
  skin: number[];
  /** 灵兽名称 */
  name: string;
  /** 品质颜色 */
  color: number;
  /** 初始资质 */
  qualityFirst: number;
  /** 升级资质增加 */
  qualityAdd: number;
  /** 升级消耗灵兽果 */
  trainAddList: number[];
  /** 最高等级 */
  levelMax: number;
  /** 重复获得自动转换 */
  rewardFruitList: number[][];
  /** 初始技能数量 */
  skillFirst: number;
  /** 是否可以觉醒 */
  wakeOrNot: number;
  /** 觉醒消耗数量(按次数递增) */
  wakeCostNumList: number[];
  /** 觉醒消耗道具ID */
  wakeCostId: number;
  /** 觉醒技能增加数量 */
  wakeSkillAdd: number;
  /** 觉醒一次等级上限增加 */
  wakeLvAdd: number;
  /** 超过等级上限之后升级灵兽果消耗 */
  wakeLvCostList: number[];
  /** 超过等级上限之后升级资质增加 */
  wakeQualityAdd: number;
}

export interface IConfigPetSkill {
  /** id序号 */
  id: number;
  /** 技能描述 */
  des: string;
  /** iconID */
  iconId: number;
  /** 初始技能值 */
  firstNum: number;
}

export interface IConfigPetSkillRefresh {
  /** id序号 */
  id: number;
  /** 金币洗练技能加成 */
  skillAdd1: number;
  /** 金币洗练对应权重 */
  weight1: number;
  /** 洗练石洗练技能加成 */
  skillAdd2: number;
  /** 洗练石洗练对应权重 */
  weight2: number;
  /** 洗练石洗练消耗 */
  cost2List: number[];
  /** 洗练石保底次数 */
  max: number;
}

export interface IConfigPetSkin {
  /** id */
  id: number;
  /** 灵兽名称 */
  name: string;
  /** 品质颜色 */
  color: number;
  /** 解锁条件 */
  unlockType: number;
  /** 解锁数值 */
  unlockNum: number;
  /** 解锁增加资质 */
  add1: number;
  /** 解锁增加技能个数 */
  add2: number;
  /** 解锁增加灵兽等级上限 */
  add3: number;
  /** 图标ID */
  iconId: string;
  /** 卡牌ID */
  cardId: string;
  /** spineId */
  spineId: string;
  /** 形象 */
  showFirst: number[];
  /** 战将界面待机 */
  renderScale1: number[];
  /** 圣殿界面待机 */
  renderScale2: number[];
  /** 恭喜获得界面 */
  renderScale3: number[];
}

export interface IConfigPost {
  /** id */
  id: number;
  /** 对应仙鹤ID */
  iconId: number;
  /** 升级所需货运累计次数 */
  need: number;
  /** 挂机时长-秒 */
  time: number;
  /** 每日挂机次数 */
  maxNum: number;
  /** 每次挂机气运消耗次数上限 */
  costTimeMax: number;
  /** 气运投放数量 */
  costList: number[];
  /** 每日首次挂机必得 */
  reward1List: number[][];
  /** 等级对应新增解锁道具 */
  unlockIdList: number[];
  /** 每次投气运概率获得道具 */
  reward2List: number[][];
  /** 加速消耗 */
  speedCostList: number[];
}

export interface IConfigPupil {
  /** id */
  id: number;
  /** 品质描述 */
  des: string;
  /** 天资背景 */
  talentBg: string;
  /** 品质颜色 */
  color: number;
  /** 培养获得阅历加成 */
  rewardAdd: number;
  /** 徒弟成年繁荣度品质加成 */
  rate: number;
  /** 培养1次进度增加 */
  trainAdd: number;
  /** 随机招徒抽中权重 */
  weight: number;
  /** 解锁的总因果值 */
  unlock: number;
  /** 指定招收仙玉消耗 */
  cost: number;
  /** 初始属性条数 */
  firstNum: number;
  /** 出师领悟随机属性范围值 */
  rewardAttrList: number[];
  /** 出师获得书籍ID */
  bookIdList: number[];
  /** 出师获得随机书籍数量值 */
  bookNum: number;
  /** 结拜成功获得道具 */
  rewardList: number[];
  /** 出师领悟随机属性ID */
  rewardAttrIdList: number[];
  /** 初始随机属性ID和值 */
  firstDefendList: number[][];
  /** 成年基础繁荣度 */
  basicSpeed: number;
  /** 徒弟培养成年总进度 */
  finish: number;
  /** 成年未婚徒弟上限 */
  max: number;
  /** 联姻徒弟基础属性增加倍数上限 */
  maxAdd: number;
  /** 解锁徒弟培养槽位所需主角等级 */
  unlockPlaceList: number[];
  /** 任命徒弟槽位解锁所需徒弟出师数量 */
  unlockWorkPlaceList: number[];
  /** 任命徒弟额外加成百分比 */
  placeAdd: number[];
  /** 活力恢复时间 */
  time: number;
  /** 结伴发布时间 */
  time2: number;
  /** 基础属性前端展现对应进度 */
  showList: number[];
  /** 弟子一键培养解锁主角等级 */
  unlockFast: number;
  /** 一次点击消耗体力数量 */
  costNum: number;
  /** 生成属性最多ID数量 */
  maxAttr: number;
  /** 触发双胞胎概率 */
  doubleRate: number;
}

export interface IConfigPupilDrop {
  /** id */
  id: number;
  /** 掉落 */
  drop: number[];
  /** 初始存储上限 */
  max: number;
  /** 每日掉落概率 */
  dropRate: number[];
}

export interface IConfigPupilName {
  /** id */
  id: number;
  /** 名字描述 */
  des: string;
  /** 性别 */
  sex: number;
  /** 徒弟皮肤ID */
  skinId: number;
  /** 结拜机器人品质 */
  colorAi: number;
  /** 结拜机器人基础属性 */
  attrAi01: number[][];
  /** 结拜机器人随机一个特殊属性 */
  attrAi02: number[][];
  /** 结拜弟子最低个数 */
  numberAi: number;
}

export interface IConfigPupilSkin {
  /** id */
  id: number;
  /** 待机动画ID */
  spineId: number;
  /** 头像ID */
  headId: string;
  /** 性别 */
  sex: number;
}

export interface IConfigRight {
  /** id */
  id: number;
  /** 系统名称 */
  name: string;
  /** 可开启条件 */
  openList: number[][];
  /** 月卡立即获取 */
  reward1List: number[][];
  /** 月卡每日领取 */
  reward2List: number[][];
  /** 终身卡立即获取 */
  reward3List: number[][];
  /** 终身卡每日领取 */
  reward4List: number[][];
}

export interface IConfigSetUp {
  /** id */
  id: number;
  /** 系统名称 */
  name: string;
}

export interface IConfigShop {
  /** id */
  id: number;
  /** 道具id */
  itemsList: number[][];
  /** 商店类型 */
  type: number;
  /** 解锁条件 */
  unlockType: number;
  /** 解锁数值 */
  unlockNum: number;
  /** 国内充值ID */
  rmbId: number;
  /** 货币id */
  cointype: number;
  /** 货币价格 */
  coinPrice: number;
  /** 每次购买价格增加 */
  priceAdd: number;
  /** 限购类型 */
  maxtype: number;
  /** 限购次数 */
  max: number;
  /** 排序 */
  sort: number;
}

export interface IConfigSkillShow {
  /** ID */
  id: number;
  /** name */
  name: string;
  /** scrpitName */
  scrpitName: string;
  /** actName */
  actName: string;
  /** bulletId */
  bulletId: number;
  /** effectAudio1 */
  effectAudio1: number;
  /** effectAudioTime1 */
  effectAudioTime1: number;
  /** effectAudio2 */
  effectAudio2: number;
  /** effectAudioTime2 */
  effectAudioTime2: number;
}

export interface IConfigSoul {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 品质 */
  color: number;
  /** 阶级上限 */
  max: number;
  /** 每阶升级消耗 */
  costList: number[];
  /** 初始属性 */
  firstAttrList: number[][];
  /** 升级属性增加 */
  addAttrList: number[][];
  /** 初始主属性权重 */
  firstRateList: number[];
  /** 随机到主属性时两个权重增加 */
  addRateList: number[];
  /** 初始主属性 */
  firstMasterAttrList: number[];
  /** 副属性集合 */
  sunAttrList: number[];
  /** 进阶主副属性增加 */
  addMasterSonAttrList: number[];
  /** 放生获得初始升级道具 */
  freeRewardList: number[];
  /** 价格购买 */
  priceList: number[];
  /** 免费刷新概率 */
  freeRefreshRate: number;
  /** 高级刷新概率 */
  costRefreshRate: number;
  /** 每日免费刷新次数 */
  freeRefreshMax: number;
  /** 每日高级刷新次数 */
  costRefreshMax: number;
  /** 高级刷新消耗 */
  costRefreshCostList: number[];
  /** 升级道具ID */
  costId: number;
  /** 刷出橙魂的保底次数 */
  min: number;
  /** 放生返还比例 */
  returnRate: number;
  /** 初始槽位数量 */
  firstPlace: number;
  /** 槽位上限 */
  maxPlace: number;
  /** 解锁槽位消耗 */
  costPlaceList: number[];
  /** 免费刷新概率展示 */
  rateShow1: number[];
  /** 高级刷新概率展示 */
  rateShow2: number[];
  /** 吞噬主属性继承 */
  inherit: number[];
  /** 吞噬最高次数 */
  eatMax: number[];
  /** 金色吞噬获得随机道具 */
  reward01List: number[][];
  /** 红色吞噬获得随机道具 */
  reward02List: number[][];
  /** 吞噬消耗 */
  eatCostList: number[][];
}

export interface IConfigSoulPicture {
  /** id */
  id: number;
  /** 名字 */
  name: string;
  /** 图鉴兽魂ID */
  soulId: number[];
  /** 激活随机出一个战斗属性 */
  firstAttr: number[][];
  /** 对应刷新概率 */
  refreshRate: number[];
  /** 对应刷新属性 */
  refreshAttr: number[][];
  /** 刷新消耗道具 */
  cost: number[];
  /** 品质范围 */
  color: number[][];
}

export interface IConfigSpineShow {
  /** ID */
  id: number;
  /** 实例化脚本名 */
  scrpitName: string;
  /** 冲锋动作 */
  runScrpit: string;
  /** 冲锋缩放 */
  gameScale: number[];
  /** 冲刺烟雾 */
  showYanWu: number;
  /** 冲锋倍数 */
  gameTimeScale: number[];
  /** 战斗比例大小 */
  renderScale: number[];
  /** 卡片 */
  halfRes: string;
  /** 立绘 */
  imageRes: string;
  /** 怪物动画 */
  spineRes: string;
  /** 怪物动画字段 */
  prefabPath: string;
  /** 技能ID */
  skillIdList: number[];
  /** 连击id */
  doubleId: number;
  /** 眩晕buff效果 */
  stunBuffId: number;
}

export interface IConfigSystemOpen {
  /** id */
  id: number;
  /** 系统名称 */
  name: string;
  /** 开启条件 */
  openList: number[];
  /** 是否做开启动画 */
  animation: number;
  /** 是否在主角升级界面显示 */
  show: number;
  /** 系统未开启的时候需隐藏图标 */
  hide: number;
  /** 是否在府邸界面显示 */
  fudiShow: number;
  /** 是否可收缩图标 */
  isFix: number;
  /** 排序 */
  sort: number;
  /** 解锁弹窗动画表现 */
  openShow: number;
  /** 活动id */
  activityId: number[];
  /** 显示图标 */
  icon: string;
  /** 节点名称 */
  nodeName: string;
  /** 路由 */
  route: string;
  /** 引导开始结束序号 */
  guideV2List: number[];
}

export interface IConfigTalk {
  /** ID */
  id: number;
  /** 抽中奖励道具 */
  talk: string;
}

export interface IConfigTask {
  /** id */
  id: number;
  /** 任务标题 */
  title: string;
  /** 任务描述 */
  des: string;
  /** 是否做累计计数 */
  addUp: number;
  /** 是否由客户端完成 */
  clientDo: number;
  /** 引导分类 */
  guideType: number;
}

export interface IConfigTaskMain {
  /** id */
  id: number;
  /** 显示ID */
  idShow: number;
  /** 引导 */
  guide: number;
  /** 自定义引导步骤 */
  guidePathId: number;
  /** 任务类型 */
  type: number;
  /** 前置任务 */
  firstTaskList: number[];
  /** 任务ID */
  taskId: number;
  /** 完成次数 */
  finishList: number[];
  /** 任务奖励 */
  rewardList: number[][];
}

export interface IConfigTemple {
  /** ID */
  id: number;
  /** 圣殿名称 */
  name: string;
  /** 对应称号ID */
  titleId: number;
  /** 膜拜奖励 */
  worshipRewardList: number[];
  /** 展示战将ID */
  heroId: number[];
}

export interface IConfigTempleShenji {
  /** ID */
  id: number;
  /** 概率 */
  rate: number;
  /** 最大次数 */
  max: number;
  /** 标题 */
  title: string;
  /** 文本 */
  text: string;
  /** 描述 */
  des: string;
  /** 点赞触发神迹的概率 */
  tapRate: number;
  /** 点赞获得仙玉 */
  tapRewardList: number[];
}

export interface IConfigTempleTree {
  /** ID */
  id: number;
  /** 运势等差增加1的数量 */
  yunshiAdd1: number;
  /** 运势等差增加1的次数 */
  Add1Time: number;
  /** 运势等差增加2的数量 */
  yunshiAdd2: number;
  /** 运势等差增加2的次数 */
  Add2Time: number;
  /** 运势增加对应获得圣殿币数量 */
  rewardList: number[][];
}

export interface IConfigTitle {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 获取途径 */
  source: string;
  /** 称号背景 */
  res: string;
  /** 时间类型 */
  timeType: number;
  /** 持续时长-天 */
  timeLast: number;
  /** 重复获得转化为道具 */
  changeItemList: number[][];
  /** 图标id */
  iconId: string;
  /** 解锁条件 */
  unlock: number[];
  /** 是否隐藏 */
  isHide: number;
  /** 属性加成 */
  attrAdd: number[][];
  /** 运势产出 */
  yunshiAdd: number;
}

export interface IConfigTrain {
  /** id */
  id: number;
  /** 解锁主角等级 */
  unlockLv: number;
  /** 试炼BOSS */
  monsterId: number[];
  /** 挑战成功奖励 */
  reward01List: number[][];
  /** 扫荡奖励 */
  reward02List: number[][];
  /** 扫荡仙玉消耗 */
  cost: number[][];
  /** 每日可扫荡次数 */
  dayTime: number;
}

export interface IConfigTravle {
  /** ID */
  id: number;
  /** 抽中奖励道具 */
  reward1List: number[];
  /** 抽取道具的概率 */
  rate1: number;
  /** 对应位置DI */
  place: number;
  /** 每次抽取必得 */
  reward2List: number[];
  /** 体力恢复时间 */
  renew: number;
  /** 偶遇仙友的概率 */
  rate2: number;
  /** 偶遇仙友保底次数 */
  minTime1: number;
  /** 游历初始次数 */
  firstTime: number;
  /** 无需解锁也可偶遇的仙友ID */
  friendIdList: number[];
  /** 需解锁才可偶遇的仙友的 */
  friendId2List: number[];
  /** 偶遇一次已解锁仙友加因果 */
  add: number;
  /** 体力恢复道具道具 */
  itemId: number;
}

export interface IConfigTravlePlace {
  /** ID */
  id: number;
  /** 地名 */
  name: string;
  /** 对应仙友 */
  friendList: number[];
}

export interface IConfigUnion {
  /** id */
  id: number;
  /** 等级所需经验 */
  exp: number;
  /** 仙盟人数 */
  num: number;
  /** 创建消耗 */
  cost: number[][];
  /** 贡献减少 */
  decrease: number;
  /** 改名消耗 */
  nameCost: number[][];
  /** 每日排行榜一键点赞获得奖励(本服、跨服) */
  rewardList: number[][];
  /** 战力榜入榜最低战力条件 */
  requirement01: number;
  /** 繁荣度榜入榜最低繁荣度条件 */
  requirement02: number;
  /** 战盟榜入榜最低战力条件 */
  requirement03: number;
  /** 战力、繁荣度、战盟上榜最高人数 */
  max: number[];
}

export interface IConfigUnionBoss {
  /** id */
  id: number;
  /** 动画ID */
  monsterId: number;
  /** 镜像动画ID */
  monsterId2: number;
  /** 解锁所需战盟等级 */
  unlock: number;
  /** 怪物战力 */
  monsterPower: number[][];
  /** 镜像怪物战力 */
  monsterDiePower: number[][];
  /** 击杀后未挑战成员保底获得 */
  rewardFloor: number[][];
  /** 个人伤害转换为贡献参数 */
  num: number[];
  /** 击杀者获得贡献值最大 */
  killMax: number[];
  /** 击杀后挑战成员获得 */
  rewardChallenge: number[][];
  /** 每次挑战回合数 */
  round: number;
  /** 每日免费次数 */
  free: number;
  /** 每日购买次数 */
  buy: number;
  /** 购买消耗 */
  costList: number[];
  /** 默认召唤时间 */
  time2: number;
}

export interface IConfigUnionBossReward {
  /** id */
  id: number;
  /** 对应总伤害 */
  harm: number;
  /** 伤害对应奖励2 */
  rewardList: number[][];
}

export interface IConfigUnionBuy {
  /** id */
  id: number;
  /** 触发的权重 */
  rate: number;
  /** 价格 */
  price: number[];
  /** 道具奖励 */
  rewardList: number[][];
  /** 领一个宝箱触发权重 */
  rateList: number[];
  /** 持续时间 */
  lastTime: number;
  /** 价格上限范围 */
  priceRange: number[];
  /** 砍价下限 */
  priceMin: number[];
  /** 砍价上限 */
  priceMax: number[];
}

export interface IConfigUnionDonate {
  /** id */
  id: number;
  /** 捐献名称 */
  name: string;
  /** 捐献仙玉消耗 */
  costList: number[];
  /** 每日上限 */
  max: number;
  /** 冷却时间-秒 */
  cold: number;
  /** 捐献奖励 */
  rewardList: number[][];
}

export interface IConfigUnionTask {
  /** id */
  id: number;
  /** 任务ID */
  taskId: number;
  /** 完成次数 */
  finish: number;
  /** 任务奖励 */
  rewardList: number[][];
  /** 活跃度奖励 */
  taskReward: number[][];
}

export interface IConfigUnlockType {
  /** id */
  id: number;
  /** 任务标题 */
  title: string;
  /** 任务描述 */
  des: string;
}

export interface IConfigVipReward {
  /** ID */
  id: number;
  /** 升级所需贵族点 */
  expNeed: number;
  /** 武将知己奖励 */
  reward1List: number[][];
  /** 道具奖励 */
  reward2List: number[][];
}

