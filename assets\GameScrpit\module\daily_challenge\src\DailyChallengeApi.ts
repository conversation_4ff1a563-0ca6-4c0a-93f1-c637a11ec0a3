import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../../game/mgr/ApiHandler";
import { ChallengeSubCmd } from "../../../game/net/cmd/CmdData";
import { ChallengeMessage, ChallengeResponse } from "../../../game/net/protocol/Challenge";
import { CommRankMessage } from "../../../game/net/protocol/Player";
import { DailyChallengeModule } from "./DailyChallengeModule";

export class DailyChallengeApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   console.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       console.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       console.log(`${errorCode}`);
  //       console.log(data);
  //     }
  //   );
  // }
  // ==================== ChallengeAction  ====================
  // 路由: 29 - 1  --- 【获取每日挑战的信息】 --- 【ChallengeAction:69】【info】
  //     方法返回值: ChallengeMessage
  // 路由: 29 - 2  --- 【获取排行榜】 --- 【ChallengeAction:79】【rank】
  //     方法返回值: CommRankMessage
  // 路由: 29 - 3  --- 【进行每日挑战】 --- 【ChallengeAction:113】【challenge】
  //     方法返回值: ChallengeResponse
  public info(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      ChallengeMessage,
      ChallengeSubCmd.info,
      null,
      (data: ChallengeMessage) => {
        console.log(data);
        DailyChallengeModule.data.challengeMessage = data;
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        console.log(`${errorCode}`);
        console.log(data);
        return false;
      }
    );
  }
  public rank(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      CommRankMessage,
      ChallengeSubCmd.rank,
      null,
      (data: CommRankMessage) => {
        console.log(data);
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        console.log(`${errorCode}`);
        console.log(data);
        return false;
      }
    );
  }
  public challenge(success?: ApiHandlerSuccess) {
    ApiHandler.instance.request(
      ChallengeResponse,
      ChallengeSubCmd.challenge,
      null,
      (data: ChallengeResponse) => {
        console.log(data);
        DailyChallengeModule.data.challengeMessage.count = data.count;
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        console.log(`${errorCode}`);
        console.log(data);
        return false;
      }
    );
  }
}
