import { _decorator, Component, EventTouch, instantiate, Label, Node } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
import { ClubModule } from "../../../../club/ClubModule";
import { ExchangeShopAdapter } from "../../adapter/ExchangeShopViewHolder";
import { ItemCost } from "db://assets/GameScrpit/game/common/ItemCost";
import { GoodsModule } from "../../../../goods/GoodsModule";
import { CommonModule } from "../../CommonModule";
import { SystemOpenEnum } from "db://assets/GameScrpit/game/GameDefine";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIExchangeShopPop } from "./UIExchangeShopPop";
import { ListAdapter } from "db://assets/platform/src/core/ui/adapter_view/ListAdapter";
import { FmButton } from "db://assets/platform/src/core/ui/components/FmButton";
const { ccclass, property } = _decorator;

@ccclass("UIExchangeShop")
@routeConfig({
  bundle: BundleEnum.BUNDLE_COMMON,
  url: "prefab/ui/UIExchangeShop",
  nextHop: [],
  exit: "dialog_close",
})
export class UIExchangeShop extends BaseCtrl {
  public playShowAni: boolean = true;
  private shopList = [
    {
      name: "古镜商店",
      types: [3, 4],
      typesName: ["今日限购", "灵兽兑换"],
      systemOpen: SystemOpenEnum.HUNT_天荒古境,
    },
    {
      name: "福地商店",
      types: [10],
      typesName: [""],
      systemOpen: SystemOpenEnum.FARM_福地洞天,
    },
    {
      name: "战盟商店",
      types: [11],
      typesName: [""],
      systemOpen: SystemOpenEnum.CLUB_战盟系统,
    },
    {
      name: "试练商店",
      types: [13],
      typesName: [""],
      systemOpen: SystemOpenEnum.TRAIN_三界试练,
    },
    {
      name: "每日挑战",
      types: [14],
      typesName: [""],
      systemOpen: SystemOpenEnum.DAILY_每日挑战,
    },
  ];
  private currentShop;
  private _shopAdapter: ExchangeShopAdapter;
  private _tabIndex: number = 0;
  init(args: RouteShowArgs): void {
    //
    let shopType = args.payload.type;
    this.currentShop = this.shopList.find((obj) => obj.types.includes(shopType));
    if (this.currentShop) {
      // 后续可根据需求使用 targetShop 进行相关操作
    }
  }
  start() {
    super.start();
    this._tabIndex = 0;
    this.refreshUI();
  }

  private refreshUI() {
    let shopDatas = CommonModule.config.getShopConfig(this.currentShop.types[this._tabIndex]);
    this._shopAdapter = new ExchangeShopAdapter(this.getNode("viewholder_shop"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(this._shopAdapter);
    GoodsModule.api.buyInfo((data) => {
      this._shopAdapter.setData(shopDatas);
    });

    let coinsSet = new Set<number>();
    for (let i = 0; i < shopDatas.length; i++) {
      let item = shopDatas[i];
      coinsSet.add(item.cointype);
    }
    let coinsList = [];
    coinsSet.forEach((value) => {
      coinsList.push(value);
    });
    this.getNode("item_coins").children.forEach((item) => {
      item.active = false;
    });
    for (let i = 0; i < coinsList.length; i++) {
      let coinId = coinsList[i];
      let item = this.getNode("item_coins").children[i];
      if (!item) {
        item = instantiate(this.getNode("item_coins").children[0]);
        item.parent = this.getNode("item_coins");
      }
      item.active = true;
      item.getComponentInChildren(ItemCost).setItemId(coinId);
    }

    this.getNode("lbl_shop_name").getComponent(Label).string = this.currentShop.name;

    if (this.currentShop.types.length > 1) {
      this.getNode("btn_switch_tab").active = true;
      this.getNode("btn_switch_tab").children.forEach((item) => {
        item.active = false;
      });
      for (let i = 0; i < this.currentShop.types.length; i++) {
        let item = this.getNode("btn_switch_tab").children[i];
        if (!item) {
          item = instantiate(this.getNode("btn_switch_tab").children[0]);
          item.parent = this.getNode("btn_switch_tab");
        }
        if (i === this._tabIndex) {
          item.getComponent(FmButton).selected = true;
        } else {
          item.getComponent(FmButton).selected = false;
        }
        item.active = true;
        item.getComponent(FmButton).string = this.currentShop.typesName[i];
      }
    } else {
      this.getNode("btn_switch_tab").active = false;
    }
  }

  update(deltaTime: number) {
    //
  }
  private on_click_btn_switch_shop(event: EventTouch) {
    RouteManager.uiRouteCtrl.showRoute(UIExchangeShopPop, {
      payload: { list: this.shopList, position: this.shopList.indexOf(this.currentShop) },
      onCloseBack: (args) => {
        if (args.position === undefined || args.position === null) {
          return;
        }
        this.currentShop = this.shopList[args.position];
        this._tabIndex = 0;
        this.refreshUI();
      },
    });
  }

  private on_click_btn_tab_item(event: EventTouch) {
    let target: Node = event.target;
    let index = target.getSiblingIndex();
    this._tabIndex = index;
    this.refreshUI();
  }
}
