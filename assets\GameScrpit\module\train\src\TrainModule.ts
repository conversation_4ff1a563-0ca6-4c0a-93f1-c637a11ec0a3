import { GameData } from "../../../game/GameData";
import data from "../../../lib/data/data";
import { TrainApi } from "./TrainApi";
import { TrainConfig } from "./TrainConfig";
import { TrainData } from "./TrainData";
import { TrainService } from "./TrainService";
import { TrainSubscriber } from "./TrainSubscriber";
import { TrainViewModel } from "./TrainViewModel";

export class TrainModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): TrainModule {
    if (!GameData.instance.TrainModule) {
      GameData.instance.TrainModule = new TrainModule();
    }
    return GameData.instance.TrainModule;
  }
  private _data = new TrainData();
  private _api = new TrainApi();
  private _service = new TrainService();
  private _subscriber = new TrainSubscriber();
  private _viewModel = new TrainViewModel();
  private _config = new TrainConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new TrainData();
    this._api = new TrainApi();
    this._service = new TrainService();
    this._subscriber = new TrainSubscriber();
    this._viewModel = new TrainViewModel();
    this._config = new TrainConfig();

    // 模块初始化
    this._subscriber.register();
    this._api.info(() => {
      completedCallback && completedCallback();
    });
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
