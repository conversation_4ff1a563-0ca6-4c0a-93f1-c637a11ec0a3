import { _decorator, director, EventTouch, instantiate, is<PERSON><PERSON><PERSON>, Label, Node, Tween, Vec3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import TipMgr from "../../../lib/tips/TipMgr";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { FriendRouteItem } from "../../../module/friend/FriendRoute";
import { SoulRouteName } from "../../../module/soul/SoulRoute";
import { AttrEnum, Sleep, SystemOpenEnum } from "../../GameDefine";
import { HorseRouteName } from "../../../module/horse/HorseRoute";
import { PlayerRouteName, ShopRouteName } from "../../../module/player/PlayerConstant";
import { PlayerEvent } from "../../../module/player/PlayerEvent";
import { GoodsRouteName } from "../../../module/goods/GoodsRoute";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { ActivityRouteItem } from "../../../module/activity/ActivityRoute";
import { GameDirector } from "../../GameDirector";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { HorseModule } from "../../../module/horse/HorseModule";
import { Prefab } from "cc";
import { TipsMgr } from "../../../../platform/src/TipsHelper";
import { HdTiaoJianLiBaoModule } from "../../../module/hd_tiaojianlibao/HdTiaoJianLiBaoModule";
import { HdShouChongModule } from "../../../module/hd_shouchong/HdShouChongModule";
import ToolExt from "../../common/ToolExt";
import { HdVipCardRouteItem } from "../../../module/hd_vipcard/HdVipCardRoute";
import { HdShouChongRouteItem } from "../../../module/hd_shouchong/HdShouChongRoute";
import { DisciplinesRouteItem } from "../../../module/disciplines/DisciplinesRoute";
import { HdTiaoJianLiBaoRouteItem } from "../../../module/hd_tiaojianlibao/HdTiaoJianLiBaoRoute";
import { FriendModule } from "../../../module/friend/FriendModule";
import { SonhaiRouteName } from "../../../module/sonhai/SonhaiRoute";
import { ActivityModule } from "../../../module/activity/ActivityModule";

import { sonhai_activityId } from "../../../module/sonhai/SonhaiModule";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { PlayerIdentityAudioName } from "../../../module/player/PlayerConfig";
import { RewardRouteEnum } from "../../../ext_reward/RewardDefine";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { HdSevenModule } from "../../../module/hd_seven/HdSevenModule";
import { FrbpRouteItem } from "../../../module/frbp/FrbpRoute";
import { GuideRouteEnum } from "../../../ext_guide/GuideDefine";
import FmUtils from "../../../lib/utils/FmUtils";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIAfterDay } from "../ui_fund/UIAfterDay";
import { UITimeTask } from "../../../module/time_task/prefab/ui/UITimeTask";
import { UIHdFriendGoal } from "../../../module/friend_goal/src/prefab/ui/UIHdFriendGoal";
import { UIVipMain } from "../../../module/vip_haoli/src/prefab/ui/UIVipMain";
import { LuckDrawAudioName } from "../../../module/luck_draw/LuckDrawConfig";

const { ccclass, property } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("UITerritory")
export class UITerritory extends UINode {
  // 是否播放开启系统提示
  isPlaySystemOpen = false;

  activityIconMap = {
    btn_lottery: [],
    btn_tuJian: [],
    btn_shopCenter: [],
    btn_card: [10201, 10301],
    btn_shouchong: [10101],
    btn_activity_7: [10501],
    btn_friendmubiao: [],
    btn_chongzhihaoli: [10401, 10402, 10403, 10404, 10405],
    btn_xiuxing: [11001],
    btn_vip_lv: [],
    btn_tiaojianlibao: [10601],
    btn_lchk: [10901],
    btn_fd_shtx: [sonhai_activityId],
    btn_frbp: [10801],
  };

  activityHideMap = [
    "btn_shouchong",
    "btn_activity_7",
    "btn_friendmubiao",
    "btn_chongzhihaoli",
    "btn_xiuxing",
    "btn_vip_lv",
    "btn_lchk",
    "btn_fd_shtx",
    "btn_frbp",
  ];

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_TERRITORY}?prefab/ui/UITerritory`;
  }

  protected async onEvtShow(): Promise<void> {
    log.debug("UITerritory onEvtShow start");

    this.playerChangeRes(true);
    this.refreshFuncIcons(); //刷新功能按钮
    this.refreshExpandIconsShow();

    await this.setZhujueSkin();

    this.onSystemOpenCheck("UITerritory");
    BadgeMgr.instance.setBadgeId(this.getNode("btn_shengji_img"), BadgeType.UITerritory.btn_shengji.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_horse"), BadgeType.UITerritory.btn_horse.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_friend"), BadgeType.UITerritory.btn_friend.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_award"), BadgeType.UITerritory.btn_award.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_soul"), BadgeType.UITerritory.btn_soul.id);
    //活动
    BadgeMgr.instance.setBadgeId(this.getNode("btn_chongzhihaoli"), BadgeType.UITerritory.btn_chongzhihaoli.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_xiuxing"), BadgeType.UITerritory.btn_xiuxing.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_fd_shtx"), BadgeType.UITerritory.btn_fd_shtx.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_lchk"), BadgeType.UITerritory.btn_lchk.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_activity_7"), BadgeType.UITerritory.btn_activity_7.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_card"), BadgeType.UITerritory.btn_card.id);
  }
  public onEvtHide(): void {}

  public onEvtClose(): void {
    this.assetMgr.release();
    Tween.stopAllByTarget(this.node);
  }

  /**注册监听 */
  protected onRegEvent() {
    log.log("UITerritory onRegEvent");
    MsgMgr.on(PlayerEvent.ON_UIPLAYER_DATA_UPDATE, this.playerChangeRes, this);
    MsgMgr.on(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE, this.setZhujueSkin, this);
    MsgMgr.on(MsgEnum.ON_ACTIVITY_UPDATE, this.onActivityUpdate, this);
    MsgMgr.on(MsgEnum.ON_HORSE_UPDATE, this.setZhujueSkin, this);
    MsgMgr.on(MsgEnum.ON_ACTIVITY_TIAOJIAN_UPDATE, this.onTiaoJianTanChuang, this);
    MsgMgr.on(MsgEnum.UIReady, this.onSystemOpenCheck, this);
  }

  protected onDelEvent(): void {
    log.log("UITerritory onDelEvent");
    MsgMgr.off(PlayerEvent.ON_UIPLAYER_DATA_UPDATE, this.playerChangeRes, this);
    MsgMgr.off(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE, this.setZhujueSkin, this);
    MsgMgr.off(MsgEnum.ON_ACTIVITY_UPDATE, this.onActivityUpdate, this);
    MsgMgr.off(MsgEnum.ON_HORSE_UPDATE, this.setZhujueSkin, this);
    MsgMgr.off(MsgEnum.ON_ACTIVITY_TIAOJIAN_UPDATE, this.onTiaoJianTanChuang, this);
    MsgMgr.off(MsgEnum.UIReady, this.onSystemOpenCheck, this);
  }

  private onActivityUpdate() {
    this.refreshFuncIcons();
    this.refreshExpandIconsShow();
  }
  private onTiaoJianTanChuang() {
    this.refreshFuncIcons();
    this.refreshExpandIconsShow();
  }

  // 刷新展开按钮显示状态
  private refreshExpandIconsShow() {
    let showIcons = 0;
    let activityList = Object.keys(this.activityIconMap);
    for (let i = 0; i < activityList.length; i++) {
      if (this.getNode(activityList[i]).active) {
        showIcons++;
      }
    }
    if (showIcons > 8) {
      this.getNode("btn_expand").active = true;
    }
  }
  private refreshFuncIcons() {
    let activityMap = ActivityModule.data.allActivityConfig;
    if (!activityMap) {
      return;
    }
    let activityList = Object.keys(this.activityIconMap);
    for (let i = 0; i < activityList.length; i++) {
      if (this.activityIconMap[activityList[i]].length == 0) {
        this.onFunctionsItemAttach(this.getNode(activityList[i]));
      } else {
        let isShow = false;
        for (let i2 = 0; i2 < this.activityIconMap[activityList[i]].length; i2++) {
          let activityId = this.activityIconMap[activityList[i]][i2];
          if (
            activityMap[activityId] &&
            !activityMap[activityId].close &&
            GameDirector.instance.isSystemOpen(activityId)
          ) {
            isShow = true;
            let warmUpTime = activityMap[activityId].warmUpTime;
            let startTime = Math.max(activityMap[activityId].startTime, warmUpTime);
            let publicityTime = Math.max(activityMap[activityId].publicityTime, startTime);
            let publicityAfterTime = Math.max(activityMap[activityId].publicityAfterTime, publicityTime);
            let endTime = Math.max(activityMap[activityId].endTime, publicityAfterTime);
            if (TimeUtils.serverTime < startTime && TimeUtils.serverTime > warmUpTime) {
              FmUtils.setCd(
                this.getNode(activityList[i]).getChildByName("lbl_cd"),
                activityMap[activityId].startTime,
                false,
                () => {
                  this.onFunctionsItemAttach(this.getNode(activityList[i]));
                }
              );
              this.getNode(activityList[i])["messageKey"] = 510;
            } else if (TimeUtils.serverTime > publicityTime && TimeUtils.serverTime < publicityAfterTime) {
              // 结算中
              this.getNode(activityList[i]).getChildByName("lbl_tips").active = true;
              this.getNode(activityList[i]).getChildByName("lbl_title").active = false;
              this.getNode(activityList[i]).getChildByName("lbl_tips").getComponent(Label).string = "结算中";
            } else if (TimeUtils.serverTime > publicityAfterTime && TimeUtils.serverTime < endTime) {
              // 领奖中
              this.getNode(activityList[i]).getChildByName("lbl_tips").active = true;
              this.getNode(activityList[i]).getChildByName("lbl_title").active = false;
              this.getNode(activityList[i]).getChildByName("lbl_tips").getComponent(Label).string = "领奖中";
            } else {
              this.getNode(activityList[i]).getChildByName("lbl_tips").active = false;
              this.getNode(activityList[i]).getChildByName("lbl_title").active = true;
            }
            break;
          }
        }
        if (isShow) {
          this.onFunctionsItemAttach(this.getNode(activityList[i]));
        } else {
          this.getNode(activityList[i]).active = false;
        }
      }
    }
  }
  private onFunctionsItemAttach(item: Node) {
    //判断首充是否显示
    if (item.name == "btn_shouchong") {
      HdShouChongModule.service.canShowShouChong((isShow) => {
        log.log("isShow", isShow);
        item.active = isShow && GameDirector.instance.isSystemOpen(SystemOpenEnum.HD_首充);
        log.log("item.active", item.active);
      });
    } else if (item.name == "btn_card") {
      item.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.POWER_特权卡);
    } else if (item.name == "btn_activity_7") {
      HdSevenModule.service.canShowSeven((isShow) => {
        item.active = isShow;
      });
    } else if (item.name == "btn_chongzhihaoli") {
      item.active = true;
    } else if (item.name == "btn_tiaojianlibao") {
      HdTiaoJianLiBaoModule.service.canShowTiaoJianLiBao((isShow, cd: number) => {
        log.log("isShow", isShow);
        item.active = isShow;
        item.getChildByName("lbl_cd").active = true;
        FmUtils.setCd(item.getChildByName("lbl_cd"), cd, false, () => {
          this.onFunctionsItemAttach(item);
        });
      });
    } else if (item.name == "btn_friendmubiao") {
      //
      item.active = FriendModule.service.canShowFriendMubiao();
    }
    // else if (item.name == "btn_vip_lv") {
    //   //HD_贵族豪礼
    //   // item.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.HD_贵族豪礼);
    // }
    else if (item.name == "btn_fd_shtx") {
      //HD_贵族豪礼
      item.active = true;
    } else if (item.name == "btn_lottery") {
      // 玲珑宝鼎
      item.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.LUCKY_幸运抽奖);
    } else if (item.name == "btn_tuJian") {
      // 英雄图鉴
      item.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.GOODS_英雄图鉴);
    } else if (item.name == "btn_shopCenter") {
      // 商城
      item.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.GOODS_商城);
    } else {
      //默认显示
      item.active = true;
    }
  }

  private _role: Node = null;

  private async setZhujueSkin() {
    if (this._role) {
      this._role.removeFromParent();
      this._role.destroy();
    }
    this._role = await ToolExt.loadUIRole(
      this.getNode("img_zhujue"),
      PlayerModule.data.skin.skinId,
      HorseModule.data.horseMessage?.horseId || -1,
      "renderScale1",
      this
    );
  }

  /**
   * 点击活动图标详情
   */
  private onShowItemDetail(node: Node, route: string) {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.点击玲珑夺宝);

    if (node["messageKey"]) {
      TipsMgr.showTipX(node["messageKey"]);
      return;
    }
    UIMgr.instance.showDialog(route, {}, () => {
      this.onFunctionsItemAttach(node);
    });
  }
  private onShowItemDetail2(node: Node, type: new () => void) {
    AudioMgr.instance.playEffect(LuckDrawAudioName.Effect.点击玲珑夺宝);

    if (node["messageKey"]) {
      TipsMgr.showTipX(node["messageKey"]);
      return;
    }
    RouteManager.uiRouteCtrl.showRoute(type, {
      onCloseBack: () => {
        this.onFunctionsItemAttach(node);
      },
    });
  }
  private on_click_btn_lottery(event: EventTouch) {
    this.onShowItemDetail(event.target, "UILottery");
  }
  private on_click_btn_tuJian(event: EventTouch) {
    this.onShowItemDetail(event.target, GoodsRouteName.UICollect);
  }
  private on_click_btn_shopCenter(event: EventTouch) {
    this.onShowItemDetail(event.target, ShopRouteName.UIShopCenter);
  }
  private on_click_btn_card(event: EventTouch) {
    this.onShowItemDetail(event.target, HdVipCardRouteItem.UICardMain);
  }
  private on_click_btn_shouchong(event: EventTouch) {
    this.onShowItemDetail(event.target, HdShouChongRouteItem.UIShouChong);
  }
  private on_click_btn_activity_7(event: EventTouch) {
    this.onShowItemDetail(event.target, ActivityRouteItem.UIHdSeven);
  }
  private on_click_btn_friendmubiao(event: EventTouch) {
    this.onShowItemDetail2(event.target, UIHdFriendGoal);
  }
  private on_click_btn_chongzhihaoli(event: EventTouch) {
    this.onShowItemDetail(event.target, ActivityRouteItem.UIFundMain);
  }
  private on_click_btn_xiuxing(event: EventTouch) {
    this.onShowItemDetail(event.target, DisciplinesRouteItem.UIDisciplines);
  }
  private on_click_btn_vip_lv(event: EventTouch) {
    this.onShowItemDetail2(event.target, UIVipMain);
  }
  private on_click_btn_tiaojianlibao(event: EventTouch) {
    this.onShowItemDetail(event.target, HdTiaoJianLiBaoRouteItem.UITiaoJianLiBao);
  }
  private on_click_btn_lchk(event: EventTouch) {
    this.onShowItemDetail2(event.target, UIAfterDay);
  }
  private on_click_btn_time_task(event: EventTouch) {
    this.onShowItemDetail2(event.target, UITimeTask);
  }

  private on_click_btn_fd_shtx(event: EventTouch) {
    this.onShowItemDetail(event.target, SonhaiRouteName.UISonhai);
  }
  private on_click_btn_frbp(event: EventTouch) {
    this.onShowItemDetail(event.target, FrbpRouteItem.UIFrbpMain);
  }

  private on_click_btn_expand(e: EventTouch) {
    let scale = this.getNode("btn_expand").getChildByName("bg_icon").getScale();
    if (scale.x > 0) {
      this.getNode("btn_expand").getChildByName("bg_icon").setScale(-1, 1);
      this.refreshFuncIcons();
    } else {
      this.getNode("btn_expand").getChildByName("bg_icon").setScale(1, 1);
      for (let i = 0; i < this.activityHideMap.length; i++) {
        this.getNode(this.activityHideMap[i]).active = false;
      }
    }
  }
  private on_click_btn_xiangxixinxi() {
    ResMgr.loadPrefab(
      `${BundleEnum.BUNDLE_G_TERRITORY}?prefab/TipAttr`,
      (prefab: Prefab) => {
        if (isValid(this.node) == false) return;
        let node = instantiate(prefab);
        TipsMgr.showTipNode(node, -1);
      },
      this
    );
  }

  private playerChangeRes(force: boolean = false) {
    if (UIMgr.instance.getLastPageInfo().name !== PlayerRouteName.UITerritory) {
      if (!force) {
        return;
      }
    }

    let attr = PlayerModule.data.playerBattleAttrResponse.battleAttrMap;

    let baseAttr = [AttrEnum.攻击_2, AttrEnum.生命_1, AttrEnum.防御_3, AttrEnum.敏捷_4];
    let nodeBaseAttr = this.getNode("layout_base_attr");
    for (let i = 0; i < baseAttr.length; i++) {
      let node = nodeBaseAttr.children[i];
      let configAttr = JsonMgr.instance.getConfigAttribute(baseAttr[i]);
      node.getChildByName("lbl_key").getComponent(Label).string = configAttr.name;
      node.getChildByName("lbl_value").getComponent(Label).string = Formate.formatAttr(baseAttr[i], attr[baseAttr[i]]);
    }
    this.refreshFuncIcons();
  }

  // private on_click_btn_close() {
  //   UIMgr.instance.back();
  // }

  /**点击每日宝箱 */
  private on_click_btn_award() {
    AudioMgr.instance.playEffect(PlayerIdentityAudioName.Effect.点击聚宝盆);
    let configLeader = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level);
    if (PlayerModule.data.ishadGetDailyTreasure()) {
      this.getNode("btn_treasure").active = true;
      this.getNode("item_num_lab").getComponent(Label).string = `${Formate.format(configLeader.dayReward)}`;
      FmUtils.setCd(this.getNode("LblCd"), TimeUtils.getDayBegin() + 86400000);
    } else {
      PlayerModule.api.dailyTreasure((data: number[]) => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data });
      });
    }
  }

  private on_click_btn_treasure() {
    this.getNode("btn_treasure").active = false;
  }

  /** 身份升级 */
  private on_click_btn_shengji() {
    AudioMgr.instance.playEffect(1052);
    UIMgr.instance.showDialog(PlayerRouteName.UIPlayerLevelUp, {}, () => {
      // 升级后可能会解锁新的功能
      this.refreshFuncIcons();
    });
  }

  /**仙友入口 */
  private on_click_btn_friend() {
    AudioMgr.instance.playEffect(1075);
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.FRIEND_仙友系统)) {
      UIMgr.instance.showDialog(FriendRouteItem.UIFriendMain);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.FRIEND_仙友系统));
    }
  }

  /**至宝入口 */
  private on_click_btn_horse() {
    AudioMgr.instance.playEffect(1024);
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HOURSE_至宝系统)) {
      UIMgr.instance.showDialog(HorseRouteName.UIHorseList);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.HOURSE_至宝系统));
    }
  }

  /**武魂入口 */
  private on_click_btn_soul() {
    AudioMgr.instance.playEffect(1010);
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.SOUL_兽魂系统)) {
      UIMgr.instance.showDialog(SoulRouteName.UISoulDetail);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.SOUL_兽魂系统));
    }
  }

  private async playOpenAni(id: number, nodeShow: Node, list: any[], posEnd: Vec3 = null) {
    TipsMgr.setEnableTouch(false, 2, false);

    this.refreshFuncIcons(); //刷新功能按钮

    await Sleep(0.5);

    await new Promise((resolve, reject) => {
      TipsMgr.topRouteCtrl.show(
        RewardRouteEnum.TopSystemOpen,
        {
          toWorldPos: posEnd || nodeShow.getWorldPosition(),
          nodeIconAdd: instantiate(nodeShow),
        },
        () => {
          resolve(1);
        }
      );
    });

    const cfgSystemOpen = JsonMgr.instance.jsonList.c_systemOpen[id];
    if (cfgSystemOpen?.guideV2List && cfgSystemOpen.guideV2List.length > 0) {
      await new Promise((resolve, reject) => {
        TipsMgr.topRouteCtrl.show(
          GuideRouteEnum.TopEventRoute,
          { from: cfgSystemOpen.guideV2List[0], to: cfgSystemOpen.guideV2List[1] },
          () => {
            resolve(1);
          }
        );
      });
    }

    list = list.filter((item) => item != id);
    this.playOpenAniCheck(list);
    GameDirector.instance.saveSystemOpenWaitAniList(list);
  }

  private async playOpenAniCheck(list) {
    // 待开启动画列表
    if (list.includes(SystemOpenEnum.LUCKY_幸运抽奖)) {
      // 玲珑宝鼎
      let nodeShow = this.getNode("btn_lottery");
      nodeShow.active = true;
      this.playOpenAni(SystemOpenEnum.LUCKY_幸运抽奖, nodeShow, list);
    } else if (list.includes(SystemOpenEnum.GOODS_商城)) {
      // 商城
      let nodeShow = this.getNode("btn_shopCenter");
      nodeShow.active = true;
      this.playOpenAni(SystemOpenEnum.GOODS_商城, nodeShow, list);
    } else if (list.includes(SystemOpenEnum.POWER_特权卡)) {
      // 特权卡
      let nodeShow = this.getNode("btn_card");
      nodeShow.active = true;
      this.playOpenAni(SystemOpenEnum.POWER_特权卡, nodeShow, list);
    } else if (list.includes(SystemOpenEnum.CLUB_战盟系统)) {
      // 战盟系统
      const uiMain = NodeTool.findByName(director.getScene(), "UIMain");
      if (isValid(uiMain) == false) {
        this.isPlaySystemOpen = false;
        return;
      }

      let nodeShow = NodeTool.findByName(uiMain, "btn_club");
      nodeShow.active = true;
      this.playOpenAni(SystemOpenEnum.CLUB_战盟系统, nodeShow, list);
    } else if (list.includes(SystemOpenEnum.SD_圣殿)) {
      // 圣殿
      const uiMain = NodeTool.findByName(director.getScene(), "UIMain");
      if (isValid(uiMain) == false) {
        this.isPlaySystemOpen = false;
        return;
      }
      let nodeShow = NodeTool.findByName(uiMain, "btn_sheng_dian");
      nodeShow.active = true;

      let btn_tiao_zhan = NodeTool.findByName(uiMain, "btn_tiao_zhan");

      this.playOpenAni(SystemOpenEnum.SD_圣殿, nodeShow, list, btn_tiao_zhan.getWorldPosition());
    } else {
      this.isPlaySystemOpen = false;
    }
  }

  // 新功能解锁触发判断
  private async onSystemOpenCheck(name: string) {
    if (name !== "UITerritory") {
      return;
    }

    if (this.isPlaySystemOpen) {
      log.log("正在播放新功能动画");
      return;
    }
    this.isPlaySystemOpen = true;

    let list = GameDirector.instance.getSystemOpenWaitAniList();
    this.playOpenAniCheck(list);
  }
}
