{"20101": {"id": 20101, "firstSkin": 20101, "skin": [30101], "name": "鬼新娘所属灵兽", "color": 1, "qualityFirst": 2, "qualityAdd": 1, "trainAddList": [1071, 10], "levelMax": 200, "rewardFruitList": [[1071, 5]], "skillFirst": 3, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20102": {"id": 20102, "firstSkin": 20102, "jumplist": [37], "skin": [30102], "name": "巡山小妖所属灵兽", "color": 1, "qualityFirst": 2, "qualityAdd": 1, "trainAddList": [1071, 10], "levelMax": 200, "rewardFruitList": [[1071, 5]], "skillFirst": 3, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20103": {"id": 20103, "firstSkin": 20103, "jumplist": [37], "skin": [30103], "name": "千机道长所属灵兽", "color": 1, "qualityFirst": 2, "qualityAdd": 1, "trainAddList": [1071, 10], "levelMax": 200, "rewardFruitList": [[1071, 5]], "skillFirst": 3, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20105": {"id": 20105, "firstSkin": 20104, "jumplist": [37], "skin": [30101], "name": "寿星所属灵兽", "color": 1, "qualityFirst": 2, "qualityAdd": 1, "trainAddList": [1071, 10], "levelMax": 200, "rewardFruitList": [[1071, 5]], "skillFirst": 3, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20106": {"id": 20106, "firstSkin": 20105, "jumplist": [37], "skin": [30103], "name": "无常所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20107": {"id": 20107, "firstSkin": 20110, "jumplist": [37], "skin": [30102], "name": "聂小芊所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20108": {"id": 20108, "firstSkin": 20111, "jumplist": [37], "skin": [30103], "name": "钟馗所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20109": {"id": 20109, "firstSkin": 20106, "jumplist": [37], "skin": [30101], "name": "铁拐李所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20110": {"id": 20110, "firstSkin": 20108, "jumplist": [37], "name": "雷震子所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 10]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20111": {"id": 20111, "firstSkin": 20401, "jumplist": [37], "name": "太乙所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 10]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20112": {"id": 20112, "firstSkin": 20112, "jumplist": [37], "skin": [30101], "name": "干将莫邪所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20113": {"id": 20113, "firstSkin": 20110, "jumplist": [37], "skin": [30102], "name": "燕大侠所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20114": {"id": 20114, "firstSkin": 20111, "jumplist": [37], "skin": [30103], "name": "智公所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20115": {"id": 20115, "firstSkin": 20112, "jumplist": [37], "skin": [30101], "name": "蛇妖所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20116": {"id": 20116, "firstSkin": 20110, "jumplist": [37], "skin": [30102], "name": "蜘蛛精所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20117": {"id": 20117, "firstSkin": 20111, "jumplist": [37], "skin": [30103], "name": "金银角所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20118": {"id": 20118, "firstSkin": 20112, "jumplist": [37], "skin": [30101], "name": "华神医所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20120": {"id": 20120, "firstSkin": 20110, "jumplist": [37], "skin": [30102], "name": "鬼谷子所属灵兽", "color": 2, "qualityFirst": 4, "qualityAdd": 2, "trainAddList": [1071, 20], "levelMax": 200, "rewardFruitList": [[1071, 10]], "skillFirst": 4, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20121": {"id": 20121, "firstSkin": 20402, "jumplist": [37], "name": "白起所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 20]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20123": {"id": 20123, "firstSkin": 20107, "jumplist": [37], "skin": [30102], "name": "土行孙所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 20]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20124": {"id": 20124, "firstSkin": 20109, "jumplist": [37], "skin": [30103], "name": "扫地僧所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 20]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20126": {"id": 20126, "firstSkin": 20106, "jumplist": [37], "skin": [30102], "name": "大仑明王所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 20]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20127": {"id": 20127, "firstSkin": 20108, "jumplist": [37], "name": "杨戬所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 20]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20128": {"id": 20128, "firstSkin": 20401, "jumplist": [37], "name": "哪吒所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 20]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20129": {"id": 20129, "firstSkin": 20107, "jumplist": [37], "skin": [30103], "name": "青毛狮子所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 20]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20130": {"id": 20130, "firstSkin": 20109, "jumplist": [37], "skin": [30101], "name": "黄牙老象所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 20]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20202": {"id": 20202, "firstSkin": 20105, "jumplist": [37], "skin": [30101], "name": "李白所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 20]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20205": {"id": 20205, "firstSkin": 20105, "jumplist": [37], "skin": [30102], "name": "金翅大鹏所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 20]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20206": {"id": 20206, "firstSkin": 20402, "jumplist": [37], "name": "牛魔王所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 20]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20207": {"id": 20207, "firstSkin": 20108, "jumplist": [37], "name": "大侠所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 20]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20303": {"id": 20303, "firstSkin": 20106, "jumplist": [37], "skin": [30103], "name": "司马懿所属灵兽", "color": 3, "qualityFirst": 6, "qualityAdd": 3, "trainAddList": [1071, 30], "levelMax": 200, "rewardFruitList": [[1071, 40]], "skillFirst": 5, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20304": {"id": 20304, "firstSkin": 20401, "jumplist": [37], "name": "孟婆所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20306": {"id": 20306, "firstSkin": 20108, "jumplist": [37], "name": "李靖所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20307": {"id": 20307, "firstSkin": 20401, "jumplist": [37], "name": "卧龙所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20308": {"id": 20308, "firstSkin": 20402, "jumplist": [37], "name": "苏妲己所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20309": {"id": 20309, "firstSkin": 20108, "jumplist": [37], "name": "东方所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20310": {"id": 20310, "firstSkin": 20401, "jumplist": [37], "name": "孔宣所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20311": {"id": 20311, "firstSkin": 20402, "jumplist": [37], "name": "燃灯道人所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20401": {"id": 20401, "firstSkin": 20108, "jumplist": [37], "name": "天蓬元帅所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20402": {"id": 20402, "firstSkin": 20401, "jumplist": [37], "name": "斗战圣佛所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20403": {"id": 20403, "firstSkin": 20402, "jumplist": [37], "name": "卷帘大将所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20404": {"id": 20404, "firstSkin": 20108, "jumplist": [37], "name": "白龙护法所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20405": {"id": 20405, "firstSkin": 20401, "jumplist": [37], "name": "金蝉子所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20411": {"id": 20411, "firstSkin": 20402, "jumplist": [37], "name": "持国天王所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20412": {"id": 20412, "firstSkin": 20108, "jumplist": [37], "name": "增长天王所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20413": {"id": 20413, "firstSkin": 20401, "jumplist": [37], "name": "广目天王所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20414": {"id": 20414, "firstSkin": 20402, "jumplist": [37], "name": "多闻天王所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "20421": {"id": 20421, "firstSkin": 20402, "jumplist": [37], "name": "通天教主所属灵兽", "color": 4, "qualityFirst": 8, "qualityAdd": 4, "trainAddList": [1071, 40], "levelMax": 250, "rewardFruitList": [[1071, 40]], "skillFirst": 6, "wakeOrNot": 1, "wakeCostNumList": [30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 320, 340, 360, 380, 400, 450, 500, 550, 600, 650, 700]}, "-1": {"id": -1, "firstSkin": 0, "jumplist": [], "skin": [], "name": "", "color": 0, "qualityFirst": 0, "qualityAdd": 0, "trainAddList": [], "levelMax": 0, "rewardFruitList": [], "skillFirst": 0, "wakeOrNot": 0, "wakeCostNumList": [], "wakeCostId": 1073, "wakeSkillAdd": 1, "wakeLvAdd": 10, "wakeLvCostList": [1071, 100], "wakeQualityAdd": 10}}