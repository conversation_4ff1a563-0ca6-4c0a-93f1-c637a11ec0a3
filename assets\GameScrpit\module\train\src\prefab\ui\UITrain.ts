import { _decorator, Component, Node } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { TrainAdapter } from "../../adapter/TrainViewHolder";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
import { JsonMgr } from "db://assets/GameScrpit/game/mgr/JsonMgr";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIExchangeShop } from "../../../../common/src/prefab/ui/UIExchangeShop";
import { ShopTypeEnum } from "../../../../common/src/CommonConfig";
import { TrainModule } from "../../TrainModule";
const { ccclass, property } = _decorator;

@ccclass("UITrain")
@routeConfig({
  bundle: BundleEnum.BUNDLE_TRAIN,
  url: "prefab/ui/UITrain",
  nextHop: [],
  exit: "dialog_close",
})
export class UITrain extends BaseCtrl {
  public playShowAni: boolean = true;
  private _adapter: TrainAdapter;
  start() {
    super.start();
    TrainModule.api.info((data) => {
      console.log(data);
    });
    this._adapter = new TrainAdapter(this.getNode("viewholder_train"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(this._adapter);
    let datas = Object.values(JsonMgr.instance.jsonList.c_train);
    this._adapter.setData(datas);
  }

  update(deltaTime: number) {}

  on_click_btn_shangdian() {
    RouteManager.uiRouteCtrl.showRoute(UIExchangeShop, { payload: { type: ShopTypeEnum.试练商店 } });
  }
}
