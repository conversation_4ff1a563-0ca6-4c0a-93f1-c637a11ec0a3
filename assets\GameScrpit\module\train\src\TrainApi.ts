import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ApiHandlerFail, ApiHandlerSuccess } from "../../../game/mgr/ApiHandler";
import { RealmsSubCmd } from "../../../game/net/cmd/CmdData";
import { RealmsFightResponse, RealmsMessage } from "../../../game/net/protocol/Realms";
import { TrainModule } from "./TrainModule";

export class TrainApi {
  // 接口方法，数据模块在这里改好，然后通知上层，下面这个是个例子
  // public improveSkill(friendId: number, rank: number, consumeitem: boolean, success?: ApiHandlerSuccess) {
  //   let data: FriendCitySkillRequest = {
  //     friendId: friendId,
  //     rank: rank,
  //     consumeItem: consumeitem,
  //   };
  //   console.log("apiImproveSkill", friendId, rank, consumeitem);
  //   ApiHandler.instance.request(
  //     FriendCitySkillMessage,
  //     FriendSubCmd.improveSkill,
  //     FriendCitySkillRequest.encode(data),
  //     (data: FriendCitySkillMessage) => {
  //       console.log(data);
  //       FriendModule.data.updateFriendCitySkill(friendId, rank, data);
  //       success && success(data);
  //     },
  //     (errorCode: any, msg: string, data: any) => {
  //       console.log(`${errorCode}`);
  //       console.log(data);
  //     }
  //   );
  // }
  // ==================== RealmsAction  ====================
  // 路由: 28 - 1  --- 【获取驿站的信息】 --- 【RealmsAction:69】【info】
  // //     方法返回值: RealmsMessage
  // info = CmdMgr.getMergeCmd(MainCmd.RealmsCmd, 1),
  // // 路由: 28 - 2  --- 【挑战关卡】 --- 【RealmsAction:79】【conquer】
  // //     方法返回值: RealmsFightResponse
  // conquer = CmdMgr.getMergeCmd(MainCmd.RealmsCmd, 2),
  // // 路由: 28 - 3  --- 【扫荡关卡】 --- 【RealmsAction:108】【sweep】
  // //     方法返回值: RealmsFightResponse
  // sweep = CmdMgr.getMergeCmd(MainCmd.RealmsCmd, 3),
  // // 路由: 28 - 5  --- 【兑换道具】 --- 【RealmsAction:159】【redeemGoods】
  // //     方法参数: RealmsRedeemRequest
  // //     方法返回值: RealmsRedeemResponse
  // redeemGoods = CmdMgr.getMergeCmd(MainCmd.RealmsCmd, 5),

  /**
   * 获取试练的信息
   * @param success
   */
  public info(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(
      RealmsMessage,
      RealmsSubCmd.info,
      null,
      (data: RealmsMessage) => {
        //
        TrainModule.data.realmsMessage = data;
        success && success(data);
      },
      (errorCode: any, msg: string[], data: any) => {
        console.log(`${errorCode}-${msg}`);
        console.log(data);
        return false;
      }
    );
  }
  /**
   * 挑战关卡
   * @param success
   */
  public conquer(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(RealmsFightResponse, RealmsSubCmd.conquer, null, (data: RealmsFightResponse) => {
      //
      TrainModule.data.realmsMessage.maxConquerId = data.maxConquerId;
      TrainModule.data.realmsMessage.sweepCount = data.sweepCount;
      success && success(data);
    });
  }
  /**
   * 扫荡关卡
   * @param success
   */
  public sweep(success?: ApiHandlerSuccess) {
    ApiHandler.instance.requestSync(RealmsFightResponse, RealmsSubCmd.sweep, null, (data: RealmsFightResponse) => {
      //
      TrainModule.data.realmsMessage.maxConquerId = data.maxConquerId;
      TrainModule.data.realmsMessage.sweepCount = data.sweepCount;
      success && success(data);
    });
  }
}
