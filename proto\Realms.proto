syntax = "proto3";
package sim;
import "Comm.proto";

// 
message RealmsFightResponse {
  // 是否胜利
  bool win = 1;
  // Boss关卡战斗回放
  string replay = 2;
  // 已经扫荡的次数
  int32 sweepCount = 3;
  // 已经击败的最高关卡ID
  int64 maxConquerId = 4;
  // 奖励
  sim.RewardMessage rewardMessage = 5;
}

// 
message RealmsMessage {
  // 已经解锁的最高关卡ID
  int64 maxUnlockId = 1;
  // 已经击败的最高关卡ID(也是可以扫荡的关卡)
  int64 maxConquerId = 2;
  // 已经扫荡的次数
  int32 sweepCount = 3;
  // 是否领取本服一键点赞排行榜奖励
  bool takeCurServerRankReward = 4;
}

// 
message RealmsRedeemRequest {
  // 兑换的商品主键ID
  int64 redeemId = 1;
  // 购买数量
  int32 count = 2;
}

// 
message RealmsRedeemResponse {
  // 已经兑换的道具ID 和 数量 (在对应的限制类型周期内)
  map<int64,int32> redeemMap = 1;
  // 
  sim.RewardMessage rewardMessage = 2;
}

