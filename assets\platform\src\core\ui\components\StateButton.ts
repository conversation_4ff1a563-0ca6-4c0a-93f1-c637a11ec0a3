import { _decorator, CCBoolean, CCInteger, CCString, Component, Label, Node, UITransform } from "cc";
import { MessageComponent } from "./MessageComponent";
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass("StateButton")
@executeInEditMode
export class StateButton extends Component {
  // 状态
  @property({ serializable: true })
  _state: number = 0;
  // 状态数量
  @property(CCInteger)
  _stateCount: number = 0;
  // 状态列表
  @property(Node)
  _stateList: Node[] = [];

  @property({ type: CCInteger })
  set state(value: number) {
    if (value < 0) {
      value = this._stateCount - 1;
    }
    this._state = value % this._stateCount;

    this.refresh();
  }
  get state() {
    return this._state;
  }

  @property({ type: CCInteger })
  set stateCount(value: number) {
    if (value < 0) {
      value = 0;
    }
    this._stateCount = value;
    this.refresh();
  }
  get stateCount() {
    return this._stateCount;
  }

  onFocusInEditor(): void {
    this.refresh();
  }
  // 刷新
  refresh() {
    while (this.node.children.length < this._stateCount) {
      //
      let child = new Node(`state_${this.node.children.length}`); //
      child.parent = this.node;
      child.layer = this.node.layer;
      child.addComponent(UITransform);
    }
    while (this.node.children.length > this._stateCount) {
      //
      let child = this.node.children[this.node.children.length - 1];
      child.removeFromParent();
      child.destroy();
    }
    for (let i = 0; i < this.node.children.length; i++) {
      let child = this.node.children[i];
      child.active = i == this._state;
    }
  }
}
