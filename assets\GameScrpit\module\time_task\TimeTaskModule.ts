import { GameData } from "../../game/GameData";
import data from "../../lib/data/data";
import { TimeTaskApi } from "./TimeTaskApi";
import { TimeTaskConfig } from "./TimeTaskConfig";
import { TimeTaskData } from "./TimeTaskData";
import { TimeTaskRoute } from "./TimeTaskRoute";
import { TimeTaskService } from "./TimeTaskService";
import { TimeTaskSubscriber } from "./TimeTaskSubscriber";
import { TimeTaskViewModel } from "./TimeTaskViewModel";

export class TimeTaskModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): TimeTaskModule {
    if (!GameData.instance.TimeTaskModule) {
      GameData.instance.TimeTaskModule = new TimeTaskModule();
    }
    return GameData.instance.TimeTaskModule;
  }
  private _data = new TimeTaskData();
  private _api = new TimeTaskApi();
  private _service = new TimeTaskService();
  private _subscriber = new TimeTaskSubscriber();
  private _route = new TimeTaskRoute();
  private _viewModel = new TimeTaskViewModel();
  private _config = new TimeTaskConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new TimeTaskData();
    this._api = new TimeTaskApi();
    this._service = new TimeTaskService();
    this._subscriber = new TimeTaskSubscriber();
    this._route = new TimeTaskRoute();
    this._viewModel = new TimeTaskViewModel();
    this._config = new TimeTaskConfig();

    // 模块初始化
    this._subscriber.register();
    this._route.init();
    TimeTaskModule.api.timeTaskInfo(() => {
      completedCallback && completedCallback();
    });
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
