// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Horse.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface HorseMessage {
  /** 当前使用的坐骑编号 */
  horseId: number;
  /** 已经解锁的坐骑集合 */
  unLockHorseList: number[];
  /** 升阶的阶数 */
  stage: number;
  /** 当阶的已升级的次数 */
  grade: number;
  /** 已经拥有的可供升级的经验数量 */
  expPoint: number;
}

/**  */
export interface HorseUpResponse {
  /** 级 */
  grade: number;
  /** 阶 */
  stage: number;
  /** 经验值 */
  expPoint: number;
}

function createBaseHorseMessage(): HorseMessage {
  return { horseId: 0, unLockHorseList: [], stage: 0, grade: 0, expPoint: 0 };
}

export const HorseMessage: MessageFns<HorseMessage> = {
  encode(message: HorseMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.horseId !== 0) {
      writer.uint32(8).int64(message.horseId);
    }
    writer.uint32(18).fork();
    for (const v of message.unLockHorseList) {
      writer.int64(v);
    }
    writer.join();
    if (message.stage !== 0) {
      writer.uint32(24).int32(message.stage);
    }
    if (message.grade !== 0) {
      writer.uint32(32).int32(message.grade);
    }
    if (message.expPoint !== 0) {
      writer.uint32(40).int32(message.expPoint);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HorseMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHorseMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.horseId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.unLockHorseList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.unLockHorseList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.stage = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.grade = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.expPoint = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HorseMessage>, I>>(base?: I): HorseMessage {
    return HorseMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HorseMessage>, I>>(object: I): HorseMessage {
    const message = createBaseHorseMessage();
    message.horseId = object.horseId ?? 0;
    message.unLockHorseList = object.unLockHorseList?.map((e) => e) || [];
    message.stage = object.stage ?? 0;
    message.grade = object.grade ?? 0;
    message.expPoint = object.expPoint ?? 0;
    return message;
  },
};

function createBaseHorseUpResponse(): HorseUpResponse {
  return { grade: 0, stage: 0, expPoint: 0 };
}

export const HorseUpResponse: MessageFns<HorseUpResponse> = {
  encode(message: HorseUpResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.grade !== 0) {
      writer.uint32(8).int32(message.grade);
    }
    if (message.stage !== 0) {
      writer.uint32(16).int32(message.stage);
    }
    if (message.expPoint !== 0) {
      writer.uint32(24).int32(message.expPoint);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HorseUpResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHorseUpResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.grade = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.stage = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.expPoint = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HorseUpResponse>, I>>(base?: I): HorseUpResponse {
    return HorseUpResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HorseUpResponse>, I>>(object: I): HorseUpResponse {
    const message = createBaseHorseUpResponse();
    message.grade = object.grade ?? 0;
    message.stage = object.stage ?? 0;
    message.expPoint = object.expPoint ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
