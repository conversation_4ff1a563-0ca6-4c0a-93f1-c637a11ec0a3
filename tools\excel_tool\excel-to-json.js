const XLSX = require("xlsx");
const fs = require("fs");
const path = require("path");

/**
 * Excel转JSON转换器 - JavaScript简化版本
 * 支持字段格式定义：字段名:类型
 * 自动忽略临时文件（以~$开头的文件等）
 */
class ExcelToJsonConverter {
  constructor(inputDir, outputDir) {
    this.inputDir = path.resolve(inputDir);
    this.outputDir = outputDir ? path.resolve(outputDir) : path.resolve("assets/bundle_common_json/json");
    this.supportedExtensions = [".xlsx", ".xls"];
    this.encoding = "utf8";
  }

  /**
   * 设置输出目录
   */
  setOutputDir(outputDir) {
    this.outputDir = path.resolve(outputDir);
  }

  /**
   * 设置支持的文件扩展名
   */
  setSupportedExtensions(extensions) {
    this.supportedExtensions = extensions;
  }

  /**
   * 转换所有Excel文件
   */
  async convertAll(extensions) {
    try {
      console.log("开始批量转换Excel文件...");

      // 扫描目录获取有效文件
      const validFiles = this.scanDirectory(extensions);

      if (validFiles.length === 0) {
        console.log("未找到支持的Excel文件");
        return [];
      }

      console.log(`开始转换 ${validFiles.length} 个文件...`);

      // 转换每个文件
      const results = [];

      for (const filename of validFiles) {
        try {
          const result = this.convertSingle(filename);
          results.push(result);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.error(`转换文件 ${filename} 失败:`, errorMessage);
          results.push({
            success: false,
            filename,
            error: errorMessage,
          });
        }
      }

      // 统计结果
      const successCount = results.filter((r) => r.success && !r.skipped).length;
      const skippedCount = results.filter((r) => r.success && r.skipped).length;
      const failureCount = results.filter((r) => !r.success).length;

      console.log(`转换完成! 成功: ${successCount}个, 跳过: ${skippedCount}个, 失败: ${failureCount}个`);

      if (skippedCount > 0) {
        console.log("跳过的文件:");
        results
          .filter((r) => r.success && r.skipped)
          .forEach((r) => {
            console.log(`  - ${r.filename}: ${r.reason}`);
          });
      }

      if (failureCount > 0) {
        console.log("失败的文件:");
        results
          .filter((r) => !r.success)
          .forEach((r) => {
            console.log(`  - ${r.filename}: ${r.error}`);
          });
      }

      return results;
    } catch (error) {
      console.error("批量转换过程中发生错误:", error);
      throw error;
    }
  }

  /**
   * 转换单个Excel文件
   */
  convertSingle(filename) {
    try {
      const inputPath = path.join(this.inputDir, filename);
      const outputPath = this.generateOutputPath(inputPath);

      // 验证文件
      const validation = this.validateExcelFile(inputPath);
      if (!validation.valid) {
        throw new Error(validation.error || "文件验证失败");
      }

      // 处理文件
      return this.processFile(inputPath, outputPath);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        filename,
        error: errorMessage,
      };
    }
  }

  /**
   * 扫描目录获取有效文件
   */
  scanDirectory(extensions) {
    if (!fs.existsSync(this.inputDir)) {
      throw new Error(`输入目录不存在: ${this.inputDir}`);
    }

    const files = fs.readdirSync(this.inputDir);
    const targetExtensions = extensions || this.supportedExtensions;

    return files.filter((file) => {
      // 跳过临时文件
      if (file.startsWith("~$") || file.startsWith(".")) {
        return false;
      }

      // 检查扩展名
      const ext = path.extname(file).toLowerCase();
      return targetExtensions.includes(ext);
    });
  }

  /**
   * 验证Excel文件
   */
  validateExcelFile(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        return { valid: false, error: "文件不存在" };
      }

      const workbook = XLSX.readFile(filePath);
      const sheetNames = workbook.SheetNames;

      if (sheetNames.length === 0) {
        return { valid: false, error: "Excel文件中没有工作表" };
      }

      const worksheet = workbook.Sheets[sheetNames[0]];
      const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");

      // 检查是否至少有4行（描述、输出条件、字段定义、数据）
      if (range.e.r < 3) {
        return { valid: false, error: "Excel文件格式不正确，至少需要4行（描述、输出条件、字段定义、数据）" };
      }

      return { valid: true };
    } catch (error) {
      return { valid: false, error: `文件验证失败: ${error.message}` };
    }
  }

  /**
   * 处理Excel文件
   */
  processFile(inputPath, outputPath) {
    try {
      // console.log(`处理文件: ${path.basename(inputPath)}`);

      const workbook = XLSX.readFile(inputPath);
      const sheetNames = workbook.SheetNames;
      const worksheet = workbook.Sheets[sheetNames[0]];

      const result = this.parseWorksheet(worksheet, path.basename(inputPath));

      if (result.skipped) {
        return {
          success: true,
          filename: path.basename(inputPath),
          skipped: true,
          reason: result.reason,
        };
      }

      // 确保输出目录存在
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // 写入JSON文件
      fs.writeFileSync(outputPath, JSON.stringify(result.data, null, 2), this.encoding);

      return {
        success: true,
        filename: path.basename(inputPath),
        outputPath: outputPath,
      };
    } catch (error) {
      throw new Error(`处理文件失败: ${error.message}`);
    }
  }

  /**
   * 解析工作表
   */
  parseWorksheet(worksheet, filename) {
    const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");

    // 解析字段定义（第2行为输出条件，第3行为字段定义）
    const fieldDefinitions = this.parseFieldDefinitions(worksheet, range, 1, 2);

    if (!fieldDefinitions || fieldDefinitions.length === 0) {
      return {
        skipped: true,
        reason: "字段定义格式错误",
      };
    }

    // console.log(
    //   `字段定义: ${fieldDefinitions
    //     .filter((f) => f !== null)
    //     .map((f) => `${f.name}:${f.type}`)
    //     .join(", ")}`
    // );

    // 解析数据行（从第4行开始）
    const data = [];

    const rs = {};

    // 默认值 -1 项 第4行
    const rowDataDefault = {};
    let hasValidData = false;
    let hasId = false;
    for (let colIndex = range.s.c; colIndex <= range.e.c; colIndex++) {
      const field = fieldDefinitions[colIndex];
      if (!field) continue; // 跳过无效字段

      const cellAddress = XLSX.utils.encode_cell({ r: 3, c: colIndex });
      const cell = worksheet[cellAddress];
      const rawValue = cell ? cell.v : undefined;

      // -1 项有默认 空字符串，内容里不用
      let convertedValue = this.convertValue(rawValue, field.type);
      if (convertedValue === undefined) {
        if (field.type == "number") {
          convertedValue = 0;
        } else if (field.type == "string") {
          convertedValue = "";
        } else if (field.type.indexOf("[]") > 0) {
          convertedValue = [];
        }
      }

      if (convertedValue !== undefined) {
        rowDataDefault[field.name] = convertedValue;
        hasValidData = true;

        if (field.name === "id") {
          hasId = true;
        }
      }
    }

    if (hasValidData) {
      if (hasId) {
        data.push(rowDataDefault);
        rs[rowDataDefault["id"]] = rowDataDefault;
      }
    }

    // 解析数据行（从第5行开始）
    for (let rowIndex = 4; rowIndex <= range.e.r; rowIndex++) {
      const rowData = {};
      let hasValidData = false;
      let hasId = false;

      for (let colIndex = range.s.c; colIndex <= range.e.c; colIndex++) {
        const field = fieldDefinitions[colIndex];
        if (!field) continue; // 跳过无效字段

        const cellAddress = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });
        const cell = worksheet[cellAddress];
        const rawValue = cell ? cell.v : undefined;

        const convertedValue = this.convertValue(rawValue, field.type);
        if (convertedValue !== undefined) {
          rowData[field.name] = convertedValue;
          hasValidData = true;

          if (field.name === "id") {
            hasId = true;
          }
        }
      }

      if (hasValidData) {
        if (hasId) {
          data.push(rowData);
          rs[rowData["id"]] = rowData;
        }
      }
    }

    if (data.length === 0) {
      return {
        skipped: true,
        reason: "转换结果为空对象",
      };
    }

    return { data: rs };
  }

  /**
   * 解析字段定义
   */
  parseFieldDefinitions(worksheet, range, outputConditionRowIndex, fieldDefinitionRowIndex) {
    const fieldDefinitions = [];

    for (let colIndex = range.s.c; colIndex <= range.e.c; colIndex++) {
      // 获取输出条件
      const outputConditionAddress = XLSX.utils.encode_cell({ r: outputConditionRowIndex, c: colIndex });
      const outputConditionCell = worksheet[outputConditionAddress];
      const shouldOutput = outputConditionCell && outputConditionCell.v === 1;

      // 获取字段定义
      const fieldAddress = XLSX.utils.encode_cell({ r: fieldDefinitionRowIndex, c: colIndex });
      const fieldCell = worksheet[fieldAddress];

      if (!fieldCell || !fieldCell.v) {
        fieldDefinitions.push(null);
        continue;
      }

      const cellValue = String(fieldCell.v).trim();
      if (!cellValue) {
        fieldDefinitions.push(null);
        continue;
      }

      // 解析字段定义格式：字段名:类型
      const parts = cellValue.split(":");
      if (parts.length !== 2) {
        fieldDefinitions.push(null);
        continue;
      }

      const fieldName = parts[0].trim();
      const fieldType = parts[1].trim();

      // 删除字段名中的括号及其内容
      const cleanFieldName = fieldName.replace(/[()（）][^()（）]*[()（）]/g, "").replace(/[()（）]/g, "");

      if (!cleanFieldName || !this.isValidDataType(fieldType)) {
        fieldDefinitions.push(null);
        continue;
      }

      fieldDefinitions.push({
        name: cleanFieldName,
        type: fieldType,
        shouldOutput: shouldOutput,
      });
    }

    return fieldDefinitions;
  }

  /**
   * 检查是否为有效的数据类型
   */
  isValidDataType(type) {
    const validTypes = ["string", "number", "string[]", "number[]", "string[][]", "number[][]", "json"];
    return validTypes.includes(type);
  }

  /**
   * 转换值到指定类型
   */
  convertValue(value, type) {
    try {
      switch (type) {
        case "string":
          if (value == 0) {
            return "0";
          }
          value = (value || "") + "";
          value = value.replace("[/br]", "\n");
          return value ? String(value) : undefined;

        case "number":
          const num = Number(value);
          return isNaN(num) ? undefined : num;

        case "string[]":
          if (typeof value === "string") {
            return value
              .split(",")
              .map((s) => s.trim().replace("[/br]", "\n"))
              .filter((s) => s.length > 0);
          }
          return undefined;

        case "number[]":
          if (typeof value === "string") {
            const numbers = value
              .split(",")
              .map((s) => Number(s.trim()))
              .filter((n) => !isNaN(n));
            return numbers || [];
          } else if (typeof value === "number") {
            return [value];
          }
          return undefined;

        case "string[][]":
          if (typeof value === "string") {
            return value
              .split(";")
              .map((group) =>
                group
                  .split(",")
                  .map((s) => s.trim().replace("[/br]", "\n"))
                  .filter((s) => s.length > 0)
              )
              .filter((group) => group.length > 0);
          }
          return undefined;

        case "number[][]":
          if (typeof value === "string") {
            return value
              .split(";")
              .map((group) =>
                group
                  .split(",")
                  .map((s) => Number(s.trim()))
                  .filter((n) => !isNaN(n))
              )
              .filter((group) => group.length > 0);
          }
          return undefined;

        case "json":
          if (typeof value === "string") {
            value = (value || "").replace("[/br]", "\n");
            try {
              return JSON.parse(value);
            } catch (e) {
              console.log(e);
              return {};
            }
          }
          return value;

        default:
          return value;
      }
    } catch (error) {
      console.warn(`转换值失败: ${value} -> ${type}`, error);
      return undefined;
    }
  }

  /**
   * 生成输出路径
   */
  generateOutputPath(inputPath) {
    const baseName = path.basename(inputPath, path.extname(inputPath));
    // 删除文件名中的括号及其内容
    const cleanBaseName = baseName.replace(/[()（）][^()（）]*[()（）]/g, "").replace(/[()（）]/g, "");
    return path.join(this.outputDir, `${cleanBaseName}.json`);
  }

  /**
   * 获取目录统计信息
   */
  getDirectoryStats() {
    if (!fs.existsSync(this.inputDir)) {
      return { totalFiles: 0, excelFiles: 0, tempFiles: 0, validFiles: 0 };
    }

    const files = fs.readdirSync(this.inputDir);
    let totalFiles = files.length;
    let excelFiles = 0;
    let tempFiles = 0;
    let validFiles = 0;

    files.forEach((file) => {
      const ext = path.extname(file).toLowerCase();

      if (file.startsWith("~$") || file.startsWith(".")) {
        tempFiles++;
      } else if (this.supportedExtensions.includes(ext)) {
        excelFiles++;
        validFiles++;
      }
    });

    return { totalFiles, excelFiles, tempFiles, validFiles };
  }
}

/**
 * 快速转换函数
 */
async function convertExcelToJson(inputDir, outputDir, extensions) {
  const converter = new ExcelToJsonConverter(inputDir, outputDir);

  if (extensions) {
    converter.setSupportedExtensions(extensions);
  }

  return await converter.convertAll();
}

// 导出
module.exports = {
  ExcelToJsonConverter,
  convertExcelToJson,
};
