import { _decorator, Component, Label, Node } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
import { DailyChallengeRankAdapter } from "../../adapter/DailyChallengeRankViewHolder";
import { DailyChallengeModule } from "../../DailyChallengeModule";
import { CommRankMessage } from "db://assets/GameScrpit/game/net/protocol/Player";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
const { ccclass, property } = _decorator;

@ccclass("UIDailyChallengeRank")
@routeConfig({
  bundle: BundleEnum.BUNDLE_DAILY_CHALLENGE,
  url: "prefab/ui/UIDailyChallengeRank",
  nextHop: [],
  exit: "dialog_close",
})
export class UIDailyChallengeRank extends BaseCtrl {
  public playShowAni: boolean = true;
  start() {
    super.start();
    let adapter = new DailyChallengeRankAdapter(this.getNode("viewholder_rank"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(adapter);
    DailyChallengeModule.api.rank((data: CommRankMessage) => {
      adapter.setData(data.rankList);
      this.getNode("lbl_my_rank").getComponent(Label).string = `我的排名:${data.rank === -1 ? "未上榜" : data.rank}`;
      this.getNode("lbl_my_score").getComponent(Label).string = `${Formate.format(data.point)}`;
    });
  }

  update(deltaTime: number) {}
}
