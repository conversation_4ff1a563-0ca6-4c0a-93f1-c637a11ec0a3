// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Activity.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { CommIntegerListMessage, RewardMessage } from "./Comm";
import { PlayerBaseMessage, PlayerDetailMessage, PlayerSimpleMessage, PlayerTempleMessage } from "./Player";

export const protobufPackage = "sim";

/**  */
export interface AchieveMessage {
  /** 成就互动ID */
  id: number;
  /** 当前成就值 */
  targetVal: number;
  /** 是否支付 */
  paid: boolean;
  /** 已经领取基础奖励的列表 */
  basicTakeList: number[];
  /** 已经领取奖励的列表 */
  paidTakeList: number[];
}

/**  */
export interface AchievePayResponse {
  activityId: number;
  /** 成就ID */
  achieveId: number;
}

/**  */
export interface AchieveRewardRequest {
  /** 基金的ID */
  activityId: number;
  /** 成就ID */
  achieveId: number;
  /** 领取的索引处对应的奖励 从0开始 */
  index: number;
  /** 领取所有可领取的奖励 */
  takeAll: boolean;
}

/**  */
export interface AchieveRewardResponse {
  activityId: number;
  /**  */
  achieve:
    | AchieveMessage
    | undefined;
  /** 奖励情况 */
  rewardList: number[];
}

/**  */
export interface AchieveTargetValResponse {
  activityId: number;
  achieveId: number;
  targetVal: number;
}

/**  */
export interface ActivityDrawRequest {
  /** 活动ID */
  activityId: number;
  /** 是否十连抽奖 */
  tenth: boolean;
}

/**  */
export interface ActivityRechargeResponse {
  /** 充值任务完成情况 */
  leaderRecharge: LeaderRechargeMessage | undefined;
  rewardList: number[];
}

/**  */
export interface ActivityRechargeUpdateMessage {
  /** 充值的活动 */
  activityId: number;
  /** 充值获得的奖励 */
  rewardList: number[];
  /** 兑换的道具列表 */
  redeemId: number;
}

/**  */
export interface ActivityTakeRequest {
  activityId: number;
  index: number;
  /** 是否一次性领取所有可领取的奖励 */
  takeAll: boolean;
}

/**  */
export interface ActivityTakeResponse {
  rewardList: number[];
  takeList: number[];
}

/**  */
export interface ActivityUpdateMessage {
  /** 需要更新的活动ID配置 */
  configIdList: number[];
  /** 新开启的活动集合 */
  addList: number[];
  /** 生效的活动集合 */
  closeList: number[];
  /** 总的生效的模块ID集合 */
  totalList: number[];
}

/**  */
export interface ActivityUseRequest {
  activityId: number;
  /** 是否十连消耗 */
  isTenUse: boolean;
}

/**  */
export interface AdventureChooseRequest {
  activityId: number;
  bigPrizeId: number;
}

/**  */
export interface AdventureDrawResponse {
  /** 抽中的道具ID列表 */
  drawItemList: number[];
  /** 奖励列表 */
  rewardList: number[];
  /** 中大奖的次数 */
  bigWinCount: number;
  /** 当前轮次已经抽奖的次数   值为0时表示此次有中大奖,drawItemList最后一个就是大奖 */
  curCostCount: number;
  /** 大奖选中分布 */
  bigChosenMap: { [key: number]: number };
}

export interface AdventureDrawResponse_BigChosenMapEntry {
  key: number;
  value: number;
}

/**  */
export interface AdventureMessage {
  activityId: number;
  achieveMap: { [key: number]: AchieveMessage };
  /** 物料ID:兑换次数 */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 解锁条件：值 */
  limitMap: { [key: number]: number };
  /** 自选道具ID：选中的索引列表 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 本轮选中的大奖的ID 小于等于0表示未选中 */
  curBigPrizeId: number;
  /** 中大奖的次数 */
  bigWinCount: number;
  /** 当前轮次已经抽奖的次数 */
  curCostCount: number;
  /** 大奖选中分布 */
  bigChosenMap: { [key: number]: number };
  /** 累计探险次数的奖励领取索引集合 */
  achieveTakeList: number[];
  /** 今日任务列表(为任务列表配置的索引) */
  taskIndexList: number[];
  /** 今日任务完成数量 */
  targetValList: number[];
  /** 今日任务领取奖励的列表 */
  taskTakeList: number[];
}

export interface AdventureMessage_AchieveMapEntry {
  key: number;
  value: AchieveMessage | undefined;
}

export interface AdventureMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface AdventureMessage_AdMapEntry {
  key: number;
  value: number;
}

export interface AdventureMessage_LimitMapEntry {
  key: number;
  value: number;
}

export interface AdventureMessage_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

export interface AdventureMessage_BigChosenMapEntry {
  key: number;
  value: number;
}

/**  */
export interface ConsumeTaskMessage {
  activityId: number;
  /** 物料ID:兑换次数 */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 限制条件 兑换解锁要求的值 */
  limitMap: { [key: number]: number };
  /** 成就 */
  achieveMap: { [key: number]: AchieveMessage };
  /** 当前选中的道具 小于0，代表没有选中 */
  chosenItemId: number;
  /** 是否有领取排行榜奖励 */
  boardTake: boolean;
}

export interface ConsumeTaskMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface ConsumeTaskMessage_AdMapEntry {
  key: number;
  value: number;
}

export interface ConsumeTaskMessage_LimitMapEntry {
  key: number;
  value: number;
}

export interface ConsumeTaskMessage_AchieveMapEntry {
  key: number;
  value: AchieveMessage | undefined;
}

/**  */
export interface ConsumeTaskUseResponse {
  /** 本次使用道具的数量 */
  count: number;
  /**  */
  rewardMessage: RewardMessage | undefined;
}

/**  */
export interface DaySignMessage {
  /** 今日是否已经签到 */
  sign: boolean;
  /** 是否已充值 */
  paid: boolean;
  /** 领取基础签到奖励 0-6 */
  basicList: number[];
  /** 领取付费签到奖励 0-6 */
  paidList: number[];
}

/**  */
export interface DayTaskMessage {
  /** 任务完成数量 */
  targetValList: number[];
  /** 已领取奖励的索引列表 */
  takeList: number[];
}

/**  */
export interface DayTaskResponse {
  activityId: number;
  /** 任务完成数量 */
  targetValList: number[];
}

/**  */
export interface FinishMessage {
  /** 完成次数 */
  count: number;
  /** 是否有领取奖励 */
  take: boolean;
}

/**  */
export interface FirstRechargeMessage {
  /** 当前已购买首充档位 */
  subRechargeMap: { [key: number]: FirstSubRechargeMessage };
}

export interface FirstRechargeMessage_SubRechargeMapEntry {
  key: number;
  value: FirstSubRechargeMessage | undefined;
}

/**  */
export interface FirstRechargeSignRequest {
  /** 首充分档位ID */
  id: number;
  /** 第几日的奖励 从0开始 */
  index: number;
}

/**  */
export interface FirstRechargeSignResponse {
  /**  */
  message: FirstSubRechargeMessage | undefined;
  rewardList: number[];
}

/**  */
export interface FirstSubRechargeMessage {
  /** 档位ID */
  id: number;
  /** 长度为每一档领取的奖励的配置长度  未解锁 -1;可领取 0;已领取 1 */
  stateList: number[];
}

/**  */
export interface FractureAnswerRequest {
  activityId: number;
  answer: number;
}

/**  */
export interface FractureDrawResponse {
  /** 当前触发的事件ID */
  fractureId: number;
  /** 当触发的是事件类型，这里表示的是对应关联ID */
  choiceId: number;
  /** 剩余的可探索数量 */
  remainFloorCountMap: { [key: number]: number };
  /** 奖励 */
  rewardMessage: RewardMessage | undefined;
}

export interface FractureDrawResponse_RemainFloorCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FractureEventResponse {
  /** 当前事件ID */
  fractureId: number;
  /** 当触发的是事件类型，这里表示的是对应关联ID */
  choiceId: number;
  /** 剩余的可探索数量 */
  remainFloorCountMap: { [key: number]: number };
  /**  */
  rewardMessage: RewardMessage | undefined;
}

export interface FractureEventResponse_RemainFloorCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FractureFightMessage {
  /** monsterId 和  npcMessage只生效一个，先判断npcMessage是否为空，非空对战的是玩家，否则为配置表怪物 */
  monsterId: number;
  /** 当前已挑战过的次数 */
  fightCount: number;
  /** npc类型，当前触发的是真实玩家 */
  npcMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 触发的是小怪/BOSS/玩家剩余的血量 */
  remainHp: number;
  /** 冷却时间，如果战斗有冷却时间，必须在这个时间后发起 */
  fightColdTime: number;
}

/**  */
export interface FractureFightResponse {
  /** 当前触发的事件ID */
  fractureId: number;
  /** 当触发的是事件类型，这里表示的是对应关联ID */
  choiceId: number;
  /** 剩余的可探索数量 */
  remainFloorCountMap: { [key: number]: number };
  /** 是否胜利 */
  win: boolean;
  /** Boss关卡战斗回放 */
  replay: string;
  /**  */
  fightMessage:
    | FractureFightMessage
    | undefined;
  /** 新增的日志,可能为空 */
  newLog:
    | FractureLogMessage
    | undefined;
  /** 奖励 */
  rewardMessage: RewardMessage | undefined;
}

export interface FractureFightResponse_RemainFloorCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FractureLogAssistResponse {
  activityId: number;
  /**  */
  logMessage:
    | FractureLogMessage
    | undefined;
  /** 剩余的可探索数量 */
  remainFloorCountMap: { [key: number]: number };
  /** 参与协助的玩家信息 */
  assistUserMessage: PlayerSimpleMessage | undefined;
}

export interface FractureLogAssistResponse_RemainFloorCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FractureLogFightResponse {
  /** 如果已经别击杀了，则error为true,尤其是已发起协助的情况下，可能被其他玩家已经击杀 */
  error: boolean;
  /** 如果error为true时有值，更新日志 */
  fractureLogMessage: FractureLogMessage | undefined;
  win: boolean;
  replay: string;
  remainHp: number;
  /** 剩余的可探索数量 */
  remainFloorCountMap: { [key: number]: number };
  assistMembers: PlayerBaseMessage[];
}

export interface FractureLogFightResponse_RemainFloorCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FractureLogMessage {
  id: number;
  /** 是否发起协助了 */
  assist: boolean;
  /** 怪物ID */
  monsterId: number;
  /** 事件ID */
  fractureId: number;
  /** npc信息 如果npc有值，本次战斗对象就是npc玩家，否则取怪物ID */
  npcMessage:
    | PlayerSimpleMessage
    | undefined;
  /** 剩余血量 */
  remainHp: number;
  /** 是否已领取奖励 */
  take: boolean;
  /** 冷却时间 */
  coldTime: number;
  /** 参与协助的用户的信息 */
  helpMembers: PlayerBaseMessage[];
  /** 当前已挑战过的次数 */
  fightCount: number;
}

/**  */
export interface FractureLogRequest {
  activityId: number;
  logId: number;
}

/**  */
export interface FractureMessage {
  activityId: number;
  /** 物料ID:兑换次数 */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 限制条件 兑换解锁要求的值 */
  limitMap: { [key: number]: number };
  /** 自选礼包的选中情况 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 当前所处的楼层 */
  floorId: number;
  /** 当前触发的事件ID 小于等于0表示还未搜索当前楼层 */
  fractureId: number;
  /** 当触发的是事件类型，这里表示的是对应关联ID */
  choiceId: number;
  /** 路障相关 */
  roadMessage:
    | FractureRoadMessage
    | undefined;
  /** 小怪，BOSS,NPC类型相关 */
  fightMessage:
    | FractureFightMessage
    | undefined;
  /** 剩余的可探索数量 */
  remainFloorCountMap: { [key: number]: number };
  /** 是否领取排行榜奖励 */
  boardTake: boolean;
}

export interface FractureMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface FractureMessage_AdMapEntry {
  key: number;
  value: number;
}

export interface FractureMessage_LimitMapEntry {
  key: number;
  value: number;
}

export interface FractureMessage_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

export interface FractureMessage_RemainFloorCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FractureRoadAssistResponse {
  activityId: number;
  assistCount: number;
  /** 参与协助的玩家信息 */
  assistUserMessage: PlayerSimpleMessage | undefined;
}

/**  */
export interface FractureRoadMessage {
  /** 是否发起了协助 */
  assist: boolean;
  /** 已经被协助的数量 */
  assistCount: number;
  /** 路障的开放时间 */
  roadDeadline: number;
}

/**  */
export interface FractureSearchResponse {
  /** 当前触发的事件ID */
  fractureId: number;
  /** 当触发的是事件类型，这里表示的是对应关联ID */
  choiceId: number;
  /** 剩余的可探索数量 */
  remainFloorCountMap: { [key: number]: number };
  /** 路障相关 */
  roadMessage:
    | FractureRoadMessage
    | undefined;
  /** 小怪，BOSS,NPC类型相关 */
  fightMessage:
    | FractureFightMessage
    | undefined;
  /** 搜索获得奖励 */
  rewardMessage: RewardMessage | undefined;
}

export interface FractureSearchResponse_RemainFloorCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FractureSkipResponse {
  /** 当前触发的事件ID */
  fractureId: number;
  /** 当触发的是事件类型，这里表示的是对应关联ID */
  choiceId: number;
  /** 新增的日志(不为空就加入到日志列表中) */
  newLog: FractureLogMessage | undefined;
}

/**  */
export interface FractureTrapResponse {
  /** 当前事件ID */
  fractureId: number;
  /** 当触发的是事件类型，这里表示的是对应关联ID */
  choiceId: number;
  /** 剩余的可探索数量 */
  remainFloorCountMap: { [key: number]: number };
  /**  */
  roadMessage:
    | FractureRoadMessage
    | undefined;
  /** 完成后的奖励 */
  rewardMessage: RewardMessage | undefined;
}

export interface FractureTrapResponse_RemainFloorCountMapEntry {
  key: number;
  value: number;
}

/**  */
export interface FractureTravelRequest {
  activityId: number;
  floorId: number;
}

/**  */
export interface FundMessage {
  activityId: number;
  achieveMap: { [key: number]: AchieveMessage };
}

export interface FundMessage_AchieveMapEntry {
  key: number;
  value: AchieveMessage | undefined;
}

/**  */
export interface LeaderFundMessage {
  activityId: number;
  /** 物料ID:兑换次数 */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 解锁条件：值 */
  limitMap: { [key: number]: number };
  /** 自选礼包的选中情况 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 成就列表 key:成就ID */
  achieveMap: { [key: number]: AchieveMessage };
  /** 签到 */
  daySign:
    | DaySignMessage
    | undefined;
  /** 任务完成情况 */
  dayTask:
    | DayTaskMessage
    | undefined;
  /** 充值进度 */
  leaderRecharge: LeaderRechargeMessage | undefined;
}

export interface LeaderFundMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface LeaderFundMessage_AdMapEntry {
  key: number;
  value: number;
}

export interface LeaderFundMessage_LimitMapEntry {
  key: number;
  value: number;
}

export interface LeaderFundMessage_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

export interface LeaderFundMessage_AchieveMapEntry {
  key: number;
  value: AchieveMessage | undefined;
}

/**  */
export interface LeaderFundRechargeResponse {
  activityId: number;
  numerator: number;
}

/**  */
export interface LeaderRechargeMessage {
  /** 分子 */
  numerator: number;
  /** 分母 */
  denominator: number;
  /** 是否领取过奖励 */
  take: boolean;
}

/**  */
export interface LeaderSignRequest {
  activityId: number;
  /** 第几天，从0开始 */
  index: number;
}

/**  */
export interface LeaderSignResponse {
  sign: boolean;
  /** 领取基础签到奖励 0-6 */
  basicList: number[];
  /** 领取付费签到奖励 0-6 */
  paidList: number[];
  rewardList: number[];
}

/**  */
export interface LifeCardBuyResponse {
  /**  */
  vipCardMessage: VipCardMessage | undefined;
  rewardList: number[];
}

/**  */
export interface MonthCardBuyResponse {
  /**  */
  vipCardMessage: VipCardMessage | undefined;
  rewardList: number[];
}

/**  */
export interface PrayerDrawResponse {
  /** 抽中的道具ID列表 */
  drawItemList: number[];
  /** 奖励列表 */
  rewardList: number[];
}

/**  */
export interface PrayerMessage {
  activityId: number;
  achieveMap: { [key: number]: AchieveMessage };
  /** 物料ID:兑换次数 */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 解锁条件：值 */
  limitMap: { [key: number]: number };
  /** 自选道具ID：选中的索引列表 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 今日任务列表(为任务列表配置的索引) */
  taskIndexList: number[];
  /** 今日任务完成数量 */
  targetValList: number[];
  /** 今日任务领取奖励的列表 */
  taskTakeList: number[];
}

export interface PrayerMessage_AchieveMapEntry {
  key: number;
  value: AchieveMessage | undefined;
}

export interface PrayerMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface PrayerMessage_AdMapEntry {
  key: number;
  value: number;
}

export interface PrayerMessage_LimitMapEntry {
  key: number;
  value: number;
}

export interface PrayerMessage_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

/**  */
export interface ProsperityMessage {
  activityId: number;
  /** 物料ID:兑换次数 */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 限制条件 兑换活动ID解锁要求的值 */
  limitMap: { [key: number]: number };
  /** 自选礼包的选中情况 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 成就列表 key:成就ID */
  achieveMap: { [key: number]: AchieveMessage };
}

export interface ProsperityMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface ProsperityMessage_AdMapEntry {
  key: number;
  value: number;
}

export interface ProsperityMessage_LimitMapEntry {
  key: number;
  value: number;
}

export interface ProsperityMessage_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

export interface ProsperityMessage_AchieveMapEntry {
  key: number;
  value: AchieveMessage | undefined;
}

/**  */
export interface RechargeRewardTakeResponse {
  /** 奖励领取的索引集合 */
  takeList: number[];
  rewardList: number[];
}

/**  */
export interface RedeemBuyMessage {
  activityId: number;
  redeemMap: { [key: number]: number };
  /** 自选礼包的选中情况 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 获得的道具 */
  rewardList: number[];
  /** 购买的弹窗礼包ID */
  redeemId: number;
  /** 购买数量 */
  count: number;
}

export interface RedeemBuyMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface RedeemBuyMessage_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

/**  */
export interface RedeemChosenRequest {
  /** 活动ID */
  activityId: number;
  /** 商品ID */
  redeemId: number;
  /** 自选的道具所在的索引顺序,如果能选多个，传对应多个选中的索引顺序 */
  chosenIndexList: number[];
  /** 购买数量 */
  count: number;
}

/**  */
export interface RedeemLimitUpdateMessage {
  activityId: number;
  /** 限制条件 */
  limitMap: { [key: number]: number };
}

export interface RedeemLimitUpdateMessage_LimitMapEntry {
  key: number;
  value: number;
}

/**  */
export interface RedeemMessage {
  activityId: number;
  /** 物料ID:兑换次数 */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 限制条件 兑换解锁要求的值 */
  limitMap: { [key: number]: number };
  /** 自选礼包的选中情况 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 重置时间 */
  resetTime: number;
}

export interface RedeemMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface RedeemMessage_AdMapEntry {
  key: number;
  value: number;
}

export interface RedeemMessage_LimitMapEntry {
  key: number;
  value: number;
}

export interface RedeemMessage_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

/**  */
export interface RedeemRequest {
  /** 活动ID */
  activityId: number;
  /** 商品ID */
  redeemId: number;
  /** 如果付费购买或免费兑换道具，表示购买的数量;否则，默认传1 */
  count: number;
}

/**  */
export interface RedeemResponse {
  /** 购买情况 key:商品ID */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 自选礼包的选中情况 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 奖励 */
  rewardList: number[];
}

export interface RedeemResponse_RedeemMapEntry {
  key: number;
  value: number;
}

export interface RedeemResponse_AdMapEntry {
  key: number;
  value: number;
}

export interface RedeemResponse_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

/**  */
export interface SevenDaySignMessage {
  /** 活动ID */
  activityId: number;
  /** 已经签到的天数 */
  count: number;
  /** 是否今日已签到 */
  sign: boolean;
  /** 活动的结束时间 */
  endTime: number;
}

/**  */
export interface SevenDaySignResponse {
  /**  */
  sevenDaySign:
    | SevenDaySignMessage
    | undefined;
  /**  */
  rewardMessage: RewardMessage | undefined;
}

/**  */
export interface SevenDaySignRewardRequest {
  /** 七日活动 */
  activityId: number;
  /** 签到第几天 */
  index: number;
}

/**  */
export interface SimplePointMessage {
  /** 用户信息 */
  detailMessage: PlayerDetailMessage | undefined;
  pre: number;
  after: number;
  /** 积分 */
  point: number;
}

/**  */
export interface SimpleRankMessage {
  pre: number;
  after: number;
  point: number;
  rank: number;
  rankList: SimplePointMessage[];
}

/**  */
export interface TaskCompleteMessage {
  targetVal: number;
  takeList: number[];
}

/**  */
export interface TempleLikeResponse {
  /** 今日已经点赞的子殿ID集合 */
  likeList: number[];
  /** 已经获得的神迹效果ID：剩余的生效次数 */
  miracleRemainMap: { [key: number]: number };
  /** 奖励 */
  rewardMessage:
    | RewardMessage
    | undefined;
  /** 新生效的神迹ID 小于等于0就是没有获得新的神迹 */
  newMiracleId: number;
}

export interface TempleLikeResponse_MiracleRemainMapEntry {
  key: number;
  value: number;
}

/**  */
export interface TempleMessage {
  /** 子殿ID，获得子殿称号的玩家信息 */
  placeMap: { [key: number]: PlayerTempleMessage };
  /** 今日已经点赞的子殿ID集合 */
  likeList: number[];
  /** 已经获得的神迹效果ID：剩余的生效次数 */
  miracleRemainMap: { [key: number]: number };
  /** 逆袭之路的总值 */
  totalTreeVal: number;
  /** 逆袭之路领取的奖励索引集合 */
  treeTakeList: number[];
}

export interface TempleMessage_PlaceMapEntry {
  key: number;
  value: PlayerTempleMessage | undefined;
}

export interface TempleMessage_MiracleRemainMapEntry {
  key: number;
  value: number;
}

/**  */
export interface TimeTaskMessage {
  activityId: number;
  completeMap: { [key: number]: TaskCompleteMessage };
}

export interface TimeTaskMessage_CompleteMapEntry {
  key: number;
  value: TaskCompleteMessage | undefined;
}

/**  */
export interface TimeTaskResponse {
  activityId: number;
  taskId: number;
  targetVal: number;
}

/**  */
export interface TimeTaskTakeRequest {
  activityId: number;
  taskId: number;
  index: number;
  /** 是否一次性领取所有可领取的奖励 */
  takeAll: boolean;
}

/**  */
export interface TopUpMessage {
  activityId: number;
  /** 物料ID:兑换次数 */
  redeemMap: { [key: number]: number };
  /** 物料ID:广告次数 */
  adMap: { [key: number]: number };
  /** 限制条件 兑换活动ID解锁要求的值 */
  limitMap: { [key: number]: number };
  /** 自选礼包的选中情况 */
  chosenMap: { [key: number]: CommIntegerListMessage };
  /** 今日领取的充值ID 对应配置表的ID */
  signId: number;
  /** 一轮循环的签到状态 */
  signMap: { [key: number]: number };
  /** 总消费金额 */
  totalRecharge: number;
  /** 领取的累计金额索引列表，索引从0开始 */
  takeList: number[];
  /** 截止时间 */
  deadline: number;
}

export interface TopUpMessage_RedeemMapEntry {
  key: number;
  value: number;
}

export interface TopUpMessage_AdMapEntry {
  key: number;
  value: number;
}

export interface TopUpMessage_LimitMapEntry {
  key: number;
  value: number;
}

export interface TopUpMessage_ChosenMapEntry {
  key: number;
  value: CommIntegerListMessage | undefined;
}

export interface TopUpMessage_SignMapEntry {
  key: number;
  value: number;
}

/**  */
export interface TopUpRewardRequest {
  activityId: number;
  /** 累计充值赠送道具索引 */
  index: number;
}

/**  */
export interface TopUpRewardResponse {
  /** 领取的累计金额索引列表，索引从0开始 */
  takeList: number[];
  rewardList: number[];
}

/**  */
export interface TopUpSignRequest {
  activityId: number;
  signId: number;
}

/**  */
export interface TopUpSignResponse {
  /** 一轮循环的签到状态 */
  signMap: { [key: number]: number };
  /** 截止时间 */
  deadline: number;
  rewardList: number[];
}

export interface TopUpSignResponse_SignMapEntry {
  key: number;
  value: number;
}

/**  */
export interface VipCardMessage {
  /** 是否有购买终身卡 */
  life: boolean;
  /** 月卡的截止有效时间 */
  deadline: number;
  /** 是否有领取月卡的日常道具奖励 */
  takeMonthDailyReward: boolean;
  /** 是否有领取终身卡卡的日常道具奖励 */
  takeLifeDailyReward: boolean;
}

/**  */
export interface VipCardResponse {
  /**  */
  vipCard: VipCardMessage | undefined;
  rewardList: number[];
}

/**  */
export interface WindowPackMessage {
  /** 活动ID */
  activityId: number;
  /** 持续时间 key:弹窗类型 val:截止时间戳 */
  durationMap: { [key: number]: number };
  /** 购买情况 商品ID集合 */
  redeemList: number[];
  /** 冷却截止时间   key:弹窗类型 val:冷却截止时间戳 */
  coldMap: { [key: number]: number };
}

export interface WindowPackMessage_DurationMapEntry {
  key: number;
  value: number;
}

export interface WindowPackMessage_ColdMapEntry {
  key: number;
  value: number;
}

/**  */
export interface WindowRecordRequest {
  /** 活动ID */
  activityId: number;
  /** 弹窗类型 */
  type: number;
}

/**  */
export interface WindowsPackBuyResponse {
  /** 道具弹窗 */
  windowPackMessage:
    | WindowPackMessage
    | undefined;
  /** 获得的道具 */
  rewardList: number[];
  /** 购买的弹窗礼包ID */
  redeemId: number;
}

function createBaseAchieveMessage(): AchieveMessage {
  return { id: 0, targetVal: 0, paid: false, basicTakeList: [], paidTakeList: [] };
}

export const AchieveMessage: MessageFns<AchieveMessage> = {
  encode(message: AchieveMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.targetVal !== 0) {
      writer.uint32(16).int64(message.targetVal);
    }
    if (message.paid !== false) {
      writer.uint32(24).bool(message.paid);
    }
    writer.uint32(34).fork();
    for (const v of message.basicTakeList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(42).fork();
    for (const v of message.paidTakeList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AchieveMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAchieveMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.targetVal = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.paid = reader.bool();
          continue;
        }
        case 4: {
          if (tag === 32) {
            message.basicTakeList.push(reader.int32());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.basicTakeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag === 40) {
            message.paidTakeList.push(reader.int32());

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.paidTakeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AchieveMessage>, I>>(base?: I): AchieveMessage {
    return AchieveMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AchieveMessage>, I>>(object: I): AchieveMessage {
    const message = createBaseAchieveMessage();
    message.id = object.id ?? 0;
    message.targetVal = object.targetVal ?? 0;
    message.paid = object.paid ?? false;
    message.basicTakeList = object.basicTakeList?.map((e) => e) || [];
    message.paidTakeList = object.paidTakeList?.map((e) => e) || [];
    return message;
  },
};

function createBaseAchievePayResponse(): AchievePayResponse {
  return { activityId: 0, achieveId: 0 };
}

export const AchievePayResponse: MessageFns<AchievePayResponse> = {
  encode(message: AchievePayResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.achieveId !== 0) {
      writer.uint32(16).int64(message.achieveId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AchievePayResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAchievePayResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.achieveId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AchievePayResponse>, I>>(base?: I): AchievePayResponse {
    return AchievePayResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AchievePayResponse>, I>>(object: I): AchievePayResponse {
    const message = createBaseAchievePayResponse();
    message.activityId = object.activityId ?? 0;
    message.achieveId = object.achieveId ?? 0;
    return message;
  },
};

function createBaseAchieveRewardRequest(): AchieveRewardRequest {
  return { activityId: 0, achieveId: 0, index: 0, takeAll: false };
}

export const AchieveRewardRequest: MessageFns<AchieveRewardRequest> = {
  encode(message: AchieveRewardRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.achieveId !== 0) {
      writer.uint32(16).int64(message.achieveId);
    }
    if (message.index !== 0) {
      writer.uint32(24).int32(message.index);
    }
    if (message.takeAll !== false) {
      writer.uint32(32).bool(message.takeAll);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AchieveRewardRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAchieveRewardRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.achieveId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.index = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.takeAll = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AchieveRewardRequest>, I>>(base?: I): AchieveRewardRequest {
    return AchieveRewardRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AchieveRewardRequest>, I>>(object: I): AchieveRewardRequest {
    const message = createBaseAchieveRewardRequest();
    message.activityId = object.activityId ?? 0;
    message.achieveId = object.achieveId ?? 0;
    message.index = object.index ?? 0;
    message.takeAll = object.takeAll ?? false;
    return message;
  },
};

function createBaseAchieveRewardResponse(): AchieveRewardResponse {
  return { activityId: 0, achieve: undefined, rewardList: [] };
}

export const AchieveRewardResponse: MessageFns<AchieveRewardResponse> = {
  encode(message: AchieveRewardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.achieve !== undefined) {
      AchieveMessage.encode(message.achieve, writer.uint32(18).fork()).join();
    }
    writer.uint32(26).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AchieveRewardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAchieveRewardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.achieve = AchieveMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag === 25) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AchieveRewardResponse>, I>>(base?: I): AchieveRewardResponse {
    return AchieveRewardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AchieveRewardResponse>, I>>(object: I): AchieveRewardResponse {
    const message = createBaseAchieveRewardResponse();
    message.activityId = object.activityId ?? 0;
    message.achieve = (object.achieve !== undefined && object.achieve !== null)
      ? AchieveMessage.fromPartial(object.achieve)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseAchieveTargetValResponse(): AchieveTargetValResponse {
  return { activityId: 0, achieveId: 0, targetVal: 0 };
}

export const AchieveTargetValResponse: MessageFns<AchieveTargetValResponse> = {
  encode(message: AchieveTargetValResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.achieveId !== 0) {
      writer.uint32(16).int64(message.achieveId);
    }
    if (message.targetVal !== 0) {
      writer.uint32(24).int64(message.targetVal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AchieveTargetValResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAchieveTargetValResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.achieveId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.targetVal = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AchieveTargetValResponse>, I>>(base?: I): AchieveTargetValResponse {
    return AchieveTargetValResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AchieveTargetValResponse>, I>>(object: I): AchieveTargetValResponse {
    const message = createBaseAchieveTargetValResponse();
    message.activityId = object.activityId ?? 0;
    message.achieveId = object.achieveId ?? 0;
    message.targetVal = object.targetVal ?? 0;
    return message;
  },
};

function createBaseActivityDrawRequest(): ActivityDrawRequest {
  return { activityId: 0, tenth: false };
}

export const ActivityDrawRequest: MessageFns<ActivityDrawRequest> = {
  encode(message: ActivityDrawRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.tenth !== false) {
      writer.uint32(16).bool(message.tenth);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityDrawRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityDrawRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.tenth = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActivityDrawRequest>, I>>(base?: I): ActivityDrawRequest {
    return ActivityDrawRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityDrawRequest>, I>>(object: I): ActivityDrawRequest {
    const message = createBaseActivityDrawRequest();
    message.activityId = object.activityId ?? 0;
    message.tenth = object.tenth ?? false;
    return message;
  },
};

function createBaseActivityRechargeResponse(): ActivityRechargeResponse {
  return { leaderRecharge: undefined, rewardList: [] };
}

export const ActivityRechargeResponse: MessageFns<ActivityRechargeResponse> = {
  encode(message: ActivityRechargeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.leaderRecharge !== undefined) {
      LeaderRechargeMessage.encode(message.leaderRecharge, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityRechargeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityRechargeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.leaderRecharge = LeaderRechargeMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActivityRechargeResponse>, I>>(base?: I): ActivityRechargeResponse {
    return ActivityRechargeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityRechargeResponse>, I>>(object: I): ActivityRechargeResponse {
    const message = createBaseActivityRechargeResponse();
    message.leaderRecharge = (object.leaderRecharge !== undefined && object.leaderRecharge !== null)
      ? LeaderRechargeMessage.fromPartial(object.leaderRecharge)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseActivityRechargeUpdateMessage(): ActivityRechargeUpdateMessage {
  return { activityId: 0, rewardList: [], redeemId: 0 };
}

export const ActivityRechargeUpdateMessage: MessageFns<ActivityRechargeUpdateMessage> = {
  encode(message: ActivityRechargeUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    if (message.redeemId !== 0) {
      writer.uint32(24).int64(message.redeemId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityRechargeUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityRechargeUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.redeemId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActivityRechargeUpdateMessage>, I>>(base?: I): ActivityRechargeUpdateMessage {
    return ActivityRechargeUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityRechargeUpdateMessage>, I>>(
    object: I,
  ): ActivityRechargeUpdateMessage {
    const message = createBaseActivityRechargeUpdateMessage();
    message.activityId = object.activityId ?? 0;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    message.redeemId = object.redeemId ?? 0;
    return message;
  },
};

function createBaseActivityTakeRequest(): ActivityTakeRequest {
  return { activityId: 0, index: 0, takeAll: false };
}

export const ActivityTakeRequest: MessageFns<ActivityTakeRequest> = {
  encode(message: ActivityTakeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.index !== 0) {
      writer.uint32(16).int32(message.index);
    }
    if (message.takeAll !== false) {
      writer.uint32(24).bool(message.takeAll);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityTakeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityTakeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.index = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.takeAll = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActivityTakeRequest>, I>>(base?: I): ActivityTakeRequest {
    return ActivityTakeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityTakeRequest>, I>>(object: I): ActivityTakeRequest {
    const message = createBaseActivityTakeRequest();
    message.activityId = object.activityId ?? 0;
    message.index = object.index ?? 0;
    message.takeAll = object.takeAll ?? false;
    return message;
  },
};

function createBaseActivityTakeResponse(): ActivityTakeResponse {
  return { rewardList: [], takeList: [] };
}

export const ActivityTakeResponse: MessageFns<ActivityTakeResponse> = {
  encode(message: ActivityTakeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.takeList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityTakeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityTakeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.takeList.push(reader.int32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.takeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActivityTakeResponse>, I>>(base?: I): ActivityTakeResponse {
    return ActivityTakeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityTakeResponse>, I>>(object: I): ActivityTakeResponse {
    const message = createBaseActivityTakeResponse();
    message.rewardList = object.rewardList?.map((e) => e) || [];
    message.takeList = object.takeList?.map((e) => e) || [];
    return message;
  },
};

function createBaseActivityUpdateMessage(): ActivityUpdateMessage {
  return { configIdList: [], addList: [], closeList: [], totalList: [] };
}

export const ActivityUpdateMessage: MessageFns<ActivityUpdateMessage> = {
  encode(message: ActivityUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.configIdList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.addList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.closeList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.totalList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.configIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.configIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.addList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.addList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.closeList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.closeList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.totalList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.totalList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActivityUpdateMessage>, I>>(base?: I): ActivityUpdateMessage {
    return ActivityUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityUpdateMessage>, I>>(object: I): ActivityUpdateMessage {
    const message = createBaseActivityUpdateMessage();
    message.configIdList = object.configIdList?.map((e) => e) || [];
    message.addList = object.addList?.map((e) => e) || [];
    message.closeList = object.closeList?.map((e) => e) || [];
    message.totalList = object.totalList?.map((e) => e) || [];
    return message;
  },
};

function createBaseActivityUseRequest(): ActivityUseRequest {
  return { activityId: 0, isTenUse: false };
}

export const ActivityUseRequest: MessageFns<ActivityUseRequest> = {
  encode(message: ActivityUseRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.isTenUse !== false) {
      writer.uint32(16).bool(message.isTenUse);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ActivityUseRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseActivityUseRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isTenUse = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ActivityUseRequest>, I>>(base?: I): ActivityUseRequest {
    return ActivityUseRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityUseRequest>, I>>(object: I): ActivityUseRequest {
    const message = createBaseActivityUseRequest();
    message.activityId = object.activityId ?? 0;
    message.isTenUse = object.isTenUse ?? false;
    return message;
  },
};

function createBaseAdventureChooseRequest(): AdventureChooseRequest {
  return { activityId: 0, bigPrizeId: 0 };
}

export const AdventureChooseRequest: MessageFns<AdventureChooseRequest> = {
  encode(message: AdventureChooseRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.bigPrizeId !== 0) {
      writer.uint32(16).int64(message.bigPrizeId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureChooseRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureChooseRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.bigPrizeId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureChooseRequest>, I>>(base?: I): AdventureChooseRequest {
    return AdventureChooseRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureChooseRequest>, I>>(object: I): AdventureChooseRequest {
    const message = createBaseAdventureChooseRequest();
    message.activityId = object.activityId ?? 0;
    message.bigPrizeId = object.bigPrizeId ?? 0;
    return message;
  },
};

function createBaseAdventureDrawResponse(): AdventureDrawResponse {
  return { drawItemList: [], rewardList: [], bigWinCount: 0, curCostCount: 0, bigChosenMap: {} };
}

export const AdventureDrawResponse: MessageFns<AdventureDrawResponse> = {
  encode(message: AdventureDrawResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.drawItemList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    if (message.bigWinCount !== 0) {
      writer.uint32(24).int32(message.bigWinCount);
    }
    if (message.curCostCount !== 0) {
      writer.uint32(32).int32(message.curCostCount);
    }
    Object.entries(message.bigChosenMap).forEach(([key, value]) => {
      AdventureDrawResponse_BigChosenMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureDrawResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureDrawResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.drawItemList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.drawItemList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.bigWinCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.curCostCount = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = AdventureDrawResponse_BigChosenMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.bigChosenMap[entry5.key] = entry5.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureDrawResponse>, I>>(base?: I): AdventureDrawResponse {
    return AdventureDrawResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureDrawResponse>, I>>(object: I): AdventureDrawResponse {
    const message = createBaseAdventureDrawResponse();
    message.drawItemList = object.drawItemList?.map((e) => e) || [];
    message.rewardList = object.rewardList?.map((e) => e) || [];
    message.bigWinCount = object.bigWinCount ?? 0;
    message.curCostCount = object.curCostCount ?? 0;
    message.bigChosenMap = Object.entries(object.bigChosenMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseAdventureDrawResponse_BigChosenMapEntry(): AdventureDrawResponse_BigChosenMapEntry {
  return { key: 0, value: 0 };
}

export const AdventureDrawResponse_BigChosenMapEntry: MessageFns<AdventureDrawResponse_BigChosenMapEntry> = {
  encode(message: AdventureDrawResponse_BigChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureDrawResponse_BigChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureDrawResponse_BigChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureDrawResponse_BigChosenMapEntry>, I>>(
    base?: I,
  ): AdventureDrawResponse_BigChosenMapEntry {
    return AdventureDrawResponse_BigChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureDrawResponse_BigChosenMapEntry>, I>>(
    object: I,
  ): AdventureDrawResponse_BigChosenMapEntry {
    const message = createBaseAdventureDrawResponse_BigChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseAdventureMessage(): AdventureMessage {
  return {
    activityId: 0,
    achieveMap: {},
    redeemMap: {},
    adMap: {},
    limitMap: {},
    chosenMap: {},
    curBigPrizeId: 0,
    bigWinCount: 0,
    curCostCount: 0,
    bigChosenMap: {},
    achieveTakeList: [],
    taskIndexList: [],
    targetValList: [],
    taskTakeList: [],
  };
}

export const AdventureMessage: MessageFns<AdventureMessage> = {
  encode(message: AdventureMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.achieveMap).forEach(([key, value]) => {
      AdventureMessage_AchieveMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      AdventureMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      AdventureMessage_AdMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.limitMap).forEach(([key, value]) => {
      AdventureMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      AdventureMessage_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    if (message.curBigPrizeId !== 0) {
      writer.uint32(56).int64(message.curBigPrizeId);
    }
    if (message.bigWinCount !== 0) {
      writer.uint32(64).int32(message.bigWinCount);
    }
    if (message.curCostCount !== 0) {
      writer.uint32(72).int32(message.curCostCount);
    }
    Object.entries(message.bigChosenMap).forEach(([key, value]) => {
      AdventureMessage_BigChosenMapEntry.encode({ key: key as any, value }, writer.uint32(82).fork()).join();
    });
    writer.uint32(90).fork();
    for (const v of message.achieveTakeList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(98).fork();
    for (const v of message.taskIndexList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(106).fork();
    for (const v of message.targetValList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(114).fork();
    for (const v of message.taskTakeList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = AdventureMessage_AchieveMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.achieveMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = AdventureMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.redeemMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = AdventureMessage_AdMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.adMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = AdventureMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.limitMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = AdventureMessage_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.chosenMap[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.curBigPrizeId = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.bigWinCount = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.curCostCount = reader.int32();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          const entry10 = AdventureMessage_BigChosenMapEntry.decode(reader, reader.uint32());
          if (entry10.value !== undefined) {
            message.bigChosenMap[entry10.key] = entry10.value;
          }
          continue;
        }
        case 11: {
          if (tag === 88) {
            message.achieveTakeList.push(reader.int32());

            continue;
          }

          if (tag === 90) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.achieveTakeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 12: {
          if (tag === 96) {
            message.taskIndexList.push(reader.int32());

            continue;
          }

          if (tag === 98) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.taskIndexList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 13: {
          if (tag === 104) {
            message.targetValList.push(reader.int32());

            continue;
          }

          if (tag === 106) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.targetValList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 14: {
          if (tag === 112) {
            message.taskTakeList.push(reader.int32());

            continue;
          }

          if (tag === 114) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.taskTakeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureMessage>, I>>(base?: I): AdventureMessage {
    return AdventureMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureMessage>, I>>(object: I): AdventureMessage {
    const message = createBaseAdventureMessage();
    message.activityId = object.activityId ?? 0;
    message.achieveMap = Object.entries(object.achieveMap ?? {}).reduce<{ [key: number]: AchieveMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = AchieveMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.curBigPrizeId = object.curBigPrizeId ?? 0;
    message.bigWinCount = object.bigWinCount ?? 0;
    message.curCostCount = object.curCostCount ?? 0;
    message.bigChosenMap = Object.entries(object.bigChosenMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.achieveTakeList = object.achieveTakeList?.map((e) => e) || [];
    message.taskIndexList = object.taskIndexList?.map((e) => e) || [];
    message.targetValList = object.targetValList?.map((e) => e) || [];
    message.taskTakeList = object.taskTakeList?.map((e) => e) || [];
    return message;
  },
};

function createBaseAdventureMessage_AchieveMapEntry(): AdventureMessage_AchieveMapEntry {
  return { key: 0, value: undefined };
}

export const AdventureMessage_AchieveMapEntry: MessageFns<AdventureMessage_AchieveMapEntry> = {
  encode(message: AdventureMessage_AchieveMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      AchieveMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureMessage_AchieveMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureMessage_AchieveMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = AchieveMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureMessage_AchieveMapEntry>, I>>(
    base?: I,
  ): AdventureMessage_AchieveMapEntry {
    return AdventureMessage_AchieveMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureMessage_AchieveMapEntry>, I>>(
    object: I,
  ): AdventureMessage_AchieveMapEntry {
    const message = createBaseAdventureMessage_AchieveMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? AchieveMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseAdventureMessage_RedeemMapEntry(): AdventureMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const AdventureMessage_RedeemMapEntry: MessageFns<AdventureMessage_RedeemMapEntry> = {
  encode(message: AdventureMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureMessage_RedeemMapEntry>, I>>(base?: I): AdventureMessage_RedeemMapEntry {
    return AdventureMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureMessage_RedeemMapEntry>, I>>(
    object: I,
  ): AdventureMessage_RedeemMapEntry {
    const message = createBaseAdventureMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseAdventureMessage_AdMapEntry(): AdventureMessage_AdMapEntry {
  return { key: 0, value: 0 };
}

export const AdventureMessage_AdMapEntry: MessageFns<AdventureMessage_AdMapEntry> = {
  encode(message: AdventureMessage_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureMessage_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureMessage_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureMessage_AdMapEntry>, I>>(base?: I): AdventureMessage_AdMapEntry {
    return AdventureMessage_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureMessage_AdMapEntry>, I>>(object: I): AdventureMessage_AdMapEntry {
    const message = createBaseAdventureMessage_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseAdventureMessage_LimitMapEntry(): AdventureMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const AdventureMessage_LimitMapEntry: MessageFns<AdventureMessage_LimitMapEntry> = {
  encode(message: AdventureMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureMessage_LimitMapEntry>, I>>(base?: I): AdventureMessage_LimitMapEntry {
    return AdventureMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureMessage_LimitMapEntry>, I>>(
    object: I,
  ): AdventureMessage_LimitMapEntry {
    const message = createBaseAdventureMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseAdventureMessage_ChosenMapEntry(): AdventureMessage_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const AdventureMessage_ChosenMapEntry: MessageFns<AdventureMessage_ChosenMapEntry> = {
  encode(message: AdventureMessage_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureMessage_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureMessage_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureMessage_ChosenMapEntry>, I>>(base?: I): AdventureMessage_ChosenMapEntry {
    return AdventureMessage_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureMessage_ChosenMapEntry>, I>>(
    object: I,
  ): AdventureMessage_ChosenMapEntry {
    const message = createBaseAdventureMessage_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseAdventureMessage_BigChosenMapEntry(): AdventureMessage_BigChosenMapEntry {
  return { key: 0, value: 0 };
}

export const AdventureMessage_BigChosenMapEntry: MessageFns<AdventureMessage_BigChosenMapEntry> = {
  encode(message: AdventureMessage_BigChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdventureMessage_BigChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdventureMessage_BigChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AdventureMessage_BigChosenMapEntry>, I>>(
    base?: I,
  ): AdventureMessage_BigChosenMapEntry {
    return AdventureMessage_BigChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdventureMessage_BigChosenMapEntry>, I>>(
    object: I,
  ): AdventureMessage_BigChosenMapEntry {
    const message = createBaseAdventureMessage_BigChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseConsumeTaskMessage(): ConsumeTaskMessage {
  return { activityId: 0, redeemMap: {}, adMap: {}, limitMap: {}, achieveMap: {}, chosenItemId: 0, boardTake: false };
}

export const ConsumeTaskMessage: MessageFns<ConsumeTaskMessage> = {
  encode(message: ConsumeTaskMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      ConsumeTaskMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      ConsumeTaskMessage_AdMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.limitMap).forEach(([key, value]) => {
      ConsumeTaskMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.achieveMap).forEach(([key, value]) => {
      ConsumeTaskMessage_AchieveMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    if (message.chosenItemId !== 0) {
      writer.uint32(48).int64(message.chosenItemId);
    }
    if (message.boardTake !== false) {
      writer.uint32(56).bool(message.boardTake);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConsumeTaskMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConsumeTaskMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = ConsumeTaskMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.redeemMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = ConsumeTaskMessage_AdMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.adMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = ConsumeTaskMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.limitMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = ConsumeTaskMessage_AchieveMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.achieveMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.chosenItemId = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.boardTake = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ConsumeTaskMessage>, I>>(base?: I): ConsumeTaskMessage {
    return ConsumeTaskMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConsumeTaskMessage>, I>>(object: I): ConsumeTaskMessage {
    const message = createBaseConsumeTaskMessage();
    message.activityId = object.activityId ?? 0;
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.achieveMap = Object.entries(object.achieveMap ?? {}).reduce<{ [key: number]: AchieveMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = AchieveMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.chosenItemId = object.chosenItemId ?? 0;
    message.boardTake = object.boardTake ?? false;
    return message;
  },
};

function createBaseConsumeTaskMessage_RedeemMapEntry(): ConsumeTaskMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const ConsumeTaskMessage_RedeemMapEntry: MessageFns<ConsumeTaskMessage_RedeemMapEntry> = {
  encode(message: ConsumeTaskMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConsumeTaskMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConsumeTaskMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ConsumeTaskMessage_RedeemMapEntry>, I>>(
    base?: I,
  ): ConsumeTaskMessage_RedeemMapEntry {
    return ConsumeTaskMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConsumeTaskMessage_RedeemMapEntry>, I>>(
    object: I,
  ): ConsumeTaskMessage_RedeemMapEntry {
    const message = createBaseConsumeTaskMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseConsumeTaskMessage_AdMapEntry(): ConsumeTaskMessage_AdMapEntry {
  return { key: 0, value: 0 };
}

export const ConsumeTaskMessage_AdMapEntry: MessageFns<ConsumeTaskMessage_AdMapEntry> = {
  encode(message: ConsumeTaskMessage_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConsumeTaskMessage_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConsumeTaskMessage_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ConsumeTaskMessage_AdMapEntry>, I>>(base?: I): ConsumeTaskMessage_AdMapEntry {
    return ConsumeTaskMessage_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConsumeTaskMessage_AdMapEntry>, I>>(
    object: I,
  ): ConsumeTaskMessage_AdMapEntry {
    const message = createBaseConsumeTaskMessage_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseConsumeTaskMessage_LimitMapEntry(): ConsumeTaskMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const ConsumeTaskMessage_LimitMapEntry: MessageFns<ConsumeTaskMessage_LimitMapEntry> = {
  encode(message: ConsumeTaskMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConsumeTaskMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConsumeTaskMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ConsumeTaskMessage_LimitMapEntry>, I>>(
    base?: I,
  ): ConsumeTaskMessage_LimitMapEntry {
    return ConsumeTaskMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConsumeTaskMessage_LimitMapEntry>, I>>(
    object: I,
  ): ConsumeTaskMessage_LimitMapEntry {
    const message = createBaseConsumeTaskMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseConsumeTaskMessage_AchieveMapEntry(): ConsumeTaskMessage_AchieveMapEntry {
  return { key: 0, value: undefined };
}

export const ConsumeTaskMessage_AchieveMapEntry: MessageFns<ConsumeTaskMessage_AchieveMapEntry> = {
  encode(message: ConsumeTaskMessage_AchieveMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      AchieveMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConsumeTaskMessage_AchieveMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConsumeTaskMessage_AchieveMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = AchieveMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ConsumeTaskMessage_AchieveMapEntry>, I>>(
    base?: I,
  ): ConsumeTaskMessage_AchieveMapEntry {
    return ConsumeTaskMessage_AchieveMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConsumeTaskMessage_AchieveMapEntry>, I>>(
    object: I,
  ): ConsumeTaskMessage_AchieveMapEntry {
    const message = createBaseConsumeTaskMessage_AchieveMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? AchieveMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseConsumeTaskUseResponse(): ConsumeTaskUseResponse {
  return { count: 0, rewardMessage: undefined };
}

export const ConsumeTaskUseResponse: MessageFns<ConsumeTaskUseResponse> = {
  encode(message: ConsumeTaskUseResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConsumeTaskUseResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConsumeTaskUseResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ConsumeTaskUseResponse>, I>>(base?: I): ConsumeTaskUseResponse {
    return ConsumeTaskUseResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConsumeTaskUseResponse>, I>>(object: I): ConsumeTaskUseResponse {
    const message = createBaseConsumeTaskUseResponse();
    message.count = object.count ?? 0;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseDaySignMessage(): DaySignMessage {
  return { sign: false, paid: false, basicList: [], paidList: [] };
}

export const DaySignMessage: MessageFns<DaySignMessage> = {
  encode(message: DaySignMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sign !== false) {
      writer.uint32(8).bool(message.sign);
    }
    if (message.paid !== false) {
      writer.uint32(16).bool(message.paid);
    }
    writer.uint32(26).fork();
    for (const v of message.basicList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.paidList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DaySignMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDaySignMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.sign = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.paid = reader.bool();
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.basicList.push(reader.int32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.basicList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.paidList.push(reader.int32());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.paidList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DaySignMessage>, I>>(base?: I): DaySignMessage {
    return DaySignMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DaySignMessage>, I>>(object: I): DaySignMessage {
    const message = createBaseDaySignMessage();
    message.sign = object.sign ?? false;
    message.paid = object.paid ?? false;
    message.basicList = object.basicList?.map((e) => e) || [];
    message.paidList = object.paidList?.map((e) => e) || [];
    return message;
  },
};

function createBaseDayTaskMessage(): DayTaskMessage {
  return { targetValList: [], takeList: [] };
}

export const DayTaskMessage: MessageFns<DayTaskMessage> = {
  encode(message: DayTaskMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.targetValList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.takeList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DayTaskMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDayTaskMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.targetValList.push(reader.int32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.targetValList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.takeList.push(reader.int32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.takeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DayTaskMessage>, I>>(base?: I): DayTaskMessage {
    return DayTaskMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DayTaskMessage>, I>>(object: I): DayTaskMessage {
    const message = createBaseDayTaskMessage();
    message.targetValList = object.targetValList?.map((e) => e) || [];
    message.takeList = object.takeList?.map((e) => e) || [];
    return message;
  },
};

function createBaseDayTaskResponse(): DayTaskResponse {
  return { activityId: 0, targetValList: [] };
}

export const DayTaskResponse: MessageFns<DayTaskResponse> = {
  encode(message: DayTaskResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    writer.uint32(18).fork();
    for (const v of message.targetValList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DayTaskResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDayTaskResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.targetValList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.targetValList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DayTaskResponse>, I>>(base?: I): DayTaskResponse {
    return DayTaskResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DayTaskResponse>, I>>(object: I): DayTaskResponse {
    const message = createBaseDayTaskResponse();
    message.activityId = object.activityId ?? 0;
    message.targetValList = object.targetValList?.map((e) => e) || [];
    return message;
  },
};

function createBaseFinishMessage(): FinishMessage {
  return { count: 0, take: false };
}

export const FinishMessage: MessageFns<FinishMessage> = {
  encode(message: FinishMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    if (message.take !== false) {
      writer.uint32(16).bool(message.take);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FinishMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFinishMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.take = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FinishMessage>, I>>(base?: I): FinishMessage {
    return FinishMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FinishMessage>, I>>(object: I): FinishMessage {
    const message = createBaseFinishMessage();
    message.count = object.count ?? 0;
    message.take = object.take ?? false;
    return message;
  },
};

function createBaseFirstRechargeMessage(): FirstRechargeMessage {
  return { subRechargeMap: {} };
}

export const FirstRechargeMessage: MessageFns<FirstRechargeMessage> = {
  encode(message: FirstRechargeMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.subRechargeMap).forEach(([key, value]) => {
      FirstRechargeMessage_SubRechargeMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FirstRechargeMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFirstRechargeMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = FirstRechargeMessage_SubRechargeMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.subRechargeMap[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FirstRechargeMessage>, I>>(base?: I): FirstRechargeMessage {
    return FirstRechargeMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FirstRechargeMessage>, I>>(object: I): FirstRechargeMessage {
    const message = createBaseFirstRechargeMessage();
    message.subRechargeMap = Object.entries(object.subRechargeMap ?? {}).reduce<
      { [key: number]: FirstSubRechargeMessage }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = FirstSubRechargeMessage.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseFirstRechargeMessage_SubRechargeMapEntry(): FirstRechargeMessage_SubRechargeMapEntry {
  return { key: 0, value: undefined };
}

export const FirstRechargeMessage_SubRechargeMapEntry: MessageFns<FirstRechargeMessage_SubRechargeMapEntry> = {
  encode(message: FirstRechargeMessage_SubRechargeMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      FirstSubRechargeMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FirstRechargeMessage_SubRechargeMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFirstRechargeMessage_SubRechargeMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = FirstSubRechargeMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FirstRechargeMessage_SubRechargeMapEntry>, I>>(
    base?: I,
  ): FirstRechargeMessage_SubRechargeMapEntry {
    return FirstRechargeMessage_SubRechargeMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FirstRechargeMessage_SubRechargeMapEntry>, I>>(
    object: I,
  ): FirstRechargeMessage_SubRechargeMapEntry {
    const message = createBaseFirstRechargeMessage_SubRechargeMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? FirstSubRechargeMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseFirstRechargeSignRequest(): FirstRechargeSignRequest {
  return { id: 0, index: 0 };
}

export const FirstRechargeSignRequest: MessageFns<FirstRechargeSignRequest> = {
  encode(message: FirstRechargeSignRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.index !== 0) {
      writer.uint32(16).int32(message.index);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FirstRechargeSignRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFirstRechargeSignRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.index = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FirstRechargeSignRequest>, I>>(base?: I): FirstRechargeSignRequest {
    return FirstRechargeSignRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FirstRechargeSignRequest>, I>>(object: I): FirstRechargeSignRequest {
    const message = createBaseFirstRechargeSignRequest();
    message.id = object.id ?? 0;
    message.index = object.index ?? 0;
    return message;
  },
};

function createBaseFirstRechargeSignResponse(): FirstRechargeSignResponse {
  return { message: undefined, rewardList: [] };
}

export const FirstRechargeSignResponse: MessageFns<FirstRechargeSignResponse> = {
  encode(message: FirstRechargeSignResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.message !== undefined) {
      FirstSubRechargeMessage.encode(message.message, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FirstRechargeSignResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFirstRechargeSignResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.message = FirstSubRechargeMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FirstRechargeSignResponse>, I>>(base?: I): FirstRechargeSignResponse {
    return FirstRechargeSignResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FirstRechargeSignResponse>, I>>(object: I): FirstRechargeSignResponse {
    const message = createBaseFirstRechargeSignResponse();
    message.message = (object.message !== undefined && object.message !== null)
      ? FirstSubRechargeMessage.fromPartial(object.message)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseFirstSubRechargeMessage(): FirstSubRechargeMessage {
  return { id: 0, stateList: [] };
}

export const FirstSubRechargeMessage: MessageFns<FirstSubRechargeMessage> = {
  encode(message: FirstSubRechargeMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    writer.uint32(18).fork();
    for (const v of message.stateList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FirstSubRechargeMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFirstSubRechargeMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.stateList.push(reader.int32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.stateList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FirstSubRechargeMessage>, I>>(base?: I): FirstSubRechargeMessage {
    return FirstSubRechargeMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FirstSubRechargeMessage>, I>>(object: I): FirstSubRechargeMessage {
    const message = createBaseFirstSubRechargeMessage();
    message.id = object.id ?? 0;
    message.stateList = object.stateList?.map((e) => e) || [];
    return message;
  },
};

function createBaseFractureAnswerRequest(): FractureAnswerRequest {
  return { activityId: 0, answer: 0 };
}

export const FractureAnswerRequest: MessageFns<FractureAnswerRequest> = {
  encode(message: FractureAnswerRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.answer !== 0) {
      writer.uint32(16).int32(message.answer);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureAnswerRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureAnswerRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.answer = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureAnswerRequest>, I>>(base?: I): FractureAnswerRequest {
    return FractureAnswerRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureAnswerRequest>, I>>(object: I): FractureAnswerRequest {
    const message = createBaseFractureAnswerRequest();
    message.activityId = object.activityId ?? 0;
    message.answer = object.answer ?? 0;
    return message;
  },
};

function createBaseFractureDrawResponse(): FractureDrawResponse {
  return { fractureId: 0, choiceId: 0, remainFloorCountMap: {}, rewardMessage: undefined };
}

export const FractureDrawResponse: MessageFns<FractureDrawResponse> = {
  encode(message: FractureDrawResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fractureId !== 0) {
      writer.uint32(8).int64(message.fractureId);
    }
    if (message.choiceId !== 0) {
      writer.uint32(16).int64(message.choiceId);
    }
    Object.entries(message.remainFloorCountMap).forEach(([key, value]) => {
      FractureDrawResponse_RemainFloorCountMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureDrawResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureDrawResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fractureId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.choiceId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = FractureDrawResponse_RemainFloorCountMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.remainFloorCountMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureDrawResponse>, I>>(base?: I): FractureDrawResponse {
    return FractureDrawResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureDrawResponse>, I>>(object: I): FractureDrawResponse {
    const message = createBaseFractureDrawResponse();
    message.fractureId = object.fractureId ?? 0;
    message.choiceId = object.choiceId ?? 0;
    message.remainFloorCountMap = Object.entries(object.remainFloorCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseFractureDrawResponse_RemainFloorCountMapEntry(): FractureDrawResponse_RemainFloorCountMapEntry {
  return { key: 0, value: 0 };
}

export const FractureDrawResponse_RemainFloorCountMapEntry: MessageFns<FractureDrawResponse_RemainFloorCountMapEntry> =
  {
    encode(
      message: FractureDrawResponse_RemainFloorCountMapEntry,
      writer: BinaryWriter = new BinaryWriter(),
    ): BinaryWriter {
      if (message.key !== 0) {
        writer.uint32(8).int32(message.key);
      }
      if (message.value !== 0) {
        writer.uint32(16).int32(message.value);
      }
      return writer;
    },

    decode(input: BinaryReader | Uint8Array, length?: number): FractureDrawResponse_RemainFloorCountMapEntry {
      const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
      let end = length === undefined ? reader.len : reader.pos + length;
      const message = createBaseFractureDrawResponse_RemainFloorCountMapEntry();
      while (reader.pos < end) {
        const tag = reader.uint32();
        switch (tag >>> 3) {
          case 1: {
            if (tag !== 8) {
              break;
            }

            message.key = reader.int32();
            continue;
          }
          case 2: {
            if (tag !== 16) {
              break;
            }

            message.value = reader.int32();
            continue;
          }
        }
        if ((tag & 7) === 4 || tag === 0) {
          break;
        }
        reader.skip(tag & 7);
      }
      return message;
    },

    create<I extends Exact<DeepPartial<FractureDrawResponse_RemainFloorCountMapEntry>, I>>(
      base?: I,
    ): FractureDrawResponse_RemainFloorCountMapEntry {
      return FractureDrawResponse_RemainFloorCountMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<FractureDrawResponse_RemainFloorCountMapEntry>, I>>(
      object: I,
    ): FractureDrawResponse_RemainFloorCountMapEntry {
      const message = createBaseFractureDrawResponse_RemainFloorCountMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    },
  };

function createBaseFractureEventResponse(): FractureEventResponse {
  return { fractureId: 0, choiceId: 0, remainFloorCountMap: {}, rewardMessage: undefined };
}

export const FractureEventResponse: MessageFns<FractureEventResponse> = {
  encode(message: FractureEventResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fractureId !== 0) {
      writer.uint32(8).int64(message.fractureId);
    }
    if (message.choiceId !== 0) {
      writer.uint32(16).int64(message.choiceId);
    }
    Object.entries(message.remainFloorCountMap).forEach(([key, value]) => {
      FractureEventResponse_RemainFloorCountMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork())
        .join();
    });
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureEventResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureEventResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fractureId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.choiceId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = FractureEventResponse_RemainFloorCountMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.remainFloorCountMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureEventResponse>, I>>(base?: I): FractureEventResponse {
    return FractureEventResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureEventResponse>, I>>(object: I): FractureEventResponse {
    const message = createBaseFractureEventResponse();
    message.fractureId = object.fractureId ?? 0;
    message.choiceId = object.choiceId ?? 0;
    message.remainFloorCountMap = Object.entries(object.remainFloorCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseFractureEventResponse_RemainFloorCountMapEntry(): FractureEventResponse_RemainFloorCountMapEntry {
  return { key: 0, value: 0 };
}

export const FractureEventResponse_RemainFloorCountMapEntry: MessageFns<
  FractureEventResponse_RemainFloorCountMapEntry
> = {
  encode(
    message: FractureEventResponse_RemainFloorCountMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureEventResponse_RemainFloorCountMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureEventResponse_RemainFloorCountMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureEventResponse_RemainFloorCountMapEntry>, I>>(
    base?: I,
  ): FractureEventResponse_RemainFloorCountMapEntry {
    return FractureEventResponse_RemainFloorCountMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureEventResponse_RemainFloorCountMapEntry>, I>>(
    object: I,
  ): FractureEventResponse_RemainFloorCountMapEntry {
    const message = createBaseFractureEventResponse_RemainFloorCountMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureFightMessage(): FractureFightMessage {
  return { monsterId: 0, fightCount: 0, npcMessage: undefined, remainHp: 0, fightColdTime: 0 };
}

export const FractureFightMessage: MessageFns<FractureFightMessage> = {
  encode(message: FractureFightMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.monsterId !== 0) {
      writer.uint32(8).int64(message.monsterId);
    }
    if (message.fightCount !== 0) {
      writer.uint32(16).int32(message.fightCount);
    }
    if (message.npcMessage !== undefined) {
      PlayerSimpleMessage.encode(message.npcMessage, writer.uint32(26).fork()).join();
    }
    if (message.remainHp !== 0) {
      writer.uint32(33).double(message.remainHp);
    }
    if (message.fightColdTime !== 0) {
      writer.uint32(40).int64(message.fightColdTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureFightMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureFightMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.monsterId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.fightCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.npcMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.remainHp = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.fightColdTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureFightMessage>, I>>(base?: I): FractureFightMessage {
    return FractureFightMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureFightMessage>, I>>(object: I): FractureFightMessage {
    const message = createBaseFractureFightMessage();
    message.monsterId = object.monsterId ?? 0;
    message.fightCount = object.fightCount ?? 0;
    message.npcMessage = (object.npcMessage !== undefined && object.npcMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.npcMessage)
      : undefined;
    message.remainHp = object.remainHp ?? 0;
    message.fightColdTime = object.fightColdTime ?? 0;
    return message;
  },
};

function createBaseFractureFightResponse(): FractureFightResponse {
  return {
    fractureId: 0,
    choiceId: 0,
    remainFloorCountMap: {},
    win: false,
    replay: "",
    fightMessage: undefined,
    newLog: undefined,
    rewardMessage: undefined,
  };
}

export const FractureFightResponse: MessageFns<FractureFightResponse> = {
  encode(message: FractureFightResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fractureId !== 0) {
      writer.uint32(8).int64(message.fractureId);
    }
    if (message.choiceId !== 0) {
      writer.uint32(16).int64(message.choiceId);
    }
    Object.entries(message.remainFloorCountMap).forEach(([key, value]) => {
      FractureFightResponse_RemainFloorCountMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork())
        .join();
    });
    if (message.win !== false) {
      writer.uint32(32).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(42).string(message.replay);
    }
    if (message.fightMessage !== undefined) {
      FractureFightMessage.encode(message.fightMessage, writer.uint32(50).fork()).join();
    }
    if (message.newLog !== undefined) {
      FractureLogMessage.encode(message.newLog, writer.uint32(58).fork()).join();
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureFightResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureFightResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fractureId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.choiceId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = FractureFightResponse_RemainFloorCountMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.remainFloorCountMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.fightMessage = FractureFightMessage.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.newLog = FractureLogMessage.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureFightResponse>, I>>(base?: I): FractureFightResponse {
    return FractureFightResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureFightResponse>, I>>(object: I): FractureFightResponse {
    const message = createBaseFractureFightResponse();
    message.fractureId = object.fractureId ?? 0;
    message.choiceId = object.choiceId ?? 0;
    message.remainFloorCountMap = Object.entries(object.remainFloorCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    message.fightMessage = (object.fightMessage !== undefined && object.fightMessage !== null)
      ? FractureFightMessage.fromPartial(object.fightMessage)
      : undefined;
    message.newLog = (object.newLog !== undefined && object.newLog !== null)
      ? FractureLogMessage.fromPartial(object.newLog)
      : undefined;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseFractureFightResponse_RemainFloorCountMapEntry(): FractureFightResponse_RemainFloorCountMapEntry {
  return { key: 0, value: 0 };
}

export const FractureFightResponse_RemainFloorCountMapEntry: MessageFns<
  FractureFightResponse_RemainFloorCountMapEntry
> = {
  encode(
    message: FractureFightResponse_RemainFloorCountMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureFightResponse_RemainFloorCountMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureFightResponse_RemainFloorCountMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureFightResponse_RemainFloorCountMapEntry>, I>>(
    base?: I,
  ): FractureFightResponse_RemainFloorCountMapEntry {
    return FractureFightResponse_RemainFloorCountMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureFightResponse_RemainFloorCountMapEntry>, I>>(
    object: I,
  ): FractureFightResponse_RemainFloorCountMapEntry {
    const message = createBaseFractureFightResponse_RemainFloorCountMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureLogAssistResponse(): FractureLogAssistResponse {
  return { activityId: 0, logMessage: undefined, remainFloorCountMap: {}, assistUserMessage: undefined };
}

export const FractureLogAssistResponse: MessageFns<FractureLogAssistResponse> = {
  encode(message: FractureLogAssistResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.logMessage !== undefined) {
      FractureLogMessage.encode(message.logMessage, writer.uint32(18).fork()).join();
    }
    Object.entries(message.remainFloorCountMap).forEach(([key, value]) => {
      FractureLogAssistResponse_RemainFloorCountMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork())
        .join();
    });
    if (message.assistUserMessage !== undefined) {
      PlayerSimpleMessage.encode(message.assistUserMessage, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureLogAssistResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureLogAssistResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.logMessage = FractureLogMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = FractureLogAssistResponse_RemainFloorCountMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.remainFloorCountMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.assistUserMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureLogAssistResponse>, I>>(base?: I): FractureLogAssistResponse {
    return FractureLogAssistResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureLogAssistResponse>, I>>(object: I): FractureLogAssistResponse {
    const message = createBaseFractureLogAssistResponse();
    message.activityId = object.activityId ?? 0;
    message.logMessage = (object.logMessage !== undefined && object.logMessage !== null)
      ? FractureLogMessage.fromPartial(object.logMessage)
      : undefined;
    message.remainFloorCountMap = Object.entries(object.remainFloorCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.assistUserMessage = (object.assistUserMessage !== undefined && object.assistUserMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.assistUserMessage)
      : undefined;
    return message;
  },
};

function createBaseFractureLogAssistResponse_RemainFloorCountMapEntry(): FractureLogAssistResponse_RemainFloorCountMapEntry {
  return { key: 0, value: 0 };
}

export const FractureLogAssistResponse_RemainFloorCountMapEntry: MessageFns<
  FractureLogAssistResponse_RemainFloorCountMapEntry
> = {
  encode(
    message: FractureLogAssistResponse_RemainFloorCountMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureLogAssistResponse_RemainFloorCountMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureLogAssistResponse_RemainFloorCountMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureLogAssistResponse_RemainFloorCountMapEntry>, I>>(
    base?: I,
  ): FractureLogAssistResponse_RemainFloorCountMapEntry {
    return FractureLogAssistResponse_RemainFloorCountMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureLogAssistResponse_RemainFloorCountMapEntry>, I>>(
    object: I,
  ): FractureLogAssistResponse_RemainFloorCountMapEntry {
    const message = createBaseFractureLogAssistResponse_RemainFloorCountMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureLogFightResponse(): FractureLogFightResponse {
  return {
    error: false,
    fractureLogMessage: undefined,
    win: false,
    replay: "",
    remainHp: 0,
    remainFloorCountMap: {},
    assistMembers: [],
  };
}

export const FractureLogFightResponse: MessageFns<FractureLogFightResponse> = {
  encode(message: FractureLogFightResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.error !== false) {
      writer.uint32(8).bool(message.error);
    }
    if (message.fractureLogMessage !== undefined) {
      FractureLogMessage.encode(message.fractureLogMessage, writer.uint32(18).fork()).join();
    }
    if (message.win !== false) {
      writer.uint32(24).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(34).string(message.replay);
    }
    if (message.remainHp !== 0) {
      writer.uint32(41).double(message.remainHp);
    }
    Object.entries(message.remainFloorCountMap).forEach(([key, value]) => {
      FractureLogFightResponse_RemainFloorCountMapEntry.encode({ key: key as any, value }, writer.uint32(50).fork())
        .join();
    });
    for (const v of message.assistMembers) {
      PlayerBaseMessage.encode(v!, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureLogFightResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureLogFightResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.error = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fractureLogMessage = FractureLogMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.remainHp = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = FractureLogFightResponse_RemainFloorCountMapEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.remainFloorCountMap[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.assistMembers.push(PlayerBaseMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureLogFightResponse>, I>>(base?: I): FractureLogFightResponse {
    return FractureLogFightResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureLogFightResponse>, I>>(object: I): FractureLogFightResponse {
    const message = createBaseFractureLogFightResponse();
    message.error = object.error ?? false;
    message.fractureLogMessage = (object.fractureLogMessage !== undefined && object.fractureLogMessage !== null)
      ? FractureLogMessage.fromPartial(object.fractureLogMessage)
      : undefined;
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    message.remainHp = object.remainHp ?? 0;
    message.remainFloorCountMap = Object.entries(object.remainFloorCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.assistMembers = object.assistMembers?.map((e) => PlayerBaseMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFractureLogFightResponse_RemainFloorCountMapEntry(): FractureLogFightResponse_RemainFloorCountMapEntry {
  return { key: 0, value: 0 };
}

export const FractureLogFightResponse_RemainFloorCountMapEntry: MessageFns<
  FractureLogFightResponse_RemainFloorCountMapEntry
> = {
  encode(
    message: FractureLogFightResponse_RemainFloorCountMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureLogFightResponse_RemainFloorCountMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureLogFightResponse_RemainFloorCountMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureLogFightResponse_RemainFloorCountMapEntry>, I>>(
    base?: I,
  ): FractureLogFightResponse_RemainFloorCountMapEntry {
    return FractureLogFightResponse_RemainFloorCountMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureLogFightResponse_RemainFloorCountMapEntry>, I>>(
    object: I,
  ): FractureLogFightResponse_RemainFloorCountMapEntry {
    const message = createBaseFractureLogFightResponse_RemainFloorCountMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureLogMessage(): FractureLogMessage {
  return {
    id: 0,
    assist: false,
    monsterId: 0,
    fractureId: 0,
    npcMessage: undefined,
    remainHp: 0,
    take: false,
    coldTime: 0,
    helpMembers: [],
    fightCount: 0,
  };
}

export const FractureLogMessage: MessageFns<FractureLogMessage> = {
  encode(message: FractureLogMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.assist !== false) {
      writer.uint32(16).bool(message.assist);
    }
    if (message.monsterId !== 0) {
      writer.uint32(24).int64(message.monsterId);
    }
    if (message.fractureId !== 0) {
      writer.uint32(32).int64(message.fractureId);
    }
    if (message.npcMessage !== undefined) {
      PlayerSimpleMessage.encode(message.npcMessage, writer.uint32(42).fork()).join();
    }
    if (message.remainHp !== 0) {
      writer.uint32(49).double(message.remainHp);
    }
    if (message.take !== false) {
      writer.uint32(56).bool(message.take);
    }
    if (message.coldTime !== 0) {
      writer.uint32(64).int64(message.coldTime);
    }
    for (const v of message.helpMembers) {
      PlayerBaseMessage.encode(v!, writer.uint32(74).fork()).join();
    }
    if (message.fightCount !== 0) {
      writer.uint32(80).int32(message.fightCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureLogMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureLogMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.assist = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.monsterId = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.fractureId = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.npcMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.remainHp = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.take = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.coldTime = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.helpMembers.push(PlayerBaseMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.fightCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureLogMessage>, I>>(base?: I): FractureLogMessage {
    return FractureLogMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureLogMessage>, I>>(object: I): FractureLogMessage {
    const message = createBaseFractureLogMessage();
    message.id = object.id ?? 0;
    message.assist = object.assist ?? false;
    message.monsterId = object.monsterId ?? 0;
    message.fractureId = object.fractureId ?? 0;
    message.npcMessage = (object.npcMessage !== undefined && object.npcMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.npcMessage)
      : undefined;
    message.remainHp = object.remainHp ?? 0;
    message.take = object.take ?? false;
    message.coldTime = object.coldTime ?? 0;
    message.helpMembers = object.helpMembers?.map((e) => PlayerBaseMessage.fromPartial(e)) || [];
    message.fightCount = object.fightCount ?? 0;
    return message;
  },
};

function createBaseFractureLogRequest(): FractureLogRequest {
  return { activityId: 0, logId: 0 };
}

export const FractureLogRequest: MessageFns<FractureLogRequest> = {
  encode(message: FractureLogRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.logId !== 0) {
      writer.uint32(16).int64(message.logId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureLogRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureLogRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.logId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureLogRequest>, I>>(base?: I): FractureLogRequest {
    return FractureLogRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureLogRequest>, I>>(object: I): FractureLogRequest {
    const message = createBaseFractureLogRequest();
    message.activityId = object.activityId ?? 0;
    message.logId = object.logId ?? 0;
    return message;
  },
};

function createBaseFractureMessage(): FractureMessage {
  return {
    activityId: 0,
    redeemMap: {},
    adMap: {},
    limitMap: {},
    chosenMap: {},
    floorId: 0,
    fractureId: 0,
    choiceId: 0,
    roadMessage: undefined,
    fightMessage: undefined,
    remainFloorCountMap: {},
    boardTake: false,
  };
}

export const FractureMessage: MessageFns<FractureMessage> = {
  encode(message: FractureMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      FractureMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      FractureMessage_AdMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.limitMap).forEach(([key, value]) => {
      FractureMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      FractureMessage_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    if (message.floorId !== 0) {
      writer.uint32(48).int32(message.floorId);
    }
    if (message.fractureId !== 0) {
      writer.uint32(56).int64(message.fractureId);
    }
    if (message.choiceId !== 0) {
      writer.uint32(64).int64(message.choiceId);
    }
    if (message.roadMessage !== undefined) {
      FractureRoadMessage.encode(message.roadMessage, writer.uint32(74).fork()).join();
    }
    if (message.fightMessage !== undefined) {
      FractureFightMessage.encode(message.fightMessage, writer.uint32(82).fork()).join();
    }
    Object.entries(message.remainFloorCountMap).forEach(([key, value]) => {
      FractureMessage_RemainFloorCountMapEntry.encode({ key: key as any, value }, writer.uint32(90).fork()).join();
    });
    if (message.boardTake !== false) {
      writer.uint32(96).bool(message.boardTake);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = FractureMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.redeemMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = FractureMessage_AdMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.adMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = FractureMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.limitMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = FractureMessage_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.chosenMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.floorId = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.fractureId = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.choiceId = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.roadMessage = FractureRoadMessage.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.fightMessage = FractureFightMessage.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          const entry11 = FractureMessage_RemainFloorCountMapEntry.decode(reader, reader.uint32());
          if (entry11.value !== undefined) {
            message.remainFloorCountMap[entry11.key] = entry11.value;
          }
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.boardTake = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureMessage>, I>>(base?: I): FractureMessage {
    return FractureMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureMessage>, I>>(object: I): FractureMessage {
    const message = createBaseFractureMessage();
    message.activityId = object.activityId ?? 0;
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.floorId = object.floorId ?? 0;
    message.fractureId = object.fractureId ?? 0;
    message.choiceId = object.choiceId ?? 0;
    message.roadMessage = (object.roadMessage !== undefined && object.roadMessage !== null)
      ? FractureRoadMessage.fromPartial(object.roadMessage)
      : undefined;
    message.fightMessage = (object.fightMessage !== undefined && object.fightMessage !== null)
      ? FractureFightMessage.fromPartial(object.fightMessage)
      : undefined;
    message.remainFloorCountMap = Object.entries(object.remainFloorCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.boardTake = object.boardTake ?? false;
    return message;
  },
};

function createBaseFractureMessage_RedeemMapEntry(): FractureMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const FractureMessage_RedeemMapEntry: MessageFns<FractureMessage_RedeemMapEntry> = {
  encode(message: FractureMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureMessage_RedeemMapEntry>, I>>(base?: I): FractureMessage_RedeemMapEntry {
    return FractureMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureMessage_RedeemMapEntry>, I>>(
    object: I,
  ): FractureMessage_RedeemMapEntry {
    const message = createBaseFractureMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureMessage_AdMapEntry(): FractureMessage_AdMapEntry {
  return { key: 0, value: 0 };
}

export const FractureMessage_AdMapEntry: MessageFns<FractureMessage_AdMapEntry> = {
  encode(message: FractureMessage_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureMessage_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureMessage_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureMessage_AdMapEntry>, I>>(base?: I): FractureMessage_AdMapEntry {
    return FractureMessage_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureMessage_AdMapEntry>, I>>(object: I): FractureMessage_AdMapEntry {
    const message = createBaseFractureMessage_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureMessage_LimitMapEntry(): FractureMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const FractureMessage_LimitMapEntry: MessageFns<FractureMessage_LimitMapEntry> = {
  encode(message: FractureMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureMessage_LimitMapEntry>, I>>(base?: I): FractureMessage_LimitMapEntry {
    return FractureMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureMessage_LimitMapEntry>, I>>(
    object: I,
  ): FractureMessage_LimitMapEntry {
    const message = createBaseFractureMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureMessage_ChosenMapEntry(): FractureMessage_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const FractureMessage_ChosenMapEntry: MessageFns<FractureMessage_ChosenMapEntry> = {
  encode(message: FractureMessage_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureMessage_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureMessage_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureMessage_ChosenMapEntry>, I>>(base?: I): FractureMessage_ChosenMapEntry {
    return FractureMessage_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureMessage_ChosenMapEntry>, I>>(
    object: I,
  ): FractureMessage_ChosenMapEntry {
    const message = createBaseFractureMessage_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseFractureMessage_RemainFloorCountMapEntry(): FractureMessage_RemainFloorCountMapEntry {
  return { key: 0, value: 0 };
}

export const FractureMessage_RemainFloorCountMapEntry: MessageFns<FractureMessage_RemainFloorCountMapEntry> = {
  encode(message: FractureMessage_RemainFloorCountMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureMessage_RemainFloorCountMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureMessage_RemainFloorCountMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureMessage_RemainFloorCountMapEntry>, I>>(
    base?: I,
  ): FractureMessage_RemainFloorCountMapEntry {
    return FractureMessage_RemainFloorCountMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureMessage_RemainFloorCountMapEntry>, I>>(
    object: I,
  ): FractureMessage_RemainFloorCountMapEntry {
    const message = createBaseFractureMessage_RemainFloorCountMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureRoadAssistResponse(): FractureRoadAssistResponse {
  return { activityId: 0, assistCount: 0, assistUserMessage: undefined };
}

export const FractureRoadAssistResponse: MessageFns<FractureRoadAssistResponse> = {
  encode(message: FractureRoadAssistResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.assistCount !== 0) {
      writer.uint32(16).int32(message.assistCount);
    }
    if (message.assistUserMessage !== undefined) {
      PlayerSimpleMessage.encode(message.assistUserMessage, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureRoadAssistResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureRoadAssistResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.assistCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.assistUserMessage = PlayerSimpleMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureRoadAssistResponse>, I>>(base?: I): FractureRoadAssistResponse {
    return FractureRoadAssistResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureRoadAssistResponse>, I>>(object: I): FractureRoadAssistResponse {
    const message = createBaseFractureRoadAssistResponse();
    message.activityId = object.activityId ?? 0;
    message.assistCount = object.assistCount ?? 0;
    message.assistUserMessage = (object.assistUserMessage !== undefined && object.assistUserMessage !== null)
      ? PlayerSimpleMessage.fromPartial(object.assistUserMessage)
      : undefined;
    return message;
  },
};

function createBaseFractureRoadMessage(): FractureRoadMessage {
  return { assist: false, assistCount: 0, roadDeadline: 0 };
}

export const FractureRoadMessage: MessageFns<FractureRoadMessage> = {
  encode(message: FractureRoadMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.assist !== false) {
      writer.uint32(8).bool(message.assist);
    }
    if (message.assistCount !== 0) {
      writer.uint32(16).int32(message.assistCount);
    }
    if (message.roadDeadline !== 0) {
      writer.uint32(24).int64(message.roadDeadline);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureRoadMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureRoadMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.assist = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.assistCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.roadDeadline = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureRoadMessage>, I>>(base?: I): FractureRoadMessage {
    return FractureRoadMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureRoadMessage>, I>>(object: I): FractureRoadMessage {
    const message = createBaseFractureRoadMessage();
    message.assist = object.assist ?? false;
    message.assistCount = object.assistCount ?? 0;
    message.roadDeadline = object.roadDeadline ?? 0;
    return message;
  },
};

function createBaseFractureSearchResponse(): FractureSearchResponse {
  return {
    fractureId: 0,
    choiceId: 0,
    remainFloorCountMap: {},
    roadMessage: undefined,
    fightMessage: undefined,
    rewardMessage: undefined,
  };
}

export const FractureSearchResponse: MessageFns<FractureSearchResponse> = {
  encode(message: FractureSearchResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fractureId !== 0) {
      writer.uint32(8).int64(message.fractureId);
    }
    if (message.choiceId !== 0) {
      writer.uint32(16).int64(message.choiceId);
    }
    Object.entries(message.remainFloorCountMap).forEach(([key, value]) => {
      FractureSearchResponse_RemainFloorCountMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork())
        .join();
    });
    if (message.roadMessage !== undefined) {
      FractureRoadMessage.encode(message.roadMessage, writer.uint32(34).fork()).join();
    }
    if (message.fightMessage !== undefined) {
      FractureFightMessage.encode(message.fightMessage, writer.uint32(42).fork()).join();
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureSearchResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureSearchResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fractureId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.choiceId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = FractureSearchResponse_RemainFloorCountMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.remainFloorCountMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.roadMessage = FractureRoadMessage.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.fightMessage = FractureFightMessage.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureSearchResponse>, I>>(base?: I): FractureSearchResponse {
    return FractureSearchResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureSearchResponse>, I>>(object: I): FractureSearchResponse {
    const message = createBaseFractureSearchResponse();
    message.fractureId = object.fractureId ?? 0;
    message.choiceId = object.choiceId ?? 0;
    message.remainFloorCountMap = Object.entries(object.remainFloorCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.roadMessage = (object.roadMessage !== undefined && object.roadMessage !== null)
      ? FractureRoadMessage.fromPartial(object.roadMessage)
      : undefined;
    message.fightMessage = (object.fightMessage !== undefined && object.fightMessage !== null)
      ? FractureFightMessage.fromPartial(object.fightMessage)
      : undefined;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseFractureSearchResponse_RemainFloorCountMapEntry(): FractureSearchResponse_RemainFloorCountMapEntry {
  return { key: 0, value: 0 };
}

export const FractureSearchResponse_RemainFloorCountMapEntry: MessageFns<
  FractureSearchResponse_RemainFloorCountMapEntry
> = {
  encode(
    message: FractureSearchResponse_RemainFloorCountMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureSearchResponse_RemainFloorCountMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureSearchResponse_RemainFloorCountMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureSearchResponse_RemainFloorCountMapEntry>, I>>(
    base?: I,
  ): FractureSearchResponse_RemainFloorCountMapEntry {
    return FractureSearchResponse_RemainFloorCountMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureSearchResponse_RemainFloorCountMapEntry>, I>>(
    object: I,
  ): FractureSearchResponse_RemainFloorCountMapEntry {
    const message = createBaseFractureSearchResponse_RemainFloorCountMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseFractureSkipResponse(): FractureSkipResponse {
  return { fractureId: 0, choiceId: 0, newLog: undefined };
}

export const FractureSkipResponse: MessageFns<FractureSkipResponse> = {
  encode(message: FractureSkipResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fractureId !== 0) {
      writer.uint32(8).int64(message.fractureId);
    }
    if (message.choiceId !== 0) {
      writer.uint32(16).int64(message.choiceId);
    }
    if (message.newLog !== undefined) {
      FractureLogMessage.encode(message.newLog, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureSkipResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureSkipResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fractureId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.choiceId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.newLog = FractureLogMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureSkipResponse>, I>>(base?: I): FractureSkipResponse {
    return FractureSkipResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureSkipResponse>, I>>(object: I): FractureSkipResponse {
    const message = createBaseFractureSkipResponse();
    message.fractureId = object.fractureId ?? 0;
    message.choiceId = object.choiceId ?? 0;
    message.newLog = (object.newLog !== undefined && object.newLog !== null)
      ? FractureLogMessage.fromPartial(object.newLog)
      : undefined;
    return message;
  },
};

function createBaseFractureTrapResponse(): FractureTrapResponse {
  return { fractureId: 0, choiceId: 0, remainFloorCountMap: {}, roadMessage: undefined, rewardMessage: undefined };
}

export const FractureTrapResponse: MessageFns<FractureTrapResponse> = {
  encode(message: FractureTrapResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fractureId !== 0) {
      writer.uint32(8).int64(message.fractureId);
    }
    if (message.choiceId !== 0) {
      writer.uint32(16).int64(message.choiceId);
    }
    Object.entries(message.remainFloorCountMap).forEach(([key, value]) => {
      FractureTrapResponse_RemainFloorCountMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    if (message.roadMessage !== undefined) {
      FractureRoadMessage.encode(message.roadMessage, writer.uint32(34).fork()).join();
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureTrapResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureTrapResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fractureId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.choiceId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = FractureTrapResponse_RemainFloorCountMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.remainFloorCountMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.roadMessage = FractureRoadMessage.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureTrapResponse>, I>>(base?: I): FractureTrapResponse {
    return FractureTrapResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureTrapResponse>, I>>(object: I): FractureTrapResponse {
    const message = createBaseFractureTrapResponse();
    message.fractureId = object.fractureId ?? 0;
    message.choiceId = object.choiceId ?? 0;
    message.remainFloorCountMap = Object.entries(object.remainFloorCountMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.roadMessage = (object.roadMessage !== undefined && object.roadMessage !== null)
      ? FractureRoadMessage.fromPartial(object.roadMessage)
      : undefined;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseFractureTrapResponse_RemainFloorCountMapEntry(): FractureTrapResponse_RemainFloorCountMapEntry {
  return { key: 0, value: 0 };
}

export const FractureTrapResponse_RemainFloorCountMapEntry: MessageFns<FractureTrapResponse_RemainFloorCountMapEntry> =
  {
    encode(
      message: FractureTrapResponse_RemainFloorCountMapEntry,
      writer: BinaryWriter = new BinaryWriter(),
    ): BinaryWriter {
      if (message.key !== 0) {
        writer.uint32(8).int32(message.key);
      }
      if (message.value !== 0) {
        writer.uint32(16).int32(message.value);
      }
      return writer;
    },

    decode(input: BinaryReader | Uint8Array, length?: number): FractureTrapResponse_RemainFloorCountMapEntry {
      const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
      let end = length === undefined ? reader.len : reader.pos + length;
      const message = createBaseFractureTrapResponse_RemainFloorCountMapEntry();
      while (reader.pos < end) {
        const tag = reader.uint32();
        switch (tag >>> 3) {
          case 1: {
            if (tag !== 8) {
              break;
            }

            message.key = reader.int32();
            continue;
          }
          case 2: {
            if (tag !== 16) {
              break;
            }

            message.value = reader.int32();
            continue;
          }
        }
        if ((tag & 7) === 4 || tag === 0) {
          break;
        }
        reader.skip(tag & 7);
      }
      return message;
    },

    create<I extends Exact<DeepPartial<FractureTrapResponse_RemainFloorCountMapEntry>, I>>(
      base?: I,
    ): FractureTrapResponse_RemainFloorCountMapEntry {
      return FractureTrapResponse_RemainFloorCountMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<FractureTrapResponse_RemainFloorCountMapEntry>, I>>(
      object: I,
    ): FractureTrapResponse_RemainFloorCountMapEntry {
      const message = createBaseFractureTrapResponse_RemainFloorCountMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    },
  };

function createBaseFractureTravelRequest(): FractureTravelRequest {
  return { activityId: 0, floorId: 0 };
}

export const FractureTravelRequest: MessageFns<FractureTravelRequest> = {
  encode(message: FractureTravelRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.floorId !== 0) {
      writer.uint32(16).int32(message.floorId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FractureTravelRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFractureTravelRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.floorId = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FractureTravelRequest>, I>>(base?: I): FractureTravelRequest {
    return FractureTravelRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FractureTravelRequest>, I>>(object: I): FractureTravelRequest {
    const message = createBaseFractureTravelRequest();
    message.activityId = object.activityId ?? 0;
    message.floorId = object.floorId ?? 0;
    return message;
  },
};

function createBaseFundMessage(): FundMessage {
  return { activityId: 0, achieveMap: {} };
}

export const FundMessage: MessageFns<FundMessage> = {
  encode(message: FundMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.achieveMap).forEach(([key, value]) => {
      FundMessage_AchieveMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FundMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFundMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = FundMessage_AchieveMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.achieveMap[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FundMessage>, I>>(base?: I): FundMessage {
    return FundMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FundMessage>, I>>(object: I): FundMessage {
    const message = createBaseFundMessage();
    message.activityId = object.activityId ?? 0;
    message.achieveMap = Object.entries(object.achieveMap ?? {}).reduce<{ [key: number]: AchieveMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = AchieveMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseFundMessage_AchieveMapEntry(): FundMessage_AchieveMapEntry {
  return { key: 0, value: undefined };
}

export const FundMessage_AchieveMapEntry: MessageFns<FundMessage_AchieveMapEntry> = {
  encode(message: FundMessage_AchieveMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      AchieveMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FundMessage_AchieveMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFundMessage_AchieveMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = AchieveMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<FundMessage_AchieveMapEntry>, I>>(base?: I): FundMessage_AchieveMapEntry {
    return FundMessage_AchieveMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FundMessage_AchieveMapEntry>, I>>(object: I): FundMessage_AchieveMapEntry {
    const message = createBaseFundMessage_AchieveMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? AchieveMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseLeaderFundMessage(): LeaderFundMessage {
  return {
    activityId: 0,
    redeemMap: {},
    adMap: {},
    limitMap: {},
    chosenMap: {},
    achieveMap: {},
    daySign: undefined,
    dayTask: undefined,
    leaderRecharge: undefined,
  };
}

export const LeaderFundMessage: MessageFns<LeaderFundMessage> = {
  encode(message: LeaderFundMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      LeaderFundMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      LeaderFundMessage_AdMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.limitMap).forEach(([key, value]) => {
      LeaderFundMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      LeaderFundMessage_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    Object.entries(message.achieveMap).forEach(([key, value]) => {
      LeaderFundMessage_AchieveMapEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    if (message.daySign !== undefined) {
      DaySignMessage.encode(message.daySign, writer.uint32(58).fork()).join();
    }
    if (message.dayTask !== undefined) {
      DayTaskMessage.encode(message.dayTask, writer.uint32(66).fork()).join();
    }
    if (message.leaderRecharge !== undefined) {
      LeaderRechargeMessage.encode(message.leaderRecharge, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderFundMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderFundMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = LeaderFundMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.redeemMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = LeaderFundMessage_AdMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.adMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = LeaderFundMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.limitMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = LeaderFundMessage_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.chosenMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = LeaderFundMessage_AchieveMapEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.achieveMap[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.daySign = DaySignMessage.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.dayTask = DayTaskMessage.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.leaderRecharge = LeaderRechargeMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderFundMessage>, I>>(base?: I): LeaderFundMessage {
    return LeaderFundMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderFundMessage>, I>>(object: I): LeaderFundMessage {
    const message = createBaseLeaderFundMessage();
    message.activityId = object.activityId ?? 0;
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.achieveMap = Object.entries(object.achieveMap ?? {}).reduce<{ [key: number]: AchieveMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = AchieveMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.daySign = (object.daySign !== undefined && object.daySign !== null)
      ? DaySignMessage.fromPartial(object.daySign)
      : undefined;
    message.dayTask = (object.dayTask !== undefined && object.dayTask !== null)
      ? DayTaskMessage.fromPartial(object.dayTask)
      : undefined;
    message.leaderRecharge = (object.leaderRecharge !== undefined && object.leaderRecharge !== null)
      ? LeaderRechargeMessage.fromPartial(object.leaderRecharge)
      : undefined;
    return message;
  },
};

function createBaseLeaderFundMessage_RedeemMapEntry(): LeaderFundMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const LeaderFundMessage_RedeemMapEntry: MessageFns<LeaderFundMessage_RedeemMapEntry> = {
  encode(message: LeaderFundMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderFundMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderFundMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderFundMessage_RedeemMapEntry>, I>>(
    base?: I,
  ): LeaderFundMessage_RedeemMapEntry {
    return LeaderFundMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderFundMessage_RedeemMapEntry>, I>>(
    object: I,
  ): LeaderFundMessage_RedeemMapEntry {
    const message = createBaseLeaderFundMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseLeaderFundMessage_AdMapEntry(): LeaderFundMessage_AdMapEntry {
  return { key: 0, value: 0 };
}

export const LeaderFundMessage_AdMapEntry: MessageFns<LeaderFundMessage_AdMapEntry> = {
  encode(message: LeaderFundMessage_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderFundMessage_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderFundMessage_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderFundMessage_AdMapEntry>, I>>(base?: I): LeaderFundMessage_AdMapEntry {
    return LeaderFundMessage_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderFundMessage_AdMapEntry>, I>>(object: I): LeaderFundMessage_AdMapEntry {
    const message = createBaseLeaderFundMessage_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseLeaderFundMessage_LimitMapEntry(): LeaderFundMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const LeaderFundMessage_LimitMapEntry: MessageFns<LeaderFundMessage_LimitMapEntry> = {
  encode(message: LeaderFundMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderFundMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderFundMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderFundMessage_LimitMapEntry>, I>>(base?: I): LeaderFundMessage_LimitMapEntry {
    return LeaderFundMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderFundMessage_LimitMapEntry>, I>>(
    object: I,
  ): LeaderFundMessage_LimitMapEntry {
    const message = createBaseLeaderFundMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseLeaderFundMessage_ChosenMapEntry(): LeaderFundMessage_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const LeaderFundMessage_ChosenMapEntry: MessageFns<LeaderFundMessage_ChosenMapEntry> = {
  encode(message: LeaderFundMessage_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderFundMessage_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderFundMessage_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderFundMessage_ChosenMapEntry>, I>>(
    base?: I,
  ): LeaderFundMessage_ChosenMapEntry {
    return LeaderFundMessage_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderFundMessage_ChosenMapEntry>, I>>(
    object: I,
  ): LeaderFundMessage_ChosenMapEntry {
    const message = createBaseLeaderFundMessage_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseLeaderFundMessage_AchieveMapEntry(): LeaderFundMessage_AchieveMapEntry {
  return { key: 0, value: undefined };
}

export const LeaderFundMessage_AchieveMapEntry: MessageFns<LeaderFundMessage_AchieveMapEntry> = {
  encode(message: LeaderFundMessage_AchieveMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      AchieveMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderFundMessage_AchieveMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderFundMessage_AchieveMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = AchieveMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderFundMessage_AchieveMapEntry>, I>>(
    base?: I,
  ): LeaderFundMessage_AchieveMapEntry {
    return LeaderFundMessage_AchieveMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderFundMessage_AchieveMapEntry>, I>>(
    object: I,
  ): LeaderFundMessage_AchieveMapEntry {
    const message = createBaseLeaderFundMessage_AchieveMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? AchieveMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseLeaderFundRechargeResponse(): LeaderFundRechargeResponse {
  return { activityId: 0, numerator: 0 };
}

export const LeaderFundRechargeResponse: MessageFns<LeaderFundRechargeResponse> = {
  encode(message: LeaderFundRechargeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.numerator !== 0) {
      writer.uint32(16).int32(message.numerator);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderFundRechargeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderFundRechargeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.numerator = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderFundRechargeResponse>, I>>(base?: I): LeaderFundRechargeResponse {
    return LeaderFundRechargeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderFundRechargeResponse>, I>>(object: I): LeaderFundRechargeResponse {
    const message = createBaseLeaderFundRechargeResponse();
    message.activityId = object.activityId ?? 0;
    message.numerator = object.numerator ?? 0;
    return message;
  },
};

function createBaseLeaderRechargeMessage(): LeaderRechargeMessage {
  return { numerator: 0, denominator: 0, take: false };
}

export const LeaderRechargeMessage: MessageFns<LeaderRechargeMessage> = {
  encode(message: LeaderRechargeMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.numerator !== 0) {
      writer.uint32(8).int32(message.numerator);
    }
    if (message.denominator !== 0) {
      writer.uint32(16).int32(message.denominator);
    }
    if (message.take !== false) {
      writer.uint32(24).bool(message.take);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderRechargeMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderRechargeMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.numerator = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.denominator = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.take = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderRechargeMessage>, I>>(base?: I): LeaderRechargeMessage {
    return LeaderRechargeMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderRechargeMessage>, I>>(object: I): LeaderRechargeMessage {
    const message = createBaseLeaderRechargeMessage();
    message.numerator = object.numerator ?? 0;
    message.denominator = object.denominator ?? 0;
    message.take = object.take ?? false;
    return message;
  },
};

function createBaseLeaderSignRequest(): LeaderSignRequest {
  return { activityId: 0, index: 0 };
}

export const LeaderSignRequest: MessageFns<LeaderSignRequest> = {
  encode(message: LeaderSignRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.index !== 0) {
      writer.uint32(16).int32(message.index);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderSignRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderSignRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.index = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderSignRequest>, I>>(base?: I): LeaderSignRequest {
    return LeaderSignRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderSignRequest>, I>>(object: I): LeaderSignRequest {
    const message = createBaseLeaderSignRequest();
    message.activityId = object.activityId ?? 0;
    message.index = object.index ?? 0;
    return message;
  },
};

function createBaseLeaderSignResponse(): LeaderSignResponse {
  return { sign: false, basicList: [], paidList: [], rewardList: [] };
}

export const LeaderSignResponse: MessageFns<LeaderSignResponse> = {
  encode(message: LeaderSignResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sign !== false) {
      writer.uint32(8).bool(message.sign);
    }
    writer.uint32(18).fork();
    for (const v of message.basicList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.paidList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LeaderSignResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLeaderSignResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.sign = reader.bool();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.basicList.push(reader.int32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.basicList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.paidList.push(reader.int32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.paidList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 33) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LeaderSignResponse>, I>>(base?: I): LeaderSignResponse {
    return LeaderSignResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaderSignResponse>, I>>(object: I): LeaderSignResponse {
    const message = createBaseLeaderSignResponse();
    message.sign = object.sign ?? false;
    message.basicList = object.basicList?.map((e) => e) || [];
    message.paidList = object.paidList?.map((e) => e) || [];
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseLifeCardBuyResponse(): LifeCardBuyResponse {
  return { vipCardMessage: undefined, rewardList: [] };
}

export const LifeCardBuyResponse: MessageFns<LifeCardBuyResponse> = {
  encode(message: LifeCardBuyResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.vipCardMessage !== undefined) {
      VipCardMessage.encode(message.vipCardMessage, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LifeCardBuyResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLifeCardBuyResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.vipCardMessage = VipCardMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LifeCardBuyResponse>, I>>(base?: I): LifeCardBuyResponse {
    return LifeCardBuyResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LifeCardBuyResponse>, I>>(object: I): LifeCardBuyResponse {
    const message = createBaseLifeCardBuyResponse();
    message.vipCardMessage = (object.vipCardMessage !== undefined && object.vipCardMessage !== null)
      ? VipCardMessage.fromPartial(object.vipCardMessage)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseMonthCardBuyResponse(): MonthCardBuyResponse {
  return { vipCardMessage: undefined, rewardList: [] };
}

export const MonthCardBuyResponse: MessageFns<MonthCardBuyResponse> = {
  encode(message: MonthCardBuyResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.vipCardMessage !== undefined) {
      VipCardMessage.encode(message.vipCardMessage, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MonthCardBuyResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMonthCardBuyResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.vipCardMessage = VipCardMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<MonthCardBuyResponse>, I>>(base?: I): MonthCardBuyResponse {
    return MonthCardBuyResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MonthCardBuyResponse>, I>>(object: I): MonthCardBuyResponse {
    const message = createBaseMonthCardBuyResponse();
    message.vipCardMessage = (object.vipCardMessage !== undefined && object.vipCardMessage !== null)
      ? VipCardMessage.fromPartial(object.vipCardMessage)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBasePrayerDrawResponse(): PrayerDrawResponse {
  return { drawItemList: [], rewardList: [] };
}

export const PrayerDrawResponse: MessageFns<PrayerDrawResponse> = {
  encode(message: PrayerDrawResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.drawItemList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PrayerDrawResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePrayerDrawResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.drawItemList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.drawItemList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PrayerDrawResponse>, I>>(base?: I): PrayerDrawResponse {
    return PrayerDrawResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrayerDrawResponse>, I>>(object: I): PrayerDrawResponse {
    const message = createBasePrayerDrawResponse();
    message.drawItemList = object.drawItemList?.map((e) => e) || [];
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBasePrayerMessage(): PrayerMessage {
  return {
    activityId: 0,
    achieveMap: {},
    redeemMap: {},
    adMap: {},
    limitMap: {},
    chosenMap: {},
    taskIndexList: [],
    targetValList: [],
    taskTakeList: [],
  };
}

export const PrayerMessage: MessageFns<PrayerMessage> = {
  encode(message: PrayerMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.achieveMap).forEach(([key, value]) => {
      PrayerMessage_AchieveMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      PrayerMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      PrayerMessage_AdMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.limitMap).forEach(([key, value]) => {
      PrayerMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      PrayerMessage_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    writer.uint32(58).fork();
    for (const v of message.taskIndexList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(66).fork();
    for (const v of message.targetValList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(74).fork();
    for (const v of message.taskTakeList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PrayerMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePrayerMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = PrayerMessage_AchieveMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.achieveMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = PrayerMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.redeemMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = PrayerMessage_AdMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.adMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = PrayerMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.limitMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = PrayerMessage_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.chosenMap[entry6.key] = entry6.value;
          }
          continue;
        }
        case 7: {
          if (tag === 56) {
            message.taskIndexList.push(reader.int32());

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.taskIndexList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag === 64) {
            message.targetValList.push(reader.int32());

            continue;
          }

          if (tag === 66) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.targetValList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 9: {
          if (tag === 72) {
            message.taskTakeList.push(reader.int32());

            continue;
          }

          if (tag === 74) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.taskTakeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PrayerMessage>, I>>(base?: I): PrayerMessage {
    return PrayerMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrayerMessage>, I>>(object: I): PrayerMessage {
    const message = createBasePrayerMessage();
    message.activityId = object.activityId ?? 0;
    message.achieveMap = Object.entries(object.achieveMap ?? {}).reduce<{ [key: number]: AchieveMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = AchieveMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.taskIndexList = object.taskIndexList?.map((e) => e) || [];
    message.targetValList = object.targetValList?.map((e) => e) || [];
    message.taskTakeList = object.taskTakeList?.map((e) => e) || [];
    return message;
  },
};

function createBasePrayerMessage_AchieveMapEntry(): PrayerMessage_AchieveMapEntry {
  return { key: 0, value: undefined };
}

export const PrayerMessage_AchieveMapEntry: MessageFns<PrayerMessage_AchieveMapEntry> = {
  encode(message: PrayerMessage_AchieveMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      AchieveMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PrayerMessage_AchieveMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePrayerMessage_AchieveMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = AchieveMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PrayerMessage_AchieveMapEntry>, I>>(base?: I): PrayerMessage_AchieveMapEntry {
    return PrayerMessage_AchieveMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrayerMessage_AchieveMapEntry>, I>>(
    object: I,
  ): PrayerMessage_AchieveMapEntry {
    const message = createBasePrayerMessage_AchieveMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? AchieveMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBasePrayerMessage_RedeemMapEntry(): PrayerMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const PrayerMessage_RedeemMapEntry: MessageFns<PrayerMessage_RedeemMapEntry> = {
  encode(message: PrayerMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PrayerMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePrayerMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PrayerMessage_RedeemMapEntry>, I>>(base?: I): PrayerMessage_RedeemMapEntry {
    return PrayerMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrayerMessage_RedeemMapEntry>, I>>(object: I): PrayerMessage_RedeemMapEntry {
    const message = createBasePrayerMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePrayerMessage_AdMapEntry(): PrayerMessage_AdMapEntry {
  return { key: 0, value: 0 };
}

export const PrayerMessage_AdMapEntry: MessageFns<PrayerMessage_AdMapEntry> = {
  encode(message: PrayerMessage_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PrayerMessage_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePrayerMessage_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PrayerMessage_AdMapEntry>, I>>(base?: I): PrayerMessage_AdMapEntry {
    return PrayerMessage_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrayerMessage_AdMapEntry>, I>>(object: I): PrayerMessage_AdMapEntry {
    const message = createBasePrayerMessage_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePrayerMessage_LimitMapEntry(): PrayerMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const PrayerMessage_LimitMapEntry: MessageFns<PrayerMessage_LimitMapEntry> = {
  encode(message: PrayerMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PrayerMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePrayerMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PrayerMessage_LimitMapEntry>, I>>(base?: I): PrayerMessage_LimitMapEntry {
    return PrayerMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrayerMessage_LimitMapEntry>, I>>(object: I): PrayerMessage_LimitMapEntry {
    const message = createBasePrayerMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBasePrayerMessage_ChosenMapEntry(): PrayerMessage_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const PrayerMessage_ChosenMapEntry: MessageFns<PrayerMessage_ChosenMapEntry> = {
  encode(message: PrayerMessage_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PrayerMessage_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePrayerMessage_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PrayerMessage_ChosenMapEntry>, I>>(base?: I): PrayerMessage_ChosenMapEntry {
    return PrayerMessage_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrayerMessage_ChosenMapEntry>, I>>(object: I): PrayerMessage_ChosenMapEntry {
    const message = createBasePrayerMessage_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseProsperityMessage(): ProsperityMessage {
  return { activityId: 0, redeemMap: {}, adMap: {}, limitMap: {}, chosenMap: {}, achieveMap: {} };
}

export const ProsperityMessage: MessageFns<ProsperityMessage> = {
  encode(message: ProsperityMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      ProsperityMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      ProsperityMessage_AdMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.limitMap).forEach(([key, value]) => {
      ProsperityMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      ProsperityMessage_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    Object.entries(message.achieveMap).forEach(([key, value]) => {
      ProsperityMessage_AchieveMapEntry.encode({ key: key as any, value }, writer.uint32(50).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProsperityMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProsperityMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = ProsperityMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.redeemMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = ProsperityMessage_AdMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.adMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = ProsperityMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.limitMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = ProsperityMessage_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.chosenMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          const entry6 = ProsperityMessage_AchieveMapEntry.decode(reader, reader.uint32());
          if (entry6.value !== undefined) {
            message.achieveMap[entry6.key] = entry6.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ProsperityMessage>, I>>(base?: I): ProsperityMessage {
    return ProsperityMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProsperityMessage>, I>>(object: I): ProsperityMessage {
    const message = createBaseProsperityMessage();
    message.activityId = object.activityId ?? 0;
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.achieveMap = Object.entries(object.achieveMap ?? {}).reduce<{ [key: number]: AchieveMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = AchieveMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseProsperityMessage_RedeemMapEntry(): ProsperityMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const ProsperityMessage_RedeemMapEntry: MessageFns<ProsperityMessage_RedeemMapEntry> = {
  encode(message: ProsperityMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProsperityMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProsperityMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ProsperityMessage_RedeemMapEntry>, I>>(
    base?: I,
  ): ProsperityMessage_RedeemMapEntry {
    return ProsperityMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProsperityMessage_RedeemMapEntry>, I>>(
    object: I,
  ): ProsperityMessage_RedeemMapEntry {
    const message = createBaseProsperityMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseProsperityMessage_AdMapEntry(): ProsperityMessage_AdMapEntry {
  return { key: 0, value: 0 };
}

export const ProsperityMessage_AdMapEntry: MessageFns<ProsperityMessage_AdMapEntry> = {
  encode(message: ProsperityMessage_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProsperityMessage_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProsperityMessage_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ProsperityMessage_AdMapEntry>, I>>(base?: I): ProsperityMessage_AdMapEntry {
    return ProsperityMessage_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProsperityMessage_AdMapEntry>, I>>(object: I): ProsperityMessage_AdMapEntry {
    const message = createBaseProsperityMessage_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseProsperityMessage_LimitMapEntry(): ProsperityMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const ProsperityMessage_LimitMapEntry: MessageFns<ProsperityMessage_LimitMapEntry> = {
  encode(message: ProsperityMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProsperityMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProsperityMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ProsperityMessage_LimitMapEntry>, I>>(base?: I): ProsperityMessage_LimitMapEntry {
    return ProsperityMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProsperityMessage_LimitMapEntry>, I>>(
    object: I,
  ): ProsperityMessage_LimitMapEntry {
    const message = createBaseProsperityMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseProsperityMessage_ChosenMapEntry(): ProsperityMessage_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const ProsperityMessage_ChosenMapEntry: MessageFns<ProsperityMessage_ChosenMapEntry> = {
  encode(message: ProsperityMessage_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProsperityMessage_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProsperityMessage_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ProsperityMessage_ChosenMapEntry>, I>>(
    base?: I,
  ): ProsperityMessage_ChosenMapEntry {
    return ProsperityMessage_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProsperityMessage_ChosenMapEntry>, I>>(
    object: I,
  ): ProsperityMessage_ChosenMapEntry {
    const message = createBaseProsperityMessage_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseProsperityMessage_AchieveMapEntry(): ProsperityMessage_AchieveMapEntry {
  return { key: 0, value: undefined };
}

export const ProsperityMessage_AchieveMapEntry: MessageFns<ProsperityMessage_AchieveMapEntry> = {
  encode(message: ProsperityMessage_AchieveMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      AchieveMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProsperityMessage_AchieveMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProsperityMessage_AchieveMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = AchieveMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ProsperityMessage_AchieveMapEntry>, I>>(
    base?: I,
  ): ProsperityMessage_AchieveMapEntry {
    return ProsperityMessage_AchieveMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProsperityMessage_AchieveMapEntry>, I>>(
    object: I,
  ): ProsperityMessage_AchieveMapEntry {
    const message = createBaseProsperityMessage_AchieveMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? AchieveMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseRechargeRewardTakeResponse(): RechargeRewardTakeResponse {
  return { takeList: [], rewardList: [] };
}

export const RechargeRewardTakeResponse: MessageFns<RechargeRewardTakeResponse> = {
  encode(message: RechargeRewardTakeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.takeList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RechargeRewardTakeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRechargeRewardTakeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.takeList.push(reader.int32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.takeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RechargeRewardTakeResponse>, I>>(base?: I): RechargeRewardTakeResponse {
    return RechargeRewardTakeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RechargeRewardTakeResponse>, I>>(object: I): RechargeRewardTakeResponse {
    const message = createBaseRechargeRewardTakeResponse();
    message.takeList = object.takeList?.map((e) => e) || [];
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseRedeemBuyMessage(): RedeemBuyMessage {
  return { activityId: 0, redeemMap: {}, chosenMap: {}, rewardList: [], redeemId: 0, count: 0 };
}

export const RedeemBuyMessage: MessageFns<RedeemBuyMessage> = {
  encode(message: RedeemBuyMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      RedeemBuyMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      RedeemBuyMessage_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    writer.uint32(34).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    if (message.redeemId !== 0) {
      writer.uint32(40).int64(message.redeemId);
    }
    if (message.count !== 0) {
      writer.uint32(48).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemBuyMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemBuyMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = RedeemBuyMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.redeemMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = RedeemBuyMessage_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.chosenMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag === 33) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.redeemId = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemBuyMessage>, I>>(base?: I): RedeemBuyMessage {
    return RedeemBuyMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemBuyMessage>, I>>(object: I): RedeemBuyMessage {
    const message = createBaseRedeemBuyMessage();
    message.activityId = object.activityId ?? 0;
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.rewardList = object.rewardList?.map((e) => e) || [];
    message.redeemId = object.redeemId ?? 0;
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseRedeemBuyMessage_RedeemMapEntry(): RedeemBuyMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const RedeemBuyMessage_RedeemMapEntry: MessageFns<RedeemBuyMessage_RedeemMapEntry> = {
  encode(message: RedeemBuyMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemBuyMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemBuyMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemBuyMessage_RedeemMapEntry>, I>>(base?: I): RedeemBuyMessage_RedeemMapEntry {
    return RedeemBuyMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemBuyMessage_RedeemMapEntry>, I>>(
    object: I,
  ): RedeemBuyMessage_RedeemMapEntry {
    const message = createBaseRedeemBuyMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseRedeemBuyMessage_ChosenMapEntry(): RedeemBuyMessage_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const RedeemBuyMessage_ChosenMapEntry: MessageFns<RedeemBuyMessage_ChosenMapEntry> = {
  encode(message: RedeemBuyMessage_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemBuyMessage_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemBuyMessage_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemBuyMessage_ChosenMapEntry>, I>>(base?: I): RedeemBuyMessage_ChosenMapEntry {
    return RedeemBuyMessage_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemBuyMessage_ChosenMapEntry>, I>>(
    object: I,
  ): RedeemBuyMessage_ChosenMapEntry {
    const message = createBaseRedeemBuyMessage_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseRedeemChosenRequest(): RedeemChosenRequest {
  return { activityId: 0, redeemId: 0, chosenIndexList: [], count: 0 };
}

export const RedeemChosenRequest: MessageFns<RedeemChosenRequest> = {
  encode(message: RedeemChosenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.redeemId !== 0) {
      writer.uint32(16).int64(message.redeemId);
    }
    writer.uint32(26).fork();
    for (const v of message.chosenIndexList) {
      writer.int32(v);
    }
    writer.join();
    if (message.count !== 0) {
      writer.uint32(32).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemChosenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemChosenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.redeemId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.chosenIndexList.push(reader.int32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.chosenIndexList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemChosenRequest>, I>>(base?: I): RedeemChosenRequest {
    return RedeemChosenRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemChosenRequest>, I>>(object: I): RedeemChosenRequest {
    const message = createBaseRedeemChosenRequest();
    message.activityId = object.activityId ?? 0;
    message.redeemId = object.redeemId ?? 0;
    message.chosenIndexList = object.chosenIndexList?.map((e) => e) || [];
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseRedeemLimitUpdateMessage(): RedeemLimitUpdateMessage {
  return { activityId: 0, limitMap: {} };
}

export const RedeemLimitUpdateMessage: MessageFns<RedeemLimitUpdateMessage> = {
  encode(message: RedeemLimitUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.limitMap).forEach(([key, value]) => {
      RedeemLimitUpdateMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemLimitUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemLimitUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = RedeemLimitUpdateMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.limitMap[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemLimitUpdateMessage>, I>>(base?: I): RedeemLimitUpdateMessage {
    return RedeemLimitUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemLimitUpdateMessage>, I>>(object: I): RedeemLimitUpdateMessage {
    const message = createBaseRedeemLimitUpdateMessage();
    message.activityId = object.activityId ?? 0;
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseRedeemLimitUpdateMessage_LimitMapEntry(): RedeemLimitUpdateMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const RedeemLimitUpdateMessage_LimitMapEntry: MessageFns<RedeemLimitUpdateMessage_LimitMapEntry> = {
  encode(message: RedeemLimitUpdateMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemLimitUpdateMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemLimitUpdateMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemLimitUpdateMessage_LimitMapEntry>, I>>(
    base?: I,
  ): RedeemLimitUpdateMessage_LimitMapEntry {
    return RedeemLimitUpdateMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemLimitUpdateMessage_LimitMapEntry>, I>>(
    object: I,
  ): RedeemLimitUpdateMessage_LimitMapEntry {
    const message = createBaseRedeemLimitUpdateMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseRedeemMessage(): RedeemMessage {
  return { activityId: 0, redeemMap: {}, adMap: {}, limitMap: {}, chosenMap: {}, resetTime: 0 };
}

export const RedeemMessage: MessageFns<RedeemMessage> = {
  encode(message: RedeemMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      RedeemMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      RedeemMessage_AdMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.limitMap).forEach(([key, value]) => {
      RedeemMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      RedeemMessage_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    if (message.resetTime !== 0) {
      writer.uint32(48).int64(message.resetTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = RedeemMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.redeemMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = RedeemMessage_AdMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.adMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = RedeemMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.limitMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = RedeemMessage_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.chosenMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.resetTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemMessage>, I>>(base?: I): RedeemMessage {
    return RedeemMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemMessage>, I>>(object: I): RedeemMessage {
    const message = createBaseRedeemMessage();
    message.activityId = object.activityId ?? 0;
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.resetTime = object.resetTime ?? 0;
    return message;
  },
};

function createBaseRedeemMessage_RedeemMapEntry(): RedeemMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const RedeemMessage_RedeemMapEntry: MessageFns<RedeemMessage_RedeemMapEntry> = {
  encode(message: RedeemMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemMessage_RedeemMapEntry>, I>>(base?: I): RedeemMessage_RedeemMapEntry {
    return RedeemMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemMessage_RedeemMapEntry>, I>>(object: I): RedeemMessage_RedeemMapEntry {
    const message = createBaseRedeemMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseRedeemMessage_AdMapEntry(): RedeemMessage_AdMapEntry {
  return { key: 0, value: 0 };
}

export const RedeemMessage_AdMapEntry: MessageFns<RedeemMessage_AdMapEntry> = {
  encode(message: RedeemMessage_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemMessage_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemMessage_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemMessage_AdMapEntry>, I>>(base?: I): RedeemMessage_AdMapEntry {
    return RedeemMessage_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemMessage_AdMapEntry>, I>>(object: I): RedeemMessage_AdMapEntry {
    const message = createBaseRedeemMessage_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseRedeemMessage_LimitMapEntry(): RedeemMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const RedeemMessage_LimitMapEntry: MessageFns<RedeemMessage_LimitMapEntry> = {
  encode(message: RedeemMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemMessage_LimitMapEntry>, I>>(base?: I): RedeemMessage_LimitMapEntry {
    return RedeemMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemMessage_LimitMapEntry>, I>>(object: I): RedeemMessage_LimitMapEntry {
    const message = createBaseRedeemMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseRedeemMessage_ChosenMapEntry(): RedeemMessage_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const RedeemMessage_ChosenMapEntry: MessageFns<RedeemMessage_ChosenMapEntry> = {
  encode(message: RedeemMessage_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemMessage_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemMessage_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemMessage_ChosenMapEntry>, I>>(base?: I): RedeemMessage_ChosenMapEntry {
    return RedeemMessage_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemMessage_ChosenMapEntry>, I>>(object: I): RedeemMessage_ChosenMapEntry {
    const message = createBaseRedeemMessage_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseRedeemRequest(): RedeemRequest {
  return { activityId: 0, redeemId: 0, count: 0 };
}

export const RedeemRequest: MessageFns<RedeemRequest> = {
  encode(message: RedeemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.redeemId !== 0) {
      writer.uint32(16).int64(message.redeemId);
    }
    if (message.count !== 0) {
      writer.uint32(24).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.redeemId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemRequest>, I>>(base?: I): RedeemRequest {
    return RedeemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemRequest>, I>>(object: I): RedeemRequest {
    const message = createBaseRedeemRequest();
    message.activityId = object.activityId ?? 0;
    message.redeemId = object.redeemId ?? 0;
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseRedeemResponse(): RedeemResponse {
  return { redeemMap: {}, adMap: {}, chosenMap: {}, rewardList: [] };
}

export const RedeemResponse: MessageFns<RedeemResponse> = {
  encode(message: RedeemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      RedeemResponse_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      RedeemResponse_AdMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      RedeemResponse_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    writer.uint32(34).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = RedeemResponse_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.redeemMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = RedeemResponse_AdMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.adMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = RedeemResponse_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.chosenMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag === 33) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemResponse>, I>>(base?: I): RedeemResponse {
    return RedeemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemResponse>, I>>(object: I): RedeemResponse {
    const message = createBaseRedeemResponse();
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseRedeemResponse_RedeemMapEntry(): RedeemResponse_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const RedeemResponse_RedeemMapEntry: MessageFns<RedeemResponse_RedeemMapEntry> = {
  encode(message: RedeemResponse_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemResponse_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemResponse_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemResponse_RedeemMapEntry>, I>>(base?: I): RedeemResponse_RedeemMapEntry {
    return RedeemResponse_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemResponse_RedeemMapEntry>, I>>(
    object: I,
  ): RedeemResponse_RedeemMapEntry {
    const message = createBaseRedeemResponse_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseRedeemResponse_AdMapEntry(): RedeemResponse_AdMapEntry {
  return { key: 0, value: 0 };
}

export const RedeemResponse_AdMapEntry: MessageFns<RedeemResponse_AdMapEntry> = {
  encode(message: RedeemResponse_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemResponse_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemResponse_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemResponse_AdMapEntry>, I>>(base?: I): RedeemResponse_AdMapEntry {
    return RedeemResponse_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemResponse_AdMapEntry>, I>>(object: I): RedeemResponse_AdMapEntry {
    const message = createBaseRedeemResponse_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseRedeemResponse_ChosenMapEntry(): RedeemResponse_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const RedeemResponse_ChosenMapEntry: MessageFns<RedeemResponse_ChosenMapEntry> = {
  encode(message: RedeemResponse_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedeemResponse_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedeemResponse_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RedeemResponse_ChosenMapEntry>, I>>(base?: I): RedeemResponse_ChosenMapEntry {
    return RedeemResponse_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedeemResponse_ChosenMapEntry>, I>>(
    object: I,
  ): RedeemResponse_ChosenMapEntry {
    const message = createBaseRedeemResponse_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSevenDaySignMessage(): SevenDaySignMessage {
  return { activityId: 0, count: 0, sign: false, endTime: 0 };
}

export const SevenDaySignMessage: MessageFns<SevenDaySignMessage> = {
  encode(message: SevenDaySignMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.count !== 0) {
      writer.uint32(16).int32(message.count);
    }
    if (message.sign !== false) {
      writer.uint32(24).bool(message.sign);
    }
    if (message.endTime !== 0) {
      writer.uint32(32).int64(message.endTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SevenDaySignMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSevenDaySignMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.sign = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.endTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SevenDaySignMessage>, I>>(base?: I): SevenDaySignMessage {
    return SevenDaySignMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SevenDaySignMessage>, I>>(object: I): SevenDaySignMessage {
    const message = createBaseSevenDaySignMessage();
    message.activityId = object.activityId ?? 0;
    message.count = object.count ?? 0;
    message.sign = object.sign ?? false;
    message.endTime = object.endTime ?? 0;
    return message;
  },
};

function createBaseSevenDaySignResponse(): SevenDaySignResponse {
  return { sevenDaySign: undefined, rewardMessage: undefined };
}

export const SevenDaySignResponse: MessageFns<SevenDaySignResponse> = {
  encode(message: SevenDaySignResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sevenDaySign !== undefined) {
      SevenDaySignMessage.encode(message.sevenDaySign, writer.uint32(10).fork()).join();
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SevenDaySignResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSevenDaySignResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.sevenDaySign = SevenDaySignMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SevenDaySignResponse>, I>>(base?: I): SevenDaySignResponse {
    return SevenDaySignResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SevenDaySignResponse>, I>>(object: I): SevenDaySignResponse {
    const message = createBaseSevenDaySignResponse();
    message.sevenDaySign = (object.sevenDaySign !== undefined && object.sevenDaySign !== null)
      ? SevenDaySignMessage.fromPartial(object.sevenDaySign)
      : undefined;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseSevenDaySignRewardRequest(): SevenDaySignRewardRequest {
  return { activityId: 0, index: 0 };
}

export const SevenDaySignRewardRequest: MessageFns<SevenDaySignRewardRequest> = {
  encode(message: SevenDaySignRewardRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.index !== 0) {
      writer.uint32(16).int32(message.index);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SevenDaySignRewardRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSevenDaySignRewardRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.index = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SevenDaySignRewardRequest>, I>>(base?: I): SevenDaySignRewardRequest {
    return SevenDaySignRewardRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SevenDaySignRewardRequest>, I>>(object: I): SevenDaySignRewardRequest {
    const message = createBaseSevenDaySignRewardRequest();
    message.activityId = object.activityId ?? 0;
    message.index = object.index ?? 0;
    return message;
  },
};

function createBaseSimplePointMessage(): SimplePointMessage {
  return { detailMessage: undefined, pre: 0, after: 0, point: 0 };
}

export const SimplePointMessage: MessageFns<SimplePointMessage> = {
  encode(message: SimplePointMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.detailMessage !== undefined) {
      PlayerDetailMessage.encode(message.detailMessage, writer.uint32(10).fork()).join();
    }
    if (message.pre !== 0) {
      writer.uint32(17).double(message.pre);
    }
    if (message.after !== 0) {
      writer.uint32(25).double(message.after);
    }
    if (message.point !== 0) {
      writer.uint32(33).double(message.point);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SimplePointMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimplePointMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.detailMessage = PlayerDetailMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.pre = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.after = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.point = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SimplePointMessage>, I>>(base?: I): SimplePointMessage {
    return SimplePointMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SimplePointMessage>, I>>(object: I): SimplePointMessage {
    const message = createBaseSimplePointMessage();
    message.detailMessage = (object.detailMessage !== undefined && object.detailMessage !== null)
      ? PlayerDetailMessage.fromPartial(object.detailMessage)
      : undefined;
    message.pre = object.pre ?? 0;
    message.after = object.after ?? 0;
    message.point = object.point ?? 0;
    return message;
  },
};

function createBaseSimpleRankMessage(): SimpleRankMessage {
  return { pre: 0, after: 0, point: 0, rank: 0, rankList: [] };
}

export const SimpleRankMessage: MessageFns<SimpleRankMessage> = {
  encode(message: SimpleRankMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pre !== 0) {
      writer.uint32(9).double(message.pre);
    }
    if (message.after !== 0) {
      writer.uint32(17).double(message.after);
    }
    if (message.point !== 0) {
      writer.uint32(25).double(message.point);
    }
    if (message.rank !== 0) {
      writer.uint32(32).int32(message.rank);
    }
    for (const v of message.rankList) {
      SimplePointMessage.encode(v!, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SimpleRankMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimpleRankMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.pre = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.after = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.point = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rankList.push(SimplePointMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<SimpleRankMessage>, I>>(base?: I): SimpleRankMessage {
    return SimpleRankMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SimpleRankMessage>, I>>(object: I): SimpleRankMessage {
    const message = createBaseSimpleRankMessage();
    message.pre = object.pre ?? 0;
    message.after = object.after ?? 0;
    message.point = object.point ?? 0;
    message.rank = object.rank ?? 0;
    message.rankList = object.rankList?.map((e) => SimplePointMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTaskCompleteMessage(): TaskCompleteMessage {
  return { targetVal: 0, takeList: [] };
}

export const TaskCompleteMessage: MessageFns<TaskCompleteMessage> = {
  encode(message: TaskCompleteMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.targetVal !== 0) {
      writer.uint32(8).int64(message.targetVal);
    }
    writer.uint32(18).fork();
    for (const v of message.takeList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TaskCompleteMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTaskCompleteMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.targetVal = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.takeList.push(reader.int32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.takeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TaskCompleteMessage>, I>>(base?: I): TaskCompleteMessage {
    return TaskCompleteMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskCompleteMessage>, I>>(object: I): TaskCompleteMessage {
    const message = createBaseTaskCompleteMessage();
    message.targetVal = object.targetVal ?? 0;
    message.takeList = object.takeList?.map((e) => e) || [];
    return message;
  },
};

function createBaseTempleLikeResponse(): TempleLikeResponse {
  return { likeList: [], miracleRemainMap: {}, rewardMessage: undefined, newMiracleId: 0 };
}

export const TempleLikeResponse: MessageFns<TempleLikeResponse> = {
  encode(message: TempleLikeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.likeList) {
      writer.int64(v);
    }
    writer.join();
    Object.entries(message.miracleRemainMap).forEach(([key, value]) => {
      TempleLikeResponse_MiracleRemainMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(26).fork()).join();
    }
    if (message.newMiracleId !== 0) {
      writer.uint32(32).int64(message.newMiracleId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TempleLikeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTempleLikeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.likeList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.likeList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = TempleLikeResponse_MiracleRemainMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.miracleRemainMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.newMiracleId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TempleLikeResponse>, I>>(base?: I): TempleLikeResponse {
    return TempleLikeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TempleLikeResponse>, I>>(object: I): TempleLikeResponse {
    const message = createBaseTempleLikeResponse();
    message.likeList = object.likeList?.map((e) => e) || [];
    message.miracleRemainMap = Object.entries(object.miracleRemainMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    message.newMiracleId = object.newMiracleId ?? 0;
    return message;
  },
};

function createBaseTempleLikeResponse_MiracleRemainMapEntry(): TempleLikeResponse_MiracleRemainMapEntry {
  return { key: 0, value: 0 };
}

export const TempleLikeResponse_MiracleRemainMapEntry: MessageFns<TempleLikeResponse_MiracleRemainMapEntry> = {
  encode(message: TempleLikeResponse_MiracleRemainMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TempleLikeResponse_MiracleRemainMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTempleLikeResponse_MiracleRemainMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TempleLikeResponse_MiracleRemainMapEntry>, I>>(
    base?: I,
  ): TempleLikeResponse_MiracleRemainMapEntry {
    return TempleLikeResponse_MiracleRemainMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TempleLikeResponse_MiracleRemainMapEntry>, I>>(
    object: I,
  ): TempleLikeResponse_MiracleRemainMapEntry {
    const message = createBaseTempleLikeResponse_MiracleRemainMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTempleMessage(): TempleMessage {
  return { placeMap: {}, likeList: [], miracleRemainMap: {}, totalTreeVal: 0, treeTakeList: [] };
}

export const TempleMessage: MessageFns<TempleMessage> = {
  encode(message: TempleMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.placeMap).forEach(([key, value]) => {
      TempleMessage_PlaceMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    writer.uint32(18).fork();
    for (const v of message.likeList) {
      writer.int64(v);
    }
    writer.join();
    Object.entries(message.miracleRemainMap).forEach(([key, value]) => {
      TempleMessage_MiracleRemainMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    if (message.totalTreeVal !== 0) {
      writer.uint32(32).int32(message.totalTreeVal);
    }
    writer.uint32(42).fork();
    for (const v of message.treeTakeList) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TempleMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTempleMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = TempleMessage_PlaceMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.placeMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.likeList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.likeList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = TempleMessage_MiracleRemainMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.miracleRemainMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.totalTreeVal = reader.int32();
          continue;
        }
        case 5: {
          if (tag === 40) {
            message.treeTakeList.push(reader.int32());

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.treeTakeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TempleMessage>, I>>(base?: I): TempleMessage {
    return TempleMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TempleMessage>, I>>(object: I): TempleMessage {
    const message = createBaseTempleMessage();
    message.placeMap = Object.entries(object.placeMap ?? {}).reduce<{ [key: number]: PlayerTempleMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = PlayerTempleMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.likeList = object.likeList?.map((e) => e) || [];
    message.miracleRemainMap = Object.entries(object.miracleRemainMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.totalTreeVal = object.totalTreeVal ?? 0;
    message.treeTakeList = object.treeTakeList?.map((e) => e) || [];
    return message;
  },
};

function createBaseTempleMessage_PlaceMapEntry(): TempleMessage_PlaceMapEntry {
  return { key: 0, value: undefined };
}

export const TempleMessage_PlaceMapEntry: MessageFns<TempleMessage_PlaceMapEntry> = {
  encode(message: TempleMessage_PlaceMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      PlayerTempleMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TempleMessage_PlaceMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTempleMessage_PlaceMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = PlayerTempleMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TempleMessage_PlaceMapEntry>, I>>(base?: I): TempleMessage_PlaceMapEntry {
    return TempleMessage_PlaceMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TempleMessage_PlaceMapEntry>, I>>(object: I): TempleMessage_PlaceMapEntry {
    const message = createBaseTempleMessage_PlaceMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? PlayerTempleMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTempleMessage_MiracleRemainMapEntry(): TempleMessage_MiracleRemainMapEntry {
  return { key: 0, value: 0 };
}

export const TempleMessage_MiracleRemainMapEntry: MessageFns<TempleMessage_MiracleRemainMapEntry> = {
  encode(message: TempleMessage_MiracleRemainMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TempleMessage_MiracleRemainMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTempleMessage_MiracleRemainMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TempleMessage_MiracleRemainMapEntry>, I>>(
    base?: I,
  ): TempleMessage_MiracleRemainMapEntry {
    return TempleMessage_MiracleRemainMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TempleMessage_MiracleRemainMapEntry>, I>>(
    object: I,
  ): TempleMessage_MiracleRemainMapEntry {
    const message = createBaseTempleMessage_MiracleRemainMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTimeTaskMessage(): TimeTaskMessage {
  return { activityId: 0, completeMap: {} };
}

export const TimeTaskMessage: MessageFns<TimeTaskMessage> = {
  encode(message: TimeTaskMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.completeMap).forEach(([key, value]) => {
      TimeTaskMessage_CompleteMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TimeTaskMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTimeTaskMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = TimeTaskMessage_CompleteMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.completeMap[entry2.key] = entry2.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TimeTaskMessage>, I>>(base?: I): TimeTaskMessage {
    return TimeTaskMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimeTaskMessage>, I>>(object: I): TimeTaskMessage {
    const message = createBaseTimeTaskMessage();
    message.activityId = object.activityId ?? 0;
    message.completeMap = Object.entries(object.completeMap ?? {}).reduce<{ [key: number]: TaskCompleteMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = TaskCompleteMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseTimeTaskMessage_CompleteMapEntry(): TimeTaskMessage_CompleteMapEntry {
  return { key: 0, value: undefined };
}

export const TimeTaskMessage_CompleteMapEntry: MessageFns<TimeTaskMessage_CompleteMapEntry> = {
  encode(message: TimeTaskMessage_CompleteMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      TaskCompleteMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TimeTaskMessage_CompleteMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTimeTaskMessage_CompleteMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = TaskCompleteMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TimeTaskMessage_CompleteMapEntry>, I>>(
    base?: I,
  ): TimeTaskMessage_CompleteMapEntry {
    return TimeTaskMessage_CompleteMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimeTaskMessage_CompleteMapEntry>, I>>(
    object: I,
  ): TimeTaskMessage_CompleteMapEntry {
    const message = createBaseTimeTaskMessage_CompleteMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? TaskCompleteMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTimeTaskResponse(): TimeTaskResponse {
  return { activityId: 0, taskId: 0, targetVal: 0 };
}

export const TimeTaskResponse: MessageFns<TimeTaskResponse> = {
  encode(message: TimeTaskResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.taskId !== 0) {
      writer.uint32(16).int64(message.taskId);
    }
    if (message.targetVal !== 0) {
      writer.uint32(24).int64(message.targetVal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TimeTaskResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTimeTaskResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.taskId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.targetVal = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TimeTaskResponse>, I>>(base?: I): TimeTaskResponse {
    return TimeTaskResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimeTaskResponse>, I>>(object: I): TimeTaskResponse {
    const message = createBaseTimeTaskResponse();
    message.activityId = object.activityId ?? 0;
    message.taskId = object.taskId ?? 0;
    message.targetVal = object.targetVal ?? 0;
    return message;
  },
};

function createBaseTimeTaskTakeRequest(): TimeTaskTakeRequest {
  return { activityId: 0, taskId: 0, index: 0, takeAll: false };
}

export const TimeTaskTakeRequest: MessageFns<TimeTaskTakeRequest> = {
  encode(message: TimeTaskTakeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.taskId !== 0) {
      writer.uint32(16).int64(message.taskId);
    }
    if (message.index !== 0) {
      writer.uint32(24).int32(message.index);
    }
    if (message.takeAll !== false) {
      writer.uint32(32).bool(message.takeAll);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TimeTaskTakeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTimeTaskTakeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.taskId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.index = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.takeAll = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TimeTaskTakeRequest>, I>>(base?: I): TimeTaskTakeRequest {
    return TimeTaskTakeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimeTaskTakeRequest>, I>>(object: I): TimeTaskTakeRequest {
    const message = createBaseTimeTaskTakeRequest();
    message.activityId = object.activityId ?? 0;
    message.taskId = object.taskId ?? 0;
    message.index = object.index ?? 0;
    message.takeAll = object.takeAll ?? false;
    return message;
  },
};

function createBaseTopUpMessage(): TopUpMessage {
  return {
    activityId: 0,
    redeemMap: {},
    adMap: {},
    limitMap: {},
    chosenMap: {},
    signId: 0,
    signMap: {},
    totalRecharge: 0,
    takeList: [],
    deadline: 0,
  };
}

export const TopUpMessage: MessageFns<TopUpMessage> = {
  encode(message: TopUpMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      TopUpMessage_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    Object.entries(message.adMap).forEach(([key, value]) => {
      TopUpMessage_AdMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    Object.entries(message.limitMap).forEach(([key, value]) => {
      TopUpMessage_LimitMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.chosenMap).forEach(([key, value]) => {
      TopUpMessage_ChosenMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    if (message.signId !== 0) {
      writer.uint32(48).int64(message.signId);
    }
    Object.entries(message.signMap).forEach(([key, value]) => {
      TopUpMessage_SignMapEntry.encode({ key: key as any, value }, writer.uint32(58).fork()).join();
    });
    if (message.totalRecharge !== 0) {
      writer.uint32(64).int64(message.totalRecharge);
    }
    writer.uint32(74).fork();
    for (const v of message.takeList) {
      writer.int32(v);
    }
    writer.join();
    if (message.deadline !== 0) {
      writer.uint32(80).int64(message.deadline);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = TopUpMessage_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.redeemMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = TopUpMessage_AdMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.adMap[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = TopUpMessage_LimitMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.limitMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = TopUpMessage_ChosenMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.chosenMap[entry5.key] = entry5.value;
          }
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.signId = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          const entry7 = TopUpMessage_SignMapEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.signMap[entry7.key] = entry7.value;
          }
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.totalRecharge = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag === 72) {
            message.takeList.push(reader.int32());

            continue;
          }

          if (tag === 74) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.takeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.deadline = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpMessage>, I>>(base?: I): TopUpMessage {
    return TopUpMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpMessage>, I>>(object: I): TopUpMessage {
    const message = createBaseTopUpMessage();
    message.activityId = object.activityId ?? 0;
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.adMap = Object.entries(object.adMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.limitMap = Object.entries(object.limitMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.chosenMap = Object.entries(object.chosenMap ?? {}).reduce<{ [key: number]: CommIntegerListMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = CommIntegerListMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.signId = object.signId ?? 0;
    message.signMap = Object.entries(object.signMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.totalRecharge = object.totalRecharge ?? 0;
    message.takeList = object.takeList?.map((e) => e) || [];
    message.deadline = object.deadline ?? 0;
    return message;
  },
};

function createBaseTopUpMessage_RedeemMapEntry(): TopUpMessage_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const TopUpMessage_RedeemMapEntry: MessageFns<TopUpMessage_RedeemMapEntry> = {
  encode(message: TopUpMessage_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpMessage_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpMessage_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpMessage_RedeemMapEntry>, I>>(base?: I): TopUpMessage_RedeemMapEntry {
    return TopUpMessage_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpMessage_RedeemMapEntry>, I>>(object: I): TopUpMessage_RedeemMapEntry {
    const message = createBaseTopUpMessage_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTopUpMessage_AdMapEntry(): TopUpMessage_AdMapEntry {
  return { key: 0, value: 0 };
}

export const TopUpMessage_AdMapEntry: MessageFns<TopUpMessage_AdMapEntry> = {
  encode(message: TopUpMessage_AdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpMessage_AdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpMessage_AdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpMessage_AdMapEntry>, I>>(base?: I): TopUpMessage_AdMapEntry {
    return TopUpMessage_AdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpMessage_AdMapEntry>, I>>(object: I): TopUpMessage_AdMapEntry {
    const message = createBaseTopUpMessage_AdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTopUpMessage_LimitMapEntry(): TopUpMessage_LimitMapEntry {
  return { key: 0, value: 0 };
}

export const TopUpMessage_LimitMapEntry: MessageFns<TopUpMessage_LimitMapEntry> = {
  encode(message: TopUpMessage_LimitMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpMessage_LimitMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpMessage_LimitMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpMessage_LimitMapEntry>, I>>(base?: I): TopUpMessage_LimitMapEntry {
    return TopUpMessage_LimitMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpMessage_LimitMapEntry>, I>>(object: I): TopUpMessage_LimitMapEntry {
    const message = createBaseTopUpMessage_LimitMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTopUpMessage_ChosenMapEntry(): TopUpMessage_ChosenMapEntry {
  return { key: 0, value: undefined };
}

export const TopUpMessage_ChosenMapEntry: MessageFns<TopUpMessage_ChosenMapEntry> = {
  encode(message: TopUpMessage_ChosenMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== undefined) {
      CommIntegerListMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpMessage_ChosenMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpMessage_ChosenMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = CommIntegerListMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpMessage_ChosenMapEntry>, I>>(base?: I): TopUpMessage_ChosenMapEntry {
    return TopUpMessage_ChosenMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpMessage_ChosenMapEntry>, I>>(object: I): TopUpMessage_ChosenMapEntry {
    const message = createBaseTopUpMessage_ChosenMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? CommIntegerListMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseTopUpMessage_SignMapEntry(): TopUpMessage_SignMapEntry {
  return { key: 0, value: 0 };
}

export const TopUpMessage_SignMapEntry: MessageFns<TopUpMessage_SignMapEntry> = {
  encode(message: TopUpMessage_SignMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpMessage_SignMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpMessage_SignMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpMessage_SignMapEntry>, I>>(base?: I): TopUpMessage_SignMapEntry {
    return TopUpMessage_SignMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpMessage_SignMapEntry>, I>>(object: I): TopUpMessage_SignMapEntry {
    const message = createBaseTopUpMessage_SignMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTopUpRewardRequest(): TopUpRewardRequest {
  return { activityId: 0, index: 0 };
}

export const TopUpRewardRequest: MessageFns<TopUpRewardRequest> = {
  encode(message: TopUpRewardRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.index !== 0) {
      writer.uint32(16).int32(message.index);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpRewardRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpRewardRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.index = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpRewardRequest>, I>>(base?: I): TopUpRewardRequest {
    return TopUpRewardRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpRewardRequest>, I>>(object: I): TopUpRewardRequest {
    const message = createBaseTopUpRewardRequest();
    message.activityId = object.activityId ?? 0;
    message.index = object.index ?? 0;
    return message;
  },
};

function createBaseTopUpRewardResponse(): TopUpRewardResponse {
  return { takeList: [], rewardList: [] };
}

export const TopUpRewardResponse: MessageFns<TopUpRewardResponse> = {
  encode(message: TopUpRewardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.takeList) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpRewardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpRewardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.takeList.push(reader.int32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.takeList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpRewardResponse>, I>>(base?: I): TopUpRewardResponse {
    return TopUpRewardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpRewardResponse>, I>>(object: I): TopUpRewardResponse {
    const message = createBaseTopUpRewardResponse();
    message.takeList = object.takeList?.map((e) => e) || [];
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseTopUpSignRequest(): TopUpSignRequest {
  return { activityId: 0, signId: 0 };
}

export const TopUpSignRequest: MessageFns<TopUpSignRequest> = {
  encode(message: TopUpSignRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.signId !== 0) {
      writer.uint32(16).int64(message.signId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpSignRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpSignRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.signId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpSignRequest>, I>>(base?: I): TopUpSignRequest {
    return TopUpSignRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpSignRequest>, I>>(object: I): TopUpSignRequest {
    const message = createBaseTopUpSignRequest();
    message.activityId = object.activityId ?? 0;
    message.signId = object.signId ?? 0;
    return message;
  },
};

function createBaseTopUpSignResponse(): TopUpSignResponse {
  return { signMap: {}, deadline: 0, rewardList: [] };
}

export const TopUpSignResponse: MessageFns<TopUpSignResponse> = {
  encode(message: TopUpSignResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.signMap).forEach(([key, value]) => {
      TopUpSignResponse_SignMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    if (message.deadline !== 0) {
      writer.uint32(16).int64(message.deadline);
    }
    writer.uint32(26).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpSignResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpSignResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = TopUpSignResponse_SignMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.signMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.deadline = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag === 25) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpSignResponse>, I>>(base?: I): TopUpSignResponse {
    return TopUpSignResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpSignResponse>, I>>(object: I): TopUpSignResponse {
    const message = createBaseTopUpSignResponse();
    message.signMap = Object.entries(object.signMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.deadline = object.deadline ?? 0;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseTopUpSignResponse_SignMapEntry(): TopUpSignResponse_SignMapEntry {
  return { key: 0, value: 0 };
}

export const TopUpSignResponse_SignMapEntry: MessageFns<TopUpSignResponse_SignMapEntry> = {
  encode(message: TopUpSignResponse_SignMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopUpSignResponse_SignMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopUpSignResponse_SignMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<TopUpSignResponse_SignMapEntry>, I>>(base?: I): TopUpSignResponse_SignMapEntry {
    return TopUpSignResponse_SignMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopUpSignResponse_SignMapEntry>, I>>(
    object: I,
  ): TopUpSignResponse_SignMapEntry {
    const message = createBaseTopUpSignResponse_SignMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseVipCardMessage(): VipCardMessage {
  return { life: false, deadline: 0, takeMonthDailyReward: false, takeLifeDailyReward: false };
}

export const VipCardMessage: MessageFns<VipCardMessage> = {
  encode(message: VipCardMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.life !== false) {
      writer.uint32(8).bool(message.life);
    }
    if (message.deadline !== 0) {
      writer.uint32(16).int64(message.deadline);
    }
    if (message.takeMonthDailyReward !== false) {
      writer.uint32(24).bool(message.takeMonthDailyReward);
    }
    if (message.takeLifeDailyReward !== false) {
      writer.uint32(32).bool(message.takeLifeDailyReward);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VipCardMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVipCardMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.life = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.deadline = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.takeMonthDailyReward = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.takeLifeDailyReward = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<VipCardMessage>, I>>(base?: I): VipCardMessage {
    return VipCardMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VipCardMessage>, I>>(object: I): VipCardMessage {
    const message = createBaseVipCardMessage();
    message.life = object.life ?? false;
    message.deadline = object.deadline ?? 0;
    message.takeMonthDailyReward = object.takeMonthDailyReward ?? false;
    message.takeLifeDailyReward = object.takeLifeDailyReward ?? false;
    return message;
  },
};

function createBaseVipCardResponse(): VipCardResponse {
  return { vipCard: undefined, rewardList: [] };
}

export const VipCardResponse: MessageFns<VipCardResponse> = {
  encode(message: VipCardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.vipCard !== undefined) {
      VipCardMessage.encode(message.vipCard, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VipCardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVipCardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.vipCard = VipCardMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<VipCardResponse>, I>>(base?: I): VipCardResponse {
    return VipCardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VipCardResponse>, I>>(object: I): VipCardResponse {
    const message = createBaseVipCardResponse();
    message.vipCard = (object.vipCard !== undefined && object.vipCard !== null)
      ? VipCardMessage.fromPartial(object.vipCard)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseWindowPackMessage(): WindowPackMessage {
  return { activityId: 0, durationMap: {}, redeemList: [], coldMap: {} };
}

export const WindowPackMessage: MessageFns<WindowPackMessage> = {
  encode(message: WindowPackMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    Object.entries(message.durationMap).forEach(([key, value]) => {
      WindowPackMessage_DurationMapEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).join();
    });
    writer.uint32(26).fork();
    for (const v of message.redeemList) {
      writer.int64(v);
    }
    writer.join();
    Object.entries(message.coldMap).forEach(([key, value]) => {
      WindowPackMessage_ColdMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WindowPackMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWindowPackMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          const entry2 = WindowPackMessage_DurationMapEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.durationMap[entry2.key] = entry2.value;
          }
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.redeemList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.redeemList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = WindowPackMessage_ColdMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.coldMap[entry4.key] = entry4.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WindowPackMessage>, I>>(base?: I): WindowPackMessage {
    return WindowPackMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WindowPackMessage>, I>>(object: I): WindowPackMessage {
    const message = createBaseWindowPackMessage();
    message.activityId = object.activityId ?? 0;
    message.durationMap = Object.entries(object.durationMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.redeemList = object.redeemList?.map((e) => e) || [];
    message.coldMap = Object.entries(object.coldMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseWindowPackMessage_DurationMapEntry(): WindowPackMessage_DurationMapEntry {
  return { key: 0, value: 0 };
}

export const WindowPackMessage_DurationMapEntry: MessageFns<WindowPackMessage_DurationMapEntry> = {
  encode(message: WindowPackMessage_DurationMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WindowPackMessage_DurationMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWindowPackMessage_DurationMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WindowPackMessage_DurationMapEntry>, I>>(
    base?: I,
  ): WindowPackMessage_DurationMapEntry {
    return WindowPackMessage_DurationMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WindowPackMessage_DurationMapEntry>, I>>(
    object: I,
  ): WindowPackMessage_DurationMapEntry {
    const message = createBaseWindowPackMessage_DurationMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseWindowPackMessage_ColdMapEntry(): WindowPackMessage_ColdMapEntry {
  return { key: 0, value: 0 };
}

export const WindowPackMessage_ColdMapEntry: MessageFns<WindowPackMessage_ColdMapEntry> = {
  encode(message: WindowPackMessage_ColdMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WindowPackMessage_ColdMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWindowPackMessage_ColdMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WindowPackMessage_ColdMapEntry>, I>>(base?: I): WindowPackMessage_ColdMapEntry {
    return WindowPackMessage_ColdMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WindowPackMessage_ColdMapEntry>, I>>(
    object: I,
  ): WindowPackMessage_ColdMapEntry {
    const message = createBaseWindowPackMessage_ColdMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseWindowRecordRequest(): WindowRecordRequest {
  return { activityId: 0, type: 0 };
}

export const WindowRecordRequest: MessageFns<WindowRecordRequest> = {
  encode(message: WindowRecordRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.activityId !== 0) {
      writer.uint32(8).int64(message.activityId);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WindowRecordRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWindowRecordRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.activityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WindowRecordRequest>, I>>(base?: I): WindowRecordRequest {
    return WindowRecordRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WindowRecordRequest>, I>>(object: I): WindowRecordRequest {
    const message = createBaseWindowRecordRequest();
    message.activityId = object.activityId ?? 0;
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseWindowsPackBuyResponse(): WindowsPackBuyResponse {
  return { windowPackMessage: undefined, rewardList: [], redeemId: 0 };
}

export const WindowsPackBuyResponse: MessageFns<WindowsPackBuyResponse> = {
  encode(message: WindowsPackBuyResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.windowPackMessage !== undefined) {
      WindowPackMessage.encode(message.windowPackMessage, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    if (message.redeemId !== 0) {
      writer.uint32(24).int64(message.redeemId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WindowsPackBuyResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWindowsPackBuyResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.windowPackMessage = WindowPackMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.redeemId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<WindowsPackBuyResponse>, I>>(base?: I): WindowsPackBuyResponse {
    return WindowsPackBuyResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WindowsPackBuyResponse>, I>>(object: I): WindowsPackBuyResponse {
    const message = createBaseWindowsPackBuyResponse();
    message.windowPackMessage = (object.windowPackMessage !== undefined && object.windowPackMessage !== null)
      ? WindowPackMessage.fromPartial(object.windowPackMessage)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    message.redeemId = object.redeemId ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
