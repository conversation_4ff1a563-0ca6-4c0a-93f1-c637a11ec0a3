import { _decorator, Node, Prefab, instantiate } from "cc";
import { Event_ctrl } from "./Event_ctrl";
import { EventActionModule } from "db://assets/GameScrpit/module/event_action/src/EventActionModule";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { AssetMgr } from "db://assets/platform/src/ResHelper";
import MsgEnum from "../../../event/MsgEnum";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { GameMapTrim } from "../prefab_ctrl/GameMapTrim";
import TickerMgr from "db://assets/GameScrpit/lib/ticker/TickerMgr";
import { ThiefEventMessage } from "../../../net/protocol/WorldEvent";
import GuideMgr from "db://assets/GameScrpit/ext_guide/GuideMgr";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("EventCtrlManager")
export class EventCtrlManager {
  private static _instance: EventCtrlManager;
  private _assetMgr: AssetMgr;
  private _node_ground: Node;
  private _createdEvents = new Map<number, Event_ctrl>(); // 改为 Map 结构
  private _trimEvents = new Map<number, GameMapTrim>();

  public static get instance(): EventCtrlManager {
    if (!this._instance) {
      this._instance = new EventCtrlManager();
    }
    return this._instance;
  }

  public remove() {
    MsgMgr.off(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.checkAndCreatePrefabs, this);
    EventCtrlManager._instance = null;
  }

  public init(node_ground: Node) {
    MsgMgr.off(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.checkAndCreatePrefabs, this);
    MsgMgr.on(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.checkAndCreatePrefabs, this);
    this._node_ground = node_ground;
    this._assetMgr = AssetMgr.create();
    if (!EventCtrlManager._instance) {
      EventCtrlManager._instance = this;
    }
    // 调用检查并创建预制体的方法
    this.checkAndCreatePrefabs();
  }

  /**
   * 创建Event_ctrl事件的预制体
   * @param prefab - Event_ctrl对应的预制体
   * @param parentNode - 预制体实例要添加到的父节点
   * @returns 创建好的Event_ctrl组件实例
   */
  public createPathThiefEvent(prefab: Prefab, parentNode: Node, id: number): Event_ctrl | null {
    // 存在性检查前置
    if (this._createdEvents.has(id)) {
      log.warn(`事件 ${id} 已存在，直接返回已创建的实例`);
      return this._createdEvents.get(id);
    }

    if (!prefab || !parentNode) {
      log.error("预制体或父节点不能为空");
      return null;
    }

    const thiefNode = instantiate(prefab);
    const thiefCtrl = thiefNode.getComponent(Event_ctrl);

    if (!thiefCtrl) {
      log.error("预制体上未找到Event_ctrl组件");
      return null;
    }

    if (parentNode.isValid == false) {
      log.error("父节点无效");
      return;
    }

    parentNode.addChild(thiefNode);
    thiefCtrl.initialize({ eventId: id });

    // 记录新创建的实例
    this._createdEvents.set(id, thiefCtrl);
    return thiefCtrl;
  }

  /**
   * 检查进度并创建预制体
   * 遍历 eventTotalProgressMap，对比 eventProgressMap 中的次数，
   * 若次数不满足则创建对应的事件预制体
   */
  public async checkAndCreatePrefabs() {
    const totalProgressMap = EventActionModule.data.eventTrainMessage.eventMap;

    this._trimEvents.forEach((val, key) => {
      if (!totalProgressMap[key]) {
        val.setEvent(null);
      }
    });

    // 第二层校验：创建新实例
    for (const id in totalProgressMap) {
      const obj = totalProgressMap[id];
      const numericId = Number(id);
      this.loadEvent(obj, numericId);
    }

    //
  }

  private async loadEvent(obj: ThiefEventMessage, numericId: number) {
    // 已存在实例的校验
    if (this._createdEvents.has(numericId)) {
      // 当进度达标或数据异常时进行销毁
      if (obj.progress >= obj.totalProgress || obj.totalProgress <= 0) {
        this.removeEvent(numericId);
        log.log(`销毁事件 ${numericId}，当前进度 ${obj.progress}/${obj.totalProgress}`);
      }
      return;
    }

    if (this._trimEvents.has(numericId)) {
      this._trimEvents.get(numericId).setEvent(Number(numericId));
      return;
    }

    if (!this._node_ground || this._node_ground.isValid == false) {
      return;
    }

    // 新建实例的条件校验
    if (obj.progress < obj.totalProgress && obj.totalProgress > 0) {
      let db = JsonMgr.instance.jsonList.c_event2[numericId];
      if (db && db.type == 1) {
        this.loadEventType1(numericId);
      } else if (db && db.type == 2) {
      } else if (db && db.type == 3) {
        this.loadEventType3(numericId);
      } else if (db && db.type == 4) {
      } else if (db && db.type == 5) {
        this.loadEventType5(db, numericId);
      }
    }
  }

  private async loadEventType3(numericId) {
    const prefab = await this.getPrefabById(numericId);
  }

  private async loadEventType1(numericId) {
    //小偷事件
    const prefab = await this.getPrefabById(numericId);
    if (prefab) {
      if (numericId == 101) {
        GuideMgr.startGuide({ stepId: 72 }); // 三界图标隐藏，所以这个引导暂时没用
      }
      this.createPathThiefEvent(prefab, this._node_ground, numericId);
    }
  }

  private loadEventType5(db, numericId) {
    if (this._node_ground.isValid == false) {
      return;
    }
    //收割挂机事件
    let trimId = db.unlock[1];

    let trimNode = this._node_ground.getChildByName("trim_" + trimId);
    if (trimNode && trimNode.getComponent(GameMapTrim)) {
      trimNode.getComponent(GameMapTrim).setEvent(numericId);
      this._trimEvents.set(numericId, trimNode.getComponent(GameMapTrim));
    } else {
      if (!trimNode) {
        log.error("找不到装饰" + "trim_" + trimId);
      } else if (trimNode && !trimNode.getComponent(GameMapTrim)) {
        log.error("装饰" + "trim_" + trimId + "没有GameMapTrim组件");
      }
      TickerMgr.setTimeout(1, this.loadEventType5.bind(this, db, numericId));
    }
  }

  /**
   * 根据 id 获取对应的预制体
   * @param id - 事件的 id
   * @returns 对应的预制体，如果没有则返回 null
   */
  private async getPrefabById(id: number): Promise<Prefab> {
    let db = JsonMgr.instance.jsonList.c_event2[id];
    if (!db) {
      log.error("不存在配置=====");
      return;
    }

    if (db.prefabPath.length < 2) {
      log.error("没有预制体路径配置=====");
      return;
    }

    return await this._assetMgr.loadPrefabSync(db.prefabPath[0], db.prefabPath[1]);
  }

  public removeEvent(id: number) {
    const ctrl = this._createdEvents.get(id);
    if (ctrl) {
      ctrl.node.destroy();
      this._createdEvents.delete(id);
    }

    if (this._trimEvents.has(id)) {
      this._trimEvents.delete(id);
    }
  }
}
