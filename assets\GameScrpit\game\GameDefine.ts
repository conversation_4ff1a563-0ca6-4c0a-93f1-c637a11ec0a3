const forReview = false;

export const FmConfig = {
  version: "1.2.6",
  packageType: "DEBUG", // DEBUG, TEST, PROD
  origin: "DEV",
  forReview: forReview,
  stopNewGuide: false, // 停止新手引导
  isDebug: false, // 是否调试

  jumpXiaoYouXi: true, // 跳过小游戏
  jumpManHua: false, //跳过漫画

  gameHealthCheck: false,

  lodingIs: false,
};

let gHttpUrl = "https://admin.xmallx.cn/api/";
let hotUpdateUrl = "http://m.xmallx.cn/version.json";

if (FmConfig.forReview) {
  gHttpUrl = "https://admin.review.xmallx.cn/api/";
  hotUpdateUrl = "http://m.xmallx.cn/re/version.json";
}

/**
 * 游戏配置
 */
export const GameConfig = {
  gHttpUrl: gHttpUrl,
  hotUpdateUrl: hotUpdateUrl,
};

// 游戏层级
export enum LayerEnum {
  // 背景
  BG = 1,

  // 游戏主世界
  GAME = 2,

  // UI层
  UI = 8,

  // 弹窗层
  TOP = 16,

  // 忽略层
  IGNORE = 1024,
}

// 类型构造器
export type TypeConstructor<T = unknown> = new (...args: any[]) => T;

// 剧情ID枚举
export enum GuideIdStep {
  开场动画 = 1,
  对话引导2 = 2,
  对话引导3 = 3,
  对话引导4 = 4,
  对话引导5 = 5,
  对话引导6 = 6,
  对话引导7 = 7,
  开场对话引导12 = 12,
}

// 剧情ID枚举
export enum ModelGuideId {
  玲珑夺宝 = 1,
}

export const GuideTalkList = [12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];

// 与服务器心跳间隔 单位：秒
export const HeartBeatSec = 3;

// 与服务器心跳最长时间间隔 单位 豪秒
export const HeartBeatMaxSec = 5 * 60 * 1000;

// 与服务器心跳最大丢失次数
export const MaxHeatBeatLostTimes = 3;

// 种族
export enum RaceEnum {
  人族 = 1,
  神族 = 2,
  妖族 = 3,
  冥族 = 4,
  巫族 = 5,
}

export enum SystemOpenEnum {
  HERO_战将系统 = 1,
  FIGHT_关卡系统 = 2,
  FRIEND_仙友系统 = 3,
  // 乔迁系统 = 4,
  PUPIL_弟子系统 = 5,
  PET_灵兽系统 = 6,
  LUCKY_幸运抽奖 = 7,
  AZST_演武场 = 8,
  TERRAIN_领地 = 9,
  TERRAIN_领地换装 = 10,
  HOURSE_至宝系统 = 11,
  SOUL_兽魂系统 = 12,
  GOODS_英雄图鉴 = 13,
  POST_驿站系统 = 14,
  HUNT_天荒古境 = 15,
  GOODS_商城 = 16,
  POWER_特权卡 = 10201,
  FARM_福地洞天 = 18,
  CLUB_战盟系统 = 19,
  TRAVEL_游历系统 = 20,
  FINGHT_三倍战斗加速 = 21,
  HUNT_灵兽入侵 = 22,
  HUNT_洪荒出现 = 23,
  CLUB_战盟BOSS = 24,
  WZRY_五族荣耀 = 25,
  CHAT_聊天 = 26,
  WZYL_五族预览 = 27,
  SJXJ_三界小家 = 28,
  HZBX_号召宝箱 = 29,
  SD_圣殿 = 30,
  FRACTURE_时空裂隙 = 31,

  SJSJ_三界事件 = 32,
  PET_宠物系统 = 33,
  TRAIN_三界试练 = 34,
  RANK_排行 = 35,
  DAILY_每日挑战 = 36,

  HD_首充 = 10101,
  HD_七日签到 = 102,
  FRIEND_仙友目标 = 103,
  HD_充值好礼 = 104,
  HD_修行 = 105,
  HD_贵族豪礼 = 106,

  HD_山海探险 = 110,
}

export enum SystemOpenCondition {
  PlayerLevel = 1,
  FightLevel = 2,
  HeroNum = 4,
  MainTaskCount = 4,
  FriendCount = 5,
}

export enum SHOP_UNLOCK_TYPE {
  /**无条件 */
  无条件 = 0,
  /**仙盟等级 */
  仙盟等级 = 1,
  /**对应战将 */
  对应战将 = 2,
  /**拥有灵兽数量 */
  拥有灵兽数量 = 3,
}

export enum tweenTagEnum {
  UILevelGame_Tag = 1,
  UIBatmanMain_son_Tag = 2,

  UIGameMapCity_son_Tag = 11,
  UICityDetail_Tag = 12,
  UIPupilTrain_Tag = 13,
  UIPupilList_Tag = 13,

  UITaskMainPage_Tag = 13,
  TopItemAwardPop_Tag = 14,

  UIFightWin_Tag = 15,
  UIFightPage_Tag = 17,

  UITravelGet_Tag = 18,

  UIAzstWin_Tag = 20,

  UIHuntBossFinish_Tag = 22,

  UIHuntBossStruck_Tag = 24,

  UIPostBet_Tag = 26,

  UIHuntPreparePrimitive_Tag = 28,

  UIHuntFight_Tag = 30,
}

export enum PageZero {
  UIGameMap = 10,
  UIHeroMain = 10,

  UILottery = 20,
  UITravel = 20,
  UIHorseList = 20,
  UISoulDetail = 20,
  UIPupilPage = 20,
  UICityDetail = 20,

  UISoulList = 22,
}

export enum DialogZero {
  UIKnappsack = 0,
  UIAzst = 0,
  UIPost = 0,
  UISiteDetail = 0,
  UITravelDialogue = 0,
  UITravelGet = 0,
  UIPlayerMsg = 0,
  UIPlayerLevelUp = 0,
  UIFarmMain = 0,
  UIFarmCollect = 2,
  UIFarmShop = 2,
  UIAzstAward = 2,
  UIAzstLog = 2,
  UIAzstPk = 2,
  UIPostLevelUp = 2,
  UIPostSpeed = 2,
  UILotteryProb = 2,
  UILotteryShop = 2,
  UIHorseAttrDetail = 2,
  UISoulAttrPop = 2,
  UISoulFreePop = 2,
  UISoulGetPop = 2,
  UISoulRatePop = 2,
  UISoulSlotPop = 2,
  UIPlayerChangeName = 2,
  UIPlayerLevelUpRes = 2,
  UIEnergyUpgrade = 2,
  UIAddVitalityPop = 2,
  UIPupilAddResPop = 2,
  UIPupilAdultResPop = 2,
  UIPupilRatePreview = 2,

  UIItemJoinPop = 12,
  UIHelpPop = 16,
  UIFriendDialog = 20,
}

// 权限条件类型
export enum RightConditionEnum {
  Level = 1, // 主角身份等级
  ChapterCount = 2, // 累计通过关卡数量
  HeroCount = 3, // 累计获得门客数量
  MainTaskCount = 4, // 累计完成任务数量
  FriendCount = 5, // 累计获得知己的数量
  MonthCard = 6, // 月卡生效
  LifeCard = 7, // 终身卡生效
}

// 权限
export enum RightEnum {
  战斗3倍加速 = 101, // 101	战斗3倍加速
  跳过广告 = 102, // 102	跳过广告
  每日战盟免费中级捐献 = 103, // 103	每日战盟免费中级捐献
  每日战盟免费高级捐献 = 104, // 104	每日战盟免费高级捐献
  弟子一键培养 = 105, // 105	弟子一键培养
  游历上限加3 = 106, // 106	游历上限+3
  挑战券上限加2 = 107, // 107	挑战券上限+2
  建筑繁荣度百5 = 108, // 108	建筑繁荣度+5%
  弟子培养上限加100 = 109, // 109	弟子培养上限+100
  自动号召 = 110, // 110	自动号召
  号召宝箱一键打开 = 111, // 111	一键打开
}

/**服务器列表 */
export interface IServerGroup {
  group: "";
  serverList: IServerItem[];
}

export interface IServerItem {
  serverName: string;
  serverGroup: string;
  websocket: string;
  status: string;
}

/**=====================================================背包协议===================================================== */

/** 主角加成属性 */
export class IAttr {
  /**生命 */
  1: number = 0;
  /**攻击 */
  2: number = 0;
  /**防御 */
  3: number = 0;
  /**敏捷 */
  4: number = 0;
  /**击晕 */
  21: number = 0;
  /**闪避 */
  22: number = 0;
  /**连击 */
  23: number = 0;
  /**反击 */
  24: number = 0;
  /**暴击 */
  25: number = 0;
  /**吸血 */
  26: number = 0;
  /**伤害增加 */
  27: number = 0;
  /**抗击晕 */
  31: number = 0;
  /**抗闪避 */
  32: number = 0;
  /**抗连击 */
  33: number = 0;
  /**抗反击 */
  34: number = 0;
  /**抗暴击 */
  35: number = 0;
  /**抗吸血 */
  36: number = 0;
  /**伤害减免 */
  37: number = 0;
}

/**基础属性 固定值 */
export class BaseAttr {
  // 基础属性 固定值
  1: number = 0;
  2: number = 0;
  3: number = 0;
  /**敏捷 */
  4: number = 0;
}

export class HeroAddAttr {
  /**战将生命 */
  11: number = 0;
  /**战将攻击 */
  12: number = 0;
  /**战将防御 */
  13: number = 0;
  /**战将敏捷 */
  14: number = 0;
}

/**战斗抗性 */
export class ResistAttr {
  31: number = 0;
  32: number = 0;
  33: number = 0;
  34: number = 0;
  35: number = 0;
  36: number = 0;
  37: number = 0;
}

/**繁荣度组成及加成 */
export class BloomBaseAndOther {
  baseAdd: BloomBaseAdd = new BloomBaseAdd();
  otherAdd: BloomOtherAdd = new BloomOtherAdd();
}

export class BloomBaseAdd {
  /**基础 */
  basis = 0;
  /**庄园 */
  manor = 0;
  /**药铺 */
  pharmacy = 0;
  /**藏宝 */
  treasure = 0;
}

export class BloomOtherAdd {
  /**升级 */
  upgrade = 0;
  /**钱庄 */
  bank = 0;
  /**府邸 */
  mansion = 0;
  /**挚友 */
  friend = 0;
  /**商会 */
  merchant = 0;
  /**医馆 */
  hospital = 0;
  /**庄园 */
  manor = 0;
}

/**=====================================================战斗===================================================== */
/**
 * 玩家属性初始化
 */
export enum AttrEnum {
  生命_1 = 1,
  攻击_2 = 2,
  防御_3 = 3,
  敏捷_4 = 4,

  /** 百分比增加的，最终要加到基础属性上 */
  生命百分比_15 = 15,
  攻击百分比_16 = 16,
  防御百分比_17 = 17,
  敏捷百分比_18 = 18,

  击晕_21 = 21,
  闪避_22 = 22,
  连击_23 = 23,
  反击_24 = 24,
  暴击_25 = 25,
  吸血_26 = 26,
  伤害增加_27 = 27,

  抗击晕_31 = 31,
  抗闪避_32 = 32,
  抗连击_33 = 33,
  抗反击_34 = 34,
  抗暴击_35 = 35,
  抗吸血_36 = 36,
  伤害减免_37 = 37,
  资质_109 = 109,
}

/**
 * 英雄属性初始化
 */
export enum HeroAttrEnum {
  生命_1 = 1,
  攻击_2 = 2,
  防御_3 = 3,
  敏捷_4 = 4,

  /** 百分比增加的，最终要加到基础属性上 */
  战将生命百分比_11 = 11,
  战将攻击百分比_12 = 12,
  战将防御百分比_13 = 13,
  战将敏捷百分比_14 = 14,

  击晕_21 = 21,
  闪避_22 = 22,
  连击_23 = 23,
  反击_24 = 24,
  暴击_25 = 25,
  吸血_26 = 26,
  伤害增加_27 = 27,

  抗击晕_31 = 31,
  抗闪避_32 = 32,
  抗连击_33 = 33,
  抗反击_34 = 34,
  抗暴击_35 = 35,
  抗吸血_36 = 36,
  伤害减免_37 = 37,
}

// 基础属性
export const BaseAttrList = [AttrEnum.攻击_2, AttrEnum.生命_1, AttrEnum.防御_3, AttrEnum.敏捷_4];
export const ResistAttrList = [
  AttrEnum.抗击晕_31,
  AttrEnum.抗闪避_32,
  AttrEnum.抗连击_33,
  AttrEnum.抗反击_34,
  AttrEnum.抗暴击_35,
];

export enum ActorAniEnum {
  Idle = "boss_idle",
}

export enum NodeStatus {
  INIT = "INIT",
  LOAD = "LOAD",
  START = "START",
}

export class BuyDataParam {
  goodsId: number;
  goodsType: number;
  playerId: number;
  orderAmount: number;
  goodsName: string;
  platformType: string;
}

export enum RoleStatus {
  IDLE = "idle",
  WALK = "walk",
  RUN = "run",
  ATTACK = "attack",
  DIE = "die",
}

/**繁荣度配置 */

/**
 * 延迟时间
 * @param second 延迟时间
 * @returns
 */
export const Sleep = (second: number) => {
  return new Promise((resolve) => setTimeout(resolve, second * 1000));
};
