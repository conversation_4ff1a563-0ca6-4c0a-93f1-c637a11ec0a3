import { _decorator, ccenum, CCInteger, Node, tween, UITransform, v3 } from "cc";

import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { LayoutManager } from "../LayoutManager";
import { ViewHolder } from "../ListAdapter";
import { Rect } from "../../../../lib/utils/Rect";
const log = Logger.getLoger(LOG_LEVEL.STOP);
export type OnPageScroll = (node: Node, position: number, currentPos: number, positionOffset: number) => void;

const { ccclass, property } = _decorator;

enum Gravity {
  GRAVITY_CENTER = 0,
  // GRAVITY_TOP = 1,
  // GRAVITY_BOTTOM = 2,
  GRAVITY_LEFT = 3,
  GRAVITY_RIGHT = 4,
}
ccenum(Gravity);

@ccclass("GridLayoutManager")
export class GridLayoutManager extends LayoutManager {
  @property({ type: CCInteger })
  private column: number = 1;

  @property({ type: CCInteger })
  private spaceX: number = 0;

  @property({ type: CCInteger })
  private spaceY: number = 0;

  @property({ type: Gravity })
  private gravity: Gravity = Gravity.GRAVITY_LEFT;

  private _frameLoad = false;
  // 该变量用于标识是否启用分帧加载
  private _isFrameLoadEnabled = true;

  private _rowDrawCache: Node[] = [];
  protected start(): void {
    let screenWidth = this.node.getComponent(UITransform).contentSize.width;
  }

  protected update(dt: number): void {
    if (this._frameLoad) {
      let last = this.children.peekRear();
      let isFreeScreen = this.getNodeBottom(last) - this.spaceY > this.getBorderBottom();
      if (isFreeScreen) {
        let y = 0;
        for (let i = 0; i < this.column; i++) {
          let newNode = this.addNodeToTail();
          if (!newNode) {
            this._frameLoad = false;
            break;
          }
          let height = newNode.getComponent(UITransform).height;
          let anchorY = newNode.getComponent(UITransform).anchorY;
          y = this.getBorderTop() - (1 - anchorY) * height;
          if (last) {
            y = this.getNodeBottom(last) - (1 - anchorY) * height - this.spaceY;
          }
          this._rowDrawCache.push(newNode);
        }
        this._isFrameLoadEnabled = false;
        if (this.drawRow(y)) {
          this.lastRect = new Rect(
            this.getBorderLeft(),
            this.lastRect.top,
            this.getBorderRight(),
            this.getNodeBottom(this.children.peekRear())
          );
        } else {
          this._frameLoad = false;
          return;
        }
      } else {
        this._frameLoad = false;
        return;
      }
    }
  }

  protected onLayout(changed: boolean, lastRect: Rect, offsetX: number, offsetY: number, isFling: boolean): Rect {
    //
    if (!isFling) {
      if (offsetY < 0) {
        if (this.viewholders.firstVisiblePosition == 0) {
          this.getNodeTop(this.children.peekFront()) + offsetY < this.getBorderTop() && (offsetY /= 3);
        }
      } else {
        if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
          this.getNodeBottom(this.children.peekRear()) + offsetY > this.getBorderBottom() && (offsetY /= 3);
        }
      }
    }
    for (let i = 0; i < this.children.size(); i++) {
      let child = this.children.get(i);
      let childY = child.position.y + offsetY;
      this.updateChildPosition(child, child.position.x, childY);
    }
    // 如果正在分帧加载则返回
    if (this._frameLoad) {
      return lastRect;
    }
    if (this.children.size() == 0 && this._isFrameLoadEnabled) {
      this._frameLoad = true;
      return lastRect;
    } else if (this.children.size() == 0) {
      let y = 0;
      for (let i = 0; i < this.column; i++) {
        let newNode = this.addNodeToTail();
        if (!newNode) {
          break;
        }
        let height = newNode.getComponent(UITransform).height;
        let anchorY = newNode.getComponent(UITransform).anchorY;
        y = this.getBorderTop() - (1 - anchorY) * height;
        if (changed && lastRect.top < 0) {
          y = lastRect.top - (1 - anchorY) * height;
        }
        this._rowDrawCache.push(newNode);
      }
      if (!this.drawRow(y)) {
        return lastRect;
      }
    }
    if (offsetY < 0) {
      // 向下滚动
      for (
        let first = this.children.peekFront();
        this.getNodeTop(first) + this.spaceY < this.getBorderTop();
        first = this.children.peekFront()
      ) {
        let y = 0;
        for (let i = 0; i < this.column; i++) {
          let newNode = this.addNodeToHeader();
          if (!newNode) {
            break;
          }
          let height = newNode.getComponent(UITransform).height;
          let anchorY = newNode.getComponent(UITransform).anchorY;
          y = this.getNodeTop(first) + anchorY * height + this.spaceY;
          this._rowDrawCache.unshift(newNode);
        }
        if (!this.drawRow(y)) {
          break;
        }
      }
    } else {
      // 向上滚动
      for (
        let last = this.children.peekRear();
        this.getNodeBottom(last) - this.spaceY > this.getBorderBottom();
        last = this.children.peekRear()
      ) {
        let y = 0;
        for (let i = 0; i < this.column; i++) {
          let newNode = this.addNodeToTail();
          if (!newNode) {
            break;
          }
          let height = newNode.getComponent(UITransform).height;
          let anchorY = newNode.getComponent(UITransform).anchorY;
          y = this.getNodeBottom(last) - (1 - anchorY) * height - this.spaceY;
          this._rowDrawCache.push(newNode);
        }
        if (!this.drawRow(y)) {
          break;
        }
      }
    }
    return new Rect(
      this.getBorderLeft(),
      this.getNodeTop(this.children.peekFront()),
      this.getBorderRight(),
      this.getNodeBottom(this.children.peekRear())
    );
  }
  private drawRow(y: number): boolean {
    //
    if (this._rowDrawCache.length == 0) {
      return false;
    }
    let screenWidth = this.node.getComponent(UITransform).contentSize.width;
    let columnWidth = (screenWidth - (this.column - 1) * this.spaceX) / this.column;
    let x = 0;
    let validWidth = this._rowDrawCache.length * columnWidth + (this._rowDrawCache.length - 1) * this.spaceX;

    switch (this.gravity) {
      case Gravity.GRAVITY_CENTER:
        // 居中显示
        x = (screenWidth - validWidth) / 2 + columnWidth / 2;
        break;
      case Gravity.GRAVITY_LEFT:
        x = this.getBorderLeft() + columnWidth / 2;
        break;
      case Gravity.GRAVITY_RIGHT:
        x = screenWidth - validWidth + columnWidth / 2;
        break;
    }
    for (let i = 0; i < this._rowDrawCache.length; i++) {
      let item = this._rowDrawCache[i];
      let itemX = x + i * columnWidth + i * this.spaceX;
      this.updateChildPosition(item, itemX, y);
      if (this._frameLoad) {
        tween(item)
          .set({ scale: v3(0.1, 0.1, 1) })
          .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
          .start();
      }
    }
    this._rowDrawCache = [];
    return true;
  }
  protected onChildRecycled(child: Node, isFromHeader: boolean): boolean {
    let recycle = super.onChildRecycled(child, isFromHeader);
    let recycle2 = false;
    if (isFromHeader) {
      recycle2 = this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1;
    } else {
      recycle2 = this.viewholders.firstVisiblePosition == 0;
    }
    return recycle && !recycle2;
  }
  protected onFling(xoffset: number, yoffset: number): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    let isFirst = this.viewholders.firstVisiblePosition == 0;
    let isLast = this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1;
    if (
      (isFirst && this.getNodeTop(front) < this.getBorderTop()) ||
      (isLast && this.getNodeBottom(rear) > this.getBorderBottom())
    ) {
      this.scroller.setQuickStop();
      // log.log("fling complete");
    }
    // log.log("fling", xoffset, yoffset);
    super.onFling(xoffset, yoffset);
  }
  protected onScrollComplete(): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    let overStart = 0;
    let overEnd = 0;
    if (this.viewholders.firstVisiblePosition == 0) {
      overStart = this.getBorderTop() - this.getNodeTop(front);
    }
    if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
      overEnd = this.getBorderBottom() - this.getNodeBottom(rear);
    }
    // log.log(`overStart[${overStart}], overEnd[${overEnd}], first[${this.viewholders.firstVisiblePosition}]`);
    if (
      this.viewholders.firstVisiblePosition == 0 &&
      this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1
    ) {
      if (overStart > 0) {
        this.layoutChildrenOffset(0, overStart);
      } else if (overStart < 0 && overEnd < 0) {
        let deltaS = Math.abs(overStart) < Math.abs(overEnd) ? overStart : overEnd;
        this.layoutChildrenOffset(0, deltaS);
      }
    } else if (overStart > 0) {
      this.layoutChildrenOffset(0, overStart);
    } else if (overEnd < 0) {
      this.layoutChildrenOffset(0, overEnd);
    }
  }
  protected onFlingComplete(): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    let overStart = 0;
    let overEnd = 0;
    if (this.viewholders.firstVisiblePosition == 0) {
      overStart = this.getBorderTop() - this.getNodeTop(front);
    }
    if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
      overEnd = this.getBorderBottom() - this.getNodeBottom(rear);
    }
    // log.log(`overStart[${overStart}], overEnd[${overEnd}], first[${this.viewholders.firstVisiblePosition}]`);
    if (
      this.viewholders.firstVisiblePosition == 0 &&
      this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1
    ) {
      if (overStart > 0) {
        this.scroller.scrollBy(0, overStart);
      } else if (overStart < 0 && overEnd < 0) {
        let deltaS = Math.abs(overStart) < Math.abs(overEnd) ? overStart : overEnd;
        this.scroller.scrollBy(0, deltaS);
      }
    } else if (overStart > 0) {
      this.scroller.scrollBy(0, overStart);
    } else if (overEnd < 0) {
      this.scroller.scrollBy(0, overEnd);
    }
  }
}
class GridRow {
  private _items: Node[] = [];
}
