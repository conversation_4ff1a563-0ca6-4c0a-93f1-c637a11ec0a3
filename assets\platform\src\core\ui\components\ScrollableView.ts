import { _decorator, Component, EventTouch, Node, Size, UITransform, Vec2, NodeEventType, ccenum } from "cc";
import { <PERSON>roller } from "../../../lib/ui/Scroller";
import { Deque } from "../../../lib/utils/Deque";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { NodeTool } from "../../../../../GameScrpit/lib/utils/NodeTool";
const { ccclass, property } = _decorator;
const max_speed = 4;
const log = Logger.getLoger(LOG_LEVEL.STOP);
/**
 * @en
 * layout direction.
 *
 * @zh
 * 布局方向。
 */
enum Orientation {
  /**
   * @en
   * The horizontal direction .
   *
   * @zh
   * 水平方向。
   */
  HORIZONTAL = 0,
  /**
   * @en
   * The vertical direction.
   *
   * @zh
   * 垂直方向。
   */
  VERTICAL = 1,
}
ccenum(Orientation);

@ccclass("ScrollableView")
export class ScrollableView extends Component {
  @property(Node)
  content: Node;

  @property({ serializable: true })
  _orientation: Orientation = Orientation.VERTICAL;

  @property({ type: Orientation })
  get orientation(): Orientation {
    return this._orientation;
  }
  set orientation(value: Orientation) {
    this._orientation = value;
    // this.layout();
  }

  private _touchPosList: Deque<number[]> = new Deque();
  private touchPointId: number = -1;
  // private a = 0.0008;
  private v0 = 0;
  // private t0 = 0;

  // 是否正在触摸中
  private isTouching: boolean = false;
  private _scroller: Scroller = new Scroller();
  private _lastContentSize: Size;
  private _moveed: boolean = false;
  private _startPos: Vec2;

  private startMove(event: EventTouch) {
    if (this._moveed) {
      return;
    }

    if (event.target === this.node) {
      return;
    }
    let moveX = Math.abs(event.getUILocation().x - event.getUIStartLocation().x);
    let moveY = Math.abs(event.getUILocation().y - event.getUIStartLocation().y);
    let length = Vec2.distance(event.getUILocation(), event.getUIStartLocation());
    if (length > 20) {
      if (
        (this.orientation == Orientation.HORIZONTAL && moveX > moveY) ||
        (this.orientation == Orientation.VERTICAL && moveY > moveX)
      ) {
        const cancelEvent = new EventTouch(event.getTouches(), event.bubbles, NodeEventType.TOUCH_CANCEL);
        cancelEvent.touch = event.touch;
        cancelEvent.simulate = true;
        (event.target as Node).dispatchEvent(cancelEvent);
        this._moveed = true;
      }
    }

    // this.content.dispatchEvent(new CancelEvent(event.getAllTouches()));
  }
  protected onLoad(): void {
    // 添加触摸事件监听器
    this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this, true);
    this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this, true);
    this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this, true);
    this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this, true);
    this.content.on(Node.EventType.SIZE_CHANGED, () => {
      let curContentSize = this.content.getComponent(UITransform).contentSize;
      log.log("content size changed", curContentSize.height, this._lastContentSize?.height);
      if (!this._lastContentSize || curContentSize.height > this._lastContentSize.height) {
        this._lastContentSize = curContentSize;
        return;
      }
      this._lastContentSize = curContentSize;
      let overEnd = NodeTool.getBorderBottom(this.node) - NodeTool.getNodeBottom(this.content);
      let overStart = NodeTool.getBorderTop(this.node) - NodeTool.getNodeTop(this.content);
      let scrollHeight = this.node.getComponent(UITransform).height;
      let contentHeight = this.content.getComponent(UITransform).height;
      if (contentHeight < scrollHeight) {
        //当content小于Scroller的时候
        this._scroller.scrollBy(0, overStart);
      } else if (overStart > 0) {
        this._scroller.scrollBy(0, overStart);
      } else if (overEnd < 0) {
        this._scroller.scrollBy(0, overEnd);
      }
    });
    this._scroller.onFling = (xoffset, yoffset) => {
      if (
        NodeTool.getNodeBottom(this.content) > NodeTool.getBorderBottom(this.node) ||
        NodeTool.getNodeTop(this.content) < NodeTool.getBorderTop(this.node)
      ) {
        this._scroller.setQuickStop();
      }
      this.offsetChilds(xoffset, yoffset, true);
    };
    this._scroller.onFlingComplete = () => {
      // this.scrollCheck();
      log.log("onFlingComplete");
      let overEnd = NodeTool.getBorderBottom(this.node) - NodeTool.getNodeBottom(this.content);
      let overStart = NodeTool.getBorderTop(this.node) - NodeTool.getNodeTop(this.content);
      let scrollHeight = this.node.getComponent(UITransform).height;
      let contentHeight = this.content.getComponent(UITransform).height;
      if (contentHeight < scrollHeight) {
        //当content小于Scroller的时候
        this._scroller.scrollBy(0, overStart);
      } else if (overStart > 0) {
        this._scroller.scrollBy(0, overStart);
      } else if (overEnd < 0) {
        this._scroller.scrollBy(0, overEnd);
      }
    };
    this._scroller.onScrollComplete = () => {
      // this.scrollCheck();
      log.log("onScrollComplete");
      let overEnd = NodeTool.getBorderBottom(this.node) - NodeTool.getNodeBottom(this.content);
      let overStart = NodeTool.getBorderTop(this.node) - NodeTool.getNodeTop(this.content);
      let scrollHeight = this.node.getComponent(UITransform).height;
      let contentHeight = this.content.getComponent(UITransform).height;
      if (contentHeight < scrollHeight) {
        //当content小于Scroller的时候
        this.offsetChilds(0, overStart, true);
      } else if (overStart > 0) {
        this.offsetChilds(0, overStart, true);
      } else if (overEnd < 0) {
        this.offsetChilds(0, overEnd, true);
      }
    };
  }
  start() {}

  update(deltaTime: number) {
    if (this.isTouching) {
      return;
    }
    this._scroller.update();
  }

  private onTouchStart(event: EventTouch) {
    // event.bubbles = false;
    if (this.touchPointId > 0) {
      return;
    }
    this.touchPointId = event.getID();
    this.isTouching = true;
    this._moveed = false;
    this._startPos = event.getUILocation();
    this._touchPosList.addRear([this._startPos.x, this._startPos.y, Date.now()]);
  }
  private onTouchMove(event: EventTouch) {
    log.log("ScrollableView onTouchMove", !this.isTouching, this.touchPointId != event.getID());
    if (!this.isTouching) return;
    if (this.touchPointId != event.getID()) {
      return;
    }

    if (this._moveed) {
      event.propagationStopped = true;
    }

    this.startMove(event);
    // if (this._moveed) {
    //   event.propagationStopped = true;
    // }

    const touchPos = event.getUILocation();
    let startPos = this._touchPosList.peekRear();
    let startX = startPos[0] ?? 0;
    let startY = startPos[1] ?? 0;

    const deltaY = touchPos.y - startY;
    const deltaX = touchPos.x - startX;
    this.offsetChilds(deltaX, deltaY, false);

    if (this._touchPosList.size() > 3) {
      this._touchPosList.removeFront();
    } else {
    }
    this._touchPosList.addRear([touchPos.x, touchPos.y, Date.now()]);
  }

  private onTouchEnd(event: EventTouch) {
    log.log("ScrollableView onTouchEnd-1");

    if (this.touchPointId != event.getID()) {
      return;
    }
    log.log("ScrollableView onTouchEnd-2");

    this.touchPointId = -1;
    this.isTouching = false;
    let touchPos = event.getUILocation();
    let startPos = this._touchPosList.peekFront();
    let startX = startPos[0];
    let startY = startPos[1];
    let startTime = startPos[2];
    // log.log(Date.now());
    const deltaX = touchPos.x - startX;
    const deltaY = touchPos.y - startY;
    const deltaTime = Date.now() - startTime;
    let lastSpeedY = deltaY / deltaTime;
    let lastSpeedX = deltaX / deltaTime;
    // log.log(`lastSpeedY ${lastSpeedY}`);
    this.v0 = lastSpeedY;
    this._scroller.setFlings(lastSpeedX, lastSpeedY);
    // this.v0 = Math.max(-max_speed, Math.min(lastSpeedY, max_speed));
    // this.t0 = Date.now();
    this._touchPosList.clear();

    // let end = event.getUILocation();
    // let isMove =
    //   Math.abs(Math.abs(this._startPos.x) - Math.abs(end.x)) > 20 ||
    //   Math.abs(Math.abs(this._startPos.y) - Math.abs(end.y)) > 20;
    // if (isMove) {
    // }
    if (this._moveed) {
      event.propagationStopped = true;
      event.bubbles = false;
    }
    this._moveed = false;
    // log.log(`test distance ${this.testOffset}-${this.overBoundaryTop}`);
  }

  private onTouchCancel(event: EventTouch) {
    log.log("ScrollableView onTouchCancel-1");
    if (this.touchPointId != event.getID()) {
      return;
    }
    if (event && event.simulate) {
      return;
    }
    log.log("ScrollableView onTouchCancel-2");
    if (this._touchPosList.size() > 0) {
      this.onTouchEnd(event);
    } else {
      if (this.touchPointId != event.getID()) {
        return;
      }
      this.touchPointId = -1;
      this.isTouching = false;
    }
    this._moveed = false;
  }
  private offsetChilds(offsetX: number, offsetY: number, isFling: boolean) {
    if (this.orientation == Orientation.HORIZONTAL) {
      offsetY = 0;
      if (
        NodeTool.getNodeRight(this.content) < NodeTool.getBorderRight(this.node) ||
        NodeTool.getNodeLeft(this.content) > NodeTool.getBorderLeft(this.node)
      ) {
        offsetX /= 3;
        log.log("offsetX", offsetX);
      }
    } else {
      offsetX = 0;
      if (!isFling) {
        if (
          NodeTool.getNodeBottom(this.content) > NodeTool.getBorderBottom(this.node) ||
          NodeTool.getNodeTop(this.content) < NodeTool.getBorderTop(this.node)
        ) {
          offsetY /= 3;
        }
      }
    }

    this.content.setPosition(this.content.position.x + offsetX, this.content.position.y + offsetY);
    // this.node.children.forEach((child) => {
    //   child.setPosition(child.position.x, child.position.y + offsetY);
    // });
  }
  public scrollTo(offsetX: number, offsetY: number, animate: boolean) {
    let disTop = NodeTool.getBorderTop(this.node) - NodeTool.getNodeTop(this.content);
    let disBottom = NodeTool.getBorderBottom(this.node) - NodeTool.getNodeBottom(this.content);
    if (offsetY < disTop) {
      offsetY = disTop;
    } else if (offsetY > disBottom) {
      offsetY = disBottom;
    }
    if (animate) {
      this._scroller.scrollBy(0, offsetY, true, 400);
    } else {
      this.offsetChilds(0, offsetY, true);
    }
  }

  public scrollToStart(animate: boolean) {
    if (animate) {
      this._scroller.scrollBy(0, NodeTool.getBorderTop(this.node) - NodeTool.getNodeTop(this.content));
    } else {
      this.offsetChilds(0, NodeTool.getBorderTop(this.node) - NodeTool.getNodeTop(this.content), true);
    }
  }
  public scrolltoBottom(animate: boolean) {
    if (animate) {
      this._scroller.scrollBy(0, NodeTool.getBorderBottom(this.node) - NodeTool.getNodeBottom(this.content));
    } else {
      this.offsetChilds(0, NodeTool.getBorderBottom(this.node) - NodeTool.getNodeBottom(this.content), true);
    }
  }
}
