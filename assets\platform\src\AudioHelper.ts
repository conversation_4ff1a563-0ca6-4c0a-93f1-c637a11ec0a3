import { AudioSource, Tween } from "cc";
import { director } from "cc";
import { Node } from "cc";
import { AssetMgr, BundleEnum } from "./ResHelper";
import { AudioClip } from "cc";
import { But<PERSON> } from "cc";
import { JsonMgr } from "../../GameScrpit/game/mgr/JsonMgr";
import { persistentDevWith } from "../../GameScrpit/lib/decorators/persistent";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { Sleep } from "../../GameScrpit/game/GameDefine";
const log = Logger.getLoger(LOG_LEVEL.WARN);
// 外部引用
export const AudioName = {
  Effect: {
    攻击1: 101,
    受击: 102,
    按钮点击: 103,
    武将升级: 107,
    通用升级: 107,
    战斗失败: 108,
    战斗胜利: 109,
    点击女娲: 110,
    获得奖励: 111,
    普通关卡冲撞: 1643,
    弟子单次训练: 113,
    弟子自动训练: 114,
    关卡警报: 115,
    战力滚动: 116,
    女娲升级成功: 117,
    建筑建造成功: 118,
    建筑升级成功: 119,
    关闭窗口: 504,
    二级弹窗提示: 506,
  },
  Sound: {
    战斗: 104,
    天荒古境: 106,
    府邸和三界通用: 105,
  },
};

// 音乐资源管理
const musicAsasetMgr = AssetMgr.create();

// 配置格式
interface IConfigAudio {
  id: number;
  // 音频地址
  url: string;
  // 播放间隔
  cd: number;
  // 相对音量
  volume: number;
  //
  musicName: string;
  // // 同时播放数量;
  // maxExist: number;
  // // 资源加载
  // audioClip?: AudioClip;
  // // 是否自动释放
  // autoRelease?: boolean;
  // // 是否来源于网络
  // isRemote?: boolean;
}

// 配置文件
// export const ConfigAudioMap: { [key: string]: IConfigAudio } = {
//   受击1: { url: "effect/受击1", cd: 0.5, volume: 1, maxExist: 3 },
//   攻击1: { url: "effect/攻击1", cd: 0.5, volume: 1, maxExist: 3 },
//   按钮点击: { url: "effect/按钮点击", cd: 0.5, volume: 1, maxExist: 3 },
//   点击女娲: { url: "effect/点击女娲", cd: 0.5, volume: 1, maxExist: 3 },
//   获得奖励: { url: "effect/获得奖励", cd: 0.5, volume: 1, maxExist: 3 },
//   战斗胜利: { url: "effect/战斗胜利", cd: 0.5, volume: 1, maxExist: 3 },
//   战斗失败: { url: "effect/战斗失败", cd: 0.5, volume: 1, maxExist: 3 },
//   战力滚动: { url: "effect/战力滚动", cd: -1, volume: 1, maxExist: 3 },
//   关卡警报: { url: "effect/关卡警报", cd: 0.5, volume: 1, maxExist: 3, autoRelease: true },
//   弟子单次训练: { url: "effect/弟子单次训练", cd: 0.5, volume: 1, maxExist: 3 },
//   弟子自动训练: { url: "effect/弟子自动训练", cd: 0.5, volume: 1, maxExist: 3 },
//   普通关卡冲撞: { url: "effect/普通关卡冲撞", cd: 0.5, volume: 1, maxExist: 3 },
//   武将升级: { url: "effect/武将升级", cd: 0.5, volume: 1, maxExist: 3 },

//   天荒古境: { url: "music/天荒古境", cd: 0.5, volume: 1, maxExist: 1, autoRelease: true },
//   府邸和三界通用: { url: "music/府邸和三界通用", cd: 0.5, volume: 1, maxExist: 3, autoRelease: true },
//   战斗: { url: "music/战斗", cd: 0.5, volume: 1, maxExist: 3, autoRelease: true },

//   哪吒1: { url: "effect/daoq", cd: 0.1, volume: 1, maxExist: 3 },
//   哪吒2: { url: "effect/huoq", cd: 0.1, volume: 1, maxExist: 3 },
//   人物音效: {
//     url: "https://client-1317648075.cos.ap-guangzhou.myqcloud.com/res/audios/",
//     cd: 0.1,
//     volume: 1,
//     maxExist: 3,
//     isRemote: true,
//   },
// };
enum AudioMgrStatus {
  ENABLE_MUSIC = 1,
  ENABLE_EFFECT = 2,
}

// 播放器状态
enum AudioStatus {
  STOP = 0, // 已停止，过0.1秒在改为ready
  READY = 1, // 准备中，可被pop
  PLAYING = 2, // 播放中
  PAUSE = 3, // 暂停
}

// 播放器
export class AudioItem {
  public config: IConfigAudio;
  public audioSource: AudioSource;
  public status: AudioStatus;
  public volume: number;
  public assetMgr: AssetMgr;
  public startTime: number;

  private _timerId: number = 0;

  public isCd(now: number): boolean {
    if (this.config.cd == -1) {
      return this.status != AudioStatus.STOP;
    }

    if (now - this.startTime < this.config.cd * 1000) {
      return true;
    }
    return false;
  }
  public play(audioClip: AudioClip) {
    if (this.status != AudioStatus.READY) {
      return;
    }
    this.audioSource.volume = this.volume;
    this.audioSource.clip = audioClip;
    this.audioSource.play();
    this.status = AudioStatus.PLAYING;

    this._timerId = setTimeout(() => {
      this.release();
    }, audioClip.getDuration() * 1000 + 100);
  }

  public playOneShot(audioClip: AudioClip) {
    this.audioSource.playOneShot(audioClip, this.volume);

    let ts = audioClip.getDuration();
    // 延迟0.1秒释放
    Sleep(ts + 0.1).then(() => {
      this.release();
    });
  }

  public release() {
    clearTimeout(this._timerId);
    if (!this.audioSource) {
      this.status = AudioStatus.READY;
      return;
    }
    this.audioSource.stop();
    this.status = AudioStatus.READY;
    this.config = null;
  }
}

// 播放器对象池
class AudioItemPool {
  private _nodeEffect: Node;
  private _maxNum: number;

  // 最后pop出的索引
  private _idxLast: number = 0;

  // 已存在的列表
  private _poolList: AudioItem[] = [];

  public constructor(effectNode: Node, maxNum: number) {
    this._maxNum = maxNum;
    this._nodeEffect = effectNode;

    for (let i = 0; i < this._maxNum; i++) {
      let audioSource = this._nodeEffect.addComponent(AudioSource);
      let audioItem = new AudioItem();
      audioItem.audioSource = audioSource;
      audioItem.audioSource.playOnAwake = false;
      audioItem.status = AudioStatus.READY;
      this._poolList.push(audioItem);
    }
  }

  public get poolList() {
    return this._poolList;
  }

  /**
   *
   * @param config 声音配置，用于判断冷却状态
   * @returns
   */
  public pop(config: IConfigAudio): AudioItem {
    const nowTs = Date.now();

    function setPlaying(audioItem: AudioItem, config: IConfigAudio) {
      audioItem.config = config;
      audioItem.startTime = nowTs;
      audioItem.status = AudioStatus.PLAYING;
    }

    // 暂时不用冷却时间控制
    // for (let index = 0; index < this._poolList.length; index++) {
    //   let audioItem = this._poolList[index];
    //   if (audioItem.config?.url == config.url && audioItem.isCd(nowTs)) {
    //     log.warn("音效冷却中", config.url);
    //     return null;
    //   }
    // }

    for (let idx = this._idxLast + 1; idx < this._poolList.length; idx++) {
      if (this._poolList[idx].status == AudioStatus.READY) {
        this._idxLast = idx;

        let audioItem = this._poolList[idx];
        setPlaying(audioItem, config);
        return audioItem;
      }
    }

    for (let idx = 0; idx < this._idxLast; idx++) {
      if (this._poolList[idx].status == AudioStatus.READY) {
        this._idxLast = idx;

        let audioItem = this._poolList[idx];
        setPlaying(audioItem, config);
        return audioItem;
      }
    }

    log.warn("正在播放的音效数量超过最大值，找到最早播放的，停止并换成最新的", this._maxNum);

    let audioItem: AudioItem = this._poolList[0];
    for (let idx = 1; idx < this._poolList.length; idx++) {
      if (this._poolList[idx].startTime < audioItem.startTime) {
        this._idxLast = idx;
        audioItem = this._poolList[idx];
      }
    }

    setPlaying(audioItem, config);
    return audioItem;
  }
}

// 音乐管理器
export class AudioMgr {
  // 单例类
  private static _instance: AudioMgr;

  // 背景音乐
  private _audioSourceMusic: AudioSource;

  private _configMusic: IConfigAudio;

  // 音效的池子管理
  private _effectAudioItemPool: AudioItemPool;
  // 人物语音
  private _voiceAudioItemPool: AudioItemPool;
  // 默认音效 只在点击事件中播放
  private _defaultEffectAudioItemPool: AudioItemPool;
  @persistentDevWith(AudioMgrStatus.ENABLE_EFFECT | AudioMgrStatus.ENABLE_MUSIC)
  private _playerStatus;

  // 创建过程
  private constructor(maxEffectCount: number) {
    // 初始化音效节点
    let nodeEffect: Node = director.getScene().getChildByName("node_effect");
    if (!nodeEffect) {
      nodeEffect = new Node("node_effect");
      director.getScene().addChild(nodeEffect);
      director.addPersistRootNode(nodeEffect);
    }

    // 初始化背景音乐节点
    let nodeMusic: Node = director.getScene().getChildByName("node_music");
    if (!nodeMusic) {
      nodeMusic = new Node("node_music");
      director.getScene().addChild(nodeMusic);
      director.addPersistRootNode(nodeMusic);
      this._audioSourceMusic = nodeMusic.addComponent(AudioSource);
      this._audioSourceMusic.playOnAwake = false;
    }

    this._effectAudioItemPool = new AudioItemPool(nodeEffect, maxEffectCount);
    this._voiceAudioItemPool = new AudioItemPool(nodeEffect, maxEffectCount);
    this._defaultEffectAudioItemPool = new AudioItemPool(nodeEffect, maxEffectCount);
    // log.log(`*************************AudioMgr ${this._playerStatus}*************************`);
  }

  // 单例
  public static get instance(): AudioMgr {
    if (!this._instance) {
      this._instance = new AudioMgr(32);
    }
    return this._instance;
  }

  // 创建音频管理器
  public static create(): AudioMgr {
    let instance = new AudioMgr(32);

    return instance;
  }

  public get audioSourceMusic() {
    return this._audioSourceMusic;
  }

  /**
   *
   * @param musicId 例：AudioName.Effect.哪吒1
   * @param volume 音量
   * @returns
   */
  public playEffect(musicId: number, config: IConfigAudio = null, volume = 1): AudioItem {
    if ((this._playerStatus & AudioMgrStatus.ENABLE_EFFECT) == 0) {
      return;
    }

    let audioConfig = config;
    if (!audioConfig) {
      audioConfig = JsonMgr.instance.jsonList.c_music[musicId];
    }
    if (!audioConfig) {
      log.warn("音效配置不存在", musicId);
      return;
    }

    // 从池子里取出播放器
    let audioItem = this._effectAudioItemPool.pop(audioConfig);
    if (!audioItem) {
      return;
    }

    audioItem.config = audioConfig;
    audioItem.volume = audioConfig.volume * volume;
    musicAsasetMgr.loadAudio(
      BundleEnum.BUNDLE_PUB,
      `audio/${audioItem.config.musicName}`,
      (audioClip: AudioClip, error: any) => {
        if (error) {
          log.error(`音效加载失败 musicId: ${musicId} `, error);
          return;
        }
        audioItem.playOneShot(audioClip);
      }
    );

    // }
    return audioItem;
  }

  /**
   * 根据音效ID获取音效clip
   *
   * @param musicId 音效id
   * @returns 返回音效clip
   */
  public async getClipById(musicId: number): Promise<AudioClip> {
    const audioConfig = JsonMgr.instance.jsonList.c_music[musicId];
    if (!audioConfig) {
      log.error("音效配置不存在", musicId);
      return;
    }
    return new Promise((resolve, reject) => {
      musicAsasetMgr.loadAudio(BundleEnum.BUNDLE_PUB, `audio/${audioConfig.musicName}`, (audioClip: AudioClip) => {
        resolve(audioClip);
      });
    });
  }

  public playDefaultEffect(musicId: number, config: IConfigAudio = null, volume = 1): AudioItem {
    if ((this._playerStatus & AudioMgrStatus.ENABLE_EFFECT) == 0) {
      return null;
    }
    let audioConfig = config;
    if (!audioConfig) {
      audioConfig = JsonMgr.instance.jsonList.c_music[musicId];
    }
    if (!audioConfig) {
      log.warn("音效配置不存在", musicId);
      return null;
    }
    // 从池子里取出播放器
    let audioItem = this._defaultEffectAudioItemPool.pop(audioConfig);
    if (!audioItem) {
      return null;
    }

    audioItem.volume = audioConfig.volume * volume;
    //播放其他音效
    musicAsasetMgr.loadAudio(BundleEnum.BUNDLE_PUB, `audio/${audioItem.config.musicName}`, (audioClip: AudioClip) => {
      audioItem.play(audioClip);
    });

    // }
    return audioItem;
  }

  public playVoice(musicId: number, config: IConfigAudio = null, volume = 1): AudioItem {
    if ((this._playerStatus & AudioMgrStatus.ENABLE_EFFECT) == 0) {
      return null;
    }
    let audioConfig = config;
    if (!audioConfig) {
      audioConfig = JsonMgr.instance.jsonList.c_music[musicId];
    }
    if (!audioConfig) {
      log.warn("音效配置不存在", musicId);
      return null;
    }
    // 从池子里取出播放器
    let audioItem = this._voiceAudioItemPool.pop(audioConfig);
    if (!audioItem) {
      return null;
    }

    audioItem.volume = audioConfig.volume * volume;
    //播放其他音效
    musicAsasetMgr.loadAudio(BundleEnum.BUNDLE_PUB, `audio/${audioItem.config.musicName}`, (audioClip: AudioClip) => {
      audioItem.status = AudioStatus.READY;
      audioItem.play(audioClip);
    });

    // }
    return audioItem;
  }

  public isEffectPlaying(): boolean {
    for (let i = 0; i < this._effectAudioItemPool.poolList.length; i++) {
      let item = this._effectAudioItemPool.poolList[i];
      if (item.status != AudioStatus.STOP) {
        return true;
      }
    }
    return false;
  }

  // 播放背景音乐
  public playMusic(
    musicId: number,
    config: IConfigAudio = null,
    volume = 0.8,
    forceStart: boolean = false,
    isLoop: boolean = true
  ) {
    if ((this._playerStatus & AudioMgrStatus.ENABLE_MUSIC) == 0) {
      return;
    }
    let audioConfig = config;
    if (!audioConfig) {
      audioConfig = JsonMgr.instance.jsonList.c_music[musicId];
    }
    if (audioConfig.musicName == this._configMusic?.musicName) {
      if (!forceStart) {
        return;
      }
    }
    // 如果存在旧的就释放
    if (this._audioSourceMusic.clip) {
      this._audioSourceMusic.stop();
      this._audioSourceMusic.clip = null;
      musicAsasetMgr.releaseOne(BundleEnum.BUNDLE_PUB_SOUND, this._configMusic.url);
    }

    this._configMusic = audioConfig;
    // if (audioConfig.url) {
    //   let name = audioConfig.musicName;
    //   let remotePath = `${audioConfig.url}${audioConfig.musicName}.mp3`;

    //   assetManager.loadRemote(remotePath, (err, clip: AudioClip) => {
    //     if (err) {
    //       log.log(err);
    //     } else {
    //       if (this._configMusic.musicName != name) {
    //         return;
    //       }
    //       this._audioSourceMusic.clip && this._audioSourceMusic.stop();
    //       this._audioSourceMusic.clip = clip;
    //       this._audioSourceMusic.volume = volume * this._configMusic.volume;
    //       this._audioSourceMusic.loop = true;
    //       this._audioSourceMusic.play();
    //     }
    //   });
    // } else {
    let name = audioConfig.musicName;
    musicAsasetMgr.loadAudio(BundleEnum.BUNDLE_PUB, `audio/${this._configMusic.musicName}`, (audioClip: AudioClip) => {
      if (this._configMusic.musicName != name) {
        musicAsasetMgr.releaseOne(BundleEnum.BUNDLE_PUB, name);
        return;
      }
      // if (this._audioSourceMusic.clip) {
      //   this._audioSourceMusic.stop();
      //   this._audioSourceMusic.clip = null;
      // }
      this._audioSourceMusic.clip && this._audioSourceMusic.stop();
      this._audioSourceMusic.clip = audioClip;
      this._audioSourceMusic.volume = volume * this._configMusic.volume;
      this._audioSourceMusic.loop = isLoop;
      this._audioSourceMusic.play();
    });
    // }
  }

  public isEnableEffect() {
    return (this._playerStatus & AudioMgrStatus.ENABLE_EFFECT) != 0;
  }
  public isEnableMusic() {
    return (this._playerStatus & AudioMgrStatus.ENABLE_MUSIC) != 0;
  }
  /**
   * 关闭音效
   */
  public disableEffect() {
    this._playerStatus &= ~AudioMgrStatus.ENABLE_EFFECT;
    this._effectAudioItemPool.poolList.forEach((item) => {
      item.release();
    });
    this._voiceAudioItemPool.poolList.forEach((item) => {
      item.release();
    });
    this._defaultEffectAudioItemPool.poolList.forEach((item) => {
      item.release();
    });
  }
  /**
   * 打开音效
   */
  public enableEffect() {
    this._playerStatus |= AudioMgrStatus.ENABLE_EFFECT;
  }
  /**
   * 关闭背景音乐
   */
  public disableMusic() {
    this._playerStatus &= ~AudioMgrStatus.ENABLE_MUSIC;
    // 如果存在旧的就释放
    if (this._audioSourceMusic.clip) {
      this._audioSourceMusic.stop();
      this._audioSourceMusic.clip = null;
      musicAsasetMgr.releaseOne(BundleEnum.BUNDLE_PUB_SOUND, this._configMusic.url);
      this._configMusic = null;
    }
    // musicAsasetMgr.releaseOne(BundleEnum.BUNDLE_PUB_SOUND, this._configMusic.url);
  }
  /**
   * 打开背景音乐
   */
  public enableMusic(isOpen: boolean = true) {
    this._playerStatus |= AudioMgrStatus.ENABLE_MUSIC;
    if (isOpen == true) {
      this.playMusic(AudioName.Sound.府邸和三界通用);
    }
  }

  public addBtnEffect(node: Node) {
    node.getComponentsInChildren(Button).forEach((btn) => {
      btn.clickEvents;
      btn.node.on(Button.EventType.CLICK, () => {
        AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
      });
    });
  }
}
