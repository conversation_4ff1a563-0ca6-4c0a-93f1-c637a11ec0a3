// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Assist.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { PlayerBaseMessage } from "./Player";

export const protobufPackage = "sim";

/**  */
export interface AssistMessage {
  /** 协助ID */
  id: number;
  /** 被协助人员的人员信息 */
  playerBaseMessage:
    | PlayerBaseMessage
    | undefined;
  /** 协助类型 1-路障 2-怪物 3-npc */
  assistType: number;
  /** 参与协助人员列表 */
  assistMembers: PlayerBaseMessage[];
  /** 协助进度 */
  progress: number;
  /** 协助初始值或最大值 */
  maxProgress: number;
  /** 过期时间 */
  deadline: number;
  /** 描述:assistType = NPC,{"enemyId"："对手ID"，"enemyName":"对手名称"} */
  descMap: { [key: string]: string };
}

export interface AssistMessage_DescMapEntry {
  key: string;
  value: string;
}

/**  */
export interface AssistResponse {
  /** 0-协助成功 1-被协助模块数据不存在(被协助的成功) 2-已经协助过，数据不一致 3-被协助的模块功能暂时关闭，则前端需要重新拉取协助列表 */
  code: number;
  /** 当code=2时，有值前端需要更新 */
  assistMessage:
    | AssistMessage
    | undefined;
  /** 进度 */
  progress: number;
  /** 参与协助人员列表 */
  assistMembers: PlayerBaseMessage[];
  /** 帮助详情 assistType=MONSTER,NPC时，{"win":1或0,"replay":"战斗录像"} */
  detailMap: { [key: string]: string };
}

export interface AssistResponse_DetailMapEntry {
  key: string;
  value: string;
}

function createBaseAssistMessage(): AssistMessage {
  return {
    id: 0,
    playerBaseMessage: undefined,
    assistType: 0,
    assistMembers: [],
    progress: 0,
    maxProgress: 0,
    deadline: 0,
    descMap: {},
  };
}

export const AssistMessage: MessageFns<AssistMessage> = {
  encode(message: AssistMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.playerBaseMessage !== undefined) {
      PlayerBaseMessage.encode(message.playerBaseMessage, writer.uint32(18).fork()).join();
    }
    if (message.assistType !== 0) {
      writer.uint32(24).int32(message.assistType);
    }
    for (const v of message.assistMembers) {
      PlayerBaseMessage.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.progress !== 0) {
      writer.uint32(41).double(message.progress);
    }
    if (message.maxProgress !== 0) {
      writer.uint32(49).double(message.maxProgress);
    }
    if (message.deadline !== 0) {
      writer.uint32(56).int64(message.deadline);
    }
    Object.entries(message.descMap).forEach(([key, value]) => {
      AssistMessage_DescMapEntry.encode({ key: key as any, value }, writer.uint32(66).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssistMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssistMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.playerBaseMessage = PlayerBaseMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.assistType = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.assistMembers.push(PlayerBaseMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.progress = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.maxProgress = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.deadline = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          const entry8 = AssistMessage_DescMapEntry.decode(reader, reader.uint32());
          if (entry8.value !== undefined) {
            message.descMap[entry8.key] = entry8.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AssistMessage>, I>>(base?: I): AssistMessage {
    return AssistMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssistMessage>, I>>(object: I): AssistMessage {
    const message = createBaseAssistMessage();
    message.id = object.id ?? 0;
    message.playerBaseMessage = (object.playerBaseMessage !== undefined && object.playerBaseMessage !== null)
      ? PlayerBaseMessage.fromPartial(object.playerBaseMessage)
      : undefined;
    message.assistType = object.assistType ?? 0;
    message.assistMembers = object.assistMembers?.map((e) => PlayerBaseMessage.fromPartial(e)) || [];
    message.progress = object.progress ?? 0;
    message.maxProgress = object.maxProgress ?? 0;
    message.deadline = object.deadline ?? 0;
    message.descMap = Object.entries(object.descMap ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseAssistMessage_DescMapEntry(): AssistMessage_DescMapEntry {
  return { key: "", value: "" };
}

export const AssistMessage_DescMapEntry: MessageFns<AssistMessage_DescMapEntry> = {
  encode(message: AssistMessage_DescMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssistMessage_DescMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssistMessage_DescMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AssistMessage_DescMapEntry>, I>>(base?: I): AssistMessage_DescMapEntry {
    return AssistMessage_DescMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssistMessage_DescMapEntry>, I>>(object: I): AssistMessage_DescMapEntry {
    const message = createBaseAssistMessage_DescMapEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseAssistResponse(): AssistResponse {
  return { code: 0, assistMessage: undefined, progress: 0, assistMembers: [], detailMap: {} };
}

export const AssistResponse: MessageFns<AssistResponse> = {
  encode(message: AssistResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.code !== 0) {
      writer.uint32(8).int32(message.code);
    }
    if (message.assistMessage !== undefined) {
      AssistMessage.encode(message.assistMessage, writer.uint32(18).fork()).join();
    }
    if (message.progress !== 0) {
      writer.uint32(25).double(message.progress);
    }
    for (const v of message.assistMembers) {
      PlayerBaseMessage.encode(v!, writer.uint32(34).fork()).join();
    }
    Object.entries(message.detailMap).forEach(([key, value]) => {
      AssistResponse_DetailMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssistResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssistResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.code = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.assistMessage = AssistMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.progress = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.assistMembers.push(PlayerBaseMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = AssistResponse_DetailMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.detailMap[entry5.key] = entry5.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AssistResponse>, I>>(base?: I): AssistResponse {
    return AssistResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssistResponse>, I>>(object: I): AssistResponse {
    const message = createBaseAssistResponse();
    message.code = object.code ?? 0;
    message.assistMessage = (object.assistMessage !== undefined && object.assistMessage !== null)
      ? AssistMessage.fromPartial(object.assistMessage)
      : undefined;
    message.progress = object.progress ?? 0;
    message.assistMembers = object.assistMembers?.map((e) => PlayerBaseMessage.fromPartial(e)) || [];
    message.detailMap = Object.entries(object.detailMap ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseAssistResponse_DetailMapEntry(): AssistResponse_DetailMapEntry {
  return { key: "", value: "" };
}

export const AssistResponse_DetailMapEntry: MessageFns<AssistResponse_DetailMapEntry> = {
  encode(message: AssistResponse_DetailMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AssistResponse_DetailMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssistResponse_DetailMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<AssistResponse_DetailMapEntry>, I>>(base?: I): AssistResponse_DetailMapEntry {
    return AssistResponse_DetailMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssistResponse_DetailMapEntry>, I>>(
    object: I,
  ): AssistResponse_DetailMapEntry {
    const message = createBaseAssistResponse_DetailMapEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
