import { Asset<PERSON>anager, JsonAsset } from "cc";
import ResMgr from "../../lib/common/ResMgr";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { JsonConst } from "db://assets/GameScrpit/game/JsonConst";
import {
  IConfigAttribute,
  IConfigCopyMain,
  IConfigGuidePath,
  IConfigGuideTalk,
  IConfigGuideV2,
  IConfigItem,
  IConfigJump,
  IConfigLeaderSkin,
  IConfigSpineShow,
  IConfigSystemOpen,
} from "db://assets/GameScrpit/game/JsonDefine";
import { ResHelper } from "db://assets/platform/src/ResHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export enum Shop_Type_Enum {
  Luck_Hero_Shop = 1,
  Luck_XianYou_Shop = 2,
}

export class JsonMgr {
  private static _instance: JsonMgr;
  public static get instance(): JsonMgr {
    if (!JsonMgr._instance) {
      JsonMgr._instance = new JsonMgr();

      // 先加载语言包
      ResHelper.loadBundle(BundleEnum.BUNDLE_COMMON_JSON, (bundle: AssetManager.Bundle) => {
        bundle.load("json/c_errorcode", (err, data: JsonAsset) => {
          JsonMgr._instance._jsonList.c_errorcode = data.json;
        });
      });
    }
    return JsonMgr._instance;
  }

  private _jsonList: JsonConst = new JsonConst();
  public get jsonList() {
    return this._jsonList;
  }

  /**
   * 加载文件
   */
  public async loadConfig() {
    let self = this;
    let jsonBundle = (await ResMgr.getBundleSync(BundleEnum.BUNDLE_COMMON_JSON)) as AssetManager.Bundle;
    await new Promise((resolve, reject) => {
      jsonBundle.loadDir("json", (error: any, texture: any): any => {
        if (error) {
          log.error("配置获取错误");
          reject(error);
          return;
        } else {
          for (let i = 0; i < texture.length; i++) {
            const data = texture[i].json;
            this.initDefault(data);
            self._jsonList[texture[i].name] = data;

            if (texture[i].name == "c_copyMain") {
              let index = 0;
              for (let j in data) {
                data[j]["index"] = index;
                index++;
              }
            }
          }
          resolve(1);
        }
      });
    });
  }

  public initDefault(rs) {
    const defaultCfg = rs[-1];
    const keys = Object.keys(rs);

    if (!defaultCfg) {
      return;
    }

    const fieldList = Object.keys(defaultCfg);
    for (let i in keys) {
      const key = Number(keys[i]);
      if (key == -1) {
        continue;
      }
      let cfgNow = rs[key];

      for (let j in fieldList) {
        const field = fieldList[j];
        if (!cfgNow[field]) {
          cfgNow[field] = defaultCfg[field];
        }
      }
    }

    delete rs["-1"];
  }

  public async loadConfigOne(jsonFileName: string) {
    let rs = await ResHelper.preLoadResSync(BundleEnum.BUNDLE_COMMON_JSON, "json/" + jsonFileName);

    // 默认值
    this.initDefault(rs.json);

    this._jsonList[jsonFileName] = rs.json;
  }

  /**根据道具表里的背包类型来获取数据 */
  public getItemBagTypeList(type2: number): Array<IConfigItem> {
    let arr: Array<IConfigItem> = [];
    let c_item: { [key: number]: IConfigItem } = this.jsonList.c_item;
    let list = Object.keys(c_item);
    for (let i = 0; i < list.length; i++) {
      let data: IConfigItem = c_item[list[i]];
      for (let j = 0; j < data.type2List.length; j++) {
        if (data.type2List[j] == type2) {
          arr.push(data);
        }
      }
    }
    return arr;
  }

  /** 获取道具配置 */
  public getConfigItem(id: number): IConfigItem {
    let rs = this.jsonList.c_item[id];
    if (!rs) {
      log.error("没有找到id为 " + id + " 的道具配置");
    }
    return rs;
  }

  public getConfigCopyMain(id: number): IConfigCopyMain {
    let rs = JsonMgr.instance.jsonList.c_copyMain[id];
    if (!rs) {
      if (id != 0) {
        log.error("关卡id不存在： " + id + " 默认返回1030001");
      }
      rs = JsonMgr.instance.jsonList.c_copyMain[1030001];
    }
    return rs;
  }

  public getConfigAttribute(id: number): IConfigAttribute {
    return this.jsonList.c_attribute[id];
  }

  // 解锁配置
  public getConfigSystemOpen(id: number): IConfigSystemOpen {
    return this.jsonList.c_systemOpen[id];
  }

  public getConfigSpineShow(id: number): IConfigSpineShow {
    return JsonMgr.instance.jsonList.c_spineShow[id];
  }

  /**
   * 获取引导配置信息
   *
   * @param typeId 类型ID
   * @returns 引导配置列表
   */
  public getConfigGuidePathList(typeId: number): IConfigGuidePath[] {
    let values: IConfigGuidePath[] = Object.values(this.jsonList.c_guidePath);

    let list = values.filter((item) => {
      return item.typeId == typeId;
    });

    return list.sort((a, b) => {
      return a.id - b.id;
    });
  }

  /**
   * 获取跳转配置
   * @param id 跳转ID
   * @returns
   */
  public getConfigJump(id: number): IConfigJump {
    return this.jsonList.c_jump[id];
  }

  /** 主角皮肤 */
  public getConfigLeaderSkin(id: number): IConfigLeaderSkin {
    return this.jsonList.c_leaderSkin[id];
  }

  // 获取引导对话
  public getConfigGuideTalkList(type: number): IConfigGuideTalk[] {
    let configTalkList = [];
    Object.keys(JsonMgr.instance.jsonList.c_guideTalk).forEach((key) => {
      let talkConfig = JsonMgr.instance.jsonList.c_guideTalk[Number(key)];
      if (talkConfig.type == type) {
        configTalkList.push(talkConfig);
      }
    });
    return configTalkList;
  }

  // 获取引导配置
  public getConfigGuideV2List(from: number, to: number): IConfigGuideV2[] {
    let configV2List = [];
    Object.keys(JsonMgr.instance.jsonList.c_guideV2).forEach((key) => {
      let cfg = JsonMgr.instance.jsonList.c_guideV2[Number(key)];
      if (cfg.id >= from && cfg.id <= to) {
        configV2List.push(cfg);
      }
    });
    return configV2List;
  }

  public getDefault<T>(map: { [key: number]: T }): T {
    const keys = Object.keys(map);
    return keys.length > 0 ? map[Number(keys[0])] : null;
  }
}
