import MsgEnum from "../../game/event/MsgEnum";
import { GameData } from "../../game/GameData";
import { GameDirector } from "../../game/GameDirector";
import data from "../../lib/data/data";
import MsgMgr from "../../lib/event/MsgMgr";
import { AfterModule } from "../after/AfterModule";
import { DayActivityModule } from "../day/DayActivityModule";
import { DisciplinesModule } from "../disciplines/DisciplinesModule";
import { FractureModule } from "../fracture/FractureModule";
import { FrbpModule } from "../frbp/FrbpModule";
import { FundModule } from "../fund/FundModule";
import { HdShouChongModule } from "../hd_shouchong/HdShouChongModule";
import { HdTiaoJianLiBaoModule } from "../hd_tiaojianlibao/HdTiaoJianLiBaoModule";
import { HdVipCardModule } from "../hd_vipcard/HdVipCardModule";
import { PlayerEvent } from "../player/PlayerEvent";
import { SonhaiModule } from "../sonhai/SonhaiModule";
import { TimeTaskModule } from "../time_task/TimeTaskModule";
import { VipModule } from "../vip_haoli/src/VipModule";
import { ActivityApi } from "./ActivityApi";
import { ActivityConfig } from "./ActivityConfig";
import { ActivityID } from "./ActivityConstant";
import { ActivityData } from "./ActivityData";
import { ActivityRoute } from "./ActivityRoute";
import { ActivityService } from "./ActivityService";
import { ActivitySubscriber } from "./ActivitySubscriber";
import { ActivityViewModel } from "./ActivityViewModel";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export class ActivityModule extends data {
  private constructor() {
    super();
  }

  public static get instance(): ActivityModule {
    if (!GameData.instance.ActivityModule) {
      GameData.instance.ActivityModule = new ActivityModule();
    }
    return GameData.instance.ActivityModule;
  }
  private _data = new ActivityData();
  private _api = new ActivityApi();
  private _config = new ActivityConfig();
  private _viewModel = new ActivityViewModel();
  private _service = new ActivityService();
  private _route = new ActivityRoute();
  private _subscriber = new ActivitySubscriber();

  private _activityList: { [key: number]: any } = {
    [ActivityID.TIJIANLIBAO]: HdTiaoJianLiBaoModule.instance,
    [ActivityID.FUNDID_1]: FundModule.instance,
    [ActivityID.FUNDID_2]: FundModule.instance,
    [ActivityID.FUNDID_3]: FundModule.instance,
    [ActivityID.FUNDID_4]: FundModule.instance,
    [ActivityID.FUNDID_5]: FundModule.instance,
    [ActivityID.AFTERID]: AfterModule.instance,
    [ActivityID.DAYCHARRE]: DayActivityModule.instance,
    [ActivityID.SONHAI]: SonhaiModule.instance,
    [ActivityID.XIUXING]: DisciplinesModule.instance,
    [ActivityID.FRACTURE]: FractureModule.instance,
    [ActivityID.FRBP]: FrbpModule.instance,
    [ActivityID.TIME_TASK_1]: TimeTaskModule.instance,
    [ActivityID.TIME_TASK_2]: TimeTaskModule.instance,
  };
  private _activityInit: { [key: number]: boolean } = {};
  private _initModuleMap: Map<any, boolean> = new Map();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public static get service() {
    return this.instance._service;
  }
  private openActivity() {
    let activityList = Object.keys(this._activityList).map(Number);
    for (let i = 0; i < activityList.length; i++) {
      let isInit = this._initModuleMap.get(this._activityList[activityList[i]]) ?? false;
      if (GameDirector.instance.isSystemOpen(activityList[i]) && !isInit) {
        this._activityList[activityList[i]].init();
        this._initModuleMap.set(this._activityList[activityList[i]], true);
      }
    }
  }

  public async init(data?: any, completedCallback?: Function) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    // 初始化数据模块
    this._data = new ActivityData();
    this._api = new ActivityApi();
    this._config = new ActivityConfig();
    this._viewModel = new ActivityViewModel();
    this._service = new ActivityService();
    this._route = new ActivityRoute();
    this._subscriber = new ActivitySubscriber();
    MsgMgr.off(PlayerEvent.ON_UIPLAYER_DATA_UPDATE, this.openActivity, this);
    MsgMgr.off(MsgEnum.ON_ACTIVITY_UPDATE, this.openActivity, this);
    MsgMgr.off(MsgEnum.ON_RIGHT_UPDATE, this.openActivity, this);

    MsgMgr.on(MsgEnum.ON_RIGHT_UPDATE, this.openActivity, this);
    MsgMgr.on(MsgEnum.ON_ACTIVITY_UPDATE, this.openActivity, this);
    MsgMgr.on(PlayerEvent.ON_UIPLAYER_DATA_UPDATE, this.openActivity, this);
    await ActivityModule.service.httpGetAllActivity();

    this._route.init();
    this._subscriber.register();
    HdShouChongModule.instance.init();
    HdVipCardModule.instance.init();
    VipModule.instance.init();

    ActivityModule.service
      .init()
      .then(() => {
        completedCallback && completedCallback();
      })
      .catch(() => {
        completedCallback && completedCallback();
      });
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
