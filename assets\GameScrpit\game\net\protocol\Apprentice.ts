// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Apprentice.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface ApprenticeGetRequestMessage {
  random: boolean;
  rank: number;
}

/**  */
export interface ApprenticeMessage {
  id: number;
  /** 用户 */
  userId: number;
  /** 弟子昵称 */
  name: string;
  /** 委任师傅 */
  friendId: number;
  /** 天资/机敏 */
  intelligenceId: number;
}

/**  */
export interface ApprenticeSlotDesMessage {
  /** 槽位的第几个位置 从0开始 */
  rank: number;
  /** 槽位的徒弟id */
  apprenticeIdList: number[];
}

/**  */
export interface ApprenticeTrainChangeMessage {
  /**  */
  apprenticeTrainMessage:
    | ApprenticeTrainMessage
    | undefined;
  /**  */
  apprenticeMessage: ApprenticeMessage | undefined;
}

/**  */
export interface ApprenticeTrainMessage {
  id: number;
  /** 用户 */
  userId: number;
  /** key就是槽位索引，从0开始 */
  slotDesMap: { [key: number]: ApprenticeSlotDesMessage };
}

export interface ApprenticeTrainMessage_SlotDesMapEntry {
  key: number;
  value: ApprenticeSlotDesMessage | undefined;
}

function createBaseApprenticeGetRequestMessage(): ApprenticeGetRequestMessage {
  return { random: false, rank: 0 };
}

export const ApprenticeGetRequestMessage: MessageFns<ApprenticeGetRequestMessage> = {
  encode(message: ApprenticeGetRequestMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.random !== false) {
      writer.uint32(8).bool(message.random);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApprenticeGetRequestMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApprenticeGetRequestMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.random = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ApprenticeGetRequestMessage>, I>>(base?: I): ApprenticeGetRequestMessage {
    return ApprenticeGetRequestMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApprenticeGetRequestMessage>, I>>(object: I): ApprenticeGetRequestMessage {
    const message = createBaseApprenticeGetRequestMessage();
    message.random = object.random ?? false;
    message.rank = object.rank ?? 0;
    return message;
  },
};

function createBaseApprenticeMessage(): ApprenticeMessage {
  return { id: 0, userId: 0, name: "", friendId: 0, intelligenceId: 0 };
}

export const ApprenticeMessage: MessageFns<ApprenticeMessage> = {
  encode(message: ApprenticeMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.userId !== 0) {
      writer.uint32(16).int64(message.userId);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.friendId !== 0) {
      writer.uint32(32).int64(message.friendId);
    }
    if (message.intelligenceId !== 0) {
      writer.uint32(40).int32(message.intelligenceId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApprenticeMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApprenticeMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.friendId = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.intelligenceId = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ApprenticeMessage>, I>>(base?: I): ApprenticeMessage {
    return ApprenticeMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApprenticeMessage>, I>>(object: I): ApprenticeMessage {
    const message = createBaseApprenticeMessage();
    message.id = object.id ?? 0;
    message.userId = object.userId ?? 0;
    message.name = object.name ?? "";
    message.friendId = object.friendId ?? 0;
    message.intelligenceId = object.intelligenceId ?? 0;
    return message;
  },
};

function createBaseApprenticeSlotDesMessage(): ApprenticeSlotDesMessage {
  return { rank: 0, apprenticeIdList: [] };
}

export const ApprenticeSlotDesMessage: MessageFns<ApprenticeSlotDesMessage> = {
  encode(message: ApprenticeSlotDesMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rank !== 0) {
      writer.uint32(8).int32(message.rank);
    }
    writer.uint32(18).fork();
    for (const v of message.apprenticeIdList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApprenticeSlotDesMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApprenticeSlotDesMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.apprenticeIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.apprenticeIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ApprenticeSlotDesMessage>, I>>(base?: I): ApprenticeSlotDesMessage {
    return ApprenticeSlotDesMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApprenticeSlotDesMessage>, I>>(object: I): ApprenticeSlotDesMessage {
    const message = createBaseApprenticeSlotDesMessage();
    message.rank = object.rank ?? 0;
    message.apprenticeIdList = object.apprenticeIdList?.map((e) => e) || [];
    return message;
  },
};

function createBaseApprenticeTrainChangeMessage(): ApprenticeTrainChangeMessage {
  return { apprenticeTrainMessage: undefined, apprenticeMessage: undefined };
}

export const ApprenticeTrainChangeMessage: MessageFns<ApprenticeTrainChangeMessage> = {
  encode(message: ApprenticeTrainChangeMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.apprenticeTrainMessage !== undefined) {
      ApprenticeTrainMessage.encode(message.apprenticeTrainMessage, writer.uint32(10).fork()).join();
    }
    if (message.apprenticeMessage !== undefined) {
      ApprenticeMessage.encode(message.apprenticeMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApprenticeTrainChangeMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApprenticeTrainChangeMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.apprenticeTrainMessage = ApprenticeTrainMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.apprenticeMessage = ApprenticeMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ApprenticeTrainChangeMessage>, I>>(base?: I): ApprenticeTrainChangeMessage {
    return ApprenticeTrainChangeMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApprenticeTrainChangeMessage>, I>>(object: I): ApprenticeTrainChangeMessage {
    const message = createBaseApprenticeTrainChangeMessage();
    message.apprenticeTrainMessage =
      (object.apprenticeTrainMessage !== undefined && object.apprenticeTrainMessage !== null)
        ? ApprenticeTrainMessage.fromPartial(object.apprenticeTrainMessage)
        : undefined;
    message.apprenticeMessage = (object.apprenticeMessage !== undefined && object.apprenticeMessage !== null)
      ? ApprenticeMessage.fromPartial(object.apprenticeMessage)
      : undefined;
    return message;
  },
};

function createBaseApprenticeTrainMessage(): ApprenticeTrainMessage {
  return { id: 0, userId: 0, slotDesMap: {} };
}

export const ApprenticeTrainMessage: MessageFns<ApprenticeTrainMessage> = {
  encode(message: ApprenticeTrainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.userId !== 0) {
      writer.uint32(16).int64(message.userId);
    }
    Object.entries(message.slotDesMap).forEach(([key, value]) => {
      ApprenticeTrainMessage_SlotDesMapEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApprenticeTrainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApprenticeTrainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.userId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = ApprenticeTrainMessage_SlotDesMapEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.slotDesMap[entry3.key] = entry3.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ApprenticeTrainMessage>, I>>(base?: I): ApprenticeTrainMessage {
    return ApprenticeTrainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApprenticeTrainMessage>, I>>(object: I): ApprenticeTrainMessage {
    const message = createBaseApprenticeTrainMessage();
    message.id = object.id ?? 0;
    message.userId = object.userId ?? 0;
    message.slotDesMap = Object.entries(object.slotDesMap ?? {}).reduce<{ [key: number]: ApprenticeSlotDesMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = ApprenticeSlotDesMessage.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseApprenticeTrainMessage_SlotDesMapEntry(): ApprenticeTrainMessage_SlotDesMapEntry {
  return { key: 0, value: undefined };
}

export const ApprenticeTrainMessage_SlotDesMapEntry: MessageFns<ApprenticeTrainMessage_SlotDesMapEntry> = {
  encode(message: ApprenticeTrainMessage_SlotDesMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int32(message.key);
    }
    if (message.value !== undefined) {
      ApprenticeSlotDesMessage.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ApprenticeTrainMessage_SlotDesMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseApprenticeTrainMessage_SlotDesMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ApprenticeSlotDesMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ApprenticeTrainMessage_SlotDesMapEntry>, I>>(
    base?: I,
  ): ApprenticeTrainMessage_SlotDesMapEntry {
    return ApprenticeTrainMessage_SlotDesMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApprenticeTrainMessage_SlotDesMapEntry>, I>>(
    object: I,
  ): ApprenticeTrainMessage_SlotDesMapEntry {
    const message = createBaseApprenticeTrainMessage_SlotDesMapEntry();
    message.key = object.key ?? 0;
    message.value = (object.value !== undefined && object.value !== null)
      ? ApprenticeSlotDesMessage.fromPartial(object.value)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
