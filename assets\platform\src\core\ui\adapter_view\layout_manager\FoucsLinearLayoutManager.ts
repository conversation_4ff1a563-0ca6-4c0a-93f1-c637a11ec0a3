import { _decorator, Node, UITransform, v3 } from "cc";
import { Rect } from "../../../../lib/utils/Rect";
import { LayoutManager } from "../LayoutManager";
import { ViewHolder } from "../ListAdapter";
const { ccclass, property } = _decorator;
type onFoucsChange = (position: number) => void;
type onFoucsScrollComplete = (foucsPosition: number) => void;
@ccclass("FoucsLinearLayoutManager")
export class FoucsLinearLayoutManager extends LayoutManager {
  private _foucsPosition: number = 0;
  private _updateLayout: boolean = true;
  private _lastFoucsNode: Node;
  private _space: number = 20;
  private _foucsSpace: number = 60;
  private _onFoucsChange: onFoucsChange;
  private _onScrollComplete: onFoucsScrollComplete;

  set onFoucsChange(onFoucsChange: onFoucsChange) {
    this._onFoucsChange = onFoucsChange;
  }
  set onFoucsScrollComplete(onScrollComplete: onFoucsScrollComplete) {
    this._onScrollComplete = onScrollComplete;
  }

  protected onAttach(): void {
    super.onAttach();
    this.viewholders.wrapAroundIndex = true;
  }
  protected onLayout(changed: boolean, lastRect: Rect, offsetX: number, offsetY: number, isFling: boolean): Rect {
    for (let i = 0; i < this.children.size(); i++) {
      let child = this.children.get(i);
      let width = child.getComponent(UITransform).width;
      let height = child.getComponent(UITransform).height;
      let anchorY = child.getComponent(UITransform).anchorY;
      let anchorX = child.getComponent(UITransform).anchorX;

      let childX = child.position.x + offsetX;

      let speedAddMax = width / 2 + this._foucsSpace;
      if (Math.abs(child.position.x) < speedAddMax) {
        let ratio = (this._foucsSpace * 2 + width) / (this._space * 2 + width);
        childX = child.position.x + offsetX * ratio;

        if (Math.abs(childX) > speedAddMax) {
          // console.log(`childX: ${childX} max:${speedAddMax} offsetx:${offsetX}`);
          if (childX > 0) {
            let left = childX - speedAddMax;
            childX = speedAddMax + left / ratio;
          } else {
            let left = childX + speedAddMax;
            childX = -speedAddMax + left / ratio;
          }
        }
        // console.log(`Final childX: ${childX}`);
      } else if (Math.abs(childX) < speedAddMax) {
        // console.log(`start childX: ${childX} speedAddMax:${speedAddMax} child.position.x:${child.position.x}`);
        let ratio = (this._foucsSpace * 2 + width) / (this._space * 2 + width);
        if (offsetX < 0) {
          let left = offsetX + (child.position.x - speedAddMax);
          // console.log(`----left: ${left} offsetX: ${offsetX}`);
          childX = speedAddMax + left * ratio;
        } else {
          let left = offsetX + (child.position.x + speedAddMax);
          // console.log(`++++left: ${left} offsetX: ${offsetX}`);
          childX = -speedAddMax + left * ratio;
        }
        // console.log(`childX: ${childX}`);
      }
      let y = 0; //this.getBorderTop() - height * (1 - anchorY);
      this.updateChildPosition(child, childX, y);
    }
    if (this.children.size() == 0 || this._updateLayout) {
      this._lastFoucsNode = null;
      this._updateLayout = false;
      let newNode = this.addNode(this._foucsPosition);
      if (newNode) {
        let width = newNode.getComponent(UITransform).width;
        let height = newNode.getComponent(UITransform).height;
        let anchorX = newNode.getComponent(UITransform).anchorX;
        let anchorY = newNode.getComponent(UITransform).anchorY;
        let x = 0; //anchorX * width; //this.getBorderLeft() +
        let y = 0; //this.getBorderTop() - height * (1 - anchorY);
        this.updateChildPosition(newNode, x, y);
      } else {
        return lastRect;
      }
      let last = this.addNodeToHeader();
      if (last) {
        let width = last.getComponent(UITransform).width;
        let anchorX = last.getComponent(UITransform).anchorX;
        let left = this.getNodeLeft(newNode);
        let x = left - (width * (1 - anchorX) + this._foucsSpace);
        let y = 0;
        this.updateChildPosition(last, x, y);
      }
      let next = this.addNodeToTail();
      if (next) {
        let width = next.getComponent(UITransform).width;
        let anchorX = next.getComponent(UITransform).anchorX;
        let right = this.getNodeRight(newNode);
        let x = right + (width * anchorX + this._foucsSpace);
        let y = 0;
        this.updateChildPosition(next, x, y);
      }
    }
    let left = this.getNodeLeft(this.children.peekFront());
    while (left > this.getBorderLeft()) {
      //
      let newNode = this.addNodeToHeader();
      let width = newNode.getComponent(UITransform).width;
      let height = newNode.getComponent(UITransform).height;
      let anchorX = newNode.getComponent(UITransform).anchorX;
      let anchorY = newNode.getComponent(UITransform).anchorY;
      let x = left - (width * anchorX + this._space);
      let y = 0; //this.getBorderTop() - height * (1 - anchorY);
      this.updateChildPosition(newNode, x, y);

      left = this.getNodeLeft(newNode);
    }
    let right = this.getNodeRight(this.children.peekRear());
    while (right < this.getBorderRight()) {
      //
      let newNode = this.addNodeToTail();
      let width = newNode.getComponent(UITransform).width;
      let height = newNode.getComponent(UITransform).height;
      let anchorX = newNode.getComponent(UITransform).anchorX;
      let anchorY = newNode.getComponent(UITransform).anchorY;
      let x = right + (width * (1 - anchorX) + this._space);
      let y = 0; //this.getBorderTop() - height * (1 - anchorY);
      this.updateChildPosition(newNode, x, y);
      right = this.getNodeRight(newNode);
    }
    // for (let i = 0; i < this.children.size(); i++) {
    //   let child = this.children.get(i);
    //   if (child == this._lastFoucsNode) {
    //     child.setSiblingIndex(this.children.size() - 1);
    //     continue;
    //   }
    //   if (child.position.x < this._lastFoucsNode.position.x) {
    //     child.setSiblingIndex(i);
    //   } else {
    //     child.setSiblingIndex(0);
    //   }
    // }

    return new Rect(
      this.getBorderLeft(),
      this.getNodeTop(this.children.peekFront()),
      this.getBorderRight(),
      this.getNodeBottom(this.children.peekRear())
    );
  }
  protected onUpdateChildPosition(child: Node, x: number, y: number): void {
    let realX = x;

    let realY = y; // - 160 * Math.tan(Math.abs(x / this.getBorderLeft()));

    // child.getComponent(UITransform).width = 177 * (1 - Math.abs(x / this.getBorderLeft()));
    // let angle = 60 * (x / this.getBorderLeft());
    // child.angle = angle;

    let width = child.getComponent(UITransform).width;
    let ratio = Math.min(1, Math.abs(x / (width + this._foucsSpace)));
    let scale = 1.5 * (1 - ratio) + 1 * ratio;
    child.setScale(v3(scale, scale, 1));
    // console.log("onUpdateChildPosition", this._lastFoucsNode?.position.x);
    if (!this._lastFoucsNode || Math.abs(child.position.x) < Math.abs(this._lastFoucsNode.position.x)) {
      this._lastFoucsNode = child;
      this._onFoucsChange && this._onFoucsChange(child.getComponent(ViewHolder).position);
      // this._lastFoucsNode.setScale(v3(1.5, 1.5, 1));
    }
    super.onUpdateChildPosition(child, realX, realY);
  }
  protected onScrollComplete(): void {
    super.onScrollComplete();
    console.log("onScrollComplete");

    if (!this._lastFoucsNode) {
      return;
    }
    if (this._lastFoucsNode.position.x != 0) {
      let width = this._lastFoucsNode.getComponent(UITransform).width;
      let ratio = (this._foucsSpace * 2 + width) / (this._space * 2 + width);
      let x = -this._lastFoucsNode.position.x / ratio;
      this.layoutChildrenOffset(x, 0);
    }
    this._onScrollComplete && this._onScrollComplete(this._lastFoucsNode.getComponent(ViewHolder).position);
  }
  protected onFlingComplete(): void {
    super.onFlingComplete();
    console.log("onFlingComplete");

    if (!this._lastFoucsNode) {
      return;
    }
    // console.log("onFlingComplete", this._lastFoucsNode.position.x);
    if (this._lastFoucsNode.position.x != 0) {
      let width = this._lastFoucsNode.getComponent(UITransform).width;
      let ratio = (this._foucsSpace * 2 + width) / (this._space * 2 + width);
      let x = -this._lastFoucsNode.position.x / ratio;
      this.scroller.scrollBy(x, 0);
    }
  }

  public foucsTo(position: number, viewHolder?: Node) {
    if (!this.children) {
      return;
    }
    let node = null;
    if (viewHolder) {
      node = viewHolder;
    } else {
      for (let i = 0; i < this.children.size(); i++) {
        let child = this.children.get(i);
        if (child.getComponent(ViewHolder).position == position) {
          node = child;
          break;
        }
      }
    }
    if (node) {
      let x = 0;
      if (node.position.x < 0) {
        x = -(node.position.x + (this._foucsSpace - this._space));
      } else {
        x = -(node.position.x - (this._foucsSpace - this._space));
      }
      this.scroller.scrollBy(x, 0, true);
    }
  }
}
