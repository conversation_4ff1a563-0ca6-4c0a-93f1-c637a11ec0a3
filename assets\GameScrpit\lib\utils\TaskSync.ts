import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);

/**
 * 同步任务配置
 */
export interface TaskConfig {
  /** 任务名称 */
  name: string;
  /** 任务函数 */
  task: Function;
  /** 调用目标 */
  target?: any;
  /** 参数 */
  args?: any[];
  /** 依赖任务列表 */
  依赖任务列表?: string[];
}

/**
 * 同步任务
 * 用法：
 * let taskSync = new TaskSync(this.onEnd, this);
 * taskSync.addTask(this.task1, 1);
 */
export class TaskSync {
  /** 完成后的任务 */
  private _completeCfg: TaskConfig;

  /** 任务列表 */
  private _taskCfgList: TaskConfig[] = [];

  /** 失败任务 */
  private _errorCfg: TaskConfig;

  /** 默认target */
  private _defaultTarget: any;

  /** 正在进行任务列表 */
  private 正在进行任务列表: string[] = [];

  /** 已完成任务列表 */
  private 已完成任务列表: string[] = [];

  /** 任务数 */
  private 完成任务数: number = 0;

  /** 任务超时时间 单位：秒 */
  private _timeout: number = 0;

  /** 任务开始时间 单位：秒 */
  private _timeStart: number = 0;

  public constructor(defaultTarget: any, completeCfg: TaskConfig, errorCfg: TaskConfig, timeout: number) {
    this._completeCfg = completeCfg;
    this._errorCfg = errorCfg;
    this._timeout = timeout;
    this._defaultTarget = defaultTarget;
    this._completeCfg.task.bind(this._completeCfg.target || this._defaultTarget);
    this._errorCfg.task.bind(this._errorCfg.target || this._defaultTarget);
  }

  public addTask(taskCfg: TaskConfig) {
    taskCfg.args = taskCfg.args || [];
    taskCfg.task.bind(taskCfg.target || this._defaultTarget);
    this._taskCfgList.push(taskCfg);
  }

  private _checkDependTask(taskCfg: TaskConfig) {
    if (taskCfg.依赖任务列表) {
      for (let i = 0; i < taskCfg.依赖任务列表.length; i++) {
        const dependTaskName = taskCfg.依赖任务列表[i];
        if (!this.已完成任务列表.includes(dependTaskName)) {
          return false;
        }
      }
    }
    return true;
  }

  private _loopTask() {
    for (let i = 0; i < this._taskCfgList.length; i++) {
      if (this.正在进行任务列表.includes(this._taskCfgList[i].name)) {
        continue;
      }

      // 检查依赖任务
      if (!this._checkDependTask(this._taskCfgList[i])) {
        continue;
      }

      log.info("任务开始", `${new Date().getTime() - this._timeStart} ${this._taskCfgList[i].name}`);
      this.正在进行任务列表.push(this._taskCfgList[i].name);
      this._taskCfgList[i].task
        .bind(this._taskCfgList[i].target || this._defaultTarget)(...this._taskCfgList[i].args)
        .then(() => {
          log.info("任务完成", `${new Date().getTime() - this._timeStart} ${this._taskCfgList[i].name}`);
          this.完成任务数++;
          this.已完成任务列表.push(this._taskCfgList[i].name);

          if (this.完成任务数 >= this._taskCfgList.length) {
            log.info("所有任务完成", `${new Date().getTime() - this._timeStart}`);
            this._completeCfg.task.bind(this._completeCfg.target || this._defaultTarget)(this._completeCfg.args);
          } else {
            this._loopTask();
          }
        })
        .catch((error: any) => {
          log.error("任务失败", this._taskCfgList[i].name, error);
          this._errorCfg.task.bind(this._errorCfg.target || this._defaultTarget)(error, this._errorCfg.args);
        });
    }
  }

  public async start() {
    this._timeStart = new Date().getTime();

    this._loopTask();

    setTimeout(() => {
      if (this.完成任务数 < this._taskCfgList.length) {
        log.error("任务超时", `${this.完成任务数}/${this._taskCfgList.length}`);
        this._errorCfg.task(new Error("任务超时"), this._errorCfg.args);
      }
    }, this._timeout * 1000);
  }
}
