{"1": {"id": 1, "levelMax": 100, "breakCostList": [[1053, 1], [1051, 1], [1052, 1]], "qualityAdd": 5, "attrAddList": [[21, 10], [22, 10], [23, 10], [24, 10], [25, 10]]}, "2": {"id": 2, "levelMax": 150, "breakCostList": [[1053, 3], [1051, 3], [1052, 3]], "qualityAdd": 5, "attrAddList": [[21, 10], [22, 10], [23, 10], [24, 10], [25, 10]]}, "3": {"id": 3, "levelMax": 200, "breakCostList": [[1053, 5], [1051, 5], [1052, 5]], "qualityAdd": 5, "attrAddList": [[21, 20], [22, 20], [23, 20], [24, 20], [25, 20]]}, "4": {"id": 4, "levelMax": 250, "breakCostList": [[1053, 10], [1051, 10], [1052, 10]], "qualityAdd": 5, "attrAddList": [[21, 20], [22, 20], [23, 20], [24, 20], [25, 20]]}, "5": {"id": 5, "levelMax": 300, "breakCostList": [[1056, 1], [1054, 1], [1055, 1]], "qualityAdd": 5, "attrAddList": [[21, 30], [22, 30], [23, 30], [24, 30], [25, 30]]}, "6": {"id": 6, "levelMax": 350, "breakCostList": [[1056, 3], [1054, 3], [1055, 3]], "qualityAdd": 5, "attrAddList": [[21, 30], [22, 30], [23, 30], [24, 30], [25, 30]]}, "7": {"id": 7, "levelMax": 400, "breakCostList": [[1056, 5], [1054, 5], [1055, 5]], "qualityAdd": 5, "attrAddList": [[21, 40], [22, 40], [23, 40], [24, 40], [25, 40]]}, "8": {"id": 8, "levelMax": 450, "breakCostList": [[1056, 10], [1054, 10], [1055, 10]], "qualityAdd": 5, "attrAddList": [[21, 40], [22, 40], [23, 40], [24, 40], [25, 40]]}, "9": {"id": 9, "levelMax": 500, "breakCostList": [[1056, 20], [1054, 20], [1055, 20]], "qualityAdd": 5, "attrAddList": [[21, 50], [22, 50], [23, 50], [24, 50], [25, 50], [26, 50]]}, "10": {"id": 10, "levelMax": 550, "breakCostList": [[1056, 30], [1054, 30], [1055, 30]], "qualityAdd": 5, "attrAddList": [[21, 50], [22, 50], [23, 50], [24, 50], [25, 50], [26, 50]]}, "11": {"id": 11, "levelMax": 600, "breakCostList": [[1059, 1], [1057, 1], [1058, 1]], "qualityAdd": 5, "attrAddList": [[21, 60], [22, 60], [23, 60], [24, 60], [25, 60], [26, 60]]}, "12": {"id": 12, "levelMax": 650, "breakCostList": [[1059, 3], [1057, 3], [1058, 3]], "qualityAdd": 5, "attrAddList": [[21, 60], [22, 60], [23, 60], [24, 60], [25, 60], [26, 60]]}, "13": {"id": 13, "levelMax": 700, "breakCostList": [[1059, 5], [1057, 5], [1058, 5]], "qualityAdd": 5, "attrAddList": [[21, 60], [22, 60], [23, 60], [24, 60], [25, 60], [26, 60]]}, "-1": {"id": -1, "levelMax": 0, "breakCostList": [], "qualityAdd": 0, "attrAddList": []}}