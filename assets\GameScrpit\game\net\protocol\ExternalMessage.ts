// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: ExternalMessage.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.iohao.message";

/** 对外服数据协议 */
export interface ExternalMessage {
  /** 请求命令类型: 0 心跳，1 业务 */
  cmdCode: number;
  /** 协议开关，用于一些协议级别的开关控制，比如 安全加密校验等。 : 0 不校验 */
  protocolSwitch: number;
  /** 业务路由（高16为主, 低16为子） */
  cmdMerge: number;
  /** 响应码: 0:成功, 其他为有错误 */
  responseStatus: number;
  /** 验证信息（错误消息、异常消息），通常情况下 responseStatus == -1001 时， 会有值 */
  validMsg: string;
  /** 业务请求数据 */
  data: Uint8Array;
  /** 消息标记号；由前端请求时设置，服务器响应时会携带上； */
  msgId: number;
}

/** int 包装类 */
export interface IntValue {
  /** int 值 */
  value: number;
}

/** int list 包装类 */
export interface IntValueList {
  /** intList、intArray */
  values: number[];
}

/** long 包装类 */
export interface LongValue {
  /** long 值 */
  value: number;
}

/** long list 包装类 */
export interface LongValueList {
  /** longList、longArray */
  values: number[];
}

/** string 包装类 */
export interface StringValue {
  /** string 值 */
  value: string;
}

/** string list 包装类 */
export interface StringValueList {
  /** stringList、stringArray */
  values: string[];
}

/** bool 包装类 */
export interface BoolValue {
  /** bool 值 */
  value: boolean;
}

/** bool list 包装类 */
export interface BoolValueList {
  /** boolList、boolArray */
  values: boolean[];
}

/** pb 对象 list 包装类 */
export interface ByteValueList {
  /** pb 对象 List、pb 对象 Array */
  values: Uint8Array[];
}

/** int 包装类 -- 已经废弃的，由 IntValue 代替 */
export interface IntPb {
  /** int 值 */
  intValue: number;
}

/** int list 包装类 -- 已经废弃的，由 IntValueList 代替 */
export interface IntListPb {
  /** intList */
  intValues: number[];
}

/** long 包装类 -- 已经废弃的，由 LongValue 代替 */
export interface LongPb {
  /** long 值 */
  longValue: number;
}

/** long list 包装类 -- 已经废弃的，由 LongValueList 代替 */
export interface LongListPb {
  /** longList */
  longValues: number[];
}

/** double 包装类 */
export interface DoubleValue {
  /** double 值 */
  value: number;
}

/** double list 包装类 */
export interface DoubleValueList {
  /** doubleList、doubleArray */
  values: number[];
}

function createBaseExternalMessage(): ExternalMessage {
  return {
    cmdCode: 0,
    protocolSwitch: 0,
    cmdMerge: 0,
    responseStatus: 0,
    validMsg: "",
    data: new Uint8Array(0),
    msgId: 0,
  };
}

export const ExternalMessage: MessageFns<ExternalMessage> = {
  encode(message: ExternalMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cmdCode !== 0) {
      writer.uint32(8).int32(message.cmdCode);
    }
    if (message.protocolSwitch !== 0) {
      writer.uint32(16).int32(message.protocolSwitch);
    }
    if (message.cmdMerge !== 0) {
      writer.uint32(24).int32(message.cmdMerge);
    }
    if (message.responseStatus !== 0) {
      writer.uint32(32).sint32(message.responseStatus);
    }
    if (message.validMsg !== "") {
      writer.uint32(42).string(message.validMsg);
    }
    if (message.data.length !== 0) {
      writer.uint32(50).bytes(message.data);
    }
    if (message.msgId !== 0) {
      writer.uint32(56).int32(message.msgId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ExternalMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExternalMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.cmdCode = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.protocolSwitch = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.cmdMerge = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.responseStatus = reader.sint32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.validMsg = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.data = reader.bytes();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.msgId = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ExternalMessage>, I>>(base?: I): ExternalMessage {
    return ExternalMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExternalMessage>, I>>(object: I): ExternalMessage {
    const message = createBaseExternalMessage();
    message.cmdCode = object.cmdCode ?? 0;
    message.protocolSwitch = object.protocolSwitch ?? 0;
    message.cmdMerge = object.cmdMerge ?? 0;
    message.responseStatus = object.responseStatus ?? 0;
    message.validMsg = object.validMsg ?? "";
    message.data = object.data ?? new Uint8Array(0);
    message.msgId = object.msgId ?? 0;
    return message;
  },
};

function createBaseIntValue(): IntValue {
  return { value: 0 };
}

export const IntValue: MessageFns<IntValue> = {
  encode(message: IntValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== 0) {
      writer.uint32(8).sint32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IntValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.value = reader.sint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<IntValue>, I>>(base?: I): IntValue {
    return IntValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IntValue>, I>>(object: I): IntValue {
    const message = createBaseIntValue();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseIntValueList(): IntValueList {
  return { values: [] };
}

export const IntValueList: MessageFns<IntValueList> = {
  encode(message: IntValueList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.sint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IntValueList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.values.push(reader.sint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.sint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<IntValueList>, I>>(base?: I): IntValueList {
    return IntValueList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IntValueList>, I>>(object: I): IntValueList {
    const message = createBaseIntValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseLongValue(): LongValue {
  return { value: 0 };
}

export const LongValue: MessageFns<LongValue> = {
  encode(message: LongValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== 0) {
      writer.uint32(8).sint64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LongValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.value = longToNumber(reader.sint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LongValue>, I>>(base?: I): LongValue {
    return LongValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LongValue>, I>>(object: I): LongValue {
    const message = createBaseLongValue();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseLongValueList(): LongValueList {
  return { values: [] };
}

export const LongValueList: MessageFns<LongValueList> = {
  encode(message: LongValueList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.sint64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LongValueList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.values.push(longToNumber(reader.sint64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(longToNumber(reader.sint64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LongValueList>, I>>(base?: I): LongValueList {
    return LongValueList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LongValueList>, I>>(object: I): LongValueList {
    const message = createBaseLongValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseStringValue(): StringValue {
  return { value: "" };
}

export const StringValue: MessageFns<StringValue> = {
  encode(message: StringValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== "") {
      writer.uint32(10).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StringValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<StringValue>, I>>(base?: I): StringValue {
    return StringValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StringValue>, I>>(object: I): StringValue {
    const message = createBaseStringValue();
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseStringValueList(): StringValueList {
  return { values: [] };
}

export const StringValueList: MessageFns<StringValueList> = {
  encode(message: StringValueList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.values) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StringValueList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStringValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.values.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<StringValueList>, I>>(base?: I): StringValueList {
    return StringValueList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StringValueList>, I>>(object: I): StringValueList {
    const message = createBaseStringValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseBoolValue(): BoolValue {
  return { value: false };
}

export const BoolValue: MessageFns<BoolValue> = {
  encode(message: BoolValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== false) {
      writer.uint32(8).bool(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BoolValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoolValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.value = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BoolValue>, I>>(base?: I): BoolValue {
    return BoolValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BoolValue>, I>>(object: I): BoolValue {
    const message = createBaseBoolValue();
    message.value = object.value ?? false;
    return message;
  },
};

function createBaseBoolValueList(): BoolValueList {
  return { values: [] };
}

export const BoolValueList: MessageFns<BoolValueList> = {
  encode(message: BoolValueList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.bool(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BoolValueList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoolValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.values.push(reader.bool());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.bool());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<BoolValueList>, I>>(base?: I): BoolValueList {
    return BoolValueList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BoolValueList>, I>>(object: I): BoolValueList {
    const message = createBaseBoolValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseByteValueList(): ByteValueList {
  return { values: [] };
}

export const ByteValueList: MessageFns<ByteValueList> = {
  encode(message: ByteValueList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.values) {
      writer.uint32(10).bytes(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ByteValueList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseByteValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.values.push(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ByteValueList>, I>>(base?: I): ByteValueList {
    return ByteValueList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ByteValueList>, I>>(object: I): ByteValueList {
    const message = createBaseByteValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBaseIntPb(): IntPb {
  return { intValue: 0 };
}

export const IntPb: MessageFns<IntPb> = {
  encode(message: IntPb, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.intValue !== 0) {
      writer.uint32(8).sint32(message.intValue);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IntPb {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntPb();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.intValue = reader.sint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<IntPb>, I>>(base?: I): IntPb {
    return IntPb.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IntPb>, I>>(object: I): IntPb {
    const message = createBaseIntPb();
    message.intValue = object.intValue ?? 0;
    return message;
  },
};

function createBaseIntListPb(): IntListPb {
  return { intValues: [] };
}

export const IntListPb: MessageFns<IntListPb> = {
  encode(message: IntListPb, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.intValues) {
      writer.sint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IntListPb {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntListPb();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.intValues.push(reader.sint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.intValues.push(reader.sint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<IntListPb>, I>>(base?: I): IntListPb {
    return IntListPb.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IntListPb>, I>>(object: I): IntListPb {
    const message = createBaseIntListPb();
    message.intValues = object.intValues?.map((e) => e) || [];
    return message;
  },
};

function createBaseLongPb(): LongPb {
  return { longValue: 0 };
}

export const LongPb: MessageFns<LongPb> = {
  encode(message: LongPb, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.longValue !== 0) {
      writer.uint32(8).sint64(message.longValue);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LongPb {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongPb();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.longValue = longToNumber(reader.sint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LongPb>, I>>(base?: I): LongPb {
    return LongPb.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LongPb>, I>>(object: I): LongPb {
    const message = createBaseLongPb();
    message.longValue = object.longValue ?? 0;
    return message;
  },
};

function createBaseLongListPb(): LongListPb {
  return { longValues: [] };
}

export const LongListPb: MessageFns<LongListPb> = {
  encode(message: LongListPb, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.longValues) {
      writer.sint64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LongListPb {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLongListPb();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.longValues.push(longToNumber(reader.sint64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.longValues.push(longToNumber(reader.sint64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LongListPb>, I>>(base?: I): LongListPb {
    return LongListPb.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LongListPb>, I>>(object: I): LongListPb {
    const message = createBaseLongListPb();
    message.longValues = object.longValues?.map((e) => e) || [];
    return message;
  },
};

function createBaseDoubleValue(): DoubleValue {
  return { value: 0 };
}

export const DoubleValue: MessageFns<DoubleValue> = {
  encode(message: DoubleValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== 0) {
      writer.uint32(9).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DoubleValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDoubleValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DoubleValue>, I>>(base?: I): DoubleValue {
    return DoubleValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoubleValue>, I>>(object: I): DoubleValue {
    const message = createBaseDoubleValue();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseDoubleValueList(): DoubleValueList {
  return { values: [] };
}

export const DoubleValueList: MessageFns<DoubleValueList> = {
  encode(message: DoubleValueList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DoubleValueList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDoubleValueList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.values.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DoubleValueList>, I>>(base?: I): DoubleValueList {
    return DoubleValueList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoubleValueList>, I>>(object: I): DoubleValueList {
    const message = createBaseDoubleValueList();
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
