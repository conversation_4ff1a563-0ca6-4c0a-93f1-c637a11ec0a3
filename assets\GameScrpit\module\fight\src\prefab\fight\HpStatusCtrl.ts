import { _decorator, Sprite } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

const { ccclass, property } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

@ccclass("HpStatusCtrl")
export class HpStatusCtrl extends BaseCtrl {
  sprite_bg_bar_opacity: Sprite;

  sprite_bg_bar: Sprite;

  hpMax: number = 100;
  hpNow: number = 100;

  targetLength = 1;
  barLength = 1;
  barOpacityLength = 1;

  protected async onStart() {
    log.info("HpStatusCtrl start");
    this.sprite_bg_bar_opacity = this.getNode("bg_bar_opacity").getComponent(Sprite);
    this.sprite_bg_bar = this.getNode("bg_bar").getComponent(Sprite);
    this.sprite_bg_bar.fillRange = 1;
    this.sprite_bg_bar_opacity.fillRange = 1;
  }

  public initHp(hpMax: number) {
    log.info("HpStatusCtrl initHp");
    this.hpMax = hpMax;
    this.hpNow = hpMax;
    this.barLength = this.hpNow / this.hpMax;
    this.barOpacityLength = this.barLength;
    this.targetLength = this.barLength;
    this.runAfterStart(() => {
      this.sprite_bg_bar.fillRange = 1;
      this.sprite_bg_bar_opacity.fillRange = 1;
    });
  }

  public changeHp(value: number) {
    this.hpNow += value;
    log.info(`HpStatusCtrl changeHp ${value} ${this.hpNow}`);
    this.hpNow = Math.min(this.hpNow, this.hpMax);
    this.hpNow = Math.max(this.hpNow, 0);
    this.targetLength = this.hpNow / this.hpMax;
    if (value > 0) {
      // 加血
      this.barOpacityLength = this.targetLength;
      this.sprite_bg_bar_opacity.fillRange = this.targetLength;
    } else {
      // 扣血
      this.barLength = this.targetLength;
      this.sprite_bg_bar.fillRange = this.targetLength;
    }
  }

  protected update(dt: number): void {
    if (this.targetLength > this.barLength) {
      this.barLength += (0.5 / (1 + this.targetLength - this.barLength)) * dt;
      this.barLength = Math.min(this.barLength, this.targetLength);
      this.sprite_bg_bar.fillRange = this.barLength;
    }

    if (this.targetLength < this.barLength) {
      this.barLength -= (0.5 / (1 + this.targetLength - this.barLength)) * dt;
      this.barLength = Math.max(this.barLength, this.targetLength);
      this.sprite_bg_bar.fillRange = this.barLength;
    }

    if (this.targetLength > this.barOpacityLength) {
      this.barOpacityLength += (0.5 / (1 + this.targetLength - this.barOpacityLength)) * dt;
      this.barOpacityLength = Math.min(this.barOpacityLength, this.targetLength);
      this.sprite_bg_bar_opacity.fillRange = this.barOpacityLength;
    }

    if (this.targetLength < this.barOpacityLength) {
      this.barOpacityLength -= (0.5 / (1 + this.targetLength - this.barOpacityLength)) * dt;
      this.barOpacityLength = Math.max(this.barOpacityLength, this.targetLength);
      this.sprite_bg_bar_opacity.fillRange = this.barOpacityLength;
    }
  }
}
