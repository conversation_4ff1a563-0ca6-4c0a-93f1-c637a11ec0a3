import { _decorator, find, Input, instantiate, Label, ProgressBar, sp, Sprite, tween, Widget } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import Formate from "../../../lib/utils/Formate";
import TipMgr from "../../../lib/tips/TipMgr";
import { AttrEnum, RightConditionEnum, Sleep, SystemOpenEnum } from "../../GameDefine";
import { CityModule } from "../../../module/city/CityModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { CityRouteName } from "../../../module/city/CityConstant";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import { UIOpacity } from "cc";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { v3 } from "cc";
import { CityHireResponse, CityMessage } from "../../net/protocol/City";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { ArgsCityBuildRes } from "./UICityBuildRes";
import { HeroModule } from "../../../module/hero/HeroModule";
import ResMgr from "../../../lib/common/ResMgr";
import { CityEvent } from "../../../module/city/CityEvent";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import ToolExt from "../../common/ToolExt";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { CityAudioName } from "../../../module/city/CityConstant";
import { RewardRouteEnum } from "../../../ext_reward/RewardDefine";
import { GameDirector } from "../../GameDirector";
import { LangMgr } from "../../mgr/LangMgr";
import { ConfirmMsg } from "../UICostConfirm";
import { HdVipCardRouteItem } from "../../../module/hd_vipcard/HdVipCardRoute";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import GuideMgr from "../../../ext_guide/GuideMgr";
import FmUtils from "../../../lib/utils/FmUtils";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("UICityDetail")
export class UICityDetail extends UINode {
  protected _resetLayer = false;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UICityDetail`;
  }

  // 据点数据
  _cityId: number;

  // 自动号召
  private _autoHire: boolean = false;

  public init(param: any[]) {
    super.init(param);
    this._cityId = param[0];
  }

  protected onRegEvent() {
    MsgMgr.on(MsgEnum.ON_CITY_UPDATE, this.refresh, this);
    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.updateUserInfo, this);
    MsgMgr.on(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE, this.onUpdateHaoZhao, this);
  }

  protected onDelEvent() {
    MsgMgr.off(MsgEnum.ON_CITY_UPDATE, this.refresh, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.updateUserInfo, this);
    MsgMgr.off(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE, this.onUpdateHaoZhao, this);
  }

  updateUserInfo(id: number = 0) {
    this.getNode("lbl_item1").getComponent(Label).string = Formate.format(
      PlayerModule.data.getItemNum(ItemEnum.气运_1)
    );
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(false, 0.3);

    // 翻译
    const lblChoose = this.getNode("btn_choose").getChildByPath("Label").getComponent(Label);
    lblChoose.string = LangMgr.txMsgCode(123, [], "自动号召");

    // 初始化不自动号召
    this.on_click_btn_auto_close();

    //this.getNode("btn_hao_zhao_box").active = GameDirector.instance.isSystemOpen(SystemOpenEnum.HZBX_号召宝箱);

    // 出场动画
    const uiOpacity: UIOpacity = this.node.getComponent(UIOpacity);
    uiOpacity.opacity = 0;
    this.node.scale = v3(1, 1, 1);
    tween(this.node)
      .to(
        0.15,
        { scale: v3(1, 1, 1) },
        {
          onUpdate: (target, ratio) => {
            uiOpacity.opacity = ratio * 255;
          },
        }
      )
      .call(() => {
        // 检查功能开启
        this.onCheckSystemOpen();
      })
      .start();

    this.setNodeHero();

    this.refresh();

    this.getNode("node_info").active = false;

    // 发送场景焦点触发消息
    MsgMgr.emit(MsgEnum.ON_CITY_FOCOUS, this._cityId, this.getNode("btn_build_area").getPosition());

    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_shengji"),
      BadgeType.UIMajorCity["btn_name" + this._cityId].btn_shengji_node.id
    );
    this.getNode("bg_bottom").getComponent(Widget).updateAlignment();
    this.getNode("layout_right").getComponent(Widget).updateAlignment();
    setTimeout(() => {
      this.startAni();
    }, 16);

    let db1 = JsonMgr.instance.jsonList.c_build[this._cityId];
    AudioMgr.instance.playMusic(db1.musicId);
  }

  private startAni() {
    this.getNode("node_hero").children.forEach((node) => {
      node.active = false;
    });
    this.getNode("lbl_city_speed").active = false;
    this.getNode("progress_bar_worker").active = false;
    this.getNode("bg_shuxing").active = false;
    this.getNode("bg_zhenshouwujiang").active = false;
    this.getNode("btn_close").active = false;
    this.getNode("btn_yulan").active = false;
    this.getNode("node_shengji").active = false;
    this.getNode("node_haozhao").active = false;
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HZBX_号召宝箱)) {
      this.getNode("btn_hao_zhao_box").getChildByName("open").active = true;
      this.getNode("btn_hao_zhao_box").getChildByName("no_open").active = false;
    } else {
      this.getNode("btn_hao_zhao_box").getChildByName("open").active = false;
      this.getNode("btn_hao_zhao_box").getChildByName("no_open").active = true;
    }
    let showNodes = () => {
      // 显示赚速节点
      tween(this.getNode("lbl_city_speed"))
        .by(0, { position: v3(0, 80, 0) })
        .set({ active: true })
        .by(0.2, { position: v3(0, -80, 0) }, { easing: "backOut" })
        .start();
      // 显示族人节点
      tween(this.getNode("progress_bar_worker"))
        .by(0, { position: v3(0, 80, 0) })
        .set({ active: true })
        .by(0.2, { position: v3(0, -80, 0) }, { easing: "backOut" })
        .start();

      // 显示属性
      tween(this.getNode("bg_shuxing"))
        .by(0, { position: v3(0, -150, 0) })
        .set({ active: true })
        .by(0.2, { position: v3(0, 150, 0) }, { easing: "backOut" })
        .start();
      // 显示英雄标题
      tween(this.getNode("bg_zhenshouwujiang"))
        .by(0, { position: v3(0, 80, 0) })
        .set({ active: true })
        .by(0.2, { position: v3(0, -80, 0) }, { easing: "backOut" })
        .start();
      // 显示关闭
      tween(this.getNode("btn_close"))
        .by(0, { position: v3(80, 0, 0) })
        .set({ active: true })
        .by(0.2, { position: v3(-80, 0, 0) }, { easing: "backOut" })
        .start();
      // 显示预览
      tween(this.getNode("btn_yulan"))
        .by(0, { position: v3(-80, 0, 0) })
        .set({ active: true })
        .by(0.2, { position: v3(80, 0, 0) }, { easing: "backOut" })
        .start();
      // 显示号召宝箱
      if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HZBX_号召宝箱)) {
        tween(this.getNode("btn_hao_zhao_box"))
          .by(0, { position: v3(150, 0, 0) })
          .set({ active: true })
          .by(0.2, { position: v3(-150, 0, 0) }, { easing: "backOut" })
          .start();
      }
      // 显示升级
      tween(this.getNode("node_shengji"))
        .delay(0.1)
        .by(0, { position: v3(0, -200, 0) })
        .set({ active: true })
        .by(0.2, { position: v3(0, 200, 0) }, { easing: "backOut" })
        .start();
      // 显示号召
      tween(this.getNode("node_haozhao"))
        .delay(0.2)
        .by(0, { position: v3(0, -200, 0) })
        .set({ active: true })
        .by(0.2, { position: v3(0, 200, 0) }, { easing: "backOut" })
        .start();

      // 显示英雄节点
      for (let i = 0; i < this.getNode("node_hero").children.length; i++) {
        //
        let node = this.getNode("node_hero").children[i];
        tween(node)
          .by(0, { position: v3(0, -150, 0) })
          .delay(0.1 * i)
          .set({ active: true })
          .by(0.2, { position: v3(0, 150, 0) }, { easing: "backOut" })
          .start();
      }
    };
    tween(this.getNode("lbl_city_name"))
      .by(0, { position: v3(0, 0 - 740, 0) })
      .by(0.3, { position: v3(0, 740, 0) })
      .start();
    tween(this.getNode("bg_bottom"))
      .by(0, { position: v3(0, 0 - 740, 0) })
      .by(0.3, { position: v3(0, 740, 0) })
      .call(() => {
        showNodes();
      })
      .start();
  }

  private setNodeHero() {
    let db1 = JsonMgr.instance.jsonList.c_build[this._cityId];
    let lvdb = this.getBuildLv_db_List();

    let curLvdb = lvdb[CityModule.data.cityMessageMap.get(this._cityId).level];

    let hasList = [];
    let noList = [];

    for (let i = 0; i < db1.heroIdList.length; i++) {
      let heroId = db1.heroIdList[i];
      let color = JsonMgr.instance.jsonList.c_hero[heroId].color;
      let obj = {
        id: heroId,
        color: color,
        Rate: curLvdb.heroRateList[color - 1],
        has: true,
      };

      if (HeroModule.data.getHeroMessage(heroId)) {
        hasList.push(obj);
      } else {
        obj.has = false;
        noList.push(obj);
      }
    }

    let newHas = hasList.sort((a, b) => {
      if (a.color - b.color > 0) {
        return 1;
      } else {
        return 0;
      }
    });

    newHas = newHas.concat(noList);

    if (newHas.length < 5) {
      for (let i = newHas.length; i < 5; i++) {
        let obj = {
          id: -1,
          color: -1,
          Rate: 0,
          has: false,
        };
        newHas.push(obj);
      }
    }

    for (let i = 0; i < newHas.length && i < 5; i++) {
      let name = "node_hero_" + (i + 1);
      let node = this.getNode(name);
      let data = newHas[i];
      let info = JsonMgr.instance.jsonList.c_hero[data.id];

      node.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.touch_node_hero.bind(this)), this);
      let upIsBool = true;

      if (node["card_data"]) {
        upIsBool = false;
      }
      node["card_data"] = data;

      let bg_hero = find("bg_hero", node);
      let image = find("image", bg_hero);

      bg_hero.getComponent(Sprite).grayscale = !data.has;
      image.getComponent(Sprite).grayscale = !data.has;

      if (upIsBool == true) {
        if (data.id == -1) {
          node.getChildByName("lbl_add").active = false;

          ResMgr.loadImage(
            `${BundleEnum.BUNDLE_G_MAJORCITY}?heroCardImg/img_card0`,
            bg_hero.getComponent(Sprite),
            this
          );
        } else {
          image.getComponent(Sprite).spriteFrame = null;

          ResMgr.loadImage(
            `${BundleEnum.BUNDLE_G_MAJORCITY}?heroCardImg/img_card${data.color}`,
            bg_hero.getComponent(Sprite),
            this
          );
          ResMgr.loadImage(
            `${BundleEnum.BUNDLE_G_MAJORCITY}?heroCardImg/${info.cardId}`,
            image.getComponent(Sprite),
            this
          );
        }
      }

      if (data.has == false) {
        node.getChildByName("lbl_add").getComponent(Label).string = "未激活";
      } else {
        node.getChildByName("lbl_add").getComponent(Label).string = `繁荣度\n+${(curLvdb.heroRate * data.Rate) / 100}%`;
      }
    }
  }

  private touch_node_hero(event: Event) {
    AudioMgr.instance.playEffect(CityAudioName.Effect.点击镇守战将卡牌);

    let data = event.target["card_data"];

    //log.log("点击卡牌====", data);
    if (data.id == -1) {
      TipMgr.showTip("待开放");
      return;
    }

    UIMgr.instance.showDialog(CityRouteName.UICityHeroInfo, { card_data: data, keepMask: true });
  }

  private getBuildLv_db_List() {
    let db2 = JsonMgr.instance.jsonList.c_buildLv;
    let arr = {};
    let list = Object.keys(db2);

    for (let i = 0; i < list.length; i++) {
      let info = db2[list[i]];

      if (info.buildId == this._cityId) {
        arr[info.level] = info;
      }
    }

    return arr;
  }

  protected refresh() {
    const cityMsg = CityModule.data.cityMessageMap.get(this._cityId);
    const configCity = CityModule.data.getConfigBuild(this._cityId);
    const configBuildLv = CityModule.data.getConfigBuildLv(this._cityId, cityMsg.level);
    const configBuildLvNext = CityModule.data.getConfigBuildLv(this._cityId, cityMsg.level + 1);

    // 名称
    this.getNode("lbl_city_name").getComponent(Label).string = configCity.name;

    // 当前人数
    let workNum = cityMsg.itemHire + cityMsg.energyHire;

    // 城市繁荣度
    let bloom = CityModule.data.getCityBloom(this._cityId);

    // 设置繁荣度
    this.getNode("lbl_city_bloom").getComponent(Label).string = `繁荣度:${Formate.format(bloom)}`;

    // 气运提升
    this.getNode("lbl_city_speed").getComponent(Label).string = `气运${Formate.format(bloom)}/秒`;

    // 设置英雄提升%
    let bloomAdd = CityModule.data.getCityHeroAddBloom(this._cityId);
    bloomAdd = Math.round(bloomAdd * 10000);
    //this.getNode("lbl_bloom_add").getComponent(Label).string = `繁荣度 +${bloomAdd / 100}%`;

    // 员工招募进度
    this.getNode("lbl_worker_num").getComponent(Label).string = `${workNum}/${configBuildLv.workerMax}`;
    this.getNode("progress_bar_worker").getComponent(ProgressBar).progress = workNum / configBuildLv.workerMax;

    // 属性设置
    let attr = CityModule.data.getCityAttrMap(this._cityId);

    // %属性
    let nodeLayoutRate = this.getNode("layout_attr_rate");
    const attrList = [31, 32, 33, 34, 35, 36];
    for (let i = 0; i < nodeLayoutRate.children.length; i++) {
      // let config = JsonMgr.instance.jsonList.c_attribute[attrList[i]];
      let item = nodeLayoutRate.children[i];
      item.getComponent(Label).string = Formate.formatAttribute(attrList[i], attr[attrList[i]]);
    }
    // 基础属性
    const baseAttrList = [AttrEnum.攻击_2, AttrEnum.生命_1, AttrEnum.防御_3, AttrEnum.敏捷_4];
    let nodeLayoutBase = this.getNode("layout_attr_base");
    for (let i = 0; i < nodeLayoutBase.children.length; i++) {
      nodeLayoutBase.children[i].getComponent(Label).string = Formate.formatAttribute(
        baseAttrList[i],
        attr[baseAttrList[i]]
      );
    }

    // 招募进度
    this.getNode("lbl_haozhao_cost").getComponent(Label).string = `${Formate.format(this.getHireNum())}`;

    // 满级判断
    const isMaxLv = cityMsg.level >= configBuildLv.lvMax;
    this.getNode("btn_shengji").active = !isMaxLv;
    this.getNode("progress_bar").active = !isMaxLv;
    this.getNode("layout_up_add").active = !isMaxLv;
    this.getNode("up_finish").active = isMaxLv;
    if (!isMaxLv) {
      // 升级花费
      let itemNum = PlayerModule.data.getItemNum(configBuildLv.cfg);
      // 资源情况
      this.getNode("lbl_item_cost").getComponent(Label).string = `${itemNum}/${configBuildLvNext.cost}`;
      this.getNode("progress_bar").getComponent(ProgressBar).progress = itemNum / configBuildLvNext.cost;
      this.getNode("lbl_from").getComponent(Label).string = `${Formate.formatDecimal(
        configBuildLv.rateAdd / 10000,
        1
      )}倍`;
      this.getNode("lbl_to").getComponent(Label).string = `${Formate.formatDecimal(
        configBuildLvNext.rateAdd / 10000,
        1
      )}倍`;
    }

    // 满人口判断
    // const isMaxPeople = workNum >= configBuildLv.workerMax;
    // this.getNode("btn_haozhao").active = !isMaxPeople;
    // this.getNode("bg_energy_cost").active = !isMaxPeople;
    // this.getNode("btn_choose").active = !isMaxPeople;

    // 建筑气运产出
    this.getNode("lbl_qi_yun_value").getComponent(Label).string = `${Formate.format(bloom)}/秒`;

    // 详情里的描述
    this.getNode("lbl_desc").getComponent(Label).string = configCity.des;

    this.onUpdateHaoZhao();
  }

  private onUpdateHaoZhao() {
    if (CityModule.data.cityAggregateMessage.boxTotalCount) {
      this.getNode("lbl_hao_zhao_box_num").parent.active = true;
      let lbl_hao_zhao_box_num = this.getNode("lbl_hao_zhao_box_num").getComponent(Label);
      lbl_hao_zhao_box_num.string = `${CityModule.data.cityAggregateMessage.boxTotalCount}`;
    } else {
      this.getNode("lbl_hao_zhao_box_num").parent.active = false;
    }
  }

  /** 获取招募消耗数量 */
  private getHireNum() {
    let cityMsg = CityModule.data.cityMessageMap.get(this._cityId);

    // 当前人数
    let numNow = cityMsg.energyHire + cityMsg.itemHire;

    // 默认招一个
    let num = 1;

    return CityModule.service.cityBuildWorker(this._cityId, numNow, num);
  }

  /** 自动号召 */
  private on_click_btn_choose() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._autoHire = !this._autoHire;
    if (this._autoHire) {
      if (!PlayerModule.service.hasRight(110)) {
        this._autoHire = false;

        // 提示不能开启
        const msg: ConfirmMsg = {
          msg: LangMgr.txMsgCode(116, [FmUtils.getRightCondition(110, RightConditionEnum.Level)], "提示"),
          okText: LangMgr.txMsgCode(140, [], "前往"),
        };

        UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
          if (resp?.ok) {
            UIMgr.instance.showDialog(HdVipCardRouteItem.UICardMain);
          }
        });
        return;
      }
    }

    this.getNode("btn_choose").getChildByPath("bg_duigou/bg_choose_icon").active = this._autoHire;
    this.getNode("btn_auto_close").active = this._autoHire;

    if (this._autoHire) {
      this.on_click_btn_haozhao();
    }
  }

  /**点击据点升级 */
  private on_click_btn_shengji() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    const cityMsg = CityModule.data.cityMessageMap.get(this._cityId);
    const configBuildLv = CityModule.data.getConfigBuildLv(this._cityId, cityMsg.level);
    const configBuildLvNext = CityModule.data.getConfigBuildLv(this._cityId, cityMsg.level + 1);

    let itemNum = PlayerModule.data.getItemNum(configBuildLv.cfg);
    if (itemNum < configBuildLvNext.cost) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: configBuildLvNext.cfg,
        needNum: configBuildLvNext.cost,
      });
      //弹窗礼包事件
      MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, configBuildLvNext.cfg);
      return;
    }
    if (cityMsg.level == configBuildLv.lvMax) {
      TipMgr.showTip(`该据点已满级`);
      return;
    }

    // 建造
    let argsCityBuildRes: ArgsCityBuildRes = {
      oldBloom: CityModule.data.getCityBloom(this._cityId),
      cityMsg: undefined,
      isCreate: true,
      rewardList: [],
    };

    CityModule.api.upgradeCity(cityMsg.cityId, async (data: CityMessage) => {
      TipsMgr.setEnableTouch(false, 2, false);
      AudioMgr.instance.playEffect(CityAudioName.Effect.升级成功);
      MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "CITY_LEVEL_UP");
      this.setNodeHero();

      // 播放建造动画时间
      let buildTimeSecond = 0;
      const cfgBuild = JsonMgr.instance.jsonList.c_build[this._cityId];
      if (cfgBuild?.unlockLvList?.indexOf(data.level) != -1) {
        buildTimeSecond = 2;
      }
      await Sleep(buildTimeSecond);

      // 刷新数据
      const cfgMap = JsonMgr.instance.jsonList.c_buildTrimReward;
      const keyList = Object.keys(cfgMap);
      for (let key of keyList) {
        const cfg = cfgMap[key];
        if (cfg.c_buildLv == CityModule.data.cityTotalLevel) {
          TipsMgr.topRouteCtrl.showPrefab(
            BundleEnum.BUNDLE_G_GAME_MAP,
            "prefab/top/TopCityComponentReward",
            {
              trimId: cfg.id,
            },
            () => {
              this.backFun();
            }
          );
          break;
        }
      }
    });
  }

  // 点击据点返回主界面
  private on_click_btn_build_area() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.backFun();
  }

  // 返回事件
  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    this.backFun();
    // UIMgr.instance.back();
  }

  backFun() {
    TipsMgr.setEnableTouch(false, 0.8);
    let showNodes = () => {
      // 显示赚速节点
      tween(this.getNode("lbl_city_speed"))
        .delay(0.2)

        .by(0.2, { position: v3(0, 80, 0) }, { easing: "backIn" })
        .set({ active: false })
        .start();
      // 显示族人节点
      tween(this.getNode("progress_bar_worker"))
        .delay(0.2)

        .by(0.2, { position: v3(0, 80, 0) }, { easing: "backIn" })
        .set({ active: false })
        .start();

      // 显示属性
      tween(this.getNode("bg_shuxing"))
        .delay(0.2)

        .by(0.2, { position: v3(0, -150, 0) }, { easing: "backIn" })
        .set({ active: false })
        .start();
      // 显示英雄标题
      tween(this.getNode("bg_zhenshouwujiang"))
        .delay(0.2)

        .by(0.2, { position: v3(0, 80, 0) }, { easing: "backIn" })
        .set({ active: false })
        .start();
      // 显示关闭
      tween(this.getNode("btn_close"))
        .delay(0.2)

        .by(0.2, { position: v3(80, 0, 0) }, { easing: "backIn" })
        .set({ active: false })
        .start();
      // 显示预览
      tween(this.getNode("btn_yulan"))
        .delay(0.2)
        .by(0.2, { position: v3(-80, 0, 0) }, { easing: "backIn" })
        .set({ active: false })
        .start();

      if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HZBX_号召宝箱)) {
        // 显示号召宝箱
        tween(this.getNode("btn_hao_zhao_box"))
          .delay(0.2)
          .by(0.2, { position: v3(150, 0, 0) }, { easing: "backIn" })
          .set({ active: false })
          .start();
      }

      // 显示升级
      tween(this.getNode("node_shengji"))
        .delay(0.1)
        .by(0.2, { position: v3(0, -200, 0) }, { easing: "backIn" })
        .set({ active: false })
        .start();
      // 显示号召
      tween(this.getNode("node_haozhao"))
        .by(0.2, { position: v3(0, -200, 0) }, { easing: "backIn" })
        .set({ active: false })
        .start();

      // 显示英雄节点
      for (let i = 0; i < this.getNode("node_hero").children.length; i++) {
        //
        let node = this.getNode("node_hero").children[i];
        tween(node)
          .delay(0.1 * (this.getNode("node_hero").children.length - 1 - i) + 0.2)
          .by(0.2, { position: v3(0, -150, 0) }, { easing: "backIn" })
          .set({ active: false })
          .start();
      }
    };
    tween(this.getNode("lbl_city_name"))
      .delay(0.8)
      .by(0.3, { position: v3(0, -740, 0) })
      .start();
    tween(this.getNode("bg_bottom"))
      .call(() => {
        showNodes();
      })
      .delay(0.8)
      .call(() => {
        MsgMgr.emit(MsgEnum.ON_CITY_UN_FOCOUS);
      })
      .by(0.3, { position: v3(0, -740, 0) })
      .call(() => {
        UIMgr.instance.back();
      })
      .start();
    // const uiOpacity: UIOpacity = this.node.getComponent(UIOpacity);
    // uiOpacity.opacity = 255;
    // tween(this.node)
    //   .to(
    //     0.3,
    //     { scale: v3(1, 1, 1) },
    //     {
    //       onUpdate: (target, ratio) => {
    //         uiOpacity.opacity = (1 - ratio) * 255;
    //       },
    //     }
    //   )
    //   .call(() => {
    //     UIMgr.instance.back();
    //   })
    //   .start();
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_info").active = true;
    let db1 = JsonMgr.instance.jsonList.c_build[this._cityId];
    this.getNode("lbl_1").getComponent(Label).string += "\n" + LangMgr.txMsgCode(454, [db1.goldProduce]);
  }

  private on_click_btn_close_info() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("node_info").active = false;
  }

  on_click_btn_yulan() {
    AudioMgr.instance.playEffect(CityAudioName.Effect.点击预览图标);
    UIMgr.instance.showDialog(CityRouteName.UICityReel, { curCityId: this._cityId });
  }

  /**招募人口 */
  private on_click_btn_haozhao() {
    TipsMgr.setEnableTouch(false, 1);
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let cityMsg = CityModule.data.cityMessageMap.get(this._cityId);
    let cfgBuildLv = CityModule.data.getConfigBuildLv(this._cityId, cityMsg.level);

    if (cfgBuildLv.workerMax <= cityMsg.itemHire + cityMsg.energyHire) {
      this.on_click_btn_auto_close();
      TipMgr.showTip(LangMgr.txMsgCode(145, [], `人口已达最大值`));
      return;
    }

    if (this.getHireNum() > PlayerModule.data.getItemNum(ItemEnum.气运_1)) {
      this.on_click_btn_auto_close();
      //  获取气运的路径弹窗
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: ItemEnum.气运_1,
        needNum: this.getHireNum(),
      });
      return;
    }

    if (CityModule.service.isBoxMax()) {
      this.on_click_btn_auto_close();
      TipMgr.showTip(LangMgr.txMsgCode(118, [], `宝箱已收集满，请先使用宝箱`));

      GuideMgr.startGuide({
        stepId: 77,
        args: { buildId: this._cityId },
        startPos: this.getNode("btn_haozhao").worldPosition,
      });

      return;
    }

    CityModule.api.hireOneWorker(cityMsg.cityId, (resp: CityHireResponse) => {
      TipsMgr.setEnableTouch(true);
      // 冷却时间
      let cdTime = 0.2;
      if (resp.nextAutoTimeStamp > TimeUtils.serverTime + cdTime * 1000) {
        cdTime = (resp.nextAutoTimeStamp - TimeUtils.serverTime) / 1000;
      }

      if (this._autoHire) {
        // 自动号召人口
        tween(this.node)
          .delay(cdTime)
          .call(() => {
            if (this._autoHire) {
              this.on_click_btn_haozhao();
            }
          })
          .start();
      }

      // 播放声音
      AudioMgr.instance.playEffect(CityAudioName.Effect.号召成功);
      // 按钮特效
      this.getNode("spine_zhao_huan_btn_action").active = true;
      const spineAni = this.getNode("spine_zhao_huan_btn_action").getComponent(sp.Skeleton);
      spineAni.setCompleteListener(() => {
        this.getNode("spine_zhao_huan_btn_action").active = false;
      });
      spineAni.setAnimation(0, "animation", false);

      // 引导事件通知
      MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "BUILD_HAOZHAO");
      // 招募动画消息通知
      MsgMgr.emit(CityEvent.ON_CITY_ANI, cityMsg.cityId);
    });
  }

  on_click_btn_hao_zhao_box() {
    AudioMgr.instance.playEffect(1510);
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.HZBX_号召宝箱)) {
      let config = JsonMgr.instance.jsonList.c_systemOpen[SystemOpenEnum.HZBX_号召宝箱];
      TipsMgr.showTipX(115, [config.openList[1]], "");
      return;
    }
    UIMgr.instance.showDialog(CityRouteName.UIHaoZhaoBox);
  }

  /**
   * 检查功能开启
   */
  async onCheckSystemOpen() {
    let list = GameDirector.instance.getSystemOpenWaitAniList();
    if (!list || !list.includes(SystemOpenEnum.HZBX_号召宝箱)) {
      return;
    }

    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HZBX_号召宝箱)) {
      this.getNode("btn_hao_zhao_box").getChildByName("open").active = true;
      this.getNode("btn_hao_zhao_box").getChildByName("no_open").active = false;
    } else {
      this.getNode("btn_hao_zhao_box").getChildByName("open").active = false;
      this.getNode("btn_hao_zhao_box").getChildByName("no_open").active = true;
    }

    let nodeShow = this.getNode("btn_hao_zhao_box");
    nodeShow.active = true;
    TipsMgr.setEnableTouch(false, 2, false);
    await Sleep(0.5);
    TipsMgr.topRouteCtrl.show(
      RewardRouteEnum.TopSystemOpen,
      {
        toWorldPos: nodeShow.getWorldPosition(),
        nodeIconAdd: instantiate(nodeShow),
      },
      () => {
        list = list.filter((e) => e !== SystemOpenEnum.HZBX_号召宝箱);
        GameDirector.instance.saveSystemOpenWaitAniList(list);
      }
    );
  }

  on_click_btn_auto_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._autoHire = false;
    this.getNode("btn_choose").getChildByPath("bg_duigou/bg_choose_icon").active = this._autoHire;
    this.getNode("btn_auto_close").active = false;
  }
}
