export enum SpGuide103002 {
  FIRST_FIGHT = 1,
  SECOND_FIGHT = 2,
  SECOND_FIGHT_FINISH = 3,
  END = 4,
}

/**
 * 战斗人物转向
 */
export enum FaceToEnum {
  left = "left",
  right = "right",
}

export enum ChangeValueType {
  普攻 = 1,
  暴击 = 2,
  必杀 = 3,
  吸血 = 4,
}

/**
 * 行动方式
 */
export enum BaseSkillType {
  静止 = 0,
  普攻 = 1,
  暴击 = 2,
  连击 = 3,
  击晕 = 4,
  闪避 = 5,
  吸血 = 6,
  反击 = 7,
}

export enum AttrKeyEnum {
  血量Max = 1,
  攻击 = 2,
  防御 = 3,
  敏捷 = 4,
  击晕 = 21,
  闪避 = 22,
  连击 = 23,
  反击 = 24,
  暴击 = 25,
  吸血 = 26,
  伤害增加 = 27,
  抗击晕 = 31,
  抗闪避 = 32,
  抗连击 = 33,
  抗反击 = 34,
  抗暴击 = 35,
  抗吸血 = 36,
  伤害减免 = 37,
  血量 = 101,
  护盾 = 102,
  晕眩状态 = 103,
  冰冻状态 = 104,
  重生次数 = 105,
}

/**
 * 动作列表
 */
export enum ActionEnum {
  idle = "idle",
  attack1 = "attack1",
  attack2 = "attack2",
  attack3 = "attack3",
  run1 = "run1",
  run2 = "run2",
  run3 = "run3",
  jump1 = "jump1",
  jump2 = "jump2",
  skill1 = "skill1",
  skill2 = "skill2",
  hurt = "hurt",
  die = "die",
  spell = "spell",
  vertigo = "vertigo",
}

export enum HpStatusType {
  green = "Green",
  red = "Red",
}

// 定义 BattleReplayDTO 接口
export interface BattleReplayDTO {
  /**
   * 场景信息
   */
  scene: SceneDTO;

  /**
   * 队伍信息
   */
  teamList: CharacterDTO[][];

  /**
   * 战斗开始前动作
   */
  beforeRoundActionList: BattleActionDTO[];

  /**
   * 回合信息
   */
  roundList: RoundDTO[];

  /**
   * 队伍结束状态
   */
  teamListFinally: CharacterDTO[][];

  /**
   * 胜利失败结果 {队伍ID: 1-胜利,2-失败}
   */
  winLoseMap: { [key: number]: number };
}

// 定义 BuffDTO 接口
export interface BuffDTO {
  /**
   * 内部ID
   */
  id: number | null;

  /**
   * buff 附在谁身上
   */
  fromId: number | null;

  /**
   * buff 附在谁身上
   */
  targetId: number | null;

  /**
   * 归属内部ID
   */
  skillInterId: number | null;

  /**
   * 可持续回合 -1永生
   */
  life: number | null;

  /**
   * 计数方式
   */
  lifeType: number | null;

  /**
   * 改变量,buff移除时会恢复
   */
  changeValueList: number[][] | null;
}

// 定义 ChangeDTO 接口
export interface ChangeDTO {
  /**
   * 目标ID
   */
  targetId: number | null;

  /**
   * key为属性值
   * List[改变值,类别] 改变值-数代表减少，正数代表增加，级别没有正常，1暴击，2连击，3行动中被反击
   * 暴击，样式
   * 连击，样式
   * 行动中被反击
   */
  attrChangeMap: { [key: number]: number[] };

  /**
   * buff新增列表
   */
  newBuffList: BuffDTO[];

  /**
   * 移除的BUFF_id列表
   */
  removeBuffList: number[];

  /**
   * 引起其他变化的变化, 吸血，反伤等
   */
  otherChangeList: ChangeDTO[];
}

// 定义 SceneDTO 接口
export interface SceneDTO {
  /**
   * 战斗起源，例如关卡，排行榜，活动
   */
  fromType: number | null;

  /**
   * 美术场景ID
   */
  artId: number | null;

  /**
   * 最大回合数
   */
  roundMax: number | null;

  /**
   * 连击概率衰减
   */
  gvsj连击概率衰减List: number[];

  /**
   * 暴击最大倍率
   */
  暴击最大倍率: number | null;

  /**
   * 暴击最小倍率
   */
  暴击最小倍率: number | null;

  /**
   * 最大吸血概率
   */
  最大吸血概率: number | null;
}

// 定义 CharacterDTO 接口
export interface CharacterDTO {
  id: number;

  /**
   * 角色ID
   */
  characterId: number | null;

  /**
   * 美术形象ID
   */
  fightCharacterId: number | null;

  /**
   * 名称
   */
  name: string;

  /**
   * 坐骑形象ID
   */
  mountsArtId: number | null;

  /**
   * 角色等级
   */
  level: number | null;

  /**
   * 初始属性值
   */
  attrMap: { [key: number]: number };

  /**
   * 技能列表
   */
  skillList: SkillDTO[];

  /**
   * 初始buff列表
   */
  buffList: BuffDTO[];

  /**
   * 宠物
   */
  petList: CharacterDTO[];
}

// 定义 SkillDTO 接口
export interface SkillDTO {
  /**
   * 技能ID,用于识别用哪个逻辑处理
   */
  id: number | null;

  /**
   * 技能等级,不同等级取不同配置参数
   */
  level: number | null;

  /**
   * 冷却
   */
  cdMax: number | null;

  /**
   * 当前冷却
   */
  currentCd: number | null;

  /**
   * 技能参数
   */
  args: number[];
}

export interface BattleActionDTO {
  /**
   * 行动角色ID
   */
  characterId: number | null;

  /**
   * 行动方式
   */
  actionType: number | null;

  /**
   * 目标位置，全体技能为空，单一位置
   */
  targetId: number | null;

  /**
   * 影响角色数值改变,按顺序来进行,也有可能包含自己，例如吸血，反伤，消耗,立刻生效，请使用
   */
  数值变动List: ChangeDTO[];

  /**
   * 影响角色产生新行动
   */
  子行为List: BattleActionDTO[];

  /**
   * 使用的技能效果
   */
  skillInterId: number | null;

  /**
   * 行动结束后的hp
   */
  attrMapMap: { [key: number]: { [key: number]: number } };
}

// 定义 RoundDTO 接口
export interface RoundDTO {
  /**
   * 当前回合序号
   */
  roundIdx: number | null;

  /**
   * 动作执行列表
   */
  actionList: BattleActionDTO[];

  /**
   * 队伍变更列表，用于客户端校验，前后端联调使用,正式不输出
   */
  teamList: CharacterDTO[][];
}
