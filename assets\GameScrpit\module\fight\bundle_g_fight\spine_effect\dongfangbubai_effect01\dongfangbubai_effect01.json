{"skeleton": {"hash": "+mZ+0+GRe040IAUf1jgJuZEFPGQ=", "spine": "3.8.75", "x": -2917.58, "y": -238.96, "width": 3857.78, "height": 560, "images": "./images/", "audio": "D:/spine导出/Z_主角  待拆/东方/东方不败"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -6.74, "y": -20.03, "scaleX": 0.41, "scaleY": 0.41}, {"name": "bone2", "parent": "bone", "x": 18.47, "y": 238.36, "color": "ff0000ff"}, {"name": "bone122", "parent": "bone", "x": 146.84, "y": 483.87}, {"name": "bone123", "parent": "bone", "x": -500.57, "y": 317.45}, {"name": "bone124", "parent": "bone123", "length": 69.56, "rotation": -179.61, "x": -7.59, "y": -1.37}, {"name": "bone125", "parent": "bone124", "length": 72.47, "rotation": -3.01, "x": 70.03}, {"name": "bone126", "parent": "bone125", "length": 53.34, "rotation": 3.31, "x": 72.51, "y": -0.95}, {"name": "bone127", "parent": "bone126", "length": 46.22, "rotation": -5.32, "x": 53.34}, {"name": "bone128", "parent": "bone127", "length": 25.44, "rotation": -3.32, "x": 46.22}, {"name": "bone129", "parent": "bone128", "length": 26.72, "rotation": -1.95, "x": 25.44}, {"name": "bone130", "parent": "bone129", "length": 22.26, "rotation": 3.11, "x": 25.91, "y": -0.69}, {"name": "bone131", "parent": "bone130", "length": 16.43, "rotation": 2.67, "x": 22.26}, {"name": "zong", "parent": "root", "x": 0.16, "y": 1.18}, {"name": "glow", "parent": "zong", "x": -3.87, "y": 60.16, "scaleX": 4, "scaleY": 4}, {"name": "jj2", "parent": "zong", "x": -3.9, "y": 58.93, "scaleX": 2, "scaleY": 2}, {"name": "lzz", "parent": "zong", "x": -16.82, "y": 88.24}, {"name": "jjlz", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz2", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz3", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz4", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz5", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz6", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz7", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz8", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz9", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz10", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz11", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz12", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz13", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz14", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz15", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz16", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz17", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz18", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz19", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz20", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "jjlz21", "parent": "lzz", "x": -41.91, "y": -44.21}, {"name": "kkz", "parent": "zong", "x": -0.8, "y": 77.61}, {"name": "kk", "parent": "kkz", "x": -36.08, "y": -98.74, "scaleX": 4, "scaleY": 4}, {"name": "kk2", "parent": "kkz", "x": -36.08, "y": -98.74, "scaleX": 4, "scaleY": 4}, {"name": "kk3", "parent": "kkz", "x": -36.08, "y": -98.74, "scaleX": 4, "scaleY": 4}, {"name": "kk4", "parent": "kkz", "x": -36.08, "y": -98.74, "scaleX": 4, "scaleY": 4}, {"name": "ks2", "parent": "zong", "x": -6.56, "y": 66.64, "scaleX": 4, "scaleY": 4}, {"name": "ks3", "parent": "zong", "x": -6.56, "y": 66.64, "scaleX": 2.7412, "scaleY": 2.7412}, {"name": "nq02", "parent": "zong", "x": 38.98, "y": 72.75, "scaleX": 2, "scaleY": 2}, {"name": "nq2", "parent": "zong", "x": -69.46, "y": 72.75, "scaleX": 2, "scaleY": 2}, {"name": "xltz", "parent": "zong", "x": -2.08, "y": 62.64}, {"name": "xlt", "parent": "xltz", "x": -77.62, "y": 71.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt2", "parent": "xltz", "x": -77.62, "y": 71.37, "scaleX": 2, "scaleY": 2}, {"name": "xlt3", "parent": "xltz", "x": -77.62, "y": 71.37, "scaleX": 2, "scaleY": 2}, {"name": "xxz", "parent": "zong", "x": -9.39, "y": 59.38}, {"name": "xx", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx2", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx3", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx4", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx5", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx6", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx7", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx8", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx9", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx10", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx11", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx12", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx13", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx14", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "xx15", "parent": "xxz", "x": 96.14, "y": -52.9, "scaleX": 1.5, "scaleY": 1.5}, {"name": "runef", "parent": "root", "x": -18.47, "y": 27.59}, {"name": "run1", "parent": "runef", "x": -33.98, "y": 7.67, "scaleX": 3.578, "scaleY": 3.578}, {"name": "moveall", "parent": "root", "length": 100, "x": 14.65, "y": -141.48, "color": "ff0000ff"}, {"name": "glow2", "parent": "moveall"}, {"name": "ju", "parent": "moveall", "x": 35.45, "y": 29.54, "scaleX": 2.4522, "scaleY": 2.4522}, {"name": "ju4", "parent": "moveall"}, {"name": "ju5", "parent": "moveall"}, {"name": "ju6", "parent": "moveall"}, {"name": "ju7", "parent": "moveall"}, {"name": "ju8", "parent": "moveall"}, {"name": "ju9", "parent": "moveall"}, {"name": "ju10", "parent": "moveall"}, {"name": "r", "parent": "moveall"}, {"name": "rao", "parent": "r"}, {"name": "xia<PERSON><PERSON>o", "parent": "moveall", "scaleX": 2, "scaleY": 2}, {"name": "xiaohupo2", "parent": "moveall", "scaleX": 2, "scaleY": 2}, {"name": "xiaohupo3", "parent": "moveall", "scaleX": 2, "scaleY": 2}, {"name": "yshuo1", "parent": "moveall"}, {"name": "red_line_all", "parent": "root", "x": 86.02, "y": -330.26, "scaleY": 0.5061}, {"name": "red_line1", "parent": "red_line_all", "x": 187.05, "y": 862.92, "scaleX": 1.0202, "scaleY": 1.0202}, {"name": "red_line2", "parent": "red_line1", "length": 13.33, "rotation": 90, "x": 76.85, "y": -35.6, "scaleY": 2.8556}, {"name": "red_line5", "parent": "red_line2", "length": 13.33, "x": 13.33}, {"name": "red_line6", "parent": "red_line5", "length": 13.33, "x": 13.33}, {"name": "red_line7", "parent": "red_line6", "length": 13.33, "x": 13.33}, {"name": "red_line8", "parent": "red_line7", "length": 13.33, "x": 13.33}, {"name": "red_line9", "parent": "red_line8", "length": 13.33, "x": 13.33}, {"name": "red_line10", "parent": "red_line9", "length": 13.33, "x": 13.33}, {"name": "red_line11", "parent": "red_line10", "length": 13.33, "x": 13.33}, {"name": "red_line12", "parent": "red_line11", "length": 13.33, "x": 13.33}, {"name": "red_line13", "parent": "red_line12", "length": 13.33, "x": 13.33}, {"name": "red_line14", "parent": "red_line13", "length": 13.33, "x": 13.33}, {"name": "red_line15", "parent": "red_line14", "length": 13.33, "x": 13.33}, {"name": "red_line16", "parent": "red_line15", "length": 13.33, "x": 13.33}, {"name": "red_line17", "parent": "red_line16", "length": 13.33, "x": 13.33}, {"name": "red_line18", "parent": "red_line17", "length": 13.33, "x": 13.33}, {"name": "red_line19", "parent": "red_line18", "length": 13.33, "x": 13.33}, {"name": "red_line20", "parent": "red_line19", "length": 13.33, "x": 13.33}, {"name": "red_line21", "parent": "red_line20", "length": 13.33, "x": 13.33}, {"name": "red_line22", "parent": "red_line21", "length": 13.33, "x": 13.33}, {"name": "red_line23", "parent": "red_line22", "length": 13.33, "x": 13.33}, {"name": "red_line3", "parent": "red_line_all", "x": 288.09, "y": 894.96, "scaleX": 1.0202, "scaleY": 1.0202}, {"name": "red_line4", "parent": "red_line3", "length": 13.33, "rotation": 90, "x": 76.85, "y": -35.6, "scaleY": 2.8556}, {"name": "red_line24", "parent": "red_line4", "length": 13.33, "x": 13.33}, {"name": "red_line25", "parent": "red_line24", "length": 13.33, "x": 13.33}, {"name": "red_line26", "parent": "red_line25", "length": 13.33, "x": 13.33}, {"name": "red_line27", "parent": "red_line26", "length": 13.33, "x": 13.33}, {"name": "red_line28", "parent": "red_line27", "length": 13.33, "x": 13.33}, {"name": "red_line29", "parent": "red_line28", "length": 13.33, "x": 13.33}, {"name": "red_line30", "parent": "red_line29", "length": 13.33, "x": 13.33}, {"name": "red_line31", "parent": "red_line30", "length": 13.33, "x": 13.33}, {"name": "red_line32", "parent": "red_line31", "length": 13.33, "x": 13.33}, {"name": "red_line33", "parent": "red_line32", "length": 13.33, "x": 13.33}, {"name": "red_line34", "parent": "red_line33", "length": 13.33, "x": 13.33}, {"name": "red_line35", "parent": "red_line34", "length": 13.33, "x": 13.33}, {"name": "red_line36", "parent": "red_line35", "length": 13.33, "x": 13.33}, {"name": "red_line37", "parent": "red_line36", "length": 13.33, "x": 13.33}, {"name": "red_line38", "parent": "red_line37", "length": 13.33, "x": 13.33}, {"name": "red_line39", "parent": "red_line38", "length": 13.33, "x": 13.33}, {"name": "red_line40", "parent": "red_line39", "length": 13.33, "x": 13.33}, {"name": "red_line41", "parent": "red_line40", "length": 13.33, "x": 13.33}, {"name": "red_line42", "parent": "red_line41", "length": 13.33, "x": 13.33}, {"name": "red_line43", "parent": "red_line_all", "x": 438.44, "y": 939.75, "scaleX": 1.0202, "scaleY": 1.0202}, {"name": "red_line44", "parent": "red_line43", "length": 13.33, "rotation": 90, "x": 76.85, "y": -35.6, "scaleY": 2.8556}, {"name": "red_line45", "parent": "red_line44", "length": 13.33, "x": 13.33}, {"name": "red_line46", "parent": "red_line45", "length": 13.33, "x": 13.33}, {"name": "red_line47", "parent": "red_line46", "length": 13.33, "x": 13.33}, {"name": "red_line48", "parent": "red_line47", "length": 13.33, "x": 13.33}, {"name": "red_line49", "parent": "red_line48", "length": 13.33, "x": 13.33}, {"name": "red_line50", "parent": "red_line49", "length": 13.33, "x": 13.33}, {"name": "red_line51", "parent": "red_line50", "length": 13.33, "x": 13.33}, {"name": "red_line52", "parent": "red_line51", "length": 13.33, "x": 13.33}, {"name": "red_line53", "parent": "red_line52", "length": 13.33, "x": 13.33}, {"name": "red_line54", "parent": "red_line53", "length": 13.33, "x": 13.33}, {"name": "red_line55", "parent": "red_line54", "length": 13.33, "x": 13.33}, {"name": "red_line56", "parent": "red_line55", "length": 13.33, "x": 13.33}, {"name": "red_line57", "parent": "red_line56", "length": 13.33, "x": 13.33}, {"name": "red_line58", "parent": "red_line57", "length": 13.33, "x": 13.33}, {"name": "red_line59", "parent": "red_line58", "length": 13.33, "x": 13.33}, {"name": "red_line60", "parent": "red_line59", "length": 13.33, "x": 13.33}, {"name": "red_line61", "parent": "red_line60", "length": 13.33, "x": 13.33}, {"name": "red_line62", "parent": "red_line61", "length": 13.33, "x": 13.33}, {"name": "red_line63", "parent": "red_line62", "length": 13.33, "x": 13.33}, {"name": "bone141", "parent": "root", "length": 256.92, "rotation": -0.45, "x": 74.26, "y": 99.63}, {"name": "boomall", "parent": "root", "x": 189.49, "y": 13.37, "scaleX": 2.3123, "scaleY": 2.3123, "color": "ff0000ff"}, {"name": "an", "parent": "boomall"}, {"name": "hen", "parent": "boomall"}, {"name": "kuo", "parent": "boomall"}, {"name": "quan", "parent": "boomall"}, {"name": "sg", "parent": "boomall"}, {"name": "sg2", "parent": "boomall"}, {"name": "xian", "parent": "boomall"}, {"name": "xian2", "parent": "boomall"}, {"name": "xian3", "parent": "boomall"}, {"name": "boomall2", "parent": "root", "x": 456.3, "y": 36.83, "scaleX": 3.2219, "scaleY": 3.2219, "color": "ff0000ff"}, {"name": "an2", "parent": "boomall2"}, {"name": "hen2", "parent": "boomall2"}, {"name": "kuo2", "parent": "boomall2"}, {"name": "quan2", "parent": "boomall2"}, {"name": "sg3", "parent": "boomall2"}, {"name": "sg4", "parent": "boomall2"}, {"name": "xian4", "parent": "boomall2", "rotation": 97.88}, {"name": "xian5", "parent": "boomall2", "rotation": 97.88}, {"name": "xian6", "parent": "boomall2", "rotation": 97.88}, {"name": "boomall3", "parent": "root", "x": 344.89, "y": 107.2, "scaleX": 2.0688, "scaleY": 2.0688, "color": "ff0000ff"}, {"name": "an3", "parent": "boomall3"}, {"name": "hen3", "parent": "boomall3"}, {"name": "kuo3", "parent": "boomall3"}, {"name": "quan3", "parent": "boomall3"}, {"name": "sg5", "parent": "boomall3"}, {"name": "sg6", "parent": "boomall3"}, {"name": "xian7", "parent": "boomall3", "rotation": 37.61}, {"name": "xian8", "parent": "boomall3", "rotation": 37.61}, {"name": "xian9", "parent": "boomall3", "rotation": 37.61}, {"name": "status", "parent": "root", "x": -0.09, "y": 165.4, "color": "ff0000ff"}, {"name": "qian_zj_all", "parent": "bone", "length": 113.37, "rotation": 0.39, "x": -50.86, "y": 5.35}, {"name": "hurt", "parent": "qian_zj_all", "x": 88.66, "y": 283.39, "color": "ff0000ff"}, {"name": "blood_bar", "parent": "qian_zj_all", "x": 71.3, "y": 554.4, "color": "ff0000ff"}, {"name": "spell_to", "parent": "qian_zj_all", "x": 456.22, "y": 211.69, "color": "ff0000ff"}, {"name": "attack2_to", "parent": "qian_zj_all", "x": 815.36, "y": 254.84, "color": "ff0000ff"}, {"name": "skill1_to", "parent": "qian_zj_all", "x": 560.47, "y": 273.26, "color": "ff0000ff"}, {"name": "attack1_to", "parent": "qian_zj_all", "x": 806.18, "y": 355.4, "color": "ff0000ff"}], "slots": [{"name": "a40", "bone": "bone124", "color": "ffffff00", "attachment": "a0"}, {"name": "db/jj_add/jj_1", "bone": "jj2", "blend": "additive"}, {"name": "db/ks_add/ks_1", "bone": "ks2", "blend": "additive"}, {"name": "db/ks_add/ks_2", "bone": "ks3", "blend": "additive"}, {"name": "db/kk_add/kk_01", "bone": "kk", "blend": "additive"}, {"name": "db/kk_add/kk_1", "bone": "kk2", "blend": "additive"}, {"name": "db/kk_add/kk_2", "bone": "kk3", "blend": "additive"}, {"name": "db/kk_add/kk_3", "bone": "kk4", "blend": "additive"}, {"name": "db/nuqi_add_02/nq_02_01", "bone": "nq02", "blend": "additive"}, {"name": "db/nuqi_add_02/nq_02_1", "bone": "nq2", "blend": "additive"}, {"name": "db/jjlz_add/lizi_01", "bone": "jjlz", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_1", "bone": "jjlz2", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_2", "bone": "jjlz3", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_3", "bone": "jjlz4", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_4", "bone": "jjlz5", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_5", "bone": "jjlz6", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_6", "bone": "jjlz7", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_7", "bone": "jjlz8", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_8", "bone": "jjlz9", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_9", "bone": "jjlz10", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_10", "bone": "jjlz11", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_11", "bone": "jjlz12", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_12", "bone": "jjlz13", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_13", "bone": "jjlz14", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_14", "bone": "jjlz15", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_15", "bone": "jjlz16", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_16", "bone": "jjlz17", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_17", "bone": "jjlz18", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_18", "bone": "jjlz19", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_19", "bone": "jjlz20", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/jjlz_add/lizi_20", "bone": "jjlz21", "color": "ffffff00", "attachment": "db/jjlz_add/lizi_01", "blend": "additive"}, {"name": "db/xlt_add/xlt_01", "bone": "xlt", "blend": "additive"}, {"name": "db/xlt_add/xlt_3", "bone": "xlt", "blend": "additive"}, {"name": "db/xlt_add/xlt_1", "bone": "xlt2", "blend": "additive"}, {"name": "db/xlt_add/xlt_2", "bone": "xlt3", "blend": "additive"}, {"name": "db/glow_add/glow_01", "bone": "glow", "color": "ffffff00", "attachment": "db/glow_add/glow_01", "blend": "additive"}, {"name": "db/xx/lizi_02", "bone": "xx", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_7", "bone": "xx7", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_2", "bone": "xx2", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_8", "bone": "xx8", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_3", "bone": "xx3", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_9", "bone": "xx9", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_4", "bone": "xx4", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_10", "bone": "xx10", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_14", "bone": "xx14", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_15", "bone": "xx15", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_5", "bone": "xx5", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_12", "bone": "xx12", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_11", "bone": "xx11", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_6", "bone": "xx6", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "db/xx/lizi_13", "bone": "xx13", "color": "ffffff00", "attachment": "db/xx/lizi_02"}, {"name": "0/smoke_wanghou/0_00000", "bone": "run1"}, {"name": "ju", "bone": "ju"}, {"name": "texiao/skill1/xiaohuo_1_add/xiaohuo_8", "bone": "xia<PERSON><PERSON>o", "blend": "additive"}, {"name": "texiao/skill1/xiaohuo_1_add/xiaohuo_10", "bone": "xiaohupo2", "blend": "additive"}, {"name": "texiao/skill1/xiaohuo_1_add/xiaohuo_12", "bone": "xiaohupo3", "blend": "additive"}, {"name": "texiao/skill1/xiaohuo_1_add/xiaohuo_9", "bone": "xia<PERSON><PERSON>o"}, {"name": "texiao/skill1/xiaohuo_1_add/xiaohuo_11", "bone": "xiaohupo2"}, {"name": "texiao/skill1/xiaohuo_1_add/xiaohuo_13", "bone": "xiaohupo3"}, {"name": "texiao/skill1/yshuo_add/yshuo_0", "bone": "yshuo1", "color": "ffa4a4ff", "blend": "additive"}, {"name": "glow", "bone": "glow2", "blend": "additive"}, {"name": "ju4", "bone": "ju4"}, {"name": "ju5", "bone": "ju9"}, {"name": "ju10", "bone": "ju10"}, {"name": "ju6", "bone": "ju5", "blend": "additive"}, {"name": "ju7", "bone": "ju6", "blend": "additive"}, {"name": "ju8", "bone": "ju7", "blend": "additive"}, {"name": "ju9", "bone": "ju8", "blend": "additive"}, {"name": "rao", "bone": "rao", "blend": "additive"}, {"name": "red_line", "bone": "red_line2", "color": "ffffff00", "attachment": "red_line"}, {"name": "red_line3", "bone": "red_line4", "color": "ffffff00", "attachment": "red_line"}, {"name": "red_line5", "bone": "red_line44", "color": "ffffff00", "attachment": "red_line"}, {"name": "red_line1", "bone": "red_line1", "attachment": "red_line1", "blend": "additive"}, {"name": "red_line2", "bone": "red_line3", "color": "ffffff00", "attachment": "red_line1", "blend": "additive"}, {"name": "red_line4", "bone": "red_line43", "color": "ffffff00", "attachment": "red_line1", "blend": "additive"}, {"name": "a29", "bone": "red_line23", "color": "ffffff00", "attachment": "a24"}, {"name": "a30", "bone": "red_line4", "color": "ffffff00", "attachment": "a24"}, {"name": "a31", "bone": "red_line63", "color": "ffffff00", "attachment": "a24"}, {"name": "db/glow_add/glow_1", "bone": "bone141", "color": "ffffff00", "attachment": "db/glow_add/glow_01", "blend": "additive"}, {"name": "db/glow_add/glow_2", "bone": "bone141", "color": "ffffff00", "attachment": "db/glow_add/glow_01", "blend": "additive"}, {"name": "an", "bone": "an"}, {"name": "an2", "bone": "an2"}, {"name": "an3", "bone": "an3"}, {"name": "xian", "bone": "xian", "blend": "additive"}, {"name": "xian4", "bone": "xian4", "blend": "additive"}, {"name": "xian7", "bone": "xian7", "blend": "additive"}, {"name": "xian2", "bone": "xian2", "blend": "additive"}, {"name": "xian5", "bone": "xian5", "blend": "additive"}, {"name": "xian8", "bone": "xian8", "blend": "additive"}, {"name": "xian3", "bone": "xian3", "blend": "additive"}, {"name": "xian6", "bone": "xian6", "blend": "additive"}, {"name": "xian9", "bone": "xian9", "blend": "additive"}, {"name": "quan", "bone": "quan"}, {"name": "quan3", "bone": "quan2"}, {"name": "quan5", "bone": "quan3"}, {"name": "quan2", "bone": "quan", "blend": "additive"}, {"name": "quan4", "bone": "quan2", "blend": "additive"}, {"name": "quan6", "bone": "quan3", "blend": "additive"}, {"name": "hen", "bone": "hen", "blend": "additive"}, {"name": "hen2", "bone": "hen2", "blend": "additive"}, {"name": "hen3", "bone": "hen3", "blend": "additive"}, {"name": "sg", "bone": "sg", "blend": "additive"}, {"name": "sg3", "bone": "sg3", "blend": "additive"}, {"name": "sg5", "bone": "sg5", "blend": "additive"}, {"name": "sg2", "bone": "sg2", "blend": "additive"}, {"name": "sg4", "bone": "sg4", "blend": "additive"}, {"name": "sg6", "bone": "sg6", "blend": "additive"}, {"name": "kuo", "bone": "kuo", "blend": "additive"}, {"name": "kuo2", "bone": "kuo2", "blend": "additive"}, {"name": "kuo3", "bone": "kuo3", "blend": "additive"}, {"name": "<PERSON>b<PERSON>", "bone": "bone", "attachment": "<PERSON>b<PERSON>"}], "path": [{"name": "red_line1", "bones": ["red_line2", "red_line5", "red_line6", "red_line7", "red_line8", "red_line9", "red_line10", "red_line11", "red_line12", "red_line13", "red_line14", "red_line15", "red_line16", "red_line17", "red_line18", "red_line19", "red_line20", "red_line21", "red_line22", "red_line23"], "target": "red_line1", "position": -0.9208, "spacing": 41.3}, {"name": "red_line2", "order": 1, "bones": ["red_line42", "red_line41", "red_line40", "red_line39", "red_line38", "red_line37", "red_line36", "red_line35", "red_line34", "red_line33", "red_line32", "red_line31", "red_line30", "red_line29", "red_line28", "red_line27", "red_line26", "red_line25", "red_line24", "red_line4"], "target": "red_line2"}, {"name": "red_line4", "order": 2, "bones": ["red_line44", "red_line45", "red_line46", "red_line47", "red_line48", "red_line49", "red_line50", "red_line51", "red_line52", "red_line53", "red_line54", "red_line55", "red_line56", "red_line57", "red_line58", "red_line59", "red_line60", "red_line61", "red_line62", "red_line63"], "target": "red_line4"}], "skins": [{"name": "default", "attachments": {"quan2": {"skill1/quan_00033": {"width": 30, "height": 29}}, "quan3": {"skill1/quan_00033": {"width": 30, "height": 29}}, "texiao/skill1/xiaohuo_1_add/xiaohuo_10": {"texiao/skill1/xiaohuo_1_add/xiaohuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "texiao/skill1/xiaohuo_1_add/xiaohuo_11": {"texiao/skill1/xiaohuo_1_add/xiaohuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "quan6": {"skill1/quan_00033": {"width": 30, "height": 29}}, "texiao/skill1/xiaohuo_1_add/xiaohuo_13": {"texiao/skill1/xiaohuo_1_add/xiaohuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "red_line2": {"red_line1": {"type": "path", "lengths": [737.49, 1923.81, 3894.84], "vertexCount": 9, "vertices": [-1236.46, -196.94, -1183.03, -172.82, -801.9, -0.75, -795.64, -103.81, -465.55, -140.77, 101.78, -204.3, 69.12, 37.14, 693.25, -11.02, 777.24, -17.5]}}, "ju6": {"skill1/huo4/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00038": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00040": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}}, "bonebj": {"bonebj": {"type": "boundingbox", "vertexCount": 4, "vertices": [-572.88, 812.06, -479.22, -339.94, 593.17, -330.57, 541.66, 877.62]}}, "hen": {"skill1/glow2_00128": {"width": 66, "height": 48}}, "xian2": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [9.09, -5.75, -7, -3.58, -16.12, 69.9, 16.12, 69.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "xian3": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 1e-05, 1, 1e-05], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [6.82, -5.08, -7, -5.84, -18.07, 75.38, 18.07, 75.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "xian4": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [7, -6.1, -7, -6.1, -16.08, 74.08, 16.08, 74.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "xian5": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [9.09, -5.75, -7, -3.58, -16.12, 69.9, 16.12, 69.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "xian6": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 1e-05, 1, 1e-05], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [6.82, -5.08, -7, -5.84, -18.07, 75.38, 18.07, 75.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "xian7": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [7, -6.1, -7, -6.1, -16.08, 74.08, 16.08, 74.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "xian8": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [9.09, -5.75, -7, -3.58, -16.12, 69.9, 16.12, 69.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "xian9": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 1e-05, 1, 1e-05], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [6.82, -5.08, -7, -5.84, -18.07, 75.38, 18.07, 75.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "db/xlt_add/xlt_01": {"db/xlt_add/xlt_01": {"width": 154, "height": 90}, "db/xlt_add/xlt_02": {"width": 154, "height": 90}, "db/xlt_add/xlt_03": {"width": 154, "height": 90}, "db/xlt_add/xlt_04": {"width": 154, "height": 90}, "db/xlt_add/xlt_05": {"width": 154, "height": 90}, "db/xlt_add/xlt_06": {"width": 154, "height": 90}, "db/xlt_add/xlt_07": {"width": 154, "height": 90}, "db/xlt_add/xlt_08": {"width": 154, "height": 90}, "db/xlt_add/xlt_09": {"width": 154, "height": 90}, "db/xlt_add/xlt_10": {"width": 154, "height": 90}, "db/xlt_add/xlt_11": {"width": 154, "height": 90}, "db/xlt_add/xlt_12": {"width": 154, "height": 90}, "db/xlt_add/xlt_13": {"width": 154, "height": 90}}, "rao": {"skill1/z_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [125, -125, -125, -125, -125, 125, 125, 125], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 250, "height": 250}}, "db/kk_add/kk_01": {"db/kk_add/kk_03": {"width": 79, "height": 86}, "db/kk_add/kk_05": {"width": 79, "height": 86}, "db/kk_add/kk_07": {"width": 79, "height": 86}, "db/kk_add/kk_09": {"width": 79, "height": 86}, "db/kk_add/kk_11": {"width": 79, "height": 86}}, "an": {"skill1/sg/1_00031": {"width": 125, "height": 119}, "skill1/sg/1_00032": {"width": 125, "height": 119}, "skill1/sg/1_00033": {"width": 125, "height": 119}, "skill1/sg/1_00035": {"width": 125, "height": 119}}, "db/glow_add/glow_2": {"db/glow_add/glow_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [856.93, -199.94, -653.05, -211.93, -644.97, 107.01, 865.02, 119], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 140, "height": 140}}, "hen2": {"skill1/glow2_00128": {"width": 66, "height": 48}}, "hen3": {"skill1/glow2_00128": {"width": 66, "height": 48}}, "db/glow_add/glow_01": {"db/glow_add/glow_01": {"x": -1.04, "y": -5.08, "width": 140, "height": 140}}, "kuo": {"skill1/2_00033": {"width": 50, "height": 65}}, "ju4": {"skill1/huo5/1_00031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}}, "ju5": {"skill1/huo5/1_00031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}}, "ju": {"skill1/ju/1_00017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00023": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00027": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00029": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00033": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00035": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00037": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00039": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}, "skill1/ju/1_00041": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [97, -97.5, -97, -97.5, -97, 97.5, 97, 97.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 194, "height": 195}}, "ju7": {"skill1/huo4/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00038": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00040": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}}, "ju8": {"skill1/huo4/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00038": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00040": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}}, "0/smoke_wanghou/0_00000": {"0/smoke_wanghou/0_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}, "0/smoke_wanghou/0_00011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39, -13, -38, -13, -38, 13, 39, 13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 77, "height": 26}}, "quan": {"skill1/quan_00033": {"width": 30, "height": 29}}, "sg": {"skill1/sg2/1_00032": {"width": 54, "height": 55}, "skill1/sg2/1_00033": {"width": 54, "height": 55}}, "quan4": {"skill1/quan_00033": {"width": 30, "height": 29}}, "sg2": {"skill1/sg3/1_00031": {"width": 96, "height": 115}, "skill1/sg3/1_00032": {"width": 96, "height": 115}}, "sg3": {"skill1/sg2/1_00032": {"width": 54, "height": 55}, "skill1/sg2/1_00033": {"width": 54, "height": 55}}, "sg4": {"skill1/sg3/1_00031": {"width": 96, "height": 115}, "skill1/sg3/1_00032": {"width": 96, "height": 115}}, "sg5": {"skill1/sg2/1_00032": {"width": 54, "height": 55}, "skill1/sg2/1_00033": {"width": 54, "height": 55}}, "sg6": {"skill1/sg3/1_00031": {"width": 96, "height": 115}, "skill1/sg3/1_00032": {"width": 96, "height": 115}}, "db/xx/lizi_3": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "a29": {"a24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-2.55, -12.73, -13.26, 2.81, 52.62, 13.21, 152.78, -0.63], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 72}}, "db/xlt_add/xlt_1": {"db/xlt_add/xlt_01": {"width": 154, "height": 90}, "db/xlt_add/xlt_02": {"width": 154, "height": 90}, "db/xlt_add/xlt_03": {"width": 154, "height": 90}, "db/xlt_add/xlt_04": {"width": 154, "height": 90}, "db/xlt_add/xlt_05": {"width": 154, "height": 90}, "db/xlt_add/xlt_06": {"width": 154, "height": 90}, "db/xlt_add/xlt_07": {"width": 154, "height": 90}, "db/xlt_add/xlt_08": {"width": 154, "height": 90}, "db/xlt_add/xlt_09": {"width": 154, "height": 90}, "db/xlt_add/xlt_10": {"width": 154, "height": 90}, "db/xlt_add/xlt_11": {"width": 154, "height": 90}, "db/xlt_add/xlt_12": {"width": 154, "height": 90}, "db/xlt_add/xlt_13": {"width": 154, "height": 90}}, "db/xlt_add/xlt_2": {"db/xlt_add/xlt_01": {"width": 154, "height": 90}, "db/xlt_add/xlt_02": {"width": 154, "height": 90}, "db/xlt_add/xlt_03": {"width": 154, "height": 90}, "db/xlt_add/xlt_04": {"width": 154, "height": 90}, "db/xlt_add/xlt_05": {"width": 154, "height": 90}, "db/xlt_add/xlt_06": {"width": 154, "height": 90}, "db/xlt_add/xlt_07": {"width": 154, "height": 90}, "db/xlt_add/xlt_08": {"width": 154, "height": 90}, "db/xlt_add/xlt_09": {"width": 154, "height": 90}, "db/xlt_add/xlt_10": {"width": 154, "height": 90}, "db/xlt_add/xlt_11": {"width": 154, "height": 90}, "db/xlt_add/xlt_12": {"width": 154, "height": 90}, "db/xlt_add/xlt_13": {"width": 154, "height": 90}}, "db/xlt_add/xlt_3": {"db/xlt_add/xlt_01": {"width": 154, "height": 90}, "db/xlt_add/xlt_02": {"width": 154, "height": 90}, "db/xlt_add/xlt_03": {"width": 154, "height": 90}, "db/xlt_add/xlt_04": {"width": 154, "height": 90}, "db/xlt_add/xlt_05": {"width": 154, "height": 90}, "db/xlt_add/xlt_06": {"width": 154, "height": 90}, "db/xlt_add/xlt_07": {"width": 154, "height": 90}, "db/xlt_add/xlt_08": {"width": 154, "height": 90}, "db/xlt_add/xlt_09": {"width": 154, "height": 90}, "db/xlt_add/xlt_10": {"width": 154, "height": 90}, "db/xlt_add/xlt_11": {"width": 154, "height": 90}, "db/xlt_add/xlt_12": {"width": 154, "height": 90}, "db/xlt_add/xlt_13": {"width": 154, "height": 90}}, "a30": {"a24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1.31, -12.06, -6.8, 3.37, 61.06, 14.2, 159.22, 1.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 72}}, "a31": {"a24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1.61, -12.29, -8.42, 3.09, 58.03, 14.36, 157.72, 1.88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 23, "height": 72}}, "db/nuqi_add_02/nq_02_01": {"db/nuqi_add_02/nq_02_01": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_02": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_03": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_04": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_05": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_06": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_07": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_08": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_09": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_10": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_11": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_12": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_13": {"width": 72, "height": 186}}, "db/jj_add/jj_1": {"db/jj_add/jj_05": {"width": 114, "height": 114}}, "an2": {"skill1/sg/1_00031": {"width": 125, "height": 119}, "skill1/sg/1_00032": {"width": 125, "height": 119}, "skill1/sg/1_00033": {"width": 125, "height": 119}, "skill1/sg/1_00035": {"width": 125, "height": 119}}, "db/glow_add/glow_1": {"db/glow_add/glow_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [864.95, -207.16, -645.03, -219.14, -648.48, 215.12, 861.5, 227.1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 140, "height": 140}}, "red_line1": {"red_line1": {"type": "path", "lengths": [897.41, 1986.28, 4030.63], "vertexCount": 9, "vertices": [-1393.74, 164.25, -1335.34, 159.12, -905.44, 121.36, -789.18, 59.8, -458.09, 33.31, -127.42, 6.84, 180.33, -179.56, 604.85, -122.22, 688.34, -110.95]}}, "db/nuqi_add_02/nq_02_1": {"db/nuqi_add_02/nq_02_01": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_02": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_03": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_04": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_05": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_06": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_07": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_08": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_09": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_10": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_11": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_12": {"width": 72, "height": 186}, "db/nuqi_add_02/nq_02_13": {"width": 72, "height": 186}}, "red_line3": {"red_line": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.9697, 0, 0.93939, 0, 0.90909, 0, 0.87879, 0, 0.84848, 0, 0.81818, 0, 0.78788, 0, 0.75758, 0, 0.72727, 0, 0.69697, 0, 0.66667, 0, 0.63636, 0, 0.60606, 0, 0.57576, 0, 0.54545, 0, 0.51515, 0, 0.48485, 0, 0.45455, 0, 0.42424, 0, 0.39394, 0, 0.36364, 0, 0.33333, 0, 0.30303, 0, 0.27273, 0, 0.24242, 0, 0.21212, 0, 0.18182, 0, 0.15152, 0, 0.12121, 0, 0.09091, 0, 0.06061, 0, 0.0303, 0, 0, 1, 0, 1, 0.0303, 1, 0.06061, 1, 0.09091, 1, 0.12121, 1, 0.15152, 1, 0.18182, 1, 0.21212, 1, 0.24242, 1, 0.27273, 1, 0.30303, 1, 0.33333, 1, 0.36364, 1, 0.39394, 1, 0.42424, 1, 0.45455, 1, 0.48485, 1, 0.51515, 1, 0.54545, 1, 0.57576, 1, 0.60606, 1, 0.63636, 1, 0.66667, 1, 0.69697, 1, 0.72727, 1, 0.75758, 1, 0.78788, 1, 0.81818, 1, 0.84848, 1, 0.87879, 1, 0.90909, 1, 0.93939, 1, 0.9697], "triangles": [37, 32, 36, 32, 33, 36, 36, 33, 35, 33, 34, 35, 30, 31, 38, 38, 31, 37, 31, 32, 37, 40, 29, 39, 29, 30, 39, 39, 30, 38, 27, 28, 41, 41, 28, 40, 28, 29, 40, 25, 26, 43, 43, 26, 42, 26, 27, 42, 42, 27, 41, 24, 25, 44, 44, 25, 43, 22, 23, 46, 46, 23, 45, 23, 24, 45, 45, 24, 44, 48, 21, 47, 21, 22, 47, 47, 22, 46, 19, 20, 49, 49, 20, 48, 20, 21, 48, 17, 18, 51, 51, 18, 50, 18, 19, 50, 50, 19, 49, 53, 16, 52, 16, 17, 52, 52, 17, 51, 14, 15, 54, 54, 15, 53, 15, 16, 53, 12, 13, 56, 56, 13, 55, 13, 14, 55, 55, 14, 54, 11, 12, 57, 57, 12, 56, 9, 10, 59, 59, 10, 58, 10, 11, 58, 58, 11, 57, 61, 8, 60, 8, 9, 60, 60, 9, 59, 6, 7, 62, 62, 7, 61, 7, 8, 61, 4, 5, 64, 64, 5, 63, 5, 6, 63, 63, 6, 62, 3, 4, 65, 65, 4, 64, 0, 1, 67, 1, 2, 67, 67, 2, 66, 2, 3, 66, 66, 3, 65], "vertices": [2, 108, -3.3, -3.71, 0.95373, 109, -16.63, -3.71, 0.04627, 2, 108, -3.3, 3.59, 0.94483, 109, -16.63, 3.59, 0.05517, 2, 108, 4.88, 3.59, 0.77856, 109, -8.45, 3.59, 0.22144, 3, 108, 13.06, 3.59, 0.50117, 109, -0.27, 3.59, 0.39729, 110, -13.6, 3.59, 0.10154, 4, 108, 21.24, 3.59, 0.223, 109, 7.91, 3.59, 0.46281, 110, -5.42, 3.59, 0.30791, 111, -18.75, 3.59, 0.00628, 4, 108, 29.42, 3.59, 0.05594, 109, 16.09, 3.59, 0.3061, 110, 2.77, 3.59, 0.51428, 111, -10.56, 3.59, 0.12367, 4, 109, 24.28, 3.59, 0.13025, 110, 10.95, 3.59, 0.51758, 111, -2.38, 3.59, 0.28008, 112, -15.71, 3.59, 0.07209, 4, 109, 32.46, 3.59, 0.00957, 110, 19.13, 3.59, 0.3112, 111, 5.8, 3.59, 0.42394, 112, -7.53, 3.59, 0.25529, 4, 110, 27.31, 3.59, 0.10483, 111, 13.98, 3.59, 0.34557, 112, 0.65, 3.59, 0.43859, 113, -12.68, 3.59, 0.111, 4, 111, 22.16, 3.59, 0.18916, 112, 8.83, 3.59, 0.47773, 113, -4.49, 3.59, 0.30694, 114, -17.82, 3.59, 0.02617, 4, 111, 30.35, 3.59, 0.03902, 112, 17.02, 3.59, 0.29463, 113, 3.69, 3.59, 0.50288, 114, -9.64, 3.59, 0.16346, 4, 112, 25.2, 3.59, 0.11133, 113, 11.87, 3.59, 0.47682, 114, -1.46, 3.59, 0.32894, 115, -14.79, 3.59, 0.08291, 5, 112, 33.38, 3.59, 0.00011, 113, 20.05, 3.59, 0.28088, 114, 6.72, 3.59, 0.44208, 115, -6.61, 3.59, 0.2768, 116, -19.94, 3.59, 0.00014, 4, 113, 28.23, 3.59, 0.08494, 114, 14.9, 3.59, 0.333, 115, 1.58, 3.59, 0.47068, 116, -11.75, 3.59, 0.11138, 4, 114, 23.09, 3.59, 0.16751, 115, 9.76, 3.59, 0.49875, 116, -3.57, 3.59, 0.29169, 117, -16.9, 3.59, 0.04205, 4, 114, 31.27, 3.59, 0.0282, 115, 17.94, 3.59, 0.30486, 116, 4.61, 3.59, 0.47173, 117, -8.72, 3.59, 0.19521, 4, 115, 26.12, 3.59, 0.11097, 116, 12.79, 3.59, 0.42955, 117, -0.54, 3.59, 0.35846, 118, -13.87, 3.59, 0.10102, 4, 116, 20.97, 3.59, 0.24924, 117, 7.64, 3.59, 0.43762, 118, -5.68, 3.59, 0.30097, 119, -19.01, 3.59, 0.01217, 4, 116, 29.16, 3.59, 0.06906, 117, 15.83, 3.59, 0.29455, 118, 2.5, 3.59, 0.50226, 119, -10.83, 3.59, 0.13413, 4, 117, 24.01, 3.59, 0.1313, 118, 10.68, 3.59, 0.50151, 119, -2.65, 3.59, 0.30533, 120, -15.98, 3.59, 0.06186, 4, 117, 32.19, 3.59, 0.01009, 118, 18.86, 3.59, 0.30288, 119, 5.53, 3.59, 0.45218, 120, -7.8, 3.59, 0.23484, 4, 118, 27.04, 3.59, 0.1016, 119, 13.71, 3.59, 0.37948, 120, 0.39, 3.59, 0.41465, 121, -12.94, 3.59, 0.10428, 5, 118, 35.22, 3.59, 0.00133, 119, 21.9, 3.59, 0.20827, 120, 8.57, 3.59, 0.47073, 121, -4.76, 3.59, 0.29639, 122, -18.09, 3.59, 0.02328, 4, 119, 30.08, 3.59, 0.04925, 120, 16.75, 3.59, 0.30458, 121, 3.42, 3.59, 0.48849, 122, -9.91, 3.59, 0.15768, 4, 120, 24.93, 3.59, 0.12477, 121, 11.6, 3.59, 0.47204, 122, -1.73, 3.59, 0.32589, 123, -15.06, 3.59, 0.0773, 4, 120, 33.11, 3.59, 0.00683, 121, 19.78, 3.59, 0.27993, 122, 6.45, 3.59, 0.44753, 123, -6.87, 3.59, 0.26571, 4, 121, 27.97, 3.59, 0.08783, 122, 14.64, 3.59, 0.34695, 123, 1.31, 3.59, 0.45426, 124, -12.02, 3.59, 0.11097, 4, 122, 22.82, 3.59, 0.17874, 123, 9.49, 3.59, 0.48822, 124, -3.84, 3.59, 0.2904, 125, -17.17, 3.59, 0.04264, 4, 122, 31, 3.59, 0.03381, 123, 17.67, 3.59, 0.29995, 124, 4.34, 3.59, 0.46984, 125, -8.99, 3.59, 0.19639, 4, 123, 25.85, 3.59, 0.1114, 124, 12.52, 3.59, 0.42735, 125, -0.8, 3.59, 0.36865, 126, -14.13, 3.59, 0.0926, 5, 123, 34.03, 3.59, 0.00014, 124, 20.71, 3.59, 0.24791, 125, 7.38, 3.59, 0.45563, 126, -5.95, 3.59, 0.29099, 127, -19.28, 3.59, 0.00533, 4, 124, 28.89, 3.59, 0.06847, 125, 15.56, 3.59, 0.32039, 126, 2.23, 3.59, 0.49143, 127, -11.1, 3.59, 0.11971, 3, 125, 23.74, 3.59, 0.14813, 126, 10.41, 3.59, 0.50667, 127, -2.92, 3.59, 0.3452, 3, 125, 31.92, 3.59, 0.01851, 126, 18.59, 3.59, 0.31034, 127, 5.26, 3.59, 0.67115, 2, 126, 26.78, 3.59, 0.10989, 127, 13.45, 3.59, 0.89011, 2, 126, 26.78, -3.71, 0.10976, 127, 13.45, -3.71, 0.89024, 3, 125, 31.92, -3.71, 0.01786, 126, 18.59, -3.71, 0.30866, 127, 5.26, -3.71, 0.67348, 4, 124, 37.07, -3.71, 0.00011, 125, 23.74, -3.71, 0.14672, 126, 10.41, -3.71, 0.50962, 127, -2.92, -3.71, 0.34355, 4, 124, 28.89, -3.71, 0.07262, 125, 15.56, -3.71, 0.31429, 126, 2.23, -3.71, 0.49517, 127, -11.1, -3.71, 0.11792, 5, 123, 34.04, -3.71, 0.00187, 124, 20.71, -3.71, 0.25436, 125, 7.38, -3.71, 0.44615, 126, -5.95, -3.71, 0.29421, 127, -19.28, -3.71, 0.00341, 4, 123, 25.85, -3.71, 0.11406, 124, 12.52, -3.71, 0.43669, 125, -0.8, -3.71, 0.356, 126, -14.13, -3.71, 0.09325, 4, 122, 31, -3.71, 0.03316, 123, 17.67, -3.71, 0.3042, 124, 4.34, -3.71, 0.47421, 125, -8.99, -3.71, 0.18843, 4, 122, 22.82, -3.71, 0.17743, 123, 9.49, -3.71, 0.49059, 124, -3.84, -3.71, 0.29327, 125, -17.17, -3.71, 0.03871, 4, 121, 27.97, -3.71, 0.08884, 122, 14.64, -3.71, 0.34397, 123, 1.31, -3.71, 0.45636, 124, -12.02, -3.71, 0.11084, 5, 120, 33.11, -3.71, 0.01016, 121, 19.78, -3.71, 0.27863, 122, 6.45, -3.71, 0.44419, 123, -6.87, -3.71, 0.26622, 124, -20.2, -3.71, 0.0008, 4, 120, 24.93, -3.71, 0.13143, 121, 11.6, -3.71, 0.46842, 122, -1.73, -3.71, 0.3222, 123, -15.06, -3.71, 0.07795, 4, 119, 30.08, -3.71, 0.05658, 120, 16.75, -3.71, 0.30723, 121, 3.42, -3.71, 0.48053, 122, -9.91, -3.71, 0.15566, 4, 119, 21.9, -3.71, 0.22427, 120, 8.57, -3.71, 0.46272, 121, -4.76, -3.71, 0.29074, 122, -18.09, -3.71, 0.02227, 4, 118, 27.04, -3.71, 0.10401, 119, 13.71, -3.71, 0.39906, 120, 0.39, -3.71, 0.39598, 121, -12.94, -3.71, 0.10095, 4, 117, 32.19, -3.71, 0.00765, 118, 18.86, -3.71, 0.31148, 119, 5.53, -3.71, 0.46069, 120, -7.8, -3.71, 0.22018, 4, 117, 24.01, -3.71, 0.12475, 118, 10.68, -3.71, 0.52061, 119, -2.65, -3.71, 0.30011, 120, -15.98, -3.71, 0.05453, 4, 116, 29.16, -3.71, 0.07091, 117, 15.83, -3.71, 0.28205, 118, 2.5, -3.71, 0.52172, 119, -10.83, -3.71, 0.12532, 5, 115, 34.3, -3.71, 0.00157, 116, 20.97, -3.71, 0.25137, 117, 7.65, -3.71, 0.42404, 118, -5.68, -3.71, 0.31592, 119, -19.01, -3.71, 0.0071, 4, 115, 26.12, -3.71, 0.11173, 116, 12.79, -3.71, 0.43434, 117, -0.54, -3.71, 0.34714, 118, -13.87, -3.71, 0.10679, 5, 114, 31.27, -3.71, 0.02604, 115, 17.94, -3.71, 0.30696, 116, 4.61, -3.71, 0.47549, 117, -8.72, -3.71, 0.18984, 118, -22.05, -3.71, 0.00166, 4, 114, 23.09, -3.71, 0.16319, 115, 9.76, -3.71, 0.49906, 116, -3.57, -3.71, 0.29755, 117, -16.9, -3.71, 0.0402, 4, 113, 28.23, -3.71, 0.07739, 114, 14.9, -3.71, 0.33407, 115, 1.58, -3.71, 0.47397, 116, -11.75, -3.71, 0.11458, 5, 112, 33.38, -3.71, 0.00597, 113, 20.05, -3.71, 0.25992, 114, 6.72, -3.71, 0.45286, 115, -6.61, -3.71, 0.27873, 116, -19.94, -3.71, 0.00252, 4, 112, 25.2, -3.71, 0.12095, 113, 11.87, -3.71, 0.44455, 114, -1.46, -3.71, 0.34943, 115, -14.79, -3.71, 0.08507, 4, 111, 30.35, -3.71, 0.04424, 112, 17.02, -3.71, 0.30281, 113, 3.69, -3.71, 0.4744, 114, -9.64, -3.71, 0.17855, 4, 111, 22.16, -3.71, 0.19958, 112, 8.84, -3.71, 0.47273, 113, -4.49, -3.71, 0.29397, 114, -17.82, -3.71, 0.03372, 4, 110, 27.31, -3.71, 0.09697, 111, 13.98, -3.71, 0.36907, 112, 0.65, -3.71, 0.42462, 113, -12.68, -3.71, 0.10934, 5, 109, 32.46, -3.71, 0.00619, 110, 19.13, -3.71, 0.29887, 111, 5.8, -3.71, 0.45008, 112, -7.53, -3.71, 0.24276, 113, -20.86, -3.71, 0.0021, 4, 109, 24.28, -3.71, 0.1218, 110, 10.95, -3.71, 0.50245, 111, -2.38, -3.71, 0.30887, 112, -15.71, -3.71, 0.06687, 4, 108, 29.42, -3.71, 0.06484, 109, 16.09, -3.71, 0.28368, 110, 2.77, -3.71, 0.51209, 111, -10.56, -3.71, 0.13939, 4, 108, 21.24, -3.71, 0.2408, 109, 7.91, -3.71, 0.43318, 110, -5.42, -3.71, 0.31188, 111, -18.74, -3.71, 0.01414, 3, 108, 13.06, -3.71, 0.52787, 109, -0.27, -3.71, 0.36384, 110, -13.6, -3.71, 0.10829, 3, 108, 4.88, -3.71, 0.79636, 109, -8.45, -3.71, 0.20196, 110, -21.78, -3.71, 0.00168], "hull": 68, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 0], "width": 24, "height": 154}}, "red_line4": {"red_line1": {"type": "path", "lengths": [1225.55, 2282.19, 4614.28], "vertexCount": 9, "vertices": [-1605.66, -2.96, -1547.03, -2.37, -764.65, 5.47, -671.59, 169.18, -347.96, 94.41, 134, -16.94, 48.63, -140.44, 679.59, -113.47, 763.75, -109.88]}}, "red_line5": {"red_line": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.9697, 0, 0.93939, 0, 0.90909, 0, 0.87879, 0, 0.84848, 0, 0.81818, 0, 0.78788, 0, 0.75758, 0, 0.72727, 0, 0.69697, 0, 0.66667, 0, 0.63636, 0, 0.60606, 0, 0.57576, 0, 0.54545, 0, 0.51515, 0, 0.48485, 0, 0.45455, 0, 0.42424, 0, 0.39394, 0, 0.36364, 0, 0.33333, 0, 0.30303, 0, 0.27273, 0, 0.24242, 0, 0.21212, 0, 0.18182, 0, 0.15152, 0, 0.12121, 0, 0.09091, 0, 0.06061, 0, 0.0303, 0, 0, 1, 0, 1, 0.0303, 1, 0.06061, 1, 0.09091, 1, 0.12121, 1, 0.15152, 1, 0.18182, 1, 0.21212, 1, 0.24242, 1, 0.27273, 1, 0.30303, 1, 0.33333, 1, 0.36364, 1, 0.39394, 1, 0.42424, 1, 0.45455, 1, 0.48485, 1, 0.51515, 1, 0.54545, 1, 0.57576, 1, 0.60606, 1, 0.63636, 1, 0.66667, 1, 0.69697, 1, 0.72727, 1, 0.75758, 1, 0.78788, 1, 0.81818, 1, 0.84848, 1, 0.87879, 1, 0.90909, 1, 0.93939, 1, 0.9697], "triangles": [37, 32, 36, 32, 33, 36, 36, 33, 35, 33, 34, 35, 30, 31, 38, 38, 31, 37, 31, 32, 37, 40, 29, 39, 29, 30, 39, 39, 30, 38, 27, 28, 41, 41, 28, 40, 28, 29, 40, 25, 26, 43, 43, 26, 42, 26, 27, 42, 42, 27, 41, 24, 25, 44, 44, 25, 43, 22, 23, 46, 46, 23, 45, 23, 24, 45, 45, 24, 44, 48, 21, 47, 21, 22, 47, 47, 22, 46, 19, 20, 49, 49, 20, 48, 20, 21, 48, 17, 18, 51, 51, 18, 50, 18, 19, 50, 50, 19, 49, 53, 16, 52, 16, 17, 52, 52, 17, 51, 14, 15, 54, 54, 15, 53, 15, 16, 53, 12, 13, 56, 56, 13, 55, 13, 14, 55, 55, 14, 54, 11, 12, 57, 57, 12, 56, 9, 10, 59, 59, 10, 58, 10, 11, 58, 58, 11, 57, 61, 8, 60, 8, 9, 60, 60, 9, 59, 6, 7, 62, 62, 7, 61, 7, 8, 61, 4, 5, 64, 64, 5, 63, 5, 6, 63, 63, 6, 62, 3, 4, 65, 65, 4, 64, 0, 1, 67, 1, 2, 67, 67, 2, 66, 2, 3, 66, 66, 3, 65], "vertices": [2, 129, -3.3, -3.71, 0.95373, 130, -16.63, -3.71, 0.04627, 2, 129, -3.3, 3.59, 0.94483, 130, -16.63, 3.59, 0.05517, 2, 129, 4.88, 3.59, 0.77856, 130, -8.45, 3.59, 0.22144, 3, 129, 13.06, 3.59, 0.50117, 130, -0.27, 3.59, 0.39729, 131, -13.6, 3.59, 0.10154, 4, 129, 21.24, 3.59, 0.223, 130, 7.91, 3.59, 0.46281, 131, -5.42, 3.59, 0.30791, 132, -18.75, 3.59, 0.00628, 4, 129, 29.42, 3.59, 0.05594, 130, 16.09, 3.59, 0.3061, 131, 2.77, 3.59, 0.51428, 132, -10.56, 3.59, 0.12367, 4, 130, 24.28, 3.59, 0.13025, 131, 10.95, 3.59, 0.51758, 132, -2.38, 3.59, 0.28008, 133, -15.71, 3.59, 0.07209, 4, 130, 32.46, 3.59, 0.00957, 131, 19.13, 3.59, 0.3112, 132, 5.8, 3.59, 0.42394, 133, -7.53, 3.59, 0.25529, 4, 131, 27.31, 3.59, 0.10483, 132, 13.98, 3.59, 0.34557, 133, 0.65, 3.59, 0.43859, 134, -12.68, 3.59, 0.111, 4, 132, 22.16, 3.59, 0.18916, 133, 8.83, 3.59, 0.47773, 134, -4.49, 3.59, 0.30694, 135, -17.82, 3.59, 0.02617, 4, 132, 30.35, 3.59, 0.03902, 133, 17.02, 3.59, 0.29463, 134, 3.69, 3.59, 0.50288, 135, -9.64, 3.59, 0.16346, 4, 133, 25.2, 3.59, 0.11133, 134, 11.87, 3.59, 0.47682, 135, -1.46, 3.59, 0.32894, 136, -14.79, 3.59, 0.08291, 5, 133, 33.38, 3.59, 0.00011, 134, 20.05, 3.59, 0.28088, 135, 6.72, 3.59, 0.44208, 136, -6.61, 3.59, 0.2768, 137, -19.94, 3.59, 0.00014, 4, 134, 28.23, 3.59, 0.08494, 135, 14.9, 3.59, 0.333, 136, 1.58, 3.59, 0.47068, 137, -11.75, 3.59, 0.11138, 4, 135, 23.09, 3.59, 0.16751, 136, 9.76, 3.59, 0.49875, 137, -3.57, 3.59, 0.29169, 138, -16.9, 3.59, 0.04205, 4, 135, 31.27, 3.59, 0.0282, 136, 17.94, 3.59, 0.30486, 137, 4.61, 3.59, 0.47173, 138, -8.72, 3.59, 0.19521, 4, 136, 26.12, 3.59, 0.11097, 137, 12.79, 3.59, 0.42955, 138, -0.54, 3.59, 0.35846, 139, -13.87, 3.59, 0.10102, 4, 137, 20.97, 3.59, 0.24924, 138, 7.64, 3.59, 0.43762, 139, -5.68, 3.59, 0.30097, 140, -19.01, 3.59, 0.01217, 4, 137, 29.16, 3.59, 0.06906, 138, 15.83, 3.59, 0.29455, 139, 2.5, 3.59, 0.50226, 140, -10.83, 3.59, 0.13413, 4, 138, 24.01, 3.59, 0.1313, 139, 10.68, 3.59, 0.50151, 140, -2.65, 3.59, 0.30533, 141, -15.98, 3.59, 0.06186, 4, 138, 32.19, 3.59, 0.01009, 139, 18.86, 3.59, 0.30288, 140, 5.53, 3.59, 0.45218, 141, -7.8, 3.59, 0.23484, 4, 139, 27.04, 3.59, 0.1016, 140, 13.71, 3.59, 0.37948, 141, 0.39, 3.59, 0.41465, 142, -12.94, 3.59, 0.10428, 5, 139, 35.22, 3.59, 0.00133, 140, 21.9, 3.59, 0.20827, 141, 8.57, 3.59, 0.47073, 142, -4.76, 3.59, 0.29639, 143, -18.09, 3.59, 0.02328, 4, 140, 30.08, 3.59, 0.04925, 141, 16.75, 3.59, 0.30458, 142, 3.42, 3.59, 0.48849, 143, -9.91, 3.59, 0.15768, 4, 141, 24.93, 3.59, 0.12477, 142, 11.6, 3.59, 0.47204, 143, -1.73, 3.59, 0.32589, 144, -15.06, 3.59, 0.0773, 4, 141, 33.11, 3.59, 0.00683, 142, 19.78, 3.59, 0.27993, 143, 6.45, 3.59, 0.44753, 144, -6.87, 3.59, 0.26571, 4, 142, 27.97, 3.59, 0.08783, 143, 14.64, 3.59, 0.34695, 144, 1.31, 3.59, 0.45426, 145, -12.02, 3.59, 0.11097, 4, 143, 22.82, 3.59, 0.17874, 144, 9.49, 3.59, 0.48822, 145, -3.84, 3.59, 0.2904, 146, -17.17, 3.59, 0.04264, 4, 143, 31, 3.59, 0.03381, 144, 17.67, 3.59, 0.29995, 145, 4.34, 3.59, 0.46984, 146, -8.99, 3.59, 0.19639, 4, 144, 25.85, 3.59, 0.1114, 145, 12.52, 3.59, 0.42735, 146, -0.8, 3.59, 0.36865, 147, -14.13, 3.59, 0.0926, 5, 144, 34.03, 3.59, 0.00014, 145, 20.71, 3.59, 0.24791, 146, 7.38, 3.59, 0.45563, 147, -5.95, 3.59, 0.29099, 148, -19.28, 3.59, 0.00533, 4, 145, 28.89, 3.59, 0.06847, 146, 15.56, 3.59, 0.32039, 147, 2.23, 3.59, 0.49143, 148, -11.1, 3.59, 0.11971, 3, 146, 23.74, 3.59, 0.14813, 147, 10.41, 3.59, 0.50667, 148, -2.92, 3.59, 0.3452, 3, 146, 31.92, 3.59, 0.01851, 147, 18.59, 3.59, 0.31034, 148, 5.26, 3.59, 0.67115, 2, 147, 26.78, 3.59, 0.10989, 148, 13.45, 3.59, 0.89011, 2, 147, 26.78, -3.71, 0.10976, 148, 13.45, -3.71, 0.89024, 3, 146, 31.92, -3.71, 0.01786, 147, 18.59, -3.71, 0.30866, 148, 5.26, -3.71, 0.67348, 4, 145, 37.07, -3.71, 0.00011, 146, 23.74, -3.71, 0.14672, 147, 10.41, -3.71, 0.50962, 148, -2.92, -3.71, 0.34355, 4, 145, 28.89, -3.71, 0.07262, 146, 15.56, -3.71, 0.31429, 147, 2.23, -3.71, 0.49517, 148, -11.1, -3.71, 0.11792, 5, 144, 34.04, -3.71, 0.00187, 145, 20.71, -3.71, 0.25436, 146, 7.38, -3.71, 0.44615, 147, -5.95, -3.71, 0.29421, 148, -19.28, -3.71, 0.00341, 4, 144, 25.85, -3.71, 0.11406, 145, 12.52, -3.71, 0.43669, 146, -0.8, -3.71, 0.356, 147, -14.13, -3.71, 0.09325, 4, 143, 31, -3.71, 0.03316, 144, 17.67, -3.71, 0.3042, 145, 4.34, -3.71, 0.47421, 146, -8.99, -3.71, 0.18843, 4, 143, 22.82, -3.71, 0.17743, 144, 9.49, -3.71, 0.49059, 145, -3.84, -3.71, 0.29327, 146, -17.17, -3.71, 0.03871, 4, 142, 27.97, -3.71, 0.08884, 143, 14.64, -3.71, 0.34397, 144, 1.31, -3.71, 0.45636, 145, -12.02, -3.71, 0.11084, 5, 141, 33.11, -3.71, 0.01016, 142, 19.78, -3.71, 0.27863, 143, 6.45, -3.71, 0.44419, 144, -6.87, -3.71, 0.26622, 145, -20.2, -3.71, 0.0008, 4, 141, 24.93, -3.71, 0.13143, 142, 11.6, -3.71, 0.46842, 143, -1.73, -3.71, 0.3222, 144, -15.06, -3.71, 0.07795, 4, 140, 30.08, -3.71, 0.05658, 141, 16.75, -3.71, 0.30723, 142, 3.42, -3.71, 0.48053, 143, -9.91, -3.71, 0.15566, 4, 140, 21.9, -3.71, 0.22427, 141, 8.57, -3.71, 0.46272, 142, -4.76, -3.71, 0.29074, 143, -18.09, -3.71, 0.02227, 4, 139, 27.04, -3.71, 0.10401, 140, 13.71, -3.71, 0.39906, 141, 0.39, -3.71, 0.39598, 142, -12.94, -3.71, 0.10095, 4, 138, 32.19, -3.71, 0.00765, 139, 18.86, -3.71, 0.31148, 140, 5.53, -3.71, 0.46069, 141, -7.8, -3.71, 0.22018, 4, 138, 24.01, -3.71, 0.12475, 139, 10.68, -3.71, 0.52061, 140, -2.65, -3.71, 0.30011, 141, -15.98, -3.71, 0.05453, 4, 137, 29.16, -3.71, 0.07091, 138, 15.83, -3.71, 0.28205, 139, 2.5, -3.71, 0.52172, 140, -10.83, -3.71, 0.12532, 5, 136, 34.3, -3.71, 0.00157, 137, 20.97, -3.71, 0.25137, 138, 7.65, -3.71, 0.42404, 139, -5.68, -3.71, 0.31592, 140, -19.01, -3.71, 0.0071, 4, 136, 26.12, -3.71, 0.11173, 137, 12.79, -3.71, 0.43434, 138, -0.54, -3.71, 0.34714, 139, -13.87, -3.71, 0.10679, 5, 135, 31.27, -3.71, 0.02604, 136, 17.94, -3.71, 0.30696, 137, 4.61, -3.71, 0.47549, 138, -8.72, -3.71, 0.18984, 139, -22.05, -3.71, 0.00166, 4, 135, 23.09, -3.71, 0.16319, 136, 9.76, -3.71, 0.49906, 137, -3.57, -3.71, 0.29755, 138, -16.9, -3.71, 0.0402, 4, 134, 28.23, -3.71, 0.07739, 135, 14.9, -3.71, 0.33407, 136, 1.58, -3.71, 0.47397, 137, -11.75, -3.71, 0.11458, 5, 133, 33.38, -3.71, 0.00597, 134, 20.05, -3.71, 0.25992, 135, 6.72, -3.71, 0.45286, 136, -6.61, -3.71, 0.27873, 137, -19.94, -3.71, 0.00252, 4, 133, 25.2, -3.71, 0.12095, 134, 11.87, -3.71, 0.44455, 135, -1.46, -3.71, 0.34943, 136, -14.79, -3.71, 0.08507, 4, 132, 30.35, -3.71, 0.04424, 133, 17.02, -3.71, 0.30281, 134, 3.69, -3.71, 0.4744, 135, -9.64, -3.71, 0.17855, 4, 132, 22.16, -3.71, 0.19958, 133, 8.84, -3.71, 0.47273, 134, -4.49, -3.71, 0.29397, 135, -17.82, -3.71, 0.03372, 4, 131, 27.31, -3.71, 0.09697, 132, 13.98, -3.71, 0.36907, 133, 0.65, -3.71, 0.42462, 134, -12.68, -3.71, 0.10934, 5, 130, 32.46, -3.71, 0.00619, 131, 19.13, -3.71, 0.29887, 132, 5.8, -3.71, 0.45008, 133, -7.53, -3.71, 0.24276, 134, -20.86, -3.71, 0.0021, 4, 130, 24.28, -3.71, 0.1218, 131, 10.95, -3.71, 0.50245, 132, -2.38, -3.71, 0.30887, 133, -15.71, -3.71, 0.06687, 4, 129, 29.42, -3.71, 0.06484, 130, 16.09, -3.71, 0.28368, 131, 2.77, -3.71, 0.51209, 132, -10.56, -3.71, 0.13939, 4, 129, 21.24, -3.71, 0.2408, 130, 7.91, -3.71, 0.43318, 131, -5.42, -3.71, 0.31188, 132, -18.74, -3.71, 0.01414, 3, 129, 13.06, -3.71, 0.52787, 130, -0.27, -3.71, 0.36384, 131, -13.6, -3.71, 0.10829, 3, 129, 4.88, -3.71, 0.79636, 130, -8.45, -3.71, 0.20196, 131, -21.78, -3.71, 0.00168], "hull": 68, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 0], "width": 24, "height": 154}}, "db/kk_add/kk_1": {"db/kk_add/kk_03": {"width": 79, "height": 86}, "db/kk_add/kk_05": {"width": 79, "height": 86}, "db/kk_add/kk_07": {"width": 79, "height": 86}, "db/kk_add/kk_09": {"width": 79, "height": 86}, "db/kk_add/kk_11": {"width": 79, "height": 86}}, "kuo2": {"skill1/2_00033": {"width": 50, "height": 65}}, "db/jjlz_add/lizi_9": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "an3": {"skill1/sg/1_00031": {"width": 125, "height": 119}, "skill1/sg/1_00032": {"width": 125, "height": 119}, "skill1/sg/1_00033": {"width": 125, "height": 119}, "skill1/sg/1_00035": {"width": 125, "height": 119}}, "texiao/skill1/yshuo_add/yshuo_0": {"texiao/skill1/yshuo_add/yshuo_0000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -100, -100, -100, -100, 100, 100, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "texiao/skill1/yshuo_add/yshuo_0001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -100, -100, -100, -100, 100, 100, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "texiao/skill1/yshuo_add/yshuo_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -100, -100, -100, -100, 100, 100, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "texiao/skill1/yshuo_add/yshuo_0005": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -100, -100, -100, -100, 100, 100, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "texiao/skill1/yshuo_add/yshuo_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -100, -100, -100, -100, 100, 100, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "texiao/skill1/yshuo_add/yshuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -100, -100, -100, -100, 100, 100, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}, "texiao/skill1/yshuo_add/yshuo_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100, -100, -100, -100, -100, 100, 100, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 200, "height": 200}}, "ju10": {"skill1/huo5/1_00031": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}, "skill1/huo5/1_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [147, -54.5, -147, -54.5, -147, 54.5, 147, 54.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 294, "height": 109}}, "glow": {"skill1/1_00025": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.5, -12.5, -12.5, -12.5, -12.5, 12.5, 12.5, 12.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 25, "height": 25}}, "db/ks_add/ks_1": {"db/ks_add/ks_05": {"width": 134, "height": 127}}, "db/ks_add/ks_2": {"db/ks_add/ks_05": {"width": 134, "height": 127}}, "db/jjlz_add/lizi_20": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "kuo3": {"skill1/2_00033": {"width": 50, "height": 65}}, "quan5": {"skill1/quan_00033": {"width": 30, "height": 29}}, "db/xx/lizi_2": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/kk_add/kk_3": {"db/kk_add/kk_03": {"width": 79, "height": 86}, "db/kk_add/kk_05": {"width": 79, "height": 86}, "db/kk_add/kk_07": {"width": 79, "height": 86}, "db/kk_add/kk_09": {"width": 79, "height": 86}, "db/kk_add/kk_11": {"width": 79, "height": 86}}, "db/xx/lizi_4": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_5": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_6": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_7": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_8": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_9": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/jjlz_add/lizi_01": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_1": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_2": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_3": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_4": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_5": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_6": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/kk_add/kk_2": {"db/kk_add/kk_03": {"width": 79, "height": 86}, "db/kk_add/kk_05": {"width": 79, "height": 86}, "db/kk_add/kk_07": {"width": 79, "height": 86}, "db/kk_add/kk_09": {"width": 79, "height": 86}, "db/kk_add/kk_11": {"width": 79, "height": 86}}, "db/jjlz_add/lizi_8": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "texiao/skill1/xiaohuo_1_add/xiaohuo_8": {"texiao/skill1/xiaohuo_1_add/xiaohuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "texiao/skill1/xiaohuo_1_add/xiaohuo_9": {"texiao/skill1/xiaohuo_1_add/xiaohuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "db/xx/lizi_02": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "texiao/skill1/xiaohuo_1_add/xiaohuo_12": {"texiao/skill1/xiaohuo_1_add/xiaohuo_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "texiao/skill1/xiaohuo_1_add/xiaohuo_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "xian": {"skill1/xian_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 1, 2, 3, 0, 2], "vertices": [7, -6.1, -7, -6.1, -16.08, 74.08, 16.08, 74.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6, 4, 0], "width": 14, "height": 64}}, "db/jjlz_add/lizi_7": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "red_line": {"red_line": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.9697, 0, 0.93939, 0, 0.90909, 0, 0.87879, 0, 0.84848, 0, 0.81818, 0, 0.78788, 0, 0.75758, 0, 0.72727, 0, 0.69697, 0, 0.66667, 0, 0.63636, 0, 0.60606, 0, 0.57576, 0, 0.54545, 0, 0.51515, 0, 0.48485, 0, 0.45455, 0, 0.42424, 0, 0.39394, 0, 0.36364, 0, 0.33333, 0, 0.30303, 0, 0.27273, 0, 0.24242, 0, 0.21212, 0, 0.18182, 0, 0.15152, 0, 0.12121, 0, 0.09091, 0, 0.06061, 0, 0.0303, 0, 0, 1, 0, 1, 0.0303, 1, 0.06061, 1, 0.09091, 1, 0.12121, 1, 0.15152, 1, 0.18182, 1, 0.21212, 1, 0.24242, 1, 0.27273, 1, 0.30303, 1, 0.33333, 1, 0.36364, 1, 0.39394, 1, 0.42424, 1, 0.45455, 1, 0.48485, 1, 0.51515, 1, 0.54545, 1, 0.57576, 1, 0.60606, 1, 0.63636, 1, 0.66667, 1, 0.69697, 1, 0.72727, 1, 0.75758, 1, 0.78788, 1, 0.81818, 1, 0.84848, 1, 0.87879, 1, 0.90909, 1, 0.93939, 1, 0.9697], "triangles": [37, 32, 36, 32, 33, 36, 36, 33, 35, 33, 34, 35, 30, 31, 38, 38, 31, 37, 31, 32, 37, 40, 29, 39, 29, 30, 39, 39, 30, 38, 27, 28, 41, 41, 28, 40, 28, 29, 40, 25, 26, 43, 43, 26, 42, 26, 27, 42, 42, 27, 41, 24, 25, 44, 44, 25, 43, 22, 23, 46, 46, 23, 45, 23, 24, 45, 45, 24, 44, 48, 21, 47, 21, 22, 47, 47, 22, 46, 19, 20, 49, 49, 20, 48, 20, 21, 48, 17, 18, 51, 51, 18, 50, 18, 19, 50, 50, 19, 49, 53, 16, 52, 16, 17, 52, 52, 17, 51, 14, 15, 54, 54, 15, 53, 15, 16, 53, 12, 13, 56, 56, 13, 55, 13, 14, 55, 55, 14, 54, 11, 12, 57, 57, 12, 56, 9, 10, 59, 59, 10, 58, 10, 11, 58, 58, 11, 57, 61, 8, 60, 8, 9, 60, 60, 9, 59, 6, 7, 62, 62, 7, 61, 7, 8, 61, 4, 5, 64, 64, 5, 63, 5, 6, 63, 63, 6, 62, 3, 4, 65, 65, 4, 64, 0, 1, 67, 1, 2, 67, 67, 2, 66, 2, 3, 66, 66, 3, 65], "vertices": [2, 87, -3.3, -3.71, 0.95373, 88, -16.63, -3.71, 0.04627, 2, 87, -3.3, 3.59, 0.94483, 88, -16.63, 3.59, 0.05517, 2, 87, 4.88, 3.59, 0.77856, 88, -8.45, 3.59, 0.22144, 3, 87, 13.06, 3.59, 0.50117, 88, -0.27, 3.59, 0.39729, 89, -13.6, 3.59, 0.10154, 4, 87, 21.24, 3.59, 0.223, 88, 7.91, 3.59, 0.46281, 89, -5.42, 3.59, 0.30791, 90, -18.75, 3.59, 0.00628, 4, 87, 29.42, 3.59, 0.05594, 88, 16.09, 3.59, 0.3061, 89, 2.77, 3.59, 0.51428, 90, -10.56, 3.59, 0.12367, 4, 88, 24.28, 3.59, 0.13025, 89, 10.95, 3.59, 0.51758, 90, -2.38, 3.59, 0.28008, 91, -15.71, 3.59, 0.07209, 4, 88, 32.46, 3.59, 0.00957, 89, 19.13, 3.59, 0.3112, 90, 5.8, 3.59, 0.42394, 91, -7.53, 3.59, 0.25529, 4, 89, 27.31, 3.59, 0.10483, 90, 13.98, 3.59, 0.34557, 91, 0.65, 3.59, 0.43859, 92, -12.68, 3.59, 0.111, 4, 90, 22.16, 3.59, 0.18916, 91, 8.83, 3.59, 0.47773, 92, -4.49, 3.59, 0.30694, 93, -17.82, 3.59, 0.02617, 4, 90, 30.35, 3.59, 0.03902, 91, 17.02, 3.59, 0.29463, 92, 3.69, 3.59, 0.50288, 93, -9.64, 3.59, 0.16346, 4, 91, 25.2, 3.59, 0.11133, 92, 11.87, 3.59, 0.47682, 93, -1.46, 3.59, 0.32894, 94, -14.79, 3.59, 0.08291, 5, 91, 33.38, 3.59, 0.00011, 92, 20.05, 3.59, 0.28088, 93, 6.72, 3.59, 0.44208, 94, -6.61, 3.59, 0.2768, 95, -19.94, 3.59, 0.00014, 4, 92, 28.23, 3.59, 0.08494, 93, 14.9, 3.59, 0.333, 94, 1.58, 3.59, 0.47068, 95, -11.75, 3.59, 0.11138, 4, 93, 23.09, 3.59, 0.16751, 94, 9.76, 3.59, 0.49875, 95, -3.57, 3.59, 0.29169, 96, -16.9, 3.59, 0.04205, 4, 93, 31.27, 3.59, 0.0282, 94, 17.94, 3.59, 0.30486, 95, 4.61, 3.59, 0.47173, 96, -8.72, 3.59, 0.19521, 4, 94, 26.12, 3.59, 0.11097, 95, 12.79, 3.59, 0.42955, 96, -0.54, 3.59, 0.35846, 97, -13.87, 3.59, 0.10102, 4, 95, 20.97, 3.59, 0.24924, 96, 7.64, 3.59, 0.43762, 97, -5.68, 3.59, 0.30097, 98, -19.01, 3.59, 0.01217, 4, 95, 29.16, 3.59, 0.06906, 96, 15.83, 3.59, 0.29455, 97, 2.5, 3.59, 0.50226, 98, -10.83, 3.59, 0.13413, 4, 96, 24.01, 3.59, 0.1313, 97, 10.68, 3.59, 0.50151, 98, -2.65, 3.59, 0.30533, 99, -15.98, 3.59, 0.06186, 4, 96, 32.19, 3.59, 0.01009, 97, 18.86, 3.59, 0.30288, 98, 5.53, 3.59, 0.45218, 99, -7.8, 3.59, 0.23484, 4, 97, 27.04, 3.59, 0.1016, 98, 13.71, 3.59, 0.37948, 99, 0.39, 3.59, 0.41465, 100, -12.94, 3.59, 0.10428, 5, 97, 35.22, 3.59, 0.00133, 98, 21.9, 3.59, 0.20827, 99, 8.57, 3.59, 0.47073, 100, -4.76, 3.59, 0.29639, 101, -18.09, 3.59, 0.02328, 4, 98, 30.08, 3.59, 0.04925, 99, 16.75, 3.59, 0.30458, 100, 3.42, 3.59, 0.48849, 101, -9.91, 3.59, 0.15768, 4, 99, 24.93, 3.59, 0.12477, 100, 11.6, 3.59, 0.47204, 101, -1.73, 3.59, 0.32589, 102, -15.06, 3.59, 0.0773, 4, 99, 33.11, 3.59, 0.00683, 100, 19.78, 3.59, 0.27993, 101, 6.45, 3.59, 0.44753, 102, -6.87, 3.59, 0.26571, 4, 100, 27.97, 3.59, 0.08783, 101, 14.64, 3.59, 0.34695, 102, 1.31, 3.59, 0.45426, 103, -12.02, 3.59, 0.11097, 4, 101, 22.82, 3.59, 0.17874, 102, 9.49, 3.59, 0.48822, 103, -3.84, 3.59, 0.2904, 104, -17.17, 3.59, 0.04264, 4, 101, 31, 3.59, 0.03381, 102, 17.67, 3.59, 0.29995, 103, 4.34, 3.59, 0.46984, 104, -8.99, 3.59, 0.19639, 4, 102, 25.85, 3.59, 0.1114, 103, 12.52, 3.59, 0.42735, 104, -0.8, 3.59, 0.36865, 105, -14.13, 3.59, 0.0926, 5, 102, 34.03, 3.59, 0.00014, 103, 20.71, 3.59, 0.24791, 104, 7.38, 3.59, 0.45563, 105, -5.95, 3.59, 0.29099, 106, -19.28, 3.59, 0.00533, 4, 103, 28.89, 3.59, 0.06847, 104, 15.56, 3.59, 0.32039, 105, 2.23, 3.59, 0.49143, 106, -11.1, 3.59, 0.11971, 3, 104, 23.74, 3.59, 0.14813, 105, 10.41, 3.59, 0.50667, 106, -2.92, 3.59, 0.3452, 3, 104, 31.92, 3.59, 0.01851, 105, 18.59, 3.59, 0.31034, 106, 5.26, 3.59, 0.67115, 2, 105, 26.78, 3.59, 0.10989, 106, 13.45, 3.59, 0.89011, 2, 105, 26.78, -3.71, 0.10976, 106, 13.45, -3.71, 0.89024, 3, 104, 31.92, -3.71, 0.01786, 105, 18.59, -3.71, 0.30866, 106, 5.26, -3.71, 0.67348, 4, 103, 37.07, -3.71, 0.00011, 104, 23.74, -3.71, 0.14672, 105, 10.41, -3.71, 0.50962, 106, -2.92, -3.71, 0.34355, 4, 103, 28.89, -3.71, 0.07262, 104, 15.56, -3.71, 0.31429, 105, 2.23, -3.71, 0.49517, 106, -11.1, -3.71, 0.11792, 5, 102, 34.04, -3.71, 0.00187, 103, 20.71, -3.71, 0.25436, 104, 7.38, -3.71, 0.44615, 105, -5.95, -3.71, 0.29421, 106, -19.28, -3.71, 0.00341, 4, 102, 25.85, -3.71, 0.11406, 103, 12.52, -3.71, 0.43669, 104, -0.8, -3.71, 0.356, 105, -14.13, -3.71, 0.09325, 4, 101, 31, -3.71, 0.03316, 102, 17.67, -3.71, 0.3042, 103, 4.34, -3.71, 0.47421, 104, -8.99, -3.71, 0.18843, 4, 101, 22.82, -3.71, 0.17743, 102, 9.49, -3.71, 0.49059, 103, -3.84, -3.71, 0.29327, 104, -17.17, -3.71, 0.03871, 4, 100, 27.97, -3.71, 0.08884, 101, 14.64, -3.71, 0.34397, 102, 1.31, -3.71, 0.45636, 103, -12.02, -3.71, 0.11084, 5, 99, 33.11, -3.71, 0.01016, 100, 19.78, -3.71, 0.27863, 101, 6.45, -3.71, 0.44419, 102, -6.87, -3.71, 0.26622, 103, -20.2, -3.71, 0.0008, 4, 99, 24.93, -3.71, 0.13143, 100, 11.6, -3.71, 0.46842, 101, -1.73, -3.71, 0.3222, 102, -15.06, -3.71, 0.07795, 4, 98, 30.08, -3.71, 0.05658, 99, 16.75, -3.71, 0.30723, 100, 3.42, -3.71, 0.48053, 101, -9.91, -3.71, 0.15566, 4, 98, 21.9, -3.71, 0.22427, 99, 8.57, -3.71, 0.46272, 100, -4.76, -3.71, 0.29074, 101, -18.09, -3.71, 0.02227, 4, 97, 27.04, -3.71, 0.10401, 98, 13.71, -3.71, 0.39906, 99, 0.39, -3.71, 0.39598, 100, -12.94, -3.71, 0.10095, 4, 96, 32.19, -3.71, 0.00765, 97, 18.86, -3.71, 0.31148, 98, 5.53, -3.71, 0.46069, 99, -7.8, -3.71, 0.22018, 4, 96, 24.01, -3.71, 0.12475, 97, 10.68, -3.71, 0.52061, 98, -2.65, -3.71, 0.30011, 99, -15.98, -3.71, 0.05453, 4, 95, 29.16, -3.71, 0.07091, 96, 15.83, -3.71, 0.28205, 97, 2.5, -3.71, 0.52172, 98, -10.83, -3.71, 0.12532, 5, 94, 34.3, -3.71, 0.00157, 95, 20.97, -3.71, 0.25137, 96, 7.65, -3.71, 0.42404, 97, -5.68, -3.71, 0.31592, 98, -19.01, -3.71, 0.0071, 4, 94, 26.12, -3.71, 0.11173, 95, 12.79, -3.71, 0.43434, 96, -0.54, -3.71, 0.34714, 97, -13.87, -3.71, 0.10679, 5, 93, 31.27, -3.71, 0.02604, 94, 17.94, -3.71, 0.30696, 95, 4.61, -3.71, 0.47549, 96, -8.72, -3.71, 0.18984, 97, -22.05, -3.71, 0.00166, 4, 93, 23.09, -3.71, 0.16319, 94, 9.76, -3.71, 0.49906, 95, -3.57, -3.71, 0.29755, 96, -16.9, -3.71, 0.0402, 4, 92, 28.23, -3.71, 0.07739, 93, 14.9, -3.71, 0.33407, 94, 1.58, -3.71, 0.47397, 95, -11.75, -3.71, 0.11458, 5, 91, 33.38, -3.71, 0.00597, 92, 20.05, -3.71, 0.25992, 93, 6.72, -3.71, 0.45286, 94, -6.61, -3.71, 0.27873, 95, -19.94, -3.71, 0.00252, 4, 91, 25.2, -3.71, 0.12095, 92, 11.87, -3.71, 0.44455, 93, -1.46, -3.71, 0.34943, 94, -14.79, -3.71, 0.08507, 4, 90, 30.35, -3.71, 0.04424, 91, 17.02, -3.71, 0.30281, 92, 3.69, -3.71, 0.4744, 93, -9.64, -3.71, 0.17855, 4, 90, 22.16, -3.71, 0.19958, 91, 8.84, -3.71, 0.47273, 92, -4.49, -3.71, 0.29397, 93, -17.82, -3.71, 0.03372, 4, 89, 27.31, -3.71, 0.09697, 90, 13.98, -3.71, 0.36907, 91, 0.65, -3.71, 0.42462, 92, -12.68, -3.71, 0.10934, 5, 88, 32.46, -3.71, 0.00619, 89, 19.13, -3.71, 0.29887, 90, 5.8, -3.71, 0.45008, 91, -7.53, -3.71, 0.24276, 92, -20.86, -3.71, 0.0021, 4, 88, 24.28, -3.71, 0.1218, 89, 10.95, -3.71, 0.50245, 90, -2.38, -3.71, 0.30887, 91, -15.71, -3.71, 0.06687, 4, 87, 29.42, -3.71, 0.06484, 88, 16.09, -3.71, 0.28368, 89, 2.77, -3.71, 0.51209, 90, -10.56, -3.71, 0.13939, 4, 87, 21.24, -3.71, 0.2408, 88, 7.91, -3.71, 0.43318, 89, -5.42, -3.71, 0.31188, 90, -18.74, -3.71, 0.01414, 3, 87, 13.06, -3.71, 0.52787, 88, -0.27, -3.71, 0.36384, 89, -13.6, -3.71, 0.10829, 3, 87, 4.88, -3.71, 0.79636, 88, -8.45, -3.71, 0.20196, 89, -21.78, -3.71, 0.00168], "hull": 68, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 0], "width": 24, "height": 154}}, "db/jjlz_add/lizi_10": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_11": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_12": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_13": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_14": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_15": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_16": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_17": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_18": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/jjlz_add/lizi_19": {"db/jjlz_add/lizi_01": {"width": 41, "height": 41}}, "db/xx/lizi_10": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_11": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_12": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_13": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_14": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "db/xx/lizi_15": {"db/xx/lizi_02": {"width": 47, "height": 46}}, "a40": {"a0": {"type": "mesh", "uvs": [0.25705, 0.59513, 0.3343, 0.59205, 0.41193, 0.58051, 0.48327, 0.56768, 0.54727, 0.55868, 0.61882, 0.55368, 0.69288, 0.55681, 0.7591, 0.58063, 0.81107, 0.61395, 0.85515, 0.65632, 0.89294, 0.69979, 0.9328, 0.75315, 0.96658, 0.80273, 0.99102, 0.8571, 0.99842, 0.90027, 0.99703, 0.9409, 0.98036, 0.97216, 0.95365, 0.98825, 0.92192, 0.9982, 0.88132, 1, 0.8314, 0.9957, 0.79103, 0.98506, 0.79532, 0.97075, 0.83419, 0.97605, 0.88329, 0.97729, 0.92727, 0.9692, 0.96017, 0.95046, 0.97736, 0.92513, 0.9755, 0.89292, 0.96665, 0.85738, 0.94998, 0.81697, 0.92061, 0.76478, 0.88185, 0.71691, 0.84016, 0.67509, 0.79955, 0.64092, 0.74438, 0.60737, 0.6867, 0.59237, 0.6308, 0.59103, 0.56462, 0.59425, 0.48657, 0.60508, 0.41226, 0.61732, 0.33598, 0.62842, 0.25892, 0.63084], "triangles": [20, 22, 23, 21, 22, 20, 19, 23, 24, 20, 23, 19, 15, 27, 14, 16, 27, 15, 26, 27, 16, 17, 25, 26, 17, 26, 16, 18, 24, 25, 18, 25, 17, 19, 24, 18, 29, 12, 13, 28, 29, 13, 28, 13, 14, 27, 28, 14, 32, 9, 10, 31, 10, 11, 32, 10, 31, 30, 11, 12, 31, 11, 30, 30, 12, 29, 35, 6, 7, 34, 7, 8, 35, 7, 34, 33, 8, 9, 34, 8, 33, 33, 9, 32, 37, 5, 6, 36, 37, 6, 38, 4, 5, 38, 5, 37, 39, 3, 4, 39, 4, 38, 36, 6, 35, 39, 2, 3, 39, 40, 2, 1, 2, 40, 41, 1, 40, 42, 0, 1, 42, 1, 41], "vertices": [2, 5, -3.19, 4.26, 0.89701, 6, -73.35, 0.41, 0.10299, 2, 5, 21.88, 2.88, 0.79643, 6, -48.23, 0.35, 0.20357, 3, 5, 47.23, 3.28, 0.61961, 6, -22.94, 2.08, 0.37628, 7, -89.3, -33.86, 0.00412, 4, 5, 70.56, 4.12, 0.41169, 6, 0.31, 4.15, 0.56362, 7, -68.62, -23.02, 0.02469, 8, -104.12, -67.56, 0, 4, 5, 91.44, 4.35, 0.22454, 6, 21.16, 5.47, 0.68904, 7, -49.88, -13.8, 0.08617, 8, -90.28, -51.92, 0.00025, 4, 5, 114.71, 3.53, 0.0962, 6, 44.43, 5.87, 0.69393, 7, -28.55, -4.49, 0.20452, 8, -74.07, -35.21, 0.00535, 4, 5, 138.64, 0.92, 0.02958, 6, 68.47, 4.53, 0.57536, 7, -5.83, 3.5, 0.36666, 8, -56.08, -19.2, 0.0284, 5, 5, 159.69, -5.85, 0.00575, 6, 89.84, -1.13, 0.38932, 7, 16.07, 6.48, 0.5111, 8, -36.94, -8.14, 0.09324, 9, -72.61, -41.35, 0.0006, 5, 5, 175.96, -14.25, 0.00033, 6, 106.53, -8.67, 0.20955, 7, 34.37, 5.93, 0.56912, 8, -19.8, -1.71, 0.21387, 9, -59.58, -28.49, 0.00713, 4, 6, 120.59, -18.05, 0.08642, 7, 50.96, 2.67, 0.50591, 8, -3.21, 1.57, 0.37393, 9, -45.77, -18.73, 0.03374, 5, 6, 132.61, -27.61, 0.02469, 7, 65.73, -1.54, 0.35925, 8, 12.05, 3.27, 0.51155, 9, -32.53, -10.95, 0.10144, 10, 6.37, -58.65, 0.00307, 5, 6, 145.24, -39.28, 0.00412, 7, 81.87, -7.47, 0.19835, 8, 29.23, 3.91, 0.56052, 9, -17.1, -3.36, 0.21756, 10, 0.02, -42.67, 0.01946, 4, 7, 95.88, -13.36, 0.08271, 8, 44.43, 3.77, 0.49147, 9, -3.17, 2.72, 0.35511, 10, -4.95, -28.3, 0.07071, 4, 7, 107.42, -21.28, 0.02346, 8, 58.11, 0.82, 0.34447, 9, 10.53, 5.6, 0.45362, 10, -6.75, -14.42, 0.17846, 5, 7, 112.95, -28.97, 0.00387, 8, 66.14, -4.2, 0.18792, 9, 19.9, 4.29, 0.46028, 10, -4.72, -5.18, 0.34382, 11, -25.75, -17.18, 0.00411, 5, 8, 71.71, -10.78, 0.07737, 9, 27.68, 0.55, 0.3711, 10, -0.38, 2.28, 0.52686, 11, -25.02, -8.59, 0.02055, 12, -45.3, -16.03, 0.00412, 5, 8, 72.31, -19.32, 0.02168, 9, 31.7, -7, 0.23533, 10, 7.47, 5.7, 0.65664, 11, -19.4, -2.13, 0.06166, 12, -40.78, -8.76, 0.02469, 5, 8, 68.32, -27.76, 0.00351, 9, 31.5, -16.32, 0.11433, 10, 16.75, 4.77, 0.67241, 11, -10.62, 1, 0.12333, 12, -32.61, -4.26, 0.08642, 4, 9, 29.48, -26.65, 0.0404, 10, 26.88, 1.94, 0.56473, 11, -0.25, 2.79, 0.18499, 12, -22.66, -0.85, 0.20988, 4, 9, 24.74, -38.97, 0.00935, 10, 38.8, -3.75, 0.38593, 11, 12.96, 2.75, 0.21378, 12, -9.62, 1.23, 0.39094, 4, 9, 17.64, -53.59, 0.00105, 10, 52.81, -11.97, 0.21239, 11, 29.14, 1.33, 0.20633, 12, 6.58, 2.41, 0.58022, 3, 10, 63.46, -19.97, 0.10752, 11, 42.19, -1.34, 0.18882, 12, 19.88, 1.87, 0.70366, 4, 9, 8.24, -62.37, 0.00174, 10, 60.83, -22.03, 0.09827, 11, 40.7, -4.32, 0.19635, 12, 18.89, -1.32, 0.70364, 4, 9, 14.15, -51.15, 0.01278, 10, 50.11, -15.26, 0.17891, 11, 28.11, -2.8, 0.22814, 12, 6.22, -1.83, 0.58017, 4, 9, 20.54, -36.53, 0.05061, 10, 36.03, -7.74, 0.31097, 11, 12.16, -2.04, 0.24755, 12, -9.64, -3.63, 0.39088, 5, 8, 59.31, -30.69, 0.00408, 9, 24.47, -22.67, 0.13399, 10, 22.53, -2.74, 0.42892, 11, -2.18, -3.3, 0.22319, 12, -23.59, -7.17, 0.20983, 5, 8, 64.37, -20.47, 0.0245, 9, 24.93, -11.28, 0.26256, 10, 11.21, -1.39, 0.46957, 11, -12.99, -6.94, 0.15697, 12, -33.68, -12.48, 0.0864, 6, 7, 108.44, -36.34, 0, 8, 64.76, -12.73, 0.08585, 9, 22.13, -4.05, 0.39629, 10, 3.78, -3.62, 0.40911, 11, -18.74, -12.13, 0.08406, 12, -38.53, -18.53, 0.02468, 6, 7, 105.43, -30.18, 0.00412, 8, 59.64, -8.17, 0.20462, 9, 15.59, -1.98, 0.47262, 10, 1.2, -9.97, 0.28279, 11, -18.35, -18.97, 0.03174, 12, -37.05, -25.22, 0.00411, 5, 7, 100.05, -24.17, 0.0247, 8, 52.39, -4.65, 0.36865, 9, 7.53, -1.73, 0.44732, 10, 0.32, -17.99, 0.1515, 11, -15.71, -26.6, 0.00784, 5, 7, 91.93, -18.11, 0.08643, 8, 42.57, -2.12, 0.51656, 9, -2.46, -3.43, 0.33558, 10, 1.23, -28.08, 0.06068, 11, -10.57, -35.33, 0.00074, 5, 6, 141.21, -41.64, 0.00374, 7, 79.05, -11.19, 0.20615, 8, 28.04, -0.6, 0.57852, 9, -16.35, -7.97, 0.19505, 10, 4.67, -42.28, 0.01654, 5, 6, 128.9, -31.14, 0.0228, 7, 63.66, -6.22, 0.37227, 8, 11.91, -1.84, 0.51736, 9, -30.57, -15.68, 0.085, 10, 11.24, -57.07, 0.00256, 5, 6, 115.61, -21.89, 0.08076, 7, 47.84, -2.79, 0.52419, 8, -4.03, -4.67, 0.36978, 9, -43.97, -24.76, 0.02523, 10, 19.25, -71.14, 4e-05, 6, 5, 171.76, -19.65, 0.00065, 6, 102.62, -14.28, 0.19791, 7, 32.92, -0.75, 0.59158, 8, -18.61, -8.44, 0.20556, 9, -55.75, -34.15, 0.00431, 10, 27.68, -83.61, 0, 5, 5, 154.46, -11.11, 0.00734, 6, 84.9, -6.66, 0.37073, 7, 13.63, -0.52, 0.53551, 8, -36.54, -15.54, 0.08638, 9, -69.22, -47.95, 4e-05, 4, 5, 136.04, -6.43, 0.03438, 6, 66.25, -2.95, 0.55131, 7, -5.01, -4.26, 0.38962, 8, -52.38, -26.07, 0.02469, 4, 5, 117.95, -4.68, 0.10579, 6, 48.1, -2.15, 0.66734, 7, -22.08, -10.49, 0.22275, 8, -65.81, -38.31, 0.00411, 3, 5, 96.46, -3.63, 0.23894, 6, 26.58, -2.23, 0.66332, 7, -41.92, -18.82, 0.09774, 3, 5, 70.99, -3.87, 0.428, 6, 1.16, -3.81, 0.54165, 7, -64.78, -30.04, 0.03035, 3, 5, 46.71, -4.5, 0.634, 6, -23.06, -5.72, 0.36, 7, -86.41, -41.1, 0.006, 3, 5, 21.81, -4.85, 0.80571, 6, -47.9, -7.37, 0.19391, 7, -108.72, -52.17, 0.00038, 2, 5, -3.2, -3.34, 0.90021, 6, -72.95, -7.18, 0.09979], "hull": 43, "edges": [10, 12, 12, 14, 14, 16, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 54, 56, 60, 62, 66, 68, 68, 70, 70, 72, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 82, 84, 80, 82, 76, 78, 78, 80, 72, 74, 74, 76, 62, 64, 64, 66, 48, 50, 20, 22, 22, 24, 36, 38, 38, 40, 16, 18, 18, 20, 50, 52, 52, 54, 56, 58, 58, 60, 44, 46, 46, 48, 84, 0], "width": 325, "height": 212}}, "ju9": {"skill1/huo4/1_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00032": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00034": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00036": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00038": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}, "skill1/huo4/1_00040": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [101.5, -94.5, -101.5, -94.5, -101.5, 94.5, 101.5, 94.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 203, "height": 189}}}}], "events": {"hit": {}}, "animations": {"effect01": {"bones": {"bone2": {"translate": [{"y": -5.96}]}}, "events": [{"time": 0.3333, "name": "hit"}]}, "effect02": {"slots": {"db/xx/lizi_6": {"attachment": [{"name": null}, {"time": 0.3667, "name": "db/xx/lizi_02"}]}, "ju4": {"attachment": [{"time": 0.3333, "name": "skill1/huo5/1_00031"}, {"time": 0.4, "name": "skill1/huo5/1_00032"}, {"time": 0.4333, "name": "skill1/huo5/1_00034"}, {"time": 0.5, "name": "skill1/huo5/1_00036"}, {"time": 0.5333, "name": null}]}, "db/jjlz_add/lizi_4": {"attachment": [{"name": null}, {"time": 0.1333, "name": "db/jjlz_add/lizi_01"}]}, "db/xx/lizi_12": {"attachment": [{"name": null}, {"time": 0.4667, "name": "db/xx/lizi_02"}]}, "db/xx/lizi_11": {"attachment": [{"name": null}, {"time": 0.5, "name": "db/xx/lizi_02"}]}, "xian8": {"attachment": [{"time": 0.8667, "name": "skill1/xian_00032"}, {"time": 1.2333, "name": null}]}, "hen3": {"color": [{"time": 0.8667, "color": "ff0000ff"}, {"time": 1.1, "color": "ffffff00"}], "attachment": [{"time": 0.8667, "name": "skill1/glow2_00128"}]}, "db/jjlz_add/lizi_9": {"attachment": [{"name": null}, {"time": 0.2333, "name": "db/jjlz_add/lizi_01"}]}, "kuo3": {"color": [{"time": 0.8667, "color": "ff0000ff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0.8667, "name": "skill1/2_00033"}]}, "db/xx/lizi_14": {"attachment": [{"name": null}, {"time": 0.4333, "name": "db/xx/lizi_02"}]}, "db/jjlz_add/lizi_01": {"attachment": [{"name": null}, {"time": 0.0333, "name": "db/jjlz_add/lizi_01"}]}, "xian2": {"attachment": [{"time": 0.7333, "name": "skill1/xian_00032"}, {"time": 1.1, "name": null}]}, "red_line": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "red_line5": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "db/jjlz_add/lizi_10": {"attachment": [{"name": null}, {"time": 0.2333, "name": "db/jjlz_add/lizi_01"}]}, "an2": {"color": [{"time": 0.8667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "skill1/sg/1_00032"}, {"time": 0.8, "name": "skill1/sg/1_00033"}, {"time": 0.8667, "name": "skill1/sg/1_00035"}, {"time": 1, "name": null}]}, "db/xlt_add/xlt_01": {"attachment": [{"time": 0.0333, "name": "db/xlt_add/xlt_02"}, {"time": 0.0667, "name": "db/xlt_add/xlt_03"}, {"time": 0.1, "name": "db/xlt_add/xlt_05"}, {"time": 0.1333, "name": "db/xlt_add/xlt_06"}, {"time": 0.1667, "name": "db/xlt_add/xlt_07"}, {"time": 0.2, "name": "db/xlt_add/xlt_09"}, {"time": 0.2333, "name": "db/xlt_add/xlt_10"}, {"time": 0.2667, "name": "db/xlt_add/xlt_12"}, {"time": 0.3, "name": "db/xlt_add/xlt_13"}, {"time": 0.3333, "name": null}]}, "ju6": {"attachment": [{"time": 0.2333, "name": "skill1/huo4/1_00028"}, {"time": 0.2667, "name": "skill1/huo4/1_00030"}, {"time": 0.3333, "name": "skill1/huo4/1_00032"}, {"time": 0.4, "name": "skill1/huo4/1_00034"}, {"time": 0.4333, "name": "skill1/huo4/1_00036"}, {"time": 0.5, "name": "skill1/huo4/1_00038"}, {"time": 0.5333, "name": "skill1/huo4/1_00040"}, {"time": 0.6, "name": null}]}, "ju5": {"attachment": [{"time": 0.4333, "name": "skill1/huo5/1_00031"}, {"time": 0.5, "name": "skill1/huo5/1_00032"}, {"time": 0.5333, "name": "skill1/huo5/1_00034"}, {"time": 0.6, "name": "skill1/huo5/1_00036"}, {"time": 0.6667, "name": null}]}, "db/jjlz_add/lizi_16": {"attachment": [{"name": null}, {"time": 0.3, "name": "db/jjlz_add/lizi_01"}]}, "red_line3": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "xian5": {"attachment": [{"time": 0.7667, "name": "skill1/xian_00032"}, {"time": 1.1333, "name": null}]}, "db/xx/lizi_9": {"attachment": [{"name": null}, {"time": 0.4667, "name": "db/xx/lizi_02"}]}, "a30": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "xian4": {"attachment": [{"time": 0.7667, "name": "skill1/xian_00032"}, {"time": 1.1333, "name": null}]}, "db/xx/lizi_7": {"attachment": [{"name": null}, {"time": 0.4333, "name": "db/xx/lizi_02"}]}, "db/nuqi_add_02/nq_02_01": {"attachment": [{"time": 0.3333, "name": "db/nuqi_add_02/nq_02_01"}, {"time": 0.4, "name": "db/nuqi_add_02/nq_02_02"}, {"time": 0.4333, "name": "db/nuqi_add_02/nq_02_03"}, {"time": 0.5, "name": "db/nuqi_add_02/nq_02_04"}, {"time": 0.5333, "name": "db/nuqi_add_02/nq_02_05"}, {"time": 0.5667, "name": "db/nuqi_add_02/nq_02_06"}, {"time": 0.6333, "name": "db/nuqi_add_02/nq_02_07"}, {"time": 0.6667, "name": "db/nuqi_add_02/nq_02_08"}, {"time": 0.7333, "name": "db/nuqi_add_02/nq_02_09"}, {"time": 0.7667, "name": "db/nuqi_add_02/nq_02_10"}, {"time": 0.8, "name": "db/nuqi_add_02/nq_02_11"}, {"time": 0.8667, "name": "db/nuqi_add_02/nq_02_12"}, {"time": 0.9, "name": "db/nuqi_add_02/nq_02_13"}]}, "db/kk_add/kk_1": {"color": [{"color": "ffffff90"}], "attachment": [{"time": 0.3333, "name": "db/kk_add/kk_03"}, {"time": 0.3667, "name": "db/kk_add/kk_05"}, {"time": 0.4333, "name": "db/kk_add/kk_07"}, {"time": 0.4667, "name": "db/kk_add/kk_09"}, {"time": 0.5, "name": "db/kk_add/kk_11"}, {"time": 0.5667, "name": null}]}, "db/glow_add/glow_2": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffff6d", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "color": "ffffff80", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "xian9": {"attachment": [{"time": 0.8667, "name": "skill1/xian_00032"}, {"time": 1.2333, "name": null}]}, "kuo": {"color": [{"time": 0.7, "color": "ff0000ff", "curve": "stepped"}, {"time": 0.7333, "color": "ff0000ff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.7, "name": "skill1/2_00033"}]}, "db/xx/lizi_10": {"attachment": [{"name": null}, {"time": 0.4333, "name": "db/xx/lizi_02"}]}, "db/xx/lizi_3": {"attachment": [{"name": null}, {"time": 0.3667, "name": "db/xx/lizi_02"}]}, "db/jjlz_add/lizi_17": {"attachment": [{"name": null}, {"time": 0.3, "name": "db/jjlz_add/lizi_01"}]}, "quan2": {"color": [{"time": 0.7, "color": "ff0000ff"}, {"time": 0.7333, "color": "670000ff"}, {"time": 0.8667, "color": "67000000"}], "attachment": [{"time": 0.7, "name": "skill1/quan_00033"}, {"time": 0.7333, "name": null}]}, "hen2": {"color": [{"time": 0.7667, "color": "ff0000ff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "skill1/glow2_00128"}]}, "db/xx/lizi_8": {"attachment": [{"name": null}, {"time": 0.4333, "name": "db/xx/lizi_02"}]}, "db/kk_add/kk_01": {"color": [{"color": "ffffff96"}], "attachment": [{"time": 0.3333, "name": "db/kk_add/kk_03"}, {"time": 0.3667, "name": "db/kk_add/kk_05"}, {"time": 0.4333, "name": "db/kk_add/kk_07"}, {"time": 0.4667, "name": "db/kk_add/kk_09"}, {"time": 0.5, "name": "db/kk_add/kk_11"}, {"time": 0.5333, "name": null}]}, "texiao/skill1/xiaohuo_1_add/xiaohuo_13": {"color": [{"time": 0.7, "color": "000000ff"}], "attachment": [{"time": 0.7, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0009"}, {"time": 0.7333, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0011"}, {"time": 0.8, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0015"}, {"time": 0.8667, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0019"}, {"time": 0.9, "name": null}]}, "glow": {"color": [{"time": 0.7, "color": "ff00009c"}, {"time": 0.9, "color": "ff000000"}], "attachment": [{"time": 0.7667, "name": "skill1/1_00025"}]}, "ju7": {"attachment": [{"time": 0.2333, "name": "skill1/huo4/1_00026"}, {"time": 0.3, "name": "skill1/huo4/1_00028"}, {"time": 0.3667, "name": "skill1/huo4/1_00030"}, {"time": 0.4, "name": "skill1/huo4/1_00032"}, {"time": 0.4667, "name": "skill1/huo4/1_00034"}, {"time": 0.5333, "name": "skill1/huo4/1_00036"}, {"time": 0.5667, "name": "skill1/huo4/1_00038"}, {"time": 0.6333, "name": "skill1/huo4/1_00040"}, {"time": 0.7, "name": null}]}, "sg4": {"color": [{"time": 0.7667, "color": "ff0000ff"}], "attachment": [{"time": 0.7667, "name": "skill1/sg3/1_00032"}, {"time": 0.8667, "name": null}]}, "sg5": {"color": [{"time": 0.9, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"time": 0.8667, "name": "skill1/sg2/1_00032"}, {"time": 0.9, "name": "skill1/sg2/1_00033"}, {"time": 0.9667, "name": null}]}, "hen": {"color": [{"time": 0.7333, "color": "ff0000ff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.7333, "name": "skill1/glow2_00128"}]}, "a31": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "texiao/skill1/yshuo_add/yshuo_0": {"attachment": [{"time": 0.6667, "name": "texiao/skill1/yshuo_add/yshuo_0001"}, {"time": 0.7, "name": "texiao/skill1/yshuo_add/yshuo_0003"}, {"time": 0.7333, "name": "texiao/skill1/yshuo_add/yshuo_0005"}, {"time": 0.8, "name": "texiao/skill1/yshuo_add/yshuo_0007"}, {"time": 0.8667, "name": "texiao/skill1/yshuo_add/yshuo_0009"}, {"time": 0.9, "name": "texiao/skill1/yshuo_add/yshuo_0013"}, {"time": 0.9667, "name": null}]}, "db/kk_add/kk_2": {"color": [{"color": "ffffff90"}], "attachment": [{"time": 0.3667, "name": "db/kk_add/kk_03"}, {"time": 0.4333, "name": "db/kk_add/kk_05"}, {"time": 0.4667, "name": "db/kk_add/kk_07"}, {"time": 0.5, "name": "db/kk_add/kk_09"}, {"time": 0.5667, "name": "db/kk_add/kk_11"}, {"time": 0.6, "name": null}]}, "texiao/skill1/xiaohuo_1_add/xiaohuo_11": {"color": [{"time": 0.7, "color": "000000ff"}], "attachment": [{"time": 0.7, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0009"}, {"time": 0.7333, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0011"}, {"time": 0.8, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0015"}, {"time": 0.8667, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0019"}, {"time": 0.9, "name": null}]}, "a29": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "sg": {"color": [{"time": 0.7667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}], "attachment": [{"time": 0.7333, "name": "skill1/sg2/1_00032"}, {"time": 0.7667, "name": "skill1/sg2/1_00033"}, {"time": 0.8, "name": null}]}, "db/ks_add/ks_1": {"color": [{"time": 0.6667, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00"}], "attachment": [{"time": 0.3667, "name": "db/ks_add/ks_05"}]}, "db/xx/lizi_13": {"attachment": [{"name": null}, {"time": 0.4667, "name": "db/xx/lizi_02"}]}, "texiao/skill1/xiaohuo_1_add/xiaohuo_10": {"attachment": [{"time": 0.7, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0009"}, {"time": 0.7333, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0011"}, {"time": 0.8, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0015"}, {"time": 0.8667, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0019"}, {"time": 0.9, "name": null}]}, "db/ks_add/ks_2": {"color": [{"time": 0.8, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "db/ks_add/ks_05"}]}, "texiao/skill1/xiaohuo_1_add/xiaohuo_12": {"attachment": [{"time": 0.7, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0009"}, {"time": 0.7333, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0011"}, {"time": 0.8, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0015"}, {"time": 0.8667, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0019"}, {"time": 0.9, "name": null}]}, "db/xx/lizi_2": {"attachment": [{"name": null}, {"time": 0.3667, "name": "db/xx/lizi_02"}]}, "quan5": {"color": [{"time": 0.8667, "color": "3a0000ff", "curve": "stepped"}, {"time": 0.9333, "color": "3a0000ff"}, {"time": 1.0333, "color": "67000014"}], "attachment": [{"time": 0.8667, "name": "skill1/quan_00033"}, {"time": 1.0667, "name": null}]}, "db/jjlz_add/lizi_19": {"attachment": [{"name": null}, {"time": 0.3333, "name": "db/jjlz_add/lizi_01"}]}, "db/jjlz_add/lizi_8": {"attachment": [{"name": null}, {"time": 0.2, "name": "db/jjlz_add/lizi_01"}]}, "db/jjlz_add/lizi_20": {"attachment": [{"name": null}, {"time": 0.3333, "name": "db/jjlz_add/lizi_01"}]}, "ju9": {"attachment": [{"time": 0.4667, "name": "skill1/huo4/1_00026"}, {"time": 0.5, "name": "skill1/huo4/1_00028"}, {"time": 0.5333, "name": "skill1/huo4/1_00030"}, {"time": 0.6, "name": "skill1/huo4/1_00032"}, {"time": 0.6667, "name": "skill1/huo4/1_00034"}, {"time": 0.7, "name": "skill1/huo4/1_00036"}, {"time": 0.7667, "name": "skill1/huo4/1_00038"}, {"time": 0.8333, "name": "skill1/huo4/1_00040"}, {"time": 0.8667, "name": null}]}, "db/xlt_add/xlt_2": {"attachment": [{"time": 0.2667, "name": "db/xlt_add/xlt_02"}, {"time": 0.3, "name": "db/xlt_add/xlt_03"}, {"time": 0.3333, "name": "db/xlt_add/xlt_05"}, {"time": 0.3667, "name": "db/xlt_add/xlt_06"}, {"time": 0.4, "name": "db/xlt_add/xlt_07"}, {"time": 0.4333, "name": "db/xlt_add/xlt_09"}, {"time": 0.4667, "name": "db/xlt_add/xlt_10"}, {"time": 0.5, "name": "db/xlt_add/xlt_12"}, {"time": 0.5333, "name": "db/xlt_add/xlt_13"}, {"time": 0.5667, "name": null}]}, "db/jjlz_add/lizi_13": {"attachment": [{"name": null}, {"time": 0.2667, "name": "db/jjlz_add/lizi_01"}]}, "xian6": {"attachment": [{"time": 0.7667, "name": "skill1/xian_00032"}, {"time": 1.1333, "name": null}]}, "db/xx/lizi_4": {"attachment": [{"name": null}, {"time": 0.3667, "name": "db/xx/lizi_02"}]}, "quan6": {"color": [{"time": 0.8667, "color": "670000ff"}, {"time": 1.0333, "color": "67000000"}]}, "sg3": {"color": [{"time": 0.8, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "skill1/sg2/1_00032"}, {"time": 0.8, "name": "skill1/sg2/1_00033"}, {"time": 0.8667, "name": null}]}, "quan4": {"color": [{"time": 0.7667, "color": "670000ff"}, {"time": 0.9333, "color": "67000000"}]}, "db/xx/lizi_15": {"attachment": [{"name": null}, {"time": 0.4333, "name": "db/xx/lizi_02"}]}, "ju10": {"attachment": [{"time": 0.5, "name": "skill1/huo5/1_00031"}, {"time": 0.5667, "name": "skill1/huo5/1_00032"}, {"time": 0.6, "name": "skill1/huo5/1_00034"}, {"time": 0.6333, "name": "skill1/huo5/1_00036"}, {"time": 0.6667, "name": null}]}, "db/xx/lizi_02": {"attachment": [{"name": null}, {"time": 0.3667, "name": "db/xx/lizi_02"}]}, "db/jjlz_add/lizi_14": {"attachment": [{"name": null}, {"time": 0.2667, "name": "db/jjlz_add/lizi_01"}]}, "db/nuqi_add_02/nq_02_1": {"color": [{"color": "ffffffda"}], "attachment": [{"time": 0.3667, "name": "db/nuqi_add_02/nq_02_01"}, {"time": 0.4, "name": "db/nuqi_add_02/nq_02_02"}, {"time": 0.4333, "name": "db/nuqi_add_02/nq_02_03"}, {"time": 0.5, "name": "db/nuqi_add_02/nq_02_04"}, {"time": 0.5667, "name": "db/nuqi_add_02/nq_02_05"}, {"time": 0.6333, "name": "db/nuqi_add_02/nq_02_06"}, {"time": 0.6667, "name": "db/nuqi_add_02/nq_02_07"}, {"time": 0.7333, "name": "db/nuqi_add_02/nq_02_09"}, {"time": 0.8, "name": "db/nuqi_add_02/nq_02_10"}, {"time": 0.8333, "name": "db/nuqi_add_02/nq_02_11"}, {"time": 0.9, "name": "db/nuqi_add_02/nq_02_12"}, {"time": 0.9333, "name": "db/nuqi_add_02/nq_02_13"}]}, "db/xlt_add/xlt_3": {"attachment": [{"time": 0.0333, "name": "db/xlt_add/xlt_02"}, {"time": 0.0667, "name": "db/xlt_add/xlt_03"}, {"time": 0.1, "name": "db/xlt_add/xlt_05"}, {"time": 0.1333, "name": "db/xlt_add/xlt_06"}, {"time": 0.1667, "name": "db/xlt_add/xlt_07"}, {"time": 0.2, "name": "db/xlt_add/xlt_09"}, {"time": 0.2333, "name": "db/xlt_add/xlt_10"}, {"time": 0.2667, "name": "db/xlt_add/xlt_12"}, {"time": 0.3, "name": "db/xlt_add/xlt_13"}, {"time": 0.3333, "name": null}]}, "db/jjlz_add/lizi_1": {"attachment": [{"name": null}, {"time": 0.1, "name": "db/jjlz_add/lizi_01"}]}, "db/jjlz_add/lizi_11": {"attachment": [{"name": null}, {"time": 0.2333, "name": "db/jjlz_add/lizi_01"}]}, "texiao/skill1/xiaohuo_1_add/xiaohuo_8": {"attachment": [{"time": 0.7, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0009"}, {"time": 0.7333, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0011"}, {"time": 0.8, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0015"}, {"time": 0.8667, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0019"}, {"time": 0.9, "name": null}]}, "xian": {"attachment": [{"time": 0.7333, "name": "skill1/xian_00032"}, {"time": 1.1, "name": null}]}, "db/kk_add/kk_3": {"color": [{"color": "ffffff90"}], "attachment": [{"time": 0.4333, "name": "db/kk_add/kk_03"}, {"time": 0.4667, "name": "db/kk_add/kk_05"}, {"time": 0.5, "name": "db/kk_add/kk_07"}, {"time": 0.5667, "name": "db/kk_add/kk_09"}, {"time": 0.6, "name": "db/kk_add/kk_11"}, {"time": 0.6667, "name": null}]}, "an3": {"color": [{"time": 0.9667, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}], "attachment": [{"time": 0.8667, "name": "skill1/sg/1_00032"}, {"time": 0.9, "name": "skill1/sg/1_00033"}, {"time": 0.9667, "name": "skill1/sg/1_00035"}, {"time": 1.1, "name": null}]}, "xian3": {"attachment": [{"time": 0.7333, "name": "skill1/xian_00032"}, {"time": 1.1, "name": null}]}, "sg2": {"color": [{"time": 0.7, "color": "ff0000ff"}], "attachment": [{"time": 0.7, "name": "skill1/sg3/1_00031"}, {"time": 0.7333, "name": "skill1/sg3/1_00032"}, {"time": 0.8333, "name": null}]}, "db/glow_add/glow_01": {"color": [{"time": 0.0333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffff59"}, {"time": 1.1, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.0333, "name": "db/glow_add/glow_01"}]}, "rao": {"color": [{"time": 0.0333, "color": "ff0000ff"}], "attachment": [{"time": 0.0333, "name": "skill1/z_00000"}, {"time": 0.7, "name": null}]}, "ju": {"attachment": [{"time": 0.0333, "name": "skill1/ju/1_00017"}, {"time": 0.0667, "name": "skill1/ju/1_00019"}, {"time": 0.1, "name": "skill1/ju/1_00021"}, {"time": 0.1667, "name": "skill1/ju/1_00023"}, {"time": 0.2333, "name": "skill1/ju/1_00025"}, {"time": 0.2667, "name": "skill1/ju/1_00027"}, {"time": 0.3333, "name": "skill1/ju/1_00029"}, {"time": 0.4, "name": "skill1/ju/1_00031"}, {"time": 0.4333, "name": "skill1/ju/1_00033"}, {"time": 0.5, "name": "skill1/ju/1_00035"}, {"time": 0.5333, "name": "skill1/ju/1_00037"}, {"time": 0.6, "name": "skill1/ju/1_00039"}, {"time": 0.6667, "name": "skill1/ju/1_00041"}, {"time": 0.7, "name": null}]}, "db/jj_add/jj_1": {"color": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffff52", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff52"}, {"time": 0.5, "color": "ffffff00"}], "attachment": [{"name": "db/jj_add/jj_05"}]}, "xian7": {"attachment": [{"time": 0.8667, "name": "skill1/xian_00032"}, {"time": 1.2333, "name": null}]}, "db/glow_add/glow_1": {"color": [{"time": 0.7, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffff6d", "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "color": "ffffff80", "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "color": "ffffff00"}]}, "texiao/skill1/xiaohuo_1_add/xiaohuo_9": {"color": [{"time": 0.7, "color": "000000ff"}], "attachment": [{"time": 0.7, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0009"}, {"time": 0.7333, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0011"}, {"time": 0.8, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0015"}, {"time": 0.8667, "name": "texiao/skill1/xiaohuo_1_add/xiaohuo_0019"}, {"time": 0.9, "name": null}]}, "db/jjlz_add/lizi_3": {"attachment": [{"name": null}, {"time": 0.1, "name": "db/jjlz_add/lizi_01"}]}, "db/xx/lizi_5": {"attachment": [{"name": null}, {"time": 0.3667, "name": "db/xx/lizi_02"}]}, "ju8": {"attachment": [{"time": 0.4333, "name": "skill1/huo4/1_00026"}, {"time": 0.4667, "name": "skill1/huo4/1_00028"}, {"time": 0.5333, "name": "skill1/huo4/1_00030"}, {"time": 0.5667, "name": "skill1/huo4/1_00032"}, {"time": 0.6333, "name": "skill1/huo4/1_00034"}, {"time": 0.7, "name": "skill1/huo4/1_00036"}, {"time": 0.7333, "name": "skill1/huo4/1_00038"}, {"time": 0.8, "name": "skill1/huo4/1_00040"}, {"time": 0.8667, "name": null}]}, "db/jjlz_add/lizi_12": {"attachment": [{"name": null}, {"time": 0.2667, "name": "db/jjlz_add/lizi_01"}]}, "db/jjlz_add/lizi_7": {"attachment": [{"name": null}, {"time": 0.1667, "name": "db/jjlz_add/lizi_01"}]}, "db/jjlz_add/lizi_2": {"attachment": [{"name": null}, {"time": 0.1, "name": "db/jjlz_add/lizi_01"}]}, "an": {"color": [{"time": 0.8, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.7, "name": "skill1/sg/1_00031"}, {"time": 0.7333, "name": "skill1/sg/1_00032"}, {"time": 0.7667, "name": "skill1/sg/1_00033"}, {"time": 0.8, "name": "skill1/sg/1_00035"}, {"time": 0.9333, "name": null}]}, "sg6": {"color": [{"time": 0.8667, "color": "ff0000ff"}], "attachment": [{"time": 0.8667, "name": "skill1/sg3/1_00032"}, {"time": 0.9667, "name": null}]}, "db/xlt_add/xlt_1": {"attachment": [{"time": 0.1667, "name": "db/xlt_add/xlt_01"}, {"time": 0.2, "name": "db/xlt_add/xlt_03"}, {"time": 0.2333, "name": "db/xlt_add/xlt_04"}, {"time": 0.2667, "name": "db/xlt_add/xlt_06"}, {"time": 0.3, "name": "db/xlt_add/xlt_07"}, {"time": 0.3333, "name": "db/xlt_add/xlt_09"}, {"time": 0.3667, "name": "db/xlt_add/xlt_10"}, {"time": 0.4, "name": "db/xlt_add/xlt_11"}, {"time": 0.4333, "name": "db/xlt_add/xlt_13"}, {"time": 0.4667, "name": null}]}, "quan3": {"color": [{"time": 0.7667, "color": "3a0000ff", "curve": "stepped"}, {"time": 0.8333, "color": "3a0000ff"}, {"time": 0.9333, "color": "67000014"}], "attachment": [{"time": 0.7667, "name": "skill1/quan_00033"}, {"time": 0.9667, "name": null}]}, "quan": {"color": [{"time": 0.7333, "color": "3a0000ff", "curve": "stepped"}, {"time": 0.7667, "color": "3a0000ff"}, {"time": 0.8667, "color": "67000014"}], "attachment": [{"time": 0.7333, "name": "skill1/quan_00033"}, {"time": 0.9, "name": null}]}, "kuo2": {"color": [{"time": 0.7667, "color": "ff0000ff"}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "skill1/2_00033"}]}, "db/jjlz_add/lizi_15": {"attachment": [{"name": null}, {"time": 0.2667, "name": "db/jjlz_add/lizi_01"}]}, "db/jjlz_add/lizi_5": {"attachment": [{"name": null}, {"time": 0.1667, "name": "db/jjlz_add/lizi_01"}]}, "db/jjlz_add/lizi_18": {"attachment": [{"name": null}, {"time": 0.3333, "name": "db/jjlz_add/lizi_01"}]}, "db/jjlz_add/lizi_6": {"attachment": [{"name": null}, {"time": 0.2, "name": "db/jjlz_add/lizi_01"}]}}, "bones": {"bone2": {"translate": [{"y": -5.96, "curve": 0.25, "c3": 0.348}, {"time": 0.1667, "x": -11.92, "y": -55.78, "curve": 0.25, "c3": 0}, {"time": 0.3667, "y": 108.71, "curve": "stepped"}, {"time": 0.8667, "y": 108.71, "curve": 0.235, "c2": 0.54, "c3": 0.474}, {"time": 1.1667, "y": -5.96}]}, "bone122": {"rotate": [{"angle": -7.52}], "translate": [{"x": 92.59, "y": 68.59}]}, "kk3": {"rotate": [{"angle": -83.81}], "translate": [{"x": 54.37, "y": 135.85}], "scale": [{"x": 0.6, "y": 0.8}], "shear": [{"x": -20.12}]}, "jj2": {"rotate": [{}, {"time": 0.2, "angle": 83.62}, {"time": 0.5, "angle": -131.33}], "translate": [{"x": 9.07, "y": 6.47}], "scale": [{"x": 1.4, "y": 1.4, "curve": "stepped"}, {"time": 0.1667, "x": 1.4, "y": 1.4}, {"time": 0.4667, "x": 0.6, "y": 0.6}]}, "kk2": {"rotate": [{"angle": -2.48}], "translate": [{"x": -36.58, "y": 78.05}], "scale": [{"x": 0.7, "y": 0.8}]}, "kk": {"rotate": [{"angle": 92.13}], "translate": [{"x": 19.48, "y": 78.05}], "scale": [{"x": 0.55}]}, "kk4": {"rotate": [{"angle": -13.13}], "translate": [{"x": 20.37, "y": 88.25}], "scale": [{"x": -0.6, "y": 0.8}], "shear": [{"x": 2.48}]}, "xlt": {"rotate": [{"angle": 32.83}], "translate": [{"x": 68.36, "y": -79.47}]}, "nq2": {"scale": [{"x": -1}]}, "xlt2": {"rotate": [{"angle": 175.27}], "translate": [{"x": 76.96, "y": -58.21}], "scale": [{"y": 1.5}]}, "ks2": {"rotate": [{"time": 0.3667, "curve": 0.203, "c2": 0.71, "c3": 0.75}, {"time": 0.8667, "angle": -8.37}], "scale": [{"x": 0.5, "y": 0.5, "curve": "stepped"}, {"time": 0.3667, "x": 0.5, "y": 0.5, "curve": 0.203, "c2": 0.71, "c3": 0.75}, {"time": 0.8667, "x": 0.898, "y": 0.898}]}, "jjlz": {"translate": [{"time": 0.0333, "x": -98.16, "y": 86.07}, {"time": 0.3333, "x": 23.18, "y": 50.17}], "scale": [{"x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 0.3333, "x": 0.3, "y": 0.3}, {"time": 0.3667, "x": 0, "y": 0}]}, "ks3": {"rotate": [{"time": 0.4667, "curve": 0.203, "c2": 0.71, "c3": 0.75}, {"time": 1, "angle": -12.26}], "scale": [{"x": 0.5, "y": 0.5, "curve": "stepped"}, {"time": 0.4667, "x": 0.5, "y": 0.5, "curve": 0.203, "c2": 0.71, "c3": 0.75}, {"time": 1, "x": 1.392, "y": 1.392}]}, "jjlz8": {"translate": [{"time": 0.1667, "x": 166.24, "y": 75.88}, {"time": 0.4667, "x": 43.93, "y": 41.92}], "scale": [{"time": 0.1667, "x": 0.1, "y": 0.1}, {"time": 0.4667, "x": 0.2, "y": 0.2}, {"time": 0.5, "x": 0, "y": 0}]}, "xx9": {"rotate": [{"time": 0.4667}, {"time": 1.0667, "angle": -12.13}], "translate": [{"time": 0.4667, "x": -129.53, "y": 70.26}, {"time": 0.8, "x": -84.82, "y": 63.51}, {"time": 1.0667, "x": -84.82, "y": 97.69}], "scale": [{"time": 0.4667, "x": 0.35, "y": 0.35, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.6333, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 0.6667, "x": 0.3, "y": 0.3}, {"time": 0.7, "x": 0, "y": 0}, {"time": 0.7333, "x": 0.25, "y": 0.25, "curve": "stepped"}, {"time": 0.7667, "x": 0.25, "y": 0.25}, {"time": 0.8, "x": 0.3, "y": 0.3}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 0.9667, "x": 0.4, "y": 0.4}, {"time": 1, "x": 0, "y": 0}, {"time": 1.0333, "x": 0.2, "y": 0.2}, {"time": 1.0667, "x": 0, "y": 0}]}, "xx10": {"translate": [{"time": 0.4333, "x": -81.92, "y": 27.96}, {"time": 0.7333, "x": -43.31, "y": 182.06}, {"time": 1.0333, "x": -43.31, "y": 213.67}], "scale": [{"time": 0.4333, "x": 0.2, "y": 0.2}, {"time": 0.4667, "x": 0.35, "y": 0.35, "curve": "stepped"}, {"time": 0.5, "x": 0.35, "y": 0.35}, {"time": 0.5333, "x": 0.2, "y": 0.2}, {"time": 0.5667, "x": 0.35, "y": 0.35}, {"time": 0.6, "x": 0.2, "y": 0.2, "curve": "stepped"}, {"time": 0.6667, "x": 0.25, "y": 0.25}, {"time": 0.7, "x": 0.1, "y": 0.1}, {"time": 0.7333, "x": 0.5, "y": 0.5}, {"time": 0.8, "curve": "stepped"}, {"time": 0.8333}, {"time": 0.8667, "x": 0.2, "y": 0.2}, {"time": 0.9, "x": 0.1, "y": 0.1}, {"time": 0.9333, "x": 0.4, "y": 0.4}, {"time": 0.9667, "x": 0.3, "y": 0.3}, {"time": 1.0333, "x": 0, "y": 0}]}, "jjlz18": {"translate": [{"time": 0.3, "x": -34.39, "y": -88.97}, {"time": 0.5667, "x": 35.24, "y": 37.69}], "scale": [{"time": 0.3, "x": 0.8, "y": 0.8}, {"time": 0.5667, "x": 0.2, "y": 0.2}, {"time": 0.6, "x": 0, "y": 0}]}, "jjlz9": {"translate": [{"time": 0.2, "x": -115.15, "y": 28.19}, {"time": 0.4, "x": 22.73, "y": 41.34}], "scale": [{"time": 0.2, "x": 0.5, "y": 0.5}, {"time": 0.4, "x": 0.2, "y": 0.2}, {"time": 0.4333, "x": 0, "y": 0}]}, "xx7": {"translate": [{"time": 0.4333, "x": -112.33, "y": -18.79}, {"time": 0.7, "x": -51.63, "y": 161.3}, {"time": 0.7333, "x": -48.52, "y": -10.36}, {"time": 1, "x": -48.52, "y": 16.51}], "scale": [{"time": 0.4333}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.5, "x": 0.4, "y": 0.4}, {"time": 0.5333, "x": 0.6, "y": 0.6}, {"time": 0.5667, "x": 0.1, "y": 0.1}, {"time": 0.6, "x": 0.2, "y": 0.2}, {"time": 0.6333, "x": 0.35, "y": 0.35}, {"time": 0.6667, "x": 0.15, "y": 0.15}, {"time": 0.7, "x": 0, "y": 0}, {"time": 0.7333, "x": 0.5, "y": 0.5}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0.5, "y": 0.5}, {"time": 0.9667, "x": 0.1, "y": 0.1}, {"time": 1, "x": 0.4, "y": 0.4}, {"time": 1.1, "x": 0, "y": 0}]}, "xx5": {"translate": [{"time": 0.3667, "x": -155.88, "y": 149.02}, {"time": 0.4667, "x": -155.88, "y": 167.15}, {"time": 0.5, "x": -134.86, "y": 141.73}, {"time": 0.6667, "x": -134.86, "y": 172.19}, {"time": 0.7, "x": -172.94, "y": -22.14}, {"time": 0.9667, "x": -172.94, "y": 2.5}], "scale": [{"time": 0.3667, "x": 0.3, "y": 0.3}, {"time": 0.4333, "x": 0.5, "y": 0.5}, {"time": 0.5, "x": 0.3, "y": 0.3}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.6, "x": 0.4, "y": 0.4}, {"time": 0.6333, "x": 0.15, "y": 0.15}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.7, "x": 0.4, "y": 0.4}, {"time": 0.8, "x": 0, "y": 0}, {"time": 0.8333, "x": 0.2, "y": 0.2}, {"time": 0.8667, "x": 0.3, "y": 0.3}, {"time": 0.9, "x": 0.15, "y": 0.15}, {"time": 0.9667, "x": 0, "y": 0}]}, "jjlz4": {"translate": [{"time": 0.1, "x": -33.29, "y": 126.51}, {"time": 0.4, "x": 34.48, "y": 47.08}], "scale": [{"time": 0.1, "x": 0.3, "y": 0.3}, {"time": 0.4, "x": 0.2, "y": 0.2}, {"time": 0.4333, "x": 0, "y": 0}]}, "xx6": {"rotate": [{"time": 0.3667, "angle": 46.2}], "translate": [{"time": 0.3667, "x": -128.94, "y": 168.98}, {"time": 0.4667, "x": -128.94, "y": 184.08}, {"time": 0.5, "x": -74.18, "y": -5.03}, {"time": 0.9667, "x": -74.18, "y": 58.02}], "scale": [{"time": 0.3667}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0.8, "y": 0.8}, {"time": 0.5667}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 0.8, "x": 0.7, "y": 0.7}, {"time": 0.8667, "x": 0.2, "y": 0.2}, {"time": 0.9, "x": 0, "y": 0}]}, "xx3": {"rotate": [{"time": 0.3667}, {"time": 0.9667, "angle": -12.13}], "translate": [{"time": 0.3667, "x": -129.53, "y": 70.26}, {"time": 0.7333, "x": -84.82, "y": 63.51}, {"time": 0.9667, "x": -84.82, "y": 97.69}], "scale": [{"time": 0.3667, "x": 0.35, "y": 0.35, "curve": "stepped"}, {"time": 0.4667, "x": 0.35, "y": 0.35}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.5333, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 0.5667, "x": 0.3, "y": 0.3}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.6333, "x": 0.25, "y": 0.25, "curve": "stepped"}, {"time": 0.6667, "x": 0.25, "y": 0.25}, {"time": 0.7, "x": 0, "y": 0}, {"time": 0.7333, "x": 0.3, "y": 0.3}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 0.8667, "x": 0.4, "y": 0.4, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0}, {"time": 0.9333, "x": 0.2, "y": 0.2, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0}]}, "jjlz5": {"translate": [{"time": 0.1333, "x": 6.69, "y": -110.32}, {"time": 0.4333, "x": 34.57, "y": 35.55}], "scale": [{"time": 0.1333, "x": 0.6, "y": 0.6}, {"time": 0.3333, "x": 0.5, "y": 0.5}, {"time": 0.4333, "x": 0.1, "y": 0.1}, {"time": 0.4667, "x": 0, "y": 0}]}, "xx13": {"rotate": [{"time": 0.4667, "angle": 46.2}], "translate": [{"time": 0.4667, "x": -128.94, "y": 168.98}, {"time": 0.5667, "x": -118.38, "y": 176.07}, {"time": 1.0667, "x": -122.23, "y": 256.4}], "scale": [{"time": 0.4667}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.6, "x": 0.8, "y": 0.8}, {"time": 0.6667}, {"time": 0.7, "x": 0, "y": 0}, {"time": 0.7333, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 0.8, "x": 0.3, "y": 0.3}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 0.9, "x": 0.7, "y": 0.7}, {"time": 0.9667, "x": 0.2, "y": 0.2}, {"time": 1, "x": 0, "y": 0}, {"time": 1.0333, "x": 0.35, "y": 0.35}, {"time": 1.0667, "x": 0, "y": 0}]}, "jjlz10": {"translate": [{"time": 0.2333, "x": 119.11, "y": -46.7}, {"time": 0.5667, "x": 34.57, "y": 49.66}], "scale": [{"time": 0.2333, "x": 0.6, "y": 0.6}, {"time": 0.5667, "x": 0, "y": 0}]}, "jjlz2": {"translate": [{"time": 0.1, "x": -59.93, "y": 97.74}, {"time": 0.4333, "x": 34.57, "y": 49.66}], "scale": [{"time": 0.1, "x": 0.7, "y": 0.7}, {"time": 0.4333, "x": 0, "y": 0}]}, "xx12": {"translate": [{"time": 0.4667, "x": -155.88, "y": 149.02}, {"time": 0.5667, "x": -134.86, "y": 141.73}, {"time": 0.7333, "x": -134.86, "y": 172.19}, {"time": 0.7667, "x": -208.62, "y": 173.61}], "scale": [{"time": 0.4667, "x": 0.3, "y": 0.3}, {"time": 0.5333, "x": 0.5, "y": 0.5}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.6, "x": 0.3, "y": 0.3}, {"time": 0.6333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.15, "y": 0.15}, {"time": 0.7333, "x": 0, "y": 0}, {"time": 0.7667, "x": 0.4, "y": 0.4}, {"time": 0.8, "x": 0, "y": 0}, {"time": 0.8333, "x": 0.2, "y": 0.2}, {"time": 0.8667, "x": 0.3, "y": 0.3}, {"time": 0.9, "x": 0.15, "y": 0.15}, {"time": 0.9333, "x": 0, "y": 0}]}, "xx4": {"translate": [{"time": 0.3667, "x": -20.37, "y": 152.14}, {"time": 0.6667, "x": -183.62, "y": 79.92}, {"time": 1.1333, "x": -183.62, "y": 118.46}], "scale": [{"time": 0.3667, "x": 0.2, "y": 0.2}, {"time": 0.4, "x": 0.35, "y": 0.35, "curve": "stepped"}, {"time": 0.4333, "x": 0.35, "y": 0.35}, {"time": 0.4667, "x": 0.2, "y": 0.2}, {"time": 0.5, "x": 0.35, "y": 0.35}, {"time": 0.5333, "x": 0.2, "y": 0.2, "curve": "stepped"}, {"time": 0.5667, "x": 0.2, "y": 0.2}, {"time": 0.6, "x": 0.25, "y": 0.25}, {"time": 0.6333, "x": 0.1, "y": 0.1}, {"time": 0.6667, "x": 0.5, "y": 0.5}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.8333}, {"time": 0.8667, "x": 0.2, "y": 0.2}, {"time": 0.9, "x": 0.1, "y": 0.1}, {"time": 0.9333, "x": 0.4, "y": 0.4}, {"time": 1, "x": 0, "y": 0}, {"time": 1.0333, "x": 0.3, "y": 0.3}, {"time": 1.1333, "x": 0, "y": 0}]}, "jjlz13": {"translate": [{"time": 0.2667, "x": 43.41, "y": -96.83}, {"time": 0.5, "x": 40.18, "y": 37.56}], "scale": [{"time": 0.2667, "x": 0.5, "y": 0.5}, {"time": 0.5, "x": 0, "y": 0}]}, "jjlz12": {"translate": [{"time": 0.2333, "x": -25.35, "y": 174.98}, {"time": 0.5667, "x": 40.54, "y": 45.54}], "scale": [{"time": 0.2333}, {"time": 0.5667, "x": 0.8, "y": 0.8}, {"time": 0.6, "x": 0, "y": 0}]}, "xx14": {"translate": [{"time": 0.4333, "x": -20.37, "y": 36.24}, {"time": 0.7333, "x": -26.94, "y": 104.58}, {"time": 1.0333, "x": -26.94, "y": 137.64}], "scale": [{"time": 0.4333, "x": 0.2, "y": 0.2}, {"time": 0.4667, "x": 0.35, "y": 0.35, "curve": "stepped"}, {"time": 0.5, "x": 0.35, "y": 0.35}, {"time": 0.5333, "x": 0.2, "y": 0.2}, {"time": 0.5667, "x": 0.35, "y": 0.35}, {"time": 0.6, "x": 0.2, "y": 0.2, "curve": "stepped"}, {"time": 0.6667, "x": 0.25, "y": 0.25}, {"time": 0.7, "x": 0.1, "y": 0.1}, {"time": 0.7333, "x": 0.5, "y": 0.5}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8}, {"time": 0.8667, "x": 0.2, "y": 0.2}, {"time": 0.9, "x": 0.4, "y": 0.4}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 0.9667, "x": 0.3, "y": 0.3}, {"time": 1, "x": 0, "y": 0}]}, "jjlz21": {"translate": [{"time": 0.3333, "x": -68.06, "y": 74.55}, {"time": 0.5333, "x": 44.56, "y": 44.72}], "scale": [{"time": 0.3333, "x": 0.5, "y": 0.5}, {"time": 0.5333, "x": 0.2, "y": 0.2}, {"time": 0.5667, "x": 0, "y": 0}]}, "jjlz17": {"translate": [{"time": 0.3, "x": 159.03, "y": 9.67}, {"time": 0.5333, "x": 44.23, "y": 39.72}], "scale": [{"time": 0.3, "x": 0.9, "y": 0.9}, {"time": 0.5333, "x": 0.2, "y": 0.2}, {"time": 0.5667, "x": 0, "y": 0}]}, "jjlz3": {"translate": [{"time": 0.1, "x": -12.56, "y": 117.56}, {"time": 0.4333, "x": 29.47, "y": 46.53}], "scale": [{"time": 0.3333}, {"time": 0.4333, "x": 0, "y": 0}]}, "jjlz6": {"translate": [{"time": 0.1667, "x": 134.63, "y": 92.27}, {"time": 0.4667, "x": 44.97, "y": 45.03}], "scale": [{"time": 0.3667}, {"time": 0.4667, "x": 0.5, "y": 0.5}, {"time": 0.5, "x": 0, "y": 0}]}, "jjlz20": {"translate": [{"time": 0.3333, "x": 115.83, "y": 128.94}, {"time": 0.5333, "x": 44.56, "y": 44.72}], "scale": [{"time": 0.3333, "x": 0.25, "y": 0.25}, {"time": 0.5333, "x": 0.2, "y": 0.2}, {"time": 0.5667, "x": 0, "y": 0}]}, "jjlz7": {"translate": [{"time": 0.2, "x": -89.18, "y": 39.64}, {"time": 0.3667, "x": 19.88, "y": 44.57}], "scale": [{"time": 0.2, "x": 0.3, "y": 0.3, "curve": "stepped"}, {"time": 0.3667, "x": 0.3, "y": 0.3}, {"time": 0.4, "x": 0, "y": 0}]}, "xx11": {"translate": [{"time": 0.5, "x": -155.88, "y": 149.02}, {"time": 0.6, "x": -155.88, "y": 167.15}, {"time": 0.6333, "x": -134.86, "y": 141.73}, {"time": 0.8, "x": -134.86, "y": 172.19}], "scale": [{"time": 0.5, "x": 0.3, "y": 0.3}, {"time": 0.5667, "x": 0.5, "y": 0.5}, {"time": 0.6333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.3, "y": 0.3}, {"time": 0.7, "x": 0, "y": 0}, {"time": 0.7333, "x": 0.4, "y": 0.4}, {"time": 0.7667, "x": 0.15, "y": 0.15}, {"time": 0.8, "x": 0, "y": 0}]}, "xx8": {"rotate": [{"time": 0.4333, "angle": -45.73}, {"time": 1.1667, "angle": -124.96}], "translate": [{"time": 0.4333, "x": 1.11, "y": 113.11}, {"time": 1.1667, "x": 1.11, "y": 146.6}], "scale": [{"time": 0.5, "x": 0, "y": 0}, {"time": 0.5333, "x": 0.9, "y": 0.9}, {"time": 0.5667, "x": 0.34, "y": 0.34}, {"time": 0.6333, "x": 0.1, "y": 0.1}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.7, "x": 0.3, "y": 0.3}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.8}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 0.8667, "x": 0.5, "y": 0.5}, {"time": 0.9, "x": 0.2, "y": 0.2}, {"time": 0.9333, "x": 0.1, "y": 0.1}, {"time": 0.9667, "x": 0.3, "y": 0.3}, {"time": 1, "x": 0.15, "y": 0.15}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.1, "x": 0.3, "y": 0.3}, {"time": 1.1667, "x": 0, "y": 0}]}, "jjlz11": {"translate": [{"time": 0.2333, "x": 48.56, "y": 130.48}, {"time": 0.4333, "x": 45.67, "y": 49.66}], "scale": [{"time": 0.2333, "x": 0.5, "y": 0.5}, {"time": 0.4333, "x": 0.2, "y": 0.2}, {"time": 0.4667, "x": 0, "y": 0}]}, "jjlz16": {"translate": [{"time": 0.2667, "x": -14.33, "y": 143.65}, {"time": 0.5667, "x": 40.91, "y": 45.88}], "scale": [{"time": 0.2667}, {"time": 0.5667, "x": 0.6, "y": 0.6}, {"time": 0.6, "x": 0, "y": 0}]}, "glow": {"translate": [{"time": 0.7, "y": 258.94}], "scale": [{"time": 0.7, "x": 18.077, "y": 18.077}]}, "jjlz14": {"translate": [{"time": 0.2667, "x": -86.59, "y": -62.84}, {"time": 0.5667, "x": 38.17, "y": 40.96}], "scale": [{"time": 0.2667, "x": 0.4, "y": 0.4}, {"time": 0.5667, "x": 0, "y": 0}]}, "jjlz19": {"translate": [{"time": 0.3333, "x": 12.62, "y": 151.6}, {"time": 0.5333, "x": 41.36, "y": 42.45}], "scale": [{"time": 0.3333, "x": 0.6, "y": 0.6}, {"time": 0.5333, "x": 0.5, "y": 0.5}, {"time": 0.5667, "x": 0, "y": 0}]}, "jjlz15": {"translate": [{"time": 0.2667, "x": 68.09, "y": 123.63}, {"time": 0.5333, "x": 43.22, "y": 49.66}], "scale": [{"time": 0.2667, "x": 0.8, "y": 0.8}, {"time": 0.5333, "x": 0.2, "y": 0.2}, {"time": 0.5667, "x": 0, "y": 0}]}, "xlt3": {"rotate": [{"angle": 11.78}], "translate": [{"x": 56.85, "y": -18.76}]}, "xx2": {"rotate": [{"time": 0.3667, "angle": -45.73}, {"time": 1.1, "angle": -124.96}], "translate": [{"time": 0.3667, "x": 1.11, "y": 113.11}, {"time": 1.1, "x": 1.11, "y": 146.6}], "scale": [{"time": 0.4333, "x": 0, "y": 0}, {"time": 0.4667, "x": 0.9, "y": 0.9}, {"time": 0.5, "x": 0.34, "y": 0.34}, {"time": 0.5667, "x": 0.1, "y": 0.1}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.6333, "x": 0.3, "y": 0.3}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 0.8, "x": 0.5, "y": 0.5}, {"time": 0.8333, "x": 0.2, "y": 0.2}, {"time": 0.8667, "x": 0.1, "y": 0.1}, {"time": 0.9, "x": 0.3, "y": 0.3}, {"time": 0.9333, "x": 0.15, "y": 0.15}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.0333, "x": 0.3, "y": 0.3}, {"time": 1.1, "x": 0, "y": 0}]}, "xx": {"translate": [{"time": 0.3667, "x": -51.63, "y": 135.3}, {"time": 0.7, "x": -51.63, "y": 161.3}, {"time": 0.7333, "x": -48.52, "y": -10.36}, {"time": 1.0333, "x": -48.52, "y": 16.51}], "scale": [{"time": 0.4333}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.5, "x": 0.4, "y": 0.4}, {"time": 0.5333, "x": 0.6, "y": 0.6}, {"time": 0.5667, "x": 0.1, "y": 0.1}, {"time": 0.6, "x": 0.2, "y": 0.2}, {"time": 0.6333, "x": 0.35, "y": 0.35}, {"time": 0.6667, "x": 0.15, "y": 0.15}, {"time": 0.7, "x": 0, "y": 0}, {"time": 0.7333, "x": 0.5, "y": 0.5}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8667}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 0.9667, "x": 0.5, "y": 0.5}, {"time": 1, "x": 0.2, "y": 0.2}, {"time": 1.0333, "x": 0.4, "y": 0.4}, {"time": 1.1333, "x": 0, "y": 0}]}, "xx15": {"translate": [{"time": 0.4333, "x": -20.37, "y": 152.14}, {"time": 0.7333, "x": -29.43, "y": 113.94}, {"time": 1, "x": -29.43, "y": 148.17}], "scale": [{"time": 0.4333, "x": 0.2, "y": 0.2}, {"time": 0.4667, "x": 0.35, "y": 0.35, "curve": "stepped"}, {"time": 0.5, "x": 0.35, "y": 0.35}, {"time": 0.5333, "x": 0.2, "y": 0.2}, {"time": 0.5667, "x": 0.35, "y": 0.35}, {"time": 0.6, "x": 0.2, "y": 0.2, "curve": "stepped"}, {"time": 0.6667, "x": 0.25, "y": 0.25}, {"time": 0.7, "x": 0.1, "y": 0.1}, {"time": 0.7333, "x": 0.5, "y": 0.5}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8}, {"time": 0.8333, "x": 0.2, "y": 0.2}, {"time": 0.9, "x": 0.4, "y": 0.4}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 0.9667, "x": 0.3, "y": 0.3}, {"time": 1, "x": 0, "y": 0}]}, "zong": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 67.41}]}, "ju": {"translate": [{"x": 23.91, "y": 280.25}], "scale": [{"x": 2, "y": 2}]}, "r": {"translate": [{"y": 271.61}], "scale": [{"x": 4.507, "y": 4.281, "curve": "stepped"}, {"time": 0.0333, "x": 4.507, "y": 4.281}, {"time": 0.1333, "x": 2.216, "y": 1.987}, {"time": 1, "x": 0.758, "y": 0.409}]}, "rao": {"rotate": [{"time": 0.0333}, {"time": 0.7, "angle": -78.31}]}, "ju5": {"rotate": [{"time": 0.2333, "angle": -99.3}], "translate": [{"time": 0.2333, "x": -149.38, "y": 127.73}], "scale": [{"time": 0.2333, "x": 3.046, "y": 3.046}]}, "ju6": {"translate": [{"time": 0.2333, "x": 140.07, "y": 187.64}], "scale": [{"time": 0.2333, "x": 2, "y": 2}]}, "ju4": {"rotate": [{"time": 0.3333, "angle": -107.16}], "translate": [{"time": 0.3333, "x": -139.88, "y": 100.08}], "scale": [{"time": 0.3333, "x": 1.576, "y": 1.576}], "shear": [{"time": 0.3333, "x": -27.59}]}, "ju9": {"rotate": [{"time": 0.4333, "angle": -37.67}], "translate": [{"time": 0.4333, "x": 141.76, "y": 209.38}]}, "ju7": {"rotate": [{"time": 0.4333, "angle": 123.45}], "translate": [{"time": 0.4333, "x": -51.95, "y": 355.32}], "scale": [{"time": 0.4333, "x": 1.831, "y": 2.916}], "shear": [{"time": 0.4333, "x": 46.29, "y": -7.37}]}, "ju8": {"rotate": [{"time": 0.4667, "angle": -96.13}], "translate": [{"time": 0.4667, "x": 126.65, "y": 161.98}], "scale": [{"time": 0.4667, "x": 1.496, "y": -2.98}]}, "ju10": {"rotate": [{"time": 0.5, "angle": 110.91}], "translate": [{"time": 0.5, "x": -100.52, "y": 325.64}], "scale": [{"time": 0.5, "x": 0.65, "y": 1.654}], "shear": [{"time": 0.5, "x": 10.72, "y": 4.4}]}, "yshuo1": {"translate": [{"time": 0.6667, "y": 259.31}], "scale": [{"time": 0.6667}, {"time": 0.7, "x": 3.73, "y": 3.73}]}, "xiaohupo": {"rotate": [{"time": 0.7, "angle": 102.96}], "translate": [{"time": 0.7, "x": -53.23, "y": 216.02}], "scale": [{"time": 0.7, "x": 1.197, "y": 1.06}]}, "xiaohupo2": {"rotate": [{"time": 0.7, "angle": -43.76}], "translate": [{"time": 0.7, "x": 21.06, "y": 320.77}], "scale": [{"time": 0.7, "x": 1.197, "y": 1.06}]}, "xiaohupo3": {"rotate": [{"time": 0.7, "angle": -150.99}], "translate": [{"time": 0.7, "x": 42.99, "y": 219.67}], "scale": [{"time": 0.7, "x": 1.197, "y": 1.06}]}, "bone141": {"translate": [{"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 1271.27, "y": -53.08}]}, "quan": {"translate": [{"time": 0.7, "x": -3.2, "y": 30.02}, {"time": 0.7333, "x": -4.03, "y": 32.04}], "scale": [{"time": 0.7, "x": 2.538, "y": 2.538}, {"time": 0.7333, "x": 4.905, "y": 4.905}, {"time": 0.8667, "x": 6.156, "y": 6.156}]}, "an": {"translate": [{"time": 0.7, "x": -3.28, "y": 32.75}], "scale": [{"time": 0.8}, {"time": 0.9333, "x": 1.172, "y": 1.172}]}, "kuo": {"translate": [{"time": 0.7, "x": -9.99, "y": 30.28}], "scale": [{"time": 0.7, "x": 1.766, "y": 1.766, "curve": "stepped"}, {"time": 0.7333, "x": 1.766, "y": 1.766}, {"time": 1, "x": 3.284, "y": 3.284}]}, "sg2": {"translate": [{"time": 0.7, "x": -12.41, "y": 27.62}], "scale": [{"time": 0.7333}, {"time": 0.8333, "x": 0.404, "y": 0.404}]}, "xian2": {"rotate": [{"time": 0.7333, "angle": 141.23}, {"time": 1.0667, "angle": 125.3}], "translate": [{"time": 0.7333, "x": -7.3, "y": 32.48}], "scale": [{"time": 0.7333}, {"time": 0.7667, "x": 1.557, "y": 1.557}, {"time": 1.0667, "x": 0.242, "y": 2.192}]}, "hen": {"translate": [{"time": 0.7333, "x": -7.09, "y": 40.45}], "scale": [{"time": 0.7333, "x": 4.762, "y": 7.291}]}, "xian": {"rotate": [{"time": 0.7333, "angle": 33.18}, {"time": 1.0333, "angle": 16.54}], "translate": [{"time": 0.7333, "x": -8.76, "y": 33.27}], "scale": [{"time": 0.7333}, {"time": 0.7667, "x": 1.557, "y": 1.557}, {"time": 1.0667, "x": 0.242, "y": 2.192}]}, "sg": {"translate": [{"time": 0.7333, "x": -5.63, "y": 31.66}], "scale": [{"time": 0.7333, "x": 1.5, "y": 1.5, "curve": "stepped"}, {"time": 0.7667, "x": 1.5, "y": 1.5}, {"time": 0.8, "x": 1.716, "y": 1.716}]}, "xian3": {"rotate": [{"time": 0.7333, "angle": -70.33}, {"time": 1.0667, "angle": -89.26}], "translate": [{"time": 0.7333, "x": -8.51, "y": 31.06}], "scale": [{"time": 0.7333}, {"time": 0.7667, "x": 1.557, "y": 1.557}, {"time": 1.0667, "x": 0.242, "y": 2.192}]}, "an2": {"translate": [{"time": 0.7667, "x": -3.28, "y": 32.75}], "scale": [{"time": 0.8667}, {"time": 1, "x": 1.172, "y": 1.172}]}, "hen2": {"translate": [{"time": 0.7667, "x": -7.09, "y": 40.45}], "scale": [{"time": 0.7667, "x": 4.762, "y": 7.291}]}, "kuo2": {"translate": [{"time": 0.7667, "x": -9.99, "y": 30.28}], "scale": [{"time": 0.7667, "x": 1.766, "y": 1.766}, {"time": 1.0667, "x": 3.284, "y": 3.284}]}, "quan2": {"translate": [{"time": 0.7667, "x": -4.03, "y": 32.04}], "scale": [{"time": 0.7667, "x": 4.905, "y": 4.905}, {"time": 0.9333, "x": 6.156, "y": 6.156}]}, "sg3": {"translate": [{"time": 0.7667, "x": -5.63, "y": 31.66}], "scale": [{"time": 0.7667, "x": 1.5, "y": 1.5, "curve": "stepped"}, {"time": 0.8, "x": 1.5, "y": 1.5}, {"time": 0.8667, "x": 1.716, "y": 1.716}]}, "sg4": {"translate": [{"time": 0.7667, "x": -12.41, "y": 27.62}], "scale": [{"time": 0.7667}, {"time": 0.8667, "x": 0.404, "y": 0.404}]}, "xian4": {"rotate": [{"time": 0.7667, "angle": 33.18}, {"time": 1.1, "angle": 16.54}], "translate": [{"time": 0.7667, "x": -8.76, "y": 33.27}], "scale": [{"time": 0.7667}, {"time": 0.8333, "x": 1.557, "y": 1.557}, {"time": 1.1333, "x": 0.242, "y": 2.192}]}, "xian5": {"rotate": [{"time": 0.7667, "angle": 141.23}, {"time": 1.1333, "angle": 125.3}], "translate": [{"time": 0.7667, "x": -7.3, "y": 32.48}], "scale": [{"time": 0.7667}, {"time": 0.8333, "x": 1.557, "y": 1.557}, {"time": 1.1333, "x": 0.242, "y": 2.192}]}, "xian6": {"rotate": [{"time": 0.7667, "angle": -70.33}, {"time": 1.1333, "angle": -89.26}], "translate": [{"time": 0.7667, "x": -8.51, "y": 31.06}], "scale": [{"time": 0.7667}, {"time": 0.8333, "x": 1.557, "y": 1.557}, {"time": 1.1333, "x": 0.242, "y": 2.192}]}, "an3": {"translate": [{"time": 0.8667, "x": -3.28, "y": 32.75}], "scale": [{"time": 0.9667}, {"time": 1.1, "x": 1.172, "y": 1.172}]}, "hen3": {"translate": [{"time": 0.8667, "x": -7.09, "y": 40.45}], "scale": [{"time": 0.8667, "x": 4.762, "y": 7.291}]}, "kuo3": {"translate": [{"time": 0.8667, "x": -9.99, "y": 30.28}], "scale": [{"time": 0.8667, "x": 1.766, "y": 1.766}, {"time": 1.1667, "x": 3.284, "y": 3.284}]}, "quan3": {"translate": [{"time": 0.8667, "x": -4.03, "y": 32.04}], "scale": [{"time": 0.8667, "x": 4.905, "y": 4.905}, {"time": 1.0333, "x": 6.156, "y": 6.156}]}, "sg5": {"translate": [{"time": 0.8667, "x": -5.63, "y": 31.66}], "scale": [{"time": 0.8667, "x": 1.5, "y": 1.5, "curve": "stepped"}, {"time": 0.9, "x": 1.5, "y": 1.5}, {"time": 0.9667, "x": 1.716, "y": 1.716}]}, "sg6": {"translate": [{"time": 0.8667, "x": -12.41, "y": 27.62}], "scale": [{"time": 0.8667}, {"time": 0.9667, "x": 0.404, "y": 0.404}]}, "xian7": {"rotate": [{"time": 0.8667, "angle": 33.18}, {"time": 1.2, "angle": 16.54}], "translate": [{"time": 0.8667, "x": -8.76, "y": 33.27}], "scale": [{"time": 0.8667}, {"time": 0.9333, "x": 1.557, "y": 1.557}, {"time": 1.2333, "x": 0.242, "y": 2.192}]}, "xian8": {"rotate": [{"time": 0.8667, "angle": 141.23}, {"time": 1.2333, "angle": 125.3}], "translate": [{"time": 0.8667, "x": -7.3, "y": 32.48}], "scale": [{"time": 0.8667}, {"time": 0.9333, "x": 1.557, "y": 1.557}, {"time": 1.2333, "x": 0.242, "y": 2.192}]}, "xian9": {"rotate": [{"time": 0.8667, "angle": -70.33}, {"time": 1.2333, "angle": -89.26}], "translate": [{"time": 0.8667, "x": -8.51, "y": 31.06}], "scale": [{"time": 0.8667}, {"time": 0.9333, "x": 1.557, "y": 1.557}, {"time": 1.2333, "x": 0.242, "y": 2.192}]}}, "path": {"red_line1": {"position": [{"position": 0.2677, "curve": "stepped"}, {"time": 0.7, "position": 0.2677, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "position": 0.8737}], "spacing": [{"curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "spacing": 56.6}]}, "red_line2": {"position": [{"time": 0.7, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.8333, "position": 0.4596, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.0333, "position": 1}], "spacing": [{"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "spacing": 59.6}]}, "red_line4": {"position": [{"position": 0.197, "curve": "stepped"}, {"time": 0.7, "position": 0.197, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "position": 1}], "spacing": [{"time": 0.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8667, "spacing": 52.5}]}}, "events": [{"time": 0.8, "name": "hit"}]}, "effect03": {"slots": {"0/smoke_wanghou/0_00000": {"attachment": [{"name": "0/smoke_wanghou/0_00000"}, {"time": 0.0333, "name": "0/smoke_wanghou/0_00002"}, {"time": 0.0667, "name": "0/smoke_wanghou/0_00003"}, {"time": 0.1, "name": "0/smoke_wanghou/0_00005"}, {"time": 0.1333, "name": "0/smoke_wanghou/0_00006"}, {"time": 0.1667, "name": "0/smoke_wanghou/0_00007"}, {"time": 0.2, "name": "0/smoke_wanghou/0_00009"}, {"time": 0.2333, "name": "0/smoke_wanghou/0_00010"}, {"time": 0.2667, "name": "0/smoke_wanghou/0_00011"}]}}, "bones": {"bone2": {"translate": [{"y": -11.58, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "y": 3.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "y": -11.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 3.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": -11.58}]}}}}}