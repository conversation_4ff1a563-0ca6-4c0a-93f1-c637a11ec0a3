import { GameDirector } from "../../../game/GameDirector";
import { JsonMgr } from "../../../game/mgr/JsonMgr";
import { RealmsMessage } from "../../../game/net/protocol/Realms";
import { times } from "../../../lib/utils/NumbersUtils";
import { TrainModule } from "./TrainModule";

export class TrainData {
  private _realmsMessage: RealmsMessage = {
    maxUnlockId: 0,
    maxConquerId: 0,
    sweepCount: 0,
    takeCurServerRankReward: false,
  };
  /**
   * 获取 _realmsMessage 属性值
   */
  get realmsMessage(): RealmsMessage {
    return this._realmsMessage;
  }

  /**
   * 设置 _realmsMessage 属性值
   * @param value - 要设置的新值
   */
  set realmsMessage(value: RealmsMessage) {
    this._realmsMessage = value;
    GameDirector.instance.isSystemOpen;
  }
}
