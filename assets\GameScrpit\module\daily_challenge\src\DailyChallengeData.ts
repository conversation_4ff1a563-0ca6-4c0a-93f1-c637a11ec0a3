import { JsonMgr } from "../../../game/mgr/JsonMgr";
import { ChallengeMessage } from "../../../game/net/protocol/Challenge";
import { times } from "../../../lib/utils/NumbersUtils";
import { DailyChallengeModule } from "./DailyChallengeModule";

export class DailyChallengeData {
  private _challengeMessage: ChallengeMessage;

  public get challengeMessage() {
    return this._challengeMessage;
  }

  public set challengeMessage(value: ChallengeMessage) {
    this._challengeMessage = value;
  }
}
