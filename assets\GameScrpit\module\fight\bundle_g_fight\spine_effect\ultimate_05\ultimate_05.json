{"skeleton": {"hash": "z0NKj3bLyRdXofe/GVdaVAGaMP4=", "spine": "3.8.75", "images": "../主角5/images/", "audio": "D:/spine导出/Z_新主角/主角1"}, "bones": [{"name": "root"}, {"name": "qian_zj_all", "parent": "root", "length": 113.37, "rotation": 0.39, "x": -27.59, "y": -17.83}, {"name": "R_jio1", "parent": "qian_zj_all", "rotation": -0.39, "x": 59.07, "y": 71.32, "color": "ff3f00ff"}, {"name": "L_jio1", "parent": "qian_zj_all", "rotation": -0.39, "x": 20.37, "y": 66.08, "color": "ff3f00ff"}, {"name": "L_jio2", "parent": "qian_zj_all", "rotation": -0.39, "x": 4.65, "y": 26.89, "color": "0014ffff"}, {"name": "R1_jio1", "parent": "qian_zj_all", "rotation": -0.39, "x": 44.34, "y": 71.67, "color": "ff3f00ff"}, {"name": "tx", "parent": "qian_zj_all", "length": 126.03, "rotation": 0.18, "x": -57.28, "y": -19.47, "scaleX": -2.4651, "scaleY": 2.4651}, {"name": "dg1", "parent": "tx", "rotation": 4.76, "x": -10.6, "y": 69.08, "scaleX": 1.8245, "scaleY": 0.5823}, {"name": "dg3", "parent": "tx", "rotation": -8.06, "x": -10.6, "y": 69.08, "scaleX": 2.6844, "scaleY": 0.8568}, {"name": "tx2", "parent": "tx", "rotation": -166.7, "x": -112.4, "y": 66.31, "scaleX": -1}, {"name": "tx3", "parent": "tx", "rotation": -155.11, "x": -120.53, "y": 48.37, "scaleX": -0.6809, "scaleY": 0.6809}, {"name": "tx4", "parent": "tx", "x": -100.33, "y": 59.96}, {"name": "qiliu", "parent": "root", "x": -12.69, "y": 119.7, "scaleX": 3.9962, "scaleY": 3.9962}, {"name": "daz1", "parent": "root", "rotation": -37.14, "x": 81.47, "y": 401.5, "scaleY": 1.7862}, {"name": "daz2", "parent": "daz1", "x": 44.23, "scaleX": 1.0323, "scaleY": 0.8849}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "x": 336.9, "y": 99.74, "scaleX": 1.9525, "scaleY": 1.9525}, {"name": "dgdg", "parent": "root", "x": 417.63, "y": 22.25, "scaleX": 2.1651, "scaleY": 1.8279}, {"name": "jifa", "parent": "root", "x": 589.55, "y": 166.18, "scaleY": 1.3198}, {"name": "daz3", "parent": "daz1", "x": 44.23, "scaleX": 1.0323, "scaleY": 0.8849}, {"name": "sankai", "parent": "root", "x": 23.59, "y": 456.36, "scaleX": -2.1188, "scaleY": 2.1188}, {"name": "sankai2", "parent": "root", "x": 23.59, "y": 456.36, "scaleX": -2.1188, "scaleY": 2.1188}, {"name": "heimun", "parent": "root", "x": 224.27, "y": 124.03, "scaleX": 45.2929, "scaleY": 70.9369}, {"name": "xuli_q", "parent": "root", "x": -57.22, "y": -80.29, "scaleX": 2.7986, "scaleY": 2.7986}, {"name": "guanci001_add", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add2", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add3", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add4", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add5", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add6", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanman001", "parent": "xuli_q"}, {"name": "guanquan", "parent": "xuli_q"}, {"name": "guanquan2", "parent": "xuli_q"}, {"name": "guanci001_add7", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add8", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add9", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add10", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add11", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add12", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add13", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanman1", "parent": "xuli_q"}, {"name": "guanquan3", "parent": "xuli_q"}, {"name": "guanquan4", "parent": "xuli_q"}, {"name": "all", "parent": "root", "x": -859.73, "y": 389.88, "color": "ff0000ff"}, {"name": "all2", "parent": "all", "x": 7.39, "y": 87.91, "color": "ff0000ff"}, {"name": "xizhen", "parent": "all2", "length": 100, "rotation": -7.34, "x": 22.57, "y": 3.46, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "xizhen2", "parent": "all2", "length": 100, "rotation": -7.34, "x": 43.06, "y": 5.14, "scaleX": 3.236, "scaleY": 0.2515}, {"name": "xizhen11", "parent": "all2", "length": 100, "rotation": -7.34, "x": 21.78, "y": -2.69, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "kuang", "parent": "all", "x": 275.06}, {"name": "xizhen4", "parent": "all", "length": 100, "rotation": -7.34, "x": 29.75, "y": 89.74, "scaleX": 3.236, "scaleY": 0.0301}, {"name": "all3", "parent": "all", "x": 14.96, "y": -101.29, "color": "ff0000ff"}, {"name": "xizhen5", "parent": "all3", "length": 100, "rotation": 5.49, "x": 12.83, "y": -1.37, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "xizhen6", "parent": "all3", "length": 100, "rotation": 5.49, "x": 35.41, "y": -2.98, "scaleX": 3.236, "scaleY": 0.2515}, {"name": "xizhen12", "parent": "all3", "length": 100, "rotation": 5.49, "x": 67.09, "y": 21.81, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "xizhen9", "parent": "all", "length": 100, "rotation": 1.51, "x": 35.33, "y": -24.12, "scaleX": 3.236, "scaleY": 1.4857}, {"name": "xizhen10", "parent": "all", "length": 100, "rotation": -3.24, "x": 46.95, "y": 35.09, "scaleX": 3.236, "scaleY": 1.2129}, {"name": "light", "parent": "all", "x": 23.8, "y": -3.31, "scaleX": 2.1882, "scaleY": 2.9235}, {"name": "lizi", "parent": "all", "rotation": -41.67, "x": 56.56, "y": -95.97, "scaleX": 4.1818, "scaleY": 4.1818, "color": "ffbc00ff"}, {"name": "xizhen13", "parent": "all", "length": 100, "rotation": -1.05, "x": -34.39, "y": 24.82, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "light11", "parent": "root", "length": 30, "x": 267.14, "y": -213.56, "scaleX": 2.2043, "scaleY": 0.7768}, {"name": "bone36", "parent": "root", "x": -593.3, "y": 298, "color": "002cffff"}, {"name": "biglight", "parent": "root", "x": 245.39, "y": 112.28, "scaleX": 27.3579, "scaleY": 27.3579}, {"name": "ciji", "parent": "root", "x": 181.73, "y": 102.22, "scaleX": 1.7429, "scaleY": 1.3415}, {"name": "daztx", "parent": "root", "x": -257.18, "y": -1242.25}, {"name": "daztx2", "parent": "daztx", "x": 195.33, "y": -12.21}, {"name": "dg4", "parent": "tx", "rotation": -8.06, "x": -10.6, "y": 69.08, "scaleX": 2.6844, "scaleY": 0.8568}, {"name": "zi_guazai", "parent": "all", "x": 481.04, "y": -7.45}], "slots": [{"name": "ef1/adg", "bone": "dg1"}, {"name": "ef1/adg3", "bone": "dg3"}, {"name": "ef1/adg4", "bone": "dg4"}, {"name": "ef1/baoh_01", "bone": "tx2", "color": "ffcd00ff", "dark": "ffa400", "blend": "additive"}, {"name": "ef1/baoh_1", "bone": "tx3", "color": "ffcd00ff", "dark": "ffa400", "blend": "additive"}, {"name": "ef1/dd_4", "bone": "tx4", "blend": "additive"}, {"name": "ci/qiliu00", "bone": "qiliu"}, {"name": "dazhao1/dz1/jian_skill3_3_dg1_53", "bone": "daz2"}, {"name": "dazhao1/dz1/jian_skill3_3_dg1_54", "bone": "daz3"}, {"name": "dazhao1/dz2/nu_skill_3_xuli_23", "bone": "<PERSON><PERSON><PERSON>"}, {"name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00", "bone": "dgdg"}, {"name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_00", "bone": "jifa"}, {"name": "baozhaguang/1_00000", "bone": "sankai", "color": "ffb500ff", "dark": "ff8900", "blend": "additive"}, {"name": "baozhaguang/1_0", "bone": "sankai2", "color": "ffb500ff", "dark": "ff8900", "blend": "additive"}, {"name": "img1", "bone": "heimun", "color": "0000004e"}, {"name": "texiao/skill1/guanci001", "bone": "guanci001_add", "blend": "additive"}, {"name": "texiao/skill1/guanci12", "bone": "guanci001_add13", "blend": "additive"}, {"name": "texiao/skill1/guanci2", "bone": "guanci001_add7", "blend": "additive"}, {"name": "texiao/skill1/guanci5", "bone": "guanci001_add4", "blend": "additive"}, {"name": "texiao/skill1/guanci9", "bone": "guanci001_add10", "blend": "additive"}, {"name": "texiao/skill1/guanci6", "bone": "guanci001_add5", "blend": "additive"}, {"name": "texiao/skill1/guanci10", "bone": "guanci001_add11", "blend": "additive"}, {"name": "texiao/skill1/guanci7", "bone": "guanci001_add6", "blend": "additive"}, {"name": "texiao/skill1/guanci11", "bone": "guanci001_add12", "blend": "additive"}, {"name": "texiao/skill1/guanci1", "bone": "guanci001_add2", "blend": "additive"}, {"name": "texiao/skill1/guanci3", "bone": "guanci001_add8", "blend": "additive"}, {"name": "texiao/skill1/guanci4", "bone": "guanci001_add3", "blend": "additive"}, {"name": "texiao/skill1/guanci8", "bone": "guanci001_add9", "blend": "additive"}, {"name": "texiao/skill1/guanman001", "bone": "guanman001", "blend": "additive"}, {"name": "texiao/skill1/guanman1", "bone": "guanman1", "blend": "additive"}, {"name": "texiao/skill1/guanquan", "bone": "guanquan", "blend": "additive"}, {"name": "texiao/skill1/guanquan3", "bone": "guanquan3", "blend": "additive"}, {"name": "texiao/skill1/guanquan2", "bone": "guanquan2", "blend": "additive"}, {"name": "texiao/skill1/guanquan4", "bone": "guanquan4", "blend": "additive"}, {"name": "line_fire", "bone": "kuang"}, {"name": "line_orange12", "bone": "xizhen11", "blend": "additive"}, {"name": "line_orange14", "bone": "xizhen13", "blend": "additive"}, {"name": "line_orange10", "bone": "xizhen9", "color": "ffffff6d", "blend": "additive"}, {"name": "line_orange11", "bone": "xizhen10", "color": "ffffff94", "blend": "additive"}, {"name": "line_orange5", "bone": "xizhen4", "blend": "additive"}, {"name": "line_orange3", "bone": "xizhen2"}, {"name": "line_orange2", "bone": "xizhen", "blend": "additive"}, {"name": "img", "bone": "bone36"}, {"name": "img2", "bone": "bone36"}, {"name": "line_orange6", "bone": "xizhen5", "blend": "additive"}, {"name": "line_orange13", "bone": "xizhen12", "blend": "additive"}, {"name": "line_orange7", "bone": "xizhen6"}, {"name": "light2", "bone": "light", "color": "ff8c0094", "blend": "additive"}, {"name": "light3", "bone": "light11", "blend": "additive"}, {"name": "skill2_xinchongci/skill2_xinchongc_00", "bone": "ciji", "blend": "additive"}, {"name": "yzzl_attack_d/yzzl_attack_d_00018", "bone": "daztx2", "blend": "additive"}, {"name": "light", "bone": "biglight", "color": "ffffff84"}, {"name": "hit", "bone": "qian_zj_all", "attachment": "hit"}, {"name": "blood_bar", "bone": "qian_zj_all", "attachment": "blood_bar"}, {"name": "skill1_to", "bone": "root", "attachment": "skill1_to"}], "skins": [{"name": "default", "attachments": {"img1": {"img1": {"type": "mesh", "uvs": [0.145, 0.17382, 0.54606, 0.16219, 0.53444, 0.69694, 0.13338, 0.67951], "triangles": [2, 0, 1, 3, 0, 2], "vertices": [-9.26, 12.87, 9.99, 13.43, 9.43, -12.24, -9.82, -11.4], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 48}}, "ef1/adg3": {"ef1/da1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}}, "ef1/adg4": {"ef1/da1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}, "ef1/da6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60, -60, -60, -60, -60, 60, 60, 60], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 120}}, "texiao/skill1/guanci10": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.76, -20.78, -151.24, -21.13, -151.36, 30.87, 4.64, 31.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci11": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.61, -20.28, -151.39, -21.63, -151.84, 30.36, 4.15, 31.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci12": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.26, -21.56, -150.73, -20.35, -150.33, 31.65, 5.67, 30.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "hit": {"hit": {"type": "point", "x": 38.3, "y": 124.64}}, "texiao/skill1/guanquan3": {"texiao/skill1/guanquan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.5, -48.5, -58.5, -48.5, -58.5, 48.5, 58.5, 48.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 97}}, "light": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [36, -36, -36, -36, -36, 36, 36, 36], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 72}}, "skill1_to": {"skill1_to": {"type": "point", "x": 450.85, "y": 115.85}}, "texiao/skill1/guanquan": {"texiao/skill1/guanquan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.5, -48.5, -58.5, -48.5, -58.5, 48.5, 58.5, 48.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 97}}, "line_orange7": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_fire": {"line_fire": {"type": "mesh", "uvs": [0.95689, 0.26138, 0.95709, 0.41856, 0.95729, 0.58384, 0.95747, 0.7211, 0.83854, 0.82762, 0.14677, 0.8275, 0.03238, 0.72632, 0.03243, 0.5801, 0.03248, 0.40924, 0.03253, 0.25609, 0.13497, 0.1547, 0.8471, 0.15458], "triangles": [11, 0, 1, 8, 10, 11, 8, 9, 10, 11, 1, 8, 7, 8, 1, 7, 1, 2, 7, 5, 6, 7, 4, 5, 4, 7, 2, 3, 4, 2], "vertices": [403.98, 45.8, 397.61, 20.18, 402.13, -40.32, 405.87, -63.07, 387.27, -84.23, -312.14, -147.02, -335.54, -133.61, -339.48, -109.36, -338.72, 124.31, -332.47, 149.27, -310.15, 161.24, 388.84, 68.08], "hull": 12, "edges": [0, 22, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 16, 18, 0, 2, 16, 2, 12, 14, 14, 16, 2, 4, 4, 6, 14, 4], "width": 183, "height": 168}}, "texiao/skill1/guanman1": {"texiao/skill1/guanman001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [61.5, -58, -61.5, -58, -61.5, 58, 61.5, 58], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 116}}, "texiao/skill1/guanci4": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.73, -20.88, -151.27, -21.03, -151.32, 30.97, 4.68, 31.12], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci5": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.31, -21.18, -151.69, -20.73, -151.54, 31.27, 4.46, 30.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "line_orange14": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [208.1, -30.78, -53.69, -167.09, -53.82, 141.14, 206.9, 29.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "texiao/skill1/guanci7": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.61, -20.28, -151.39, -21.63, -151.84, 30.36, 4.15, 31.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci8": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.73, -20.88, -151.27, -21.03, -151.32, 30.97, 4.68, 31.12], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci9": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.31, -21.18, -151.69, -20.73, -151.54, 31.27, 4.46, 30.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci3": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.95, -21.18, -151.05, -20.73, -150.9, 31.27, 5.1, 30.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "yzzl_attack_d/yzzl_attack_d_00018": {"yzzl_attack_d/yzzl_attack_d_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [323, -206, -323, -206, -323, 207, 323, 207], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 161, "height": 103}, "yzzl_attack_d/yzzl_attack_d_00020": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [323, -206, -323, -206, -323, 207, 323, 207], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 82}, "yzzl_attack_d/yzzl_attack_d_00022": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [323, -206, -323, -206, -323, 207, 323, 207], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 82}, "yzzl_attack_d/yzzl_attack_d_00024": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [323, -206, -323, -206, -323, 207, 323, 207], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 82}, "yzzl_attack_d/yzzl_attack_d_00026": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [323, -206, -323, -206, -323, 207, 323, 207], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 82}, "yzzl_attack_d/yzzl_attack_d_00028": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [323, -206, -323, -206, -323, 207, 323, 207], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 82}, "yzzl_attack_d/yzzl_attack_d_00030": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [323, -206, -323, -206, -323, 207, 323, 207], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 82}}, "ef1/dd_4": {"ef1/dd_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [120.08, -120.08, -120.08, -120.08, -120.08, 120.08, 120.08, 120.08], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 50, "height": 50}}, "baozhaguang/1_0": {"baozhaguang/1_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}}, "line_orange10": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange11": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange12": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [208.1, -30.78, -53.69, -167.09, -53.82, 141.14, 206.9, 29.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange13": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [181.41, -36.1, -51.04, -48.87, -50.37, 171.1, 180.78, 68.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "baozhaguang/1_00000": {"baozhaguang/1_00000": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00008": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}, "baozhaguang/1_00018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [106, -62, -106, -62, -106, 62, 106, 62], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 62}}, "img": {"img": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.8, 3.97, -138.2, 3.97, -138.2, 368.97, 182.8, 368.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 243, "height": 266}}, "ef1/adg": {"ef1/adg": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -71, -75, -71, -75, 72, 75, 72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 143}, "ef1/adg2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -71, -75, -71, -75, 72, 75, 72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 143}, "ef1/adg3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [75, -71, -75, -71, -75, 72, 75, 72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 143}}, "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00": {"dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100.24, -25.61, -374.76, -25.61, -374.76, 328.39, 100.24, 328.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 106}, "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100.24, -25.61, -374.76, -25.61, -374.76, 328.39, 100.24, 328.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 106}, "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100.24, -25.61, -374.76, -25.61, -374.76, 328.39, 100.24, 328.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 106}, "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100.24, -25.61, -374.76, -25.61, -374.76, 328.39, 100.24, 328.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 106}, "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [100.24, -25.61, -374.76, -25.61, -374.76, 328.39, 100.24, 328.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 142, "height": 106}}, "ef1/baoh_1": {"ef1/baoh_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "blood_bar": {"blood_bar": {"type": "point", "x": 29.26, "y": 247.64}}, "img2": {"img01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [182.8, 3.97, -138.2, 3.97, -138.2, 368.97, 182.8, 368.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 243, "height": 266}}, "texiao/skill1/guanci2": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.26, -21.56, -150.73, -20.35, -150.33, 31.65, 5.67, 30.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanquan2": {"texiao/skill1/guanquan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.5, -48.5, -58.5, -48.5, -58.5, 48.5, 58.5, 48.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 97}}, "ci/qiliu00": {"ci/qiliu00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -50, -112, -50, -112, 50, 113, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 100}, "ci/qiliu02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -50, -112, -50, -112, 50, 113, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 100}, "ci/qiliu04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -50, -112, -50, -112, 50, 113, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 100}, "ci/qiliu06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -50, -112, -50, -112, 50, 113, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 100}, "ci/qiliu08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -50, -112, -50, -112, 50, 113, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 100}, "ci/qiliu10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [113, -50, -112, -50, -112, 50, 113, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 225, "height": 100}}, "texiao/skill1/guanquan4": {"texiao/skill1/guanquan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.5, -48.5, -58.5, -48.5, -58.5, 48.5, 58.5, 48.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 97}}, "light2": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [72, -72, -72, -72, -72, 72, 72, 72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 72}}, "light3": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [47.33, -30.34, -15.23, -30.34, -15.23, 32.22, 47.33, 32.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 72}}, "texiao/skill1/guanci001": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.26, -21.56, -150.73, -20.35, -150.33, 31.65, 5.67, 30.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanman001": {"texiao/skill1/guanman001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [61.5, -58, -61.5, -58, -61.5, 58, 61.5, 58], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 116}}, "texiao/skill1/guanci6": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.76, -20.78, -151.24, -21.13, -151.36, 30.87, 4.64, 31.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_00": {"dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [291, -137, -290, -137, -290, 138, 291, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 137}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [291, -137, -290, -137, -290, 138, 291, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 137}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [291, -137, -290, -137, -290, 138, 291, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 137}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [291, -137, -290, -137, -290, 138, 291, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 137}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [291, -137, -290, -137, -290, 138, 291, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 137}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [291, -137, -290, -137, -290, 138, 291, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 137}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [291, -137, -290, -137, -290, 138, 291, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 137}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [291, -137, -290, -137, -290, 138, 291, 138], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 290, "height": 137}}, "ef1/baoh_01": {"ef1/baoh_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}, "ef1/baoh_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-50.5, 49.5, 49.5, 50.5, 50.5, -49.5, -49.5, -50.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "dazhao1/dz1/jian_skill3_3_dg1_53": {"dazhao1/dz1/jian_skill3_3_dg1_53": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_55": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_57": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_59": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_61": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_63": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_65": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_67": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}}, "dazhao1/dz1/jian_skill3_3_dg1_54": {"dazhao1/dz1/jian_skill3_3_dg1_53": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_55": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_57": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_59": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_61": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_63": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_65": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}, "dazhao1/dz1/jian_skill3_3_dg1_67": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [431, -203, -430, -203, -430, 204, 431, 204], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 206, "height": 97}}, "texiao/skill1/guanci1": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.95, -21.18, -151.05, -20.73, -150.9, 31.27, 5.1, 30.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "line_orange2": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange3": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "skill2_xinchongci/skill2_xinchongc_00": {"skill2_xinchongci/skill2_xinchongc_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250, -100, -250, -100, -250, 100, 250, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 250, "height": 100}, "skill2_xinchongci/skill2_xinchongc_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250, -100, -250, -100, -250, 100, 250, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 250, "height": 100}, "skill2_xinchongci/skill2_xinchongc_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250, -100, -250, -100, -250, 100, 250, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 250, "height": 100}, "skill2_xinchongci/skill2_xinchongc_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250, -100, -250, -100, -250, 100, 250, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 250, "height": 100}, "skill2_xinchongci/skill2_xinchongc_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [250, -100, -250, -100, -250, 100, 250, 100], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 250, "height": 100}}, "line_orange5": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange6": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "dazhao1/dz2/nu_skill_3_xuli_23": {"dazhao1/dz2/nu_skill_3_xuli_23": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_24": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_26": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_28": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_30": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_32": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_34": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_36": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_38": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}, "dazhao1/dz2/nu_skill_3_xuli_40": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [174, -169, -174, -169, -174, 170, 174, 170], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 174, "height": 169}}}}], "events": {"atk": {}}, "animations": {"ultimate01": {"slots": {"texiao/skill1/guanci5": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanquan2": {"color": [{"time": 0.0333, "color": "006fff00"}, {"time": 0.1667, "color": "fff200ff"}, {"time": 0.4, "color": "fff20000"}], "attachment": [{"time": 0.1667, "name": "texiao/skill1/guanquan"}, {"time": 0.4, "name": null}]}, "texiao/skill1/guanci6": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "texiao/skill1/guanci12": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00": {"attachment": [{"time": 0.8333, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00"}, {"time": 0.8762, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_01"}, {"time": 0.9191, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_03"}, {"time": 0.9619, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_05"}, {"time": 0.9962, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_07"}, {"time": 1.0306, "name": null}, {"time": 1.6667, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00"}, {"time": 1.7, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_01"}, {"time": 1.7333, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_03"}, {"time": 1.7667, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_05"}, {"time": 1.8, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_07"}, {"time": 1.8333, "name": null}, {"time": 2.1667, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00"}, {"time": 2.2, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_01"}, {"time": 2.2333, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_03"}, {"time": 2.2667, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_05"}, {"time": 2.3, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_07"}, {"time": 2.3333, "name": null}]}, "texiao/skill1/guanquan3": {"color": [{"time": 0.0333, "color": "0070ffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanquan"}, {"time": 0.2333, "name": null}]}, "ef1/adg3": {"attachment": [{"time": 2.7333, "name": "ef1/da1"}, {"time": 2.7667, "name": "ef1/da2"}, {"time": 2.8, "name": "ef1/da3"}, {"time": 2.8333, "name": "ef1/da4"}, {"time": 2.8667, "name": "ef1/da5"}, {"time": 2.9, "name": "ef1/da6"}, {"time": 2.9333, "name": null}]}, "ef1/baoh_01": {"attachment": [{"time": 1.4, "name": "ef1/baoh_01"}, {"time": 1.4333, "name": "ef1/baoh_03"}, {"time": 1.4667, "name": "ef1/baoh_05"}, {"time": 1.5, "name": "ef1/baoh_07"}, {"time": 1.5333, "name": "ef1/baoh_09"}, {"time": 1.5667, "name": "ef1/baoh_01"}, {"time": 1.6, "name": "ef1/baoh_02"}, {"time": 1.6333, "name": "ef1/baoh_03"}, {"time": 1.6667, "name": "ef1/baoh_04"}, {"time": 1.7, "name": "ef1/baoh_01"}, {"time": 1.7333, "name": "ef1/baoh_02"}, {"time": 1.7667, "name": "ef1/baoh_03"}, {"time": 1.8, "name": "ef1/baoh_04"}, {"time": 1.8333, "name": "ef1/baoh_05"}, {"time": 1.8667, "name": "ef1/baoh_06"}, {"time": 1.9, "name": "ef1/baoh_01"}, {"time": 1.9333, "name": "ef1/baoh_03"}, {"time": 1.9667, "name": "ef1/baoh_05"}, {"time": 2, "name": "ef1/baoh_07"}, {"time": 2.0333, "name": "ef1/baoh_09"}, {"time": 2.0667, "name": "ef1/baoh_01"}, {"time": 2.1, "name": "ef1/baoh_02"}, {"time": 2.1333, "name": "ef1/baoh_03"}, {"time": 2.1667, "name": "ef1/baoh_04"}, {"time": 2.2, "name": "ef1/baoh_01"}, {"time": 2.2333, "name": "ef1/baoh_02"}, {"time": 2.2667, "name": "ef1/baoh_03"}, {"time": 2.3, "name": "ef1/baoh_01"}, {"time": 2.3333, "name": "ef1/baoh_03"}, {"time": 2.3667, "name": "ef1/baoh_05"}, {"time": 2.4, "name": "ef1/baoh_07"}, {"time": 2.4333, "name": "ef1/baoh_09"}, {"time": 2.4667, "name": "ef1/baoh_01"}, {"time": 2.5, "name": "ef1/baoh_02"}, {"time": 2.5333, "name": "ef1/baoh_03"}, {"time": 2.5667, "name": "ef1/baoh_04"}, {"time": 2.6, "name": null}, {"time": 2.8, "name": "ef1/baoh_01"}, {"time": 2.8333, "name": "ef1/baoh_03"}, {"time": 2.8667, "name": "ef1/baoh_05"}, {"time": 2.9, "name": "ef1/baoh_07"}, {"time": 2.9333, "name": "ef1/baoh_09"}, {"time": 2.9667, "name": "ef1/baoh_01"}, {"time": 3, "name": "ef1/baoh_02"}, {"time": 3.0333, "name": "ef1/baoh_03"}, {"time": 3.0667, "name": "ef1/baoh_04"}, {"time": 3.1, "name": "ef1/baoh_01"}, {"time": 3.1333, "name": "ef1/baoh_02"}, {"time": 3.1667, "name": "ef1/baoh_03"}, {"time": 3.2, "name": null}]}, "texiao/skill1/guanman1": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanman001"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanquan": {"color": [{"time": 0.0333, "color": "fff200ff"}, {"time": 0.2333, "color": "fff20000"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanquan"}, {"time": 0.2333, "name": null}]}, "texiao/skill1/guanci1": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "line_fire": {"attachment": [{"name": "line_fire"}, {"time": 0.6667, "name": null}]}, "light": {"color": [{"time": 0.9, "color": "ffffff84"}, {"time": 0.9867, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffb8"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffff90", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffb8"}, {"time": 1.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "color": "ffffffb8"}, {"time": 1.6333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.7, "color": "ffffffb8"}, {"time": 1.8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "color": "ffffff90", "curve": 0.25, "c3": 0.75}, {"time": 1.9, "color": "ffffffb8"}, {"time": 1.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "color": "ffffffb8"}, {"time": 2.1333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.2, "color": "ffffffb8"}, {"time": 2.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3, "color": "ffffffb8"}, {"time": 2.3667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffffb8"}, {"time": 2.8, "color": "ffffff00"}], "attachment": [{"time": 0.9, "name": "light"}, {"time": 0.9867, "name": null}, {"time": 1.2, "name": "light"}, {"time": 1.2667, "name": null}, {"time": 1.4, "name": "light"}, {"time": 1.4667, "name": null}, {"time": 1.5667, "name": "light"}, {"time": 1.6333, "name": null}, {"time": 1.7, "name": "light"}, {"time": 1.8, "name": null}, {"time": 1.9, "name": "light"}, {"time": 1.9667, "name": null}, {"time": 2.0667, "name": "light"}, {"time": 2.1333, "name": null}, {"time": 2.2, "name": "light"}, {"time": 2.2667, "name": null}, {"time": 2.3, "name": "light"}, {"time": 2.3667, "name": null}, {"time": 2.7333, "name": "light"}, {"time": 2.8, "name": null}]}, "img1": {"color": [{"color": "00000000", "curve": 0.472, "c3": 0.48}, {"time": 0.1, "color": "0000008b", "curve": "stepped"}, {"time": 0.5333, "color": "0000008b", "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "color": "00000000"}], "attachment": [{"name": "img1"}]}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_00": {"attachment": [{"time": 0.9333, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_00"}, {"time": 0.9667, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_02"}, {"time": 1, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_04"}, {"time": 1.0333, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_08"}, {"time": 1.0667, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_10"}, {"time": 1.1, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_12"}, {"time": 1.1333, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_14"}, {"time": 1.1667, "name": null}]}, "texiao/skill1/guanci7": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "img": {"attachment": [{"name": "img"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanman001": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanman001"}, {"time": 0.6667, "name": null}]}, "line_orange12": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "line_orange13": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "line_orange14": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "line_orange7": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "dazhao1/dz2/nu_skill_3_xuli_23": {"attachment": [{"time": 2.5, "name": "dazhao1/dz2/nu_skill_3_xuli_24"}, {"time": 2.5333, "name": "dazhao1/dz2/nu_skill_3_xuli_26"}, {"time": 2.5667, "name": "dazhao1/dz2/nu_skill_3_xuli_28"}, {"time": 2.6, "name": "dazhao1/dz2/nu_skill_3_xuli_30"}, {"time": 2.6333, "name": "dazhao1/dz2/nu_skill_3_xuli_32"}, {"time": 2.6667, "name": "dazhao1/dz2/nu_skill_3_xuli_34"}, {"time": 2.7, "name": "dazhao1/dz2/nu_skill_3_xuli_38"}, {"time": 2.7333, "name": null}]}, "line_orange11": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "line_orange6": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci3": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "line_orange3": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci9": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6667, "name": null}]}, "line_orange2": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci2": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "yzzl_attack_d/yzzl_attack_d_00018": {"attachment": [{"time": 0.8667, "name": "yzzl_attack_d/yzzl_attack_d_00018"}, {"time": 0.9, "name": "yzzl_attack_d/yzzl_attack_d_00020"}, {"time": 0.9333, "name": "yzzl_attack_d/yzzl_attack_d_00022"}, {"time": 0.96, "name": null}, {"time": 2.7667, "name": "yzzl_attack_d/yzzl_attack_d_00018"}, {"time": 2.8, "name": "yzzl_attack_d/yzzl_attack_d_00020"}, {"time": 2.8333, "name": "yzzl_attack_d/yzzl_attack_d_00022"}, {"time": 2.8667, "name": "yzzl_attack_d/yzzl_attack_d_00024"}, {"time": 2.9, "name": "yzzl_attack_d/yzzl_attack_d_00030"}, {"time": 2.9333, "name": null}]}, "texiao/skill1/guanci10": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "texiao/skill1/guanci11": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "skill2_xinchongci/skill2_xinchongc_00": {"attachment": [{"time": 1.2, "name": "skill2_xinchongci/skill2_xinchongc_00"}, {"time": 1.2333, "name": "skill2_xinchongci/skill2_xinchongc_01"}, {"time": 1.2667, "name": "skill2_xinchongci/skill2_xinchongc_02"}, {"time": 1.3, "name": "skill2_xinchongci/skill2_xinchongc_03"}, {"time": 1.3333, "name": "skill2_xinchongci/skill2_xinchongc_05"}, {"time": 1.3667, "name": null}]}, "texiao/skill1/guanci001": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "light2": {"attachment": [{"name": "light"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci8": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "ef1/adg4": {"attachment": [{"time": 2.7333, "name": "ef1/da1"}, {"time": 2.7667, "name": "ef1/da2"}, {"time": 2.8, "name": "ef1/da3"}, {"time": 2.8333, "name": "ef1/da4"}, {"time": 2.8667, "name": "ef1/da5"}, {"time": 2.9, "name": "ef1/da6"}, {"time": 2.9333, "name": null}]}, "ef1/adg": {"attachment": [{"time": 1.4, "name": "ef1/adg"}, {"time": 1.4333, "name": "ef1/adg2"}, {"time": 1.4667, "name": "ef1/adg3"}, {"time": 1.5, "name": null}, {"time": 1.5667, "name": "ef1/adg"}, {"time": 1.6, "name": "ef1/adg2"}, {"time": 1.6333, "name": "ef1/adg3"}, {"time": 1.6667, "name": null}, {"time": 1.9, "name": "ef1/adg"}, {"time": 1.9333, "name": "ef1/adg2"}, {"time": 1.9667, "name": "ef1/adg3"}, {"time": 2, "name": null}, {"time": 2.0667, "name": "ef1/adg"}, {"time": 2.1, "name": "ef1/adg2"}, {"time": 2.1333, "name": "ef1/adg3"}, {"time": 2.1667, "name": null}, {"time": 2.3, "name": "ef1/adg"}, {"time": 2.3333, "name": "ef1/adg2"}, {"time": 2.3667, "name": "ef1/adg3"}, {"time": 2.4, "name": null}]}, "texiao/skill1/guanci4": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "texiao/skill1/guanquan4": {"color": [{"time": 0.0333, "color": "006fff00"}, {"time": 0.1667, "color": "fff200ff"}, {"time": 0.4, "color": "fff20000"}], "attachment": [{"time": 0.1667, "name": "texiao/skill1/guanquan"}, {"time": 0.4, "name": null}]}, "line_orange10": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}}, "bones": {"L_jio2": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -27.87, "y": 8.32, "curve": "stepped"}, {"time": 1.0134, "x": -27.87, "y": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -15.22, "y": 0.1, "curve": "stepped"}, {"time": 1.2333, "x": -15.22, "y": 0.1}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 28.06, "y": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": -5.59, "y": 4.15, "curve": "stepped"}, {"time": 1.7, "x": -5.59, "y": 4.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 28.06, "y": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -5.59, "y": 4.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -0.35, "curve": "stepped"}, {"time": 2.5667, "x": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 28.06, "y": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "x": -5.59, "y": 4.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}]}, "dg1": {"rotate": [{"time": 0.8667, "curve": 0.438, "c2": 0.09, "c3": 0.471}, {"time": 1.3667, "angle": 178.74, "curve": "stepped"}, {"time": 1.5667, "angle": -30.32, "curve": "stepped"}, {"time": 1.6667, "angle": -30.32, "curve": 0.35, "c2": 0.39, "c3": 0.734, "c4": 0.9}, {"time": 1.8667, "angle": 178.74, "curve": "stepped"}, {"time": 2.0667, "angle": -30.32, "curve": "stepped"}, {"time": 2.1667, "angle": -30.32, "curve": 0.35, "c2": 0.39, "c3": 0.734, "c4": 0.9}, {"time": 2.2667, "angle": 178.74}], "translate": [{"time": 0.8667, "curve": 0.438, "c2": 0.09, "c3": 0.471}, {"time": 1.4, "x": 91.3, "y": 30.21, "curve": "stepped"}, {"time": 1.5333, "x": -23.53, "y": 3.54, "curve": 0.34, "c2": 0.35, "c3": 0.735, "c4": 0.9}, {"time": 1.8667, "x": -0.59, "y": 0.09, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 1.9, "x": 91.3, "y": 30.21, "curve": "stepped"}, {"time": 2.0333, "x": -23.53, "y": 3.54, "curve": "stepped"}, {"time": 2.3, "x": 91.3, "y": 30.21, "curve": "stepped"}, {"time": 2.4333, "x": -23.53, "y": 3.54}], "scale": [{"time": 0.8667, "curve": 0.438, "c2": 0.09, "c3": 0.471}, {"time": 1.3667, "x": 1.999, "y": 1.827, "curve": "stepped"}, {"time": 1.5, "x": -0.845, "y": 1.696, "curve": 0.276, "c2": 0.11, "c3": 0.753}, {"time": 1.5667, "x": -0.875, "y": 2.348, "curve": "stepped"}, {"time": 1.6667, "x": -0.599, "y": 1.497, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 1.8667, "x": 1.999, "y": 1.827, "curve": "stepped"}, {"time": 2, "x": -0.845, "y": 1.696, "curve": 0.276, "c2": 0.11, "c3": 0.753}, {"time": 2.0667, "x": -0.875, "y": 2.348, "curve": "stepped"}, {"time": 2.1667, "x": -0.599, "y": 1.497, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.2667, "x": 1.999, "y": 1.827, "curve": "stepped"}, {"time": 2.4, "x": -0.845, "y": 1.696, "curve": 0.315, "c2": 0.22, "c3": 0.65, "c4": 0.56}, {"time": 2.4333, "x": -0.846, "y": 1.73}], "shear": [{"time": 1.5, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.5667, "x": 18.71, "y": 78.87, "curve": "stepped"}, {"time": 1.6667, "x": 18.71, "y": 78.87, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 1.8667, "curve": "stepped"}, {"time": 2, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 2.0667, "x": 18.71, "y": 78.87, "curve": "stepped"}, {"time": 2.1667, "x": 18.71, "y": 78.87, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.2667, "curve": "stepped"}, {"time": 2.4, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 2.4333, "x": 0.24, "y": 1}]}, "dg3": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -178.08, "curve": 0.362, "c2": 0.44, "c3": 0.73, "c4": 0.9}, {"time": 2.7667, "angle": 167.92}], "scale": [{"time": 2.6667, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.7333, "y": 3.773}], "shear": [{"time": 2.6667, "curve": 0.355, "c2": 0.41, "c3": 0.733, "c4": 0.9}, {"time": 2.7333, "x": 47.54}, {"time": 2.7667, "x": 17.8}]}, "qian_zj_all": {"translate": [{"time": 1.1667}, {"time": 1.2, "x": 271.79, "y": 1.84, "curve": "stepped"}, {"time": 3.4, "x": 271.79, "y": 1.84, "curve": 0.25, "c3": 0.75}, {"time": 3.4667}]}, "ciji": {"translate": [{"time": 1.2, "x": 73.08, "y": -12.53}], "scale": [{"time": 1.2, "x": 1.052, "y": 1.303}]}, "biglight": {"translate": [{"time": 1.2, "x": 112.26, "y": 32.81}]}, "dgdg": {"translate": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 0.9, "x": -250.42}, {"time": 1.0333, "x": 91.16}], "scale": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.0134, "x": 0.634, "y": 0.634, "curve": "stepped"}, {"time": 1.6, "x": 0.517, "y": 0.445}]}, "jifa": {"translate": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 0.9333, "x": -116.65, "y": 37.41}], "scale": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 0.9333, "y": 1.349}]}, "daztx2": {"rotate": [{"time": 2.7667, "angle": 30.94}], "translate": [{"time": 0.8667, "x": 26.58, "y": 1535.96}, {"time": 0.9333, "x": 359.15, "y": 1504.69}, {"time": 2.7667, "x": 277.73, "y": 1371.63}, {"time": 2.9667, "x": 614.75, "y": 1524.23}], "scale": [{"time": 0.8667, "x": -1.232, "y": 2.09}, {"time": 2.7667, "x": -1.031, "y": 1.627}, {"time": 2.9667, "x": -1.684, "y": 2.659}]}, "tx2": {"rotate": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.5667, "angle": 38.43, "curve": "stepped"}, {"time": 1.7, "angle": -1.61, "curve": "stepped"}, {"time": 2.0667, "angle": 38.43, "curve": "stepped"}, {"time": 2.2, "angle": -1.61, "curve": "stepped"}, {"time": 2.4667, "angle": 38.43, "curve": "stepped"}, {"time": 2.6, "angle": -1.61, "curve": "stepped"}, {"time": 2.9667, "angle": 38.43, "curve": "stepped"}, {"time": 3.1, "angle": -1.61}], "translate": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.4, "x": 25.09, "y": -2.94, "curve": "stepped"}, {"time": 1.5667, "x": 16.81, "y": 4.27, "curve": "stepped"}, {"time": 1.7, "x": -4.25, "y": -31.02, "curve": "stepped"}, {"time": 1.9, "x": 25.09, "y": -2.94, "curve": "stepped"}, {"time": 2.0667, "x": 16.81, "y": 4.27, "curve": "stepped"}, {"time": 2.2, "x": -4.25, "y": -31.02, "curve": "stepped"}, {"time": 2.3, "x": 25.09, "y": -2.94, "curve": "stepped"}, {"time": 2.4667, "x": 16.81, "y": 4.27, "curve": "stepped"}, {"time": 2.6, "x": -4.25, "y": -31.02, "curve": "stepped"}, {"time": 2.8, "x": 25.09, "y": -2.94, "curve": "stepped"}, {"time": 2.9667, "x": 16.81, "y": 4.27, "curve": "stepped"}, {"time": 3.1, "x": -4.25, "y": -31.02}], "scale": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.4, "x": 0.938, "y": 0.938, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 0.719, "y": 0.719, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 0.513, "y": 0.513, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 0.938, "y": 0.938, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 0.719, "y": 0.719, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 0.513, "y": 0.513, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 0.938, "y": 0.938, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 0.719, "y": 0.719, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 0.513, "y": 0.513, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "x": 0.938, "y": 0.938, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 0.719, "y": 0.719, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": 0.513, "y": 0.513}]}, "dg4": {"rotate": [{"time": 2.7333, "angle": -88.35}], "translate": [{"time": 2.7333, "x": 63.56, "y": 44.98}, {"time": 2.7667, "x": -9, "y": 23.02}], "scale": [{"time": 2.7333, "x": 1.299, "y": 3.227}, {"time": 2.7667, "x": -0.762, "y": 3.359}], "shear": [{"time": 2.7333, "x": -159.2, "y": -10.97}, {"time": 2.7667, "x": -128.58, "y": -10.97}]}, "tx": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 52.8, "y": 61.67, "curve": 0.813, "c2": 0.16, "c3": 0.982, "c4": 0.63}, {"time": 2.9667, "x": 635.71, "y": 202.86}], "scale": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 0.52, "y": 0.52, "curve": 0.813, "c2": 0.16, "c3": 0.982, "c4": 0.63}, {"time": 2.9667, "x": 1.858, "y": 1.858}]}, "xizhen10": {"translate": [{"x": -61.76, "y": 3.49, "curve": "stepped"}, {"time": 0.1667, "x": -61.76, "y": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -114.91, "y": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -61.76, "y": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7, "x": -86.23, "y": 4.88}], "scale": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "all3": {"translate": [{"y": 4.05, "curve": "stepped"}, {"time": 0.1667, "y": 4.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.472, "c3": 0.48}, {"time": 0.3333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7, "y": 4.05}]}, "xizhen5": {"translate": [{"x": -61.57, "y": -5.92, "curve": "stepped"}, {"time": 0.1667, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333, "curve": 0.472, "c3": 0.48}, {"time": 0.6, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6333, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7, "x": -61.57, "y": -5.92}]}, "xizhen": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "xizhen12": {"translate": [{"x": -14.15, "y": -1.36, "curve": "stepped"}, {"time": 0.1667, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.2, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.4333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5667, "curve": 0.445, "c3": 0.744, "c4": 0.43}, {"time": 0.6, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.6333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667}], "scale": [{"x": 0.944, "curve": "stepped"}, {"time": 0.1667, "x": 0.944, "curve": 0.377, "c2": 0.27, "c3": 0.685, "c4": 0.64}, {"time": 0.2, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.2333, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.3333, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.3667, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.4333, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.4667, "x": 0.75, "curve": "stepped"}, {"time": 0.5333, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.6, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.6333, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.6667, "curve": 0.418, "c2": 0.01, "c3": 0.733, "c4": 0.4}, {"time": 0.7, "x": 0.944}]}, "all2": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "xizhen13": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -63.65, "y": 8.2, "curve": "stepped"}, {"time": 0.6, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -63.65, "y": 8.2}]}, "xizhen6": {"translate": [{"x": -61.57, "y": -5.92, "curve": "stepped"}, {"time": 0.1667, "x": -61.57, "y": -5.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -61.57, "y": -5.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7, "x": -85.97, "y": -8.26}]}, "xizhen9": {"translate": [{"x": -61.83, "y": -1.63, "curve": "stepped"}, {"time": 0.1667, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333, "curve": 0.472, "c3": 0.48}, {"time": 0.6, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6333, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7, "x": -61.83, "y": -1.63}]}, "xizhen2": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.4333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5667, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7}]}, "xizhen11": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -63.65, "y": 8.2, "curve": "stepped"}, {"time": 0.6, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -63.65, "y": 8.2}]}, "all": {"translate": [{"x": 41.63, "curve": "stepped"}, {"time": 0.1667, "x": 41.63, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": 647.52}]}, "bone36": {"translate": [{"time": 0.1667, "curve": 0.472, "c3": 0.172, "c4": 0.89}, {"time": 0.4667, "x": 590.32}]}, "guanci001_add13": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "guanci001_add": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "xuli_q": {"translate": [{"time": 0.0333, "x": -8.98, "y": 60.23}], "scale": [{"time": 0.0333, "x": 0.631, "y": 0.631}]}, "guanci001_add9": {"rotate": [{"time": 0.0333, "angle": -98.24}, {"time": 0.1667, "angle": -179.34}, {"time": 0.5333, "angle": 166.16}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.013}, {"time": 0.1667, "x": 1.989, "y": 1.683}, {"time": 0.5333, "x": 1.97, "y": 0.165}]}, "guanman001": {"rotate": [{"time": 0.0333}, {"time": 0.4, "angle": -69.64}, {"time": 0.7, "angle": -133.93}], "translate": [{"time": 0.0333, "x": 36.42, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.695, "y": 0.695}]}, "guanman1": {"rotate": [{"time": 0.0333}, {"time": 0.4, "angle": -69.64}, {"time": 0.7, "angle": -133.93}], "translate": [{"time": 0.0333, "x": 36.42, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.695, "y": 0.695}]}, "guanci001_add3": {"rotate": [{"time": 0.0333, "angle": -98.24}, {"time": 0.1667, "angle": -179.34}, {"time": 0.5333, "angle": 166.16}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.013}, {"time": 0.1667, "x": 1.989, "y": 1.683}, {"time": 0.5333, "x": 1.97, "y": 0.165}]}, "guanquan4": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94, "curve": "stepped"}, {"time": 0.1667, "x": 0.94, "y": 0.94}, {"time": 0.2333, "x": 1.266, "y": 1.266}, {"time": 0.4, "x": 1.35, "y": 1.35}]}, "guanci001_add7": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "guanquan": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94}, {"time": 0.1, "x": 1.266, "y": 1.266}, {"time": 0.2333, "x": 1.35, "y": 1.35}]}, "guanquan2": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94, "curve": "stepped"}, {"time": 0.1667, "x": 0.94, "y": 0.94}, {"time": 0.2333, "x": 1.266, "y": 1.266}, {"time": 0.4, "x": 1.35, "y": 1.35}]}, "guanci001_add2": {"rotate": [{"time": 0.0333, "angle": 23.4}, {"time": 0.1667, "angle": -52.87, "curve": "stepped"}, {"time": 0.2333, "angle": -52.87, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -75.64}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 2.001, "y": 2.696}, {"time": 0.1667, "x": 1.997, "y": 2.052, "curve": "stepped"}, {"time": 0.2333, "x": 1.997, "y": 2.052, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 1.971, "y": 0.181}]}, "guanci001_add8": {"rotate": [{"time": 0.0333, "angle": 23.4}, {"time": 0.1667, "angle": -52.87, "curve": "stepped"}, {"time": 0.2333, "angle": -52.87, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -75.64}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 2.001, "y": 2.696}, {"time": 0.1667, "x": 1.997, "y": 2.052, "curve": "stepped"}, {"time": 0.2333, "x": 1.997, "y": 2.052, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 1.971, "y": 0.181}]}, "guanquan3": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94}, {"time": 0.1, "x": 1.266, "y": 1.266}, {"time": 0.2333, "x": 1.35, "y": 1.35}]}, "guanci001_add5": {"rotate": [{"time": 0.1, "angle": 35.49}, {"time": 0.2333, "angle": -30.49}, {"time": 0.6, "angle": -54.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.167, "y": 1.427}, {"time": 0.2333, "x": 0.984, "y": 0.875}, {"time": 0.6, "x": 0.978, "y": 0.112}]}, "guanci001_add12": {"rotate": [{"time": 0.1, "angle": -83.31}, {"time": 0.2333, "angle": -152.37}, {"time": 0.6, "angle": -168.29}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.025, "y": 1.253}, {"time": 0.2333, "x": 1.141, "y": 1.015}, {"time": 0.6, "x": 0.999, "y": 0.238}]}, "guanci001_add6": {"rotate": [{"time": 0.1, "angle": -83.31}, {"time": 0.2333, "angle": -152.37}, {"time": 0.6, "angle": -168.29}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.025, "y": 1.253}, {"time": 0.2333, "x": 1.141, "y": 1.015}, {"time": 0.6, "x": 0.999, "y": 0.238}]}, "guanci001_add4": {"rotate": [{"time": 0.1, "angle": 148.2}, {"time": 0.2333, "angle": 75.75}, {"time": 0.7, "angle": 66.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.173, "y": 1.434}, {"time": 0.2333, "x": 0.998, "y": 0.907}, {"time": 0.7, "x": 0.969, "y": 0.111}]}, "guanci001_add11": {"rotate": [{"time": 0.1, "angle": 35.49}, {"time": 0.2333, "angle": -30.49}, {"time": 0.6, "angle": -54.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.167, "y": 1.427}, {"time": 0.2333, "x": 0.984, "y": 0.875}, {"time": 0.6, "x": 0.978, "y": 0.112}]}, "guanci001_add10": {"rotate": [{"time": 0.1, "angle": 148.2}, {"time": 0.2333, "angle": 75.75}, {"time": 0.7, "angle": 66.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.173, "y": 1.434}, {"time": 0.2333, "x": 0.998, "y": 0.907}, {"time": 0.7, "x": 0.969, "y": 0.111}]}, "daz1": {"translate": [{"time": 0.6667, "x": -6.88, "y": 5.21}]}}, "deform": {"default": {"light": {"light": [{"time": 1.3667, "vertices": [-18, 18, 18, 18, 18, -18, -18.00001, -18]}]}}}, "drawOrder": [{"time": 0.8667, "offsets": [{"slot": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00", "offset": -9}, {"slot": "yzzl_attack_d/yzzl_attack_d_00018", "offset": -50}]}, {"time": 1.4667}]}, "ultimate02": {"slots": {"texiao/skill1/guanci5": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanquan2": {"color": [{"time": 0.0333, "color": "006fff00"}, {"time": 0.1667, "color": "fff200ff"}, {"time": 0.4, "color": "fff20000"}], "attachment": [{"time": 0.1667, "name": "texiao/skill1/guanquan"}, {"time": 0.4, "name": null}]}, "line_orange7": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci12": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00": {"attachment": [{"time": 0.8333, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00"}, {"time": 0.8762, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_01"}, {"time": 0.9191, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_03"}, {"time": 0.9619, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_05"}, {"time": 0.9962, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_07"}, {"time": 1.0306, "name": null}, {"time": 1.6667, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00"}, {"time": 1.7, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_01"}, {"time": 1.7333, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_03"}, {"time": 1.7667, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_05"}, {"time": 1.8, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_07"}, {"time": 1.8333, "name": null}, {"time": 2.1667, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00"}, {"time": 2.2, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_01"}, {"time": 2.2333, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_03"}, {"time": 2.2667, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_05"}, {"time": 2.3, "name": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_07"}, {"time": 2.3333, "name": null}]}, "texiao/skill1/guanquan3": {"color": [{"time": 0.0333, "color": "0070ffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanquan"}, {"time": 0.2333, "name": null}]}, "ef1/adg3": {"attachment": [{"time": 2.7333, "name": "ef1/da1"}, {"time": 2.7667, "name": "ef1/da2"}, {"time": 2.8, "name": "ef1/da3"}, {"time": 2.8333, "name": "ef1/da4"}, {"time": 2.8667, "name": "ef1/da5"}, {"time": 2.9, "name": "ef1/da6"}, {"time": 2.9333, "name": null}]}, "ef1/baoh_01": {"attachment": [{"time": 1.4, "name": "ef1/baoh_01"}, {"time": 1.4333, "name": "ef1/baoh_03"}, {"time": 1.4667, "name": "ef1/baoh_05"}, {"time": 1.5, "name": "ef1/baoh_07"}, {"time": 1.5333, "name": "ef1/baoh_09"}, {"time": 1.5667, "name": "ef1/baoh_01"}, {"time": 1.6, "name": "ef1/baoh_02"}, {"time": 1.6333, "name": "ef1/baoh_03"}, {"time": 1.6667, "name": "ef1/baoh_04"}, {"time": 1.7, "name": "ef1/baoh_01"}, {"time": 1.7333, "name": "ef1/baoh_02"}, {"time": 1.7667, "name": "ef1/baoh_03"}, {"time": 1.8, "name": "ef1/baoh_04"}, {"time": 1.8333, "name": "ef1/baoh_05"}, {"time": 1.8667, "name": "ef1/baoh_06"}, {"time": 1.9, "name": "ef1/baoh_01"}, {"time": 1.9333, "name": "ef1/baoh_03"}, {"time": 1.9667, "name": "ef1/baoh_05"}, {"time": 2, "name": "ef1/baoh_07"}, {"time": 2.0333, "name": "ef1/baoh_09"}, {"time": 2.0667, "name": "ef1/baoh_01"}, {"time": 2.1, "name": "ef1/baoh_02"}, {"time": 2.1333, "name": "ef1/baoh_03"}, {"time": 2.1667, "name": "ef1/baoh_04"}, {"time": 2.2, "name": "ef1/baoh_01"}, {"time": 2.2333, "name": "ef1/baoh_02"}, {"time": 2.2667, "name": "ef1/baoh_03"}, {"time": 2.3, "name": "ef1/baoh_01"}, {"time": 2.3333, "name": "ef1/baoh_03"}, {"time": 2.3667, "name": "ef1/baoh_05"}, {"time": 2.4, "name": "ef1/baoh_07"}, {"time": 2.4333, "name": "ef1/baoh_09"}, {"time": 2.4667, "name": "ef1/baoh_01"}, {"time": 2.5, "name": "ef1/baoh_02"}, {"time": 2.5333, "name": "ef1/baoh_03"}, {"time": 2.5667, "name": "ef1/baoh_04"}, {"time": 2.6, "name": null}]}, "texiao/skill1/guanquan": {"color": [{"time": 0.0333, "color": "fff200ff"}, {"time": 0.2333, "color": "fff20000"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanquan"}, {"time": 0.2333, "name": null}]}, "texiao/skill1/guanman1": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanman001"}, {"time": 0.6667, "name": null}]}, "light": {"color": [{"time": 0.9, "color": "ffffff84"}, {"time": 0.9867, "color": "ffffff00"}, {"time": 1.2, "color": "ffffffb8"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffff90", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffb8"}, {"time": 1.4667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "color": "ffffffb8"}, {"time": 1.6333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.7, "color": "ffffffb8"}, {"time": 1.8, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "color": "ffffff90", "curve": 0.25, "c3": 0.75}, {"time": 1.9, "color": "ffffffb8"}, {"time": 1.9667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "color": "ffffffb8"}, {"time": 2.1333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.2, "color": "ffffffb8"}, {"time": 2.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3, "color": "ffffffb8"}, {"time": 2.3667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "color": "ffffffb8"}, {"time": 2.8, "color": "ffffff00"}], "attachment": [{"time": 0.9, "name": "light"}, {"time": 0.9867, "name": null}, {"time": 1.2, "name": "light"}, {"time": 1.2667, "name": null}, {"time": 1.4, "name": "light"}, {"time": 1.4667, "name": null}, {"time": 1.5667, "name": "light"}, {"time": 1.6333, "name": null}, {"time": 1.7, "name": "light"}, {"time": 1.8, "name": null}, {"time": 1.9, "name": "light"}, {"time": 1.9667, "name": null}, {"time": 2.0667, "name": "light"}, {"time": 2.1333, "name": null}, {"time": 2.2, "name": "light"}, {"time": 2.2667, "name": null}, {"time": 2.3, "name": "light"}, {"time": 2.3667, "name": null}, {"time": 2.7333, "name": "light"}, {"time": 2.8, "name": null}]}, "texiao/skill1/guanci11": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_00": {"attachment": [{"time": 0.9333, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_00"}, {"time": 0.9667, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_02"}, {"time": 1, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_04"}, {"time": 1.0333, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_08"}, {"time": 1.0667, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_10"}, {"time": 1.1, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_12"}, {"time": 1.1333, "name": "dazhao1/qf_nanjian_jn2_cf_jsgx/qf_nanjian_jn2_cf_jsgx_14"}, {"time": 1.1667, "name": null}]}, "img1": {"color": [{"color": "00000000", "curve": 0.472, "c3": 0.48}, {"time": 0.1, "color": "0000008b", "curve": "stepped"}, {"time": 0.5333, "color": "0000008b", "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "color": "00000000"}], "attachment": [{"name": "img1"}]}, "texiao/skill1/guanci7": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "texiao/skill1/guanci4": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "texiao/skill1/guanman001": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanman001"}, {"time": 0.6667, "name": null}]}, "line_orange12": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "line_orange13": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "line_orange14": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci9": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6667, "name": null}]}, "dazhao1/dz2/nu_skill_3_xuli_23": {"attachment": [{"time": 2.5, "name": "dazhao1/dz2/nu_skill_3_xuli_24"}, {"time": 2.5333, "name": "dazhao1/dz2/nu_skill_3_xuli_26"}, {"time": 2.5667, "name": "dazhao1/dz2/nu_skill_3_xuli_28"}, {"time": 2.6, "name": "dazhao1/dz2/nu_skill_3_xuli_30"}, {"time": 2.6333, "name": "dazhao1/dz2/nu_skill_3_xuli_32"}, {"time": 2.6667, "name": "dazhao1/dz2/nu_skill_3_xuli_34"}, {"time": 2.7, "name": "dazhao1/dz2/nu_skill_3_xuli_38"}, {"time": 2.7333, "name": null}]}, "line_orange11": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "line_orange6": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci3": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "line_orange3": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "light2": {"attachment": [{"name": "light"}, {"time": 0.6667, "name": null}]}, "line_orange2": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci2": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "yzzl_attack_d/yzzl_attack_d_00018": {"attachment": [{"time": 0.8667, "name": "yzzl_attack_d/yzzl_attack_d_00018"}, {"time": 0.9, "name": "yzzl_attack_d/yzzl_attack_d_00020"}, {"time": 0.9333, "name": "yzzl_attack_d/yzzl_attack_d_00022"}, {"time": 0.96, "name": null}, {"time": 2.7667, "name": "yzzl_attack_d/yzzl_attack_d_00018"}, {"time": 2.8, "name": "yzzl_attack_d/yzzl_attack_d_00020"}, {"time": 2.8333, "name": "yzzl_attack_d/yzzl_attack_d_00022"}, {"time": 2.8667, "name": "yzzl_attack_d/yzzl_attack_d_00024"}, {"time": 2.9, "name": "yzzl_attack_d/yzzl_attack_d_00030"}, {"time": 2.9333, "name": null}]}, "texiao/skill1/guanci6": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "texiao/skill1/guanci10": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "line_orange10": {"attachment": [{"name": "line_orange2"}, {"time": 0.6667, "name": null}]}, "skill2_xinchongci/skill2_xinchongc_00": {"attachment": [{"time": 1.2, "name": "skill2_xinchongci/skill2_xinchongc_00"}, {"time": 1.2333, "name": "skill2_xinchongci/skill2_xinchongc_01"}, {"time": 1.2667, "name": "skill2_xinchongci/skill2_xinchongc_02"}, {"time": 1.3, "name": "skill2_xinchongci/skill2_xinchongc_03"}, {"time": 1.3333, "name": "skill2_xinchongci/skill2_xinchongc_05"}, {"time": 1.3667, "name": null}]}, "img2": {"attachment": [{"name": "img01"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci001": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "line_fire": {"attachment": [{"name": "line_fire"}, {"time": 0.6667, "name": null}]}, "texiao/skill1/guanci8": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "ef1/adg4": {"attachment": [{"time": 2.7333, "name": "ef1/da1"}, {"time": 2.7667, "name": "ef1/da2"}, {"time": 2.8, "name": "ef1/da3"}, {"time": 2.8333, "name": "ef1/da4"}, {"time": 2.8667, "name": "ef1/da5"}, {"time": 2.9, "name": "ef1/da6"}, {"time": 2.9333, "name": null}]}, "ef1/adg": {"attachment": [{"time": 1.4, "name": "ef1/adg"}, {"time": 1.4333, "name": "ef1/adg2"}, {"time": 1.4667, "name": "ef1/adg3"}, {"time": 1.5, "name": null}, {"time": 1.5667, "name": "ef1/adg"}, {"time": 1.6, "name": "ef1/adg2"}, {"time": 1.6333, "name": "ef1/adg3"}, {"time": 1.6667, "name": null}, {"time": 1.9, "name": "ef1/adg"}, {"time": 1.9333, "name": "ef1/adg2"}, {"time": 1.9667, "name": "ef1/adg3"}, {"time": 2, "name": null}, {"time": 2.0667, "name": "ef1/adg"}, {"time": 2.1, "name": "ef1/adg2"}, {"time": 2.1333, "name": "ef1/adg3"}, {"time": 2.1667, "name": null}, {"time": 2.3, "name": "ef1/adg"}, {"time": 2.3333, "name": "ef1/adg2"}, {"time": 2.3667, "name": "ef1/adg3"}, {"time": 2.4, "name": null}]}, "texiao/skill1/guanci1": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "texiao/skill1/guanquan4": {"color": [{"time": 0.0333, "color": "006fff00"}, {"time": 0.1667, "color": "fff200ff"}, {"time": 0.4, "color": "fff20000"}], "attachment": [{"time": 0.1667, "name": "texiao/skill1/guanquan"}, {"time": 0.4, "name": null}]}}, "bones": {"L_jio2": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -27.87, "y": 8.32, "curve": "stepped"}, {"time": 1.0134, "x": -27.87, "y": 8.32, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": -15.22, "y": 0.1, "curve": "stepped"}, {"time": 1.2333, "x": -15.22, "y": 0.1}, {"time": 1.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 28.06, "y": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "x": -5.59, "y": 4.15, "curve": "stepped"}, {"time": 1.7, "x": -5.59, "y": 4.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "x": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.0333, "x": 28.06, "y": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 2.1333, "x": -5.59, "y": 4.15, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": -0.35, "curve": "stepped"}, {"time": 2.5667, "x": -0.35, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 28.06, "y": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "x": -5.59, "y": 4.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 3.3333}]}, "dg1": {"rotate": [{"time": 0.8667, "curve": 0.438, "c2": 0.09, "c3": 0.471}, {"time": 1.3667, "angle": 178.74, "curve": "stepped"}, {"time": 1.5667, "angle": -30.32, "curve": "stepped"}, {"time": 1.6667, "angle": -30.32, "curve": 0.35, "c2": 0.39, "c3": 0.734, "c4": 0.9}, {"time": 1.8667, "angle": 178.74, "curve": "stepped"}, {"time": 2.0667, "angle": -30.32, "curve": "stepped"}, {"time": 2.1667, "angle": -30.32, "curve": 0.35, "c2": 0.39, "c3": 0.734, "c4": 0.9}, {"time": 2.2667, "angle": 178.74}], "translate": [{"time": 0.8667, "curve": 0.438, "c2": 0.09, "c3": 0.471}, {"time": 1.4, "x": 91.3, "y": 30.21, "curve": "stepped"}, {"time": 1.5333, "x": -23.53, "y": 3.54, "curve": 0.34, "c2": 0.35, "c3": 0.735, "c4": 0.9}, {"time": 1.8667, "x": -0.59, "y": 0.09, "curve": 0.351, "c2": 0.65, "c3": 0.686}, {"time": 1.9, "x": 91.3, "y": 30.21, "curve": "stepped"}, {"time": 2.0333, "x": -23.53, "y": 3.54, "curve": "stepped"}, {"time": 2.3, "x": 91.3, "y": 30.21, "curve": "stepped"}, {"time": 2.4333, "x": -23.53, "y": 3.54}], "scale": [{"time": 0.8667, "curve": 0.438, "c2": 0.09, "c3": 0.471}, {"time": 1.3667, "x": 1.999, "y": 1.827, "curve": "stepped"}, {"time": 1.5, "x": -0.845, "y": 1.696, "curve": 0.276, "c2": 0.11, "c3": 0.753}, {"time": 1.5667, "x": -0.875, "y": 2.348, "curve": "stepped"}, {"time": 1.6667, "x": -0.599, "y": 1.497, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 1.8667, "x": 1.999, "y": 1.827, "curve": "stepped"}, {"time": 2, "x": -0.845, "y": 1.696, "curve": 0.276, "c2": 0.11, "c3": 0.753}, {"time": 2.0667, "x": -0.875, "y": 2.348, "curve": "stepped"}, {"time": 2.1667, "x": -0.599, "y": 1.497, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.2667, "x": 1.999, "y": 1.827, "curve": "stepped"}, {"time": 2.4, "x": -0.845, "y": 1.696, "curve": 0.315, "c2": 0.22, "c3": 0.65, "c4": 0.56}, {"time": 2.4333, "x": -0.846, "y": 1.73}], "shear": [{"time": 1.5, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 1.5667, "x": 18.71, "y": 78.87, "curve": "stepped"}, {"time": 1.6667, "x": 18.71, "y": 78.87, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 1.8667, "curve": "stepped"}, {"time": 2, "curve": 0.267, "c3": 0.618, "c4": 0.42}, {"time": 2.0667, "x": 18.71, "y": 78.87, "curve": "stepped"}, {"time": 2.1667, "x": 18.71, "y": 78.87, "curve": 0.347, "c2": 0.38, "c3": 0.757}, {"time": 2.2667, "curve": "stepped"}, {"time": 2.4, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 2.4333, "x": 0.24, "y": 1}]}, "dg3": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -178.08, "curve": 0.362, "c2": 0.44, "c3": 0.73, "c4": 0.9}, {"time": 2.7667, "angle": 167.92}], "scale": [{"time": 2.6667, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 2.7333, "y": 3.773}], "shear": [{"time": 2.6667, "curve": 0.355, "c2": 0.41, "c3": 0.733, "c4": 0.9}, {"time": 2.7333, "x": 47.54}, {"time": 2.7667, "x": 17.8}]}, "qian_zj_all": {"translate": [{"time": 1.1667}, {"time": 1.2, "x": 271.79, "y": 1.84, "curve": "stepped"}, {"time": 3.4, "x": 271.79, "y": 1.84, "curve": 0.25, "c3": 0.75}, {"time": 3.4667}]}, "ciji": {"translate": [{"time": 1.2, "x": 73.08, "y": -12.53}], "scale": [{"time": 1.2, "x": 1.052, "y": 1.303}]}, "biglight": {"translate": [{"time": 1.2, "x": 112.26, "y": 32.81}]}, "dgdg": {"translate": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 0.9, "x": -250.42}, {"time": 1.0333, "x": 91.16}], "scale": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.0134, "x": 0.634, "y": 0.634, "curve": "stepped"}, {"time": 1.6, "x": 0.517, "y": 0.445}]}, "jifa": {"translate": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 0.9333, "x": -116.65, "y": 37.41}], "scale": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 0.9333, "y": 1.349}]}, "daztx2": {"rotate": [{"time": 2.7667, "angle": 30.94}], "translate": [{"time": 0.8667, "x": 26.58, "y": 1535.96}, {"time": 0.9333, "x": 359.15, "y": 1504.69}, {"time": 2.7667, "x": 277.73, "y": 1371.63}, {"time": 2.9667, "x": 614.75, "y": 1524.23}], "scale": [{"time": 0.8667, "x": -1.232, "y": 2.09}, {"time": 2.7667, "x": -1.031, "y": 1.627}, {"time": 2.9667, "x": -1.684, "y": 2.659}]}, "tx2": {"rotate": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.5667, "angle": 38.43, "curve": "stepped"}, {"time": 1.7, "angle": -1.61, "curve": "stepped"}, {"time": 2.0667, "angle": 38.43, "curve": "stepped"}, {"time": 2.2, "angle": -1.61, "curve": "stepped"}, {"time": 2.4667, "angle": 38.43, "curve": "stepped"}, {"time": 2.6, "angle": -1.61, "curve": "stepped"}, {"time": 2.9667, "angle": 38.43, "curve": "stepped"}, {"time": 3.1, "angle": -1.61}], "translate": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.4, "x": 25.09, "y": -2.94, "curve": "stepped"}, {"time": 1.5667, "x": 16.81, "y": 4.27, "curve": "stepped"}, {"time": 1.7, "x": -4.25, "y": -31.02, "curve": "stepped"}, {"time": 1.9, "x": 25.09, "y": -2.94, "curve": "stepped"}, {"time": 2.0667, "x": 16.81, "y": 4.27, "curve": "stepped"}, {"time": 2.2, "x": -4.25, "y": -31.02, "curve": "stepped"}, {"time": 2.3, "x": 25.09, "y": -2.94, "curve": "stepped"}, {"time": 2.4667, "x": 16.81, "y": 4.27, "curve": "stepped"}, {"time": 2.6, "x": -4.25, "y": -31.02, "curve": "stepped"}, {"time": 2.8, "x": 25.09, "y": -2.94, "curve": "stepped"}, {"time": 2.9667, "x": 16.81, "y": 4.27, "curve": "stepped"}, {"time": 3.1, "x": -4.25, "y": -31.02}], "scale": [{"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.4, "x": 0.938, "y": 0.938, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 0.719, "y": 0.719, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "x": 0.513, "y": 0.513, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": 0.938, "y": 0.938, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "x": 0.719, "y": 0.719, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 0.513, "y": 0.513, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "x": 0.938, "y": 0.938, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 0.719, "y": 0.719, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": 0.513, "y": 0.513, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "x": 0.938, "y": 0.938, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 0.719, "y": 0.719, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": 0.513, "y": 0.513}]}, "dg4": {"rotate": [{"time": 2.7333, "angle": -88.35}], "translate": [{"time": 2.7333, "x": 63.56, "y": 44.98}, {"time": 2.7667, "x": -9, "y": 23.02}], "scale": [{"time": 2.7333, "x": 1.299, "y": 3.227}, {"time": 2.7667, "x": -0.762, "y": 3.359}], "shear": [{"time": 2.7333, "x": -159.2, "y": -10.97}, {"time": 2.7667, "x": -128.58, "y": -10.97}]}, "tx": {"translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 52.8, "y": 61.67, "curve": 0.813, "c2": 0.16, "c3": 0.982, "c4": 0.63}, {"time": 2.9667, "x": 635.71, "y": 202.86}], "scale": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 0.52, "y": 0.52, "curve": 0.813, "c2": 0.16, "c3": 0.982, "c4": 0.63}, {"time": 2.9667, "x": 1.858, "y": 1.858}]}, "xizhen10": {"translate": [{"x": -61.76, "y": 3.49, "curve": "stepped"}, {"time": 0.1667, "x": -61.76, "y": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -114.91, "y": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -61.76, "y": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7, "x": -86.23, "y": 4.88}], "scale": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "all3": {"translate": [{"y": 4.05, "curve": "stepped"}, {"time": 0.1667, "y": 4.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.472, "c3": 0.48}, {"time": 0.3333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7, "y": 4.05}]}, "xizhen5": {"translate": [{"x": -61.57, "y": -5.92, "curve": "stepped"}, {"time": 0.1667, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333, "curve": 0.472, "c3": 0.48}, {"time": 0.6, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6333, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7, "x": -61.57, "y": -5.92}]}, "xizhen": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "xizhen12": {"translate": [{"x": -14.15, "y": -1.36, "curve": "stepped"}, {"time": 0.1667, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.2, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.4333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5667, "curve": 0.445, "c3": 0.744, "c4": 0.43}, {"time": 0.6, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.6333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667}], "scale": [{"x": 0.944, "curve": "stepped"}, {"time": 0.1667, "x": 0.944, "curve": 0.377, "c2": 0.27, "c3": 0.685, "c4": 0.64}, {"time": 0.2, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.2333, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.3333, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.3667, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.4333, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.4667, "x": 0.75, "curve": "stepped"}, {"time": 0.5333, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.6, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.6333, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.6667, "curve": 0.418, "c2": 0.01, "c3": 0.733, "c4": 0.4}, {"time": 0.7, "x": 0.944}]}, "all2": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "xizhen13": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -63.65, "y": 8.2, "curve": "stepped"}, {"time": 0.6, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -63.65, "y": 8.2}]}, "xizhen6": {"translate": [{"x": -61.57, "y": -5.92, "curve": "stepped"}, {"time": 0.1667, "x": -61.57, "y": -5.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -61.57, "y": -5.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7, "x": -85.97, "y": -8.26}]}, "xizhen9": {"translate": [{"x": -61.83, "y": -1.63, "curve": "stepped"}, {"time": 0.1667, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333, "curve": 0.472, "c3": 0.48}, {"time": 0.6, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6333, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7, "x": -61.83, "y": -1.63}]}, "xizhen2": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.4333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5667, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7}]}, "xizhen11": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -63.65, "y": 8.2, "curve": "stepped"}, {"time": 0.6, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -63.65, "y": 8.2}]}, "all": {"translate": [{"x": 41.63, "curve": "stepped"}, {"time": 0.1667, "x": 41.63, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": 647.52}]}, "bone36": {"translate": [{"time": 0.1667, "curve": 0.472, "c3": 0.172, "c4": 0.89}, {"time": 0.4667, "x": 590.32}]}, "guanci001_add13": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "guanci001_add": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "xuli_q": {"translate": [{"time": 0.0333, "x": -8.98, "y": 60.23}], "scale": [{"time": 0.0333, "x": 0.631, "y": 0.631}]}, "guanci001_add9": {"rotate": [{"time": 0.0333, "angle": -98.24}, {"time": 0.1667, "angle": -179.34}, {"time": 0.5333, "angle": 166.16}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.013}, {"time": 0.1667, "x": 1.989, "y": 1.683}, {"time": 0.5333, "x": 1.97, "y": 0.165}]}, "guanman001": {"rotate": [{"time": 0.0333}, {"time": 0.4, "angle": -69.64}, {"time": 0.7, "angle": -133.93}], "translate": [{"time": 0.0333, "x": 36.42, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.695, "y": 0.695}]}, "guanman1": {"rotate": [{"time": 0.0333}, {"time": 0.4, "angle": -69.64}, {"time": 0.7, "angle": -133.93}], "translate": [{"time": 0.0333, "x": 36.42, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.695, "y": 0.695}]}, "guanci001_add3": {"rotate": [{"time": 0.0333, "angle": -98.24}, {"time": 0.1667, "angle": -179.34}, {"time": 0.5333, "angle": 166.16}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.013}, {"time": 0.1667, "x": 1.989, "y": 1.683}, {"time": 0.5333, "x": 1.97, "y": 0.165}]}, "guanquan4": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94, "curve": "stepped"}, {"time": 0.1667, "x": 0.94, "y": 0.94}, {"time": 0.2333, "x": 1.266, "y": 1.266}, {"time": 0.4, "x": 1.35, "y": 1.35}]}, "guanci001_add7": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "guanquan": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94}, {"time": 0.1, "x": 1.266, "y": 1.266}, {"time": 0.2333, "x": 1.35, "y": 1.35}]}, "guanquan2": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94, "curve": "stepped"}, {"time": 0.1667, "x": 0.94, "y": 0.94}, {"time": 0.2333, "x": 1.266, "y": 1.266}, {"time": 0.4, "x": 1.35, "y": 1.35}]}, "guanci001_add2": {"rotate": [{"time": 0.0333, "angle": 23.4}, {"time": 0.1667, "angle": -52.87, "curve": "stepped"}, {"time": 0.2333, "angle": -52.87, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -75.64}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 2.001, "y": 2.696}, {"time": 0.1667, "x": 1.997, "y": 2.052, "curve": "stepped"}, {"time": 0.2333, "x": 1.997, "y": 2.052, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 1.971, "y": 0.181}]}, "guanci001_add8": {"rotate": [{"time": 0.0333, "angle": 23.4}, {"time": 0.1667, "angle": -52.87, "curve": "stepped"}, {"time": 0.2333, "angle": -52.87, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -75.64}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 2.001, "y": 2.696}, {"time": 0.1667, "x": 1.997, "y": 2.052, "curve": "stepped"}, {"time": 0.2333, "x": 1.997, "y": 2.052, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 1.971, "y": 0.181}]}, "guanquan3": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94}, {"time": 0.1, "x": 1.266, "y": 1.266}, {"time": 0.2333, "x": 1.35, "y": 1.35}]}, "guanci001_add5": {"rotate": [{"time": 0.1, "angle": 35.49}, {"time": 0.2333, "angle": -30.49}, {"time": 0.6, "angle": -54.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.167, "y": 1.427}, {"time": 0.2333, "x": 0.984, "y": 0.875}, {"time": 0.6, "x": 0.978, "y": 0.112}]}, "guanci001_add12": {"rotate": [{"time": 0.1, "angle": -83.31}, {"time": 0.2333, "angle": -152.37}, {"time": 0.6, "angle": -168.29}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.025, "y": 1.253}, {"time": 0.2333, "x": 1.141, "y": 1.015}, {"time": 0.6, "x": 0.999, "y": 0.238}]}, "guanci001_add6": {"rotate": [{"time": 0.1, "angle": -83.31}, {"time": 0.2333, "angle": -152.37}, {"time": 0.6, "angle": -168.29}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.025, "y": 1.253}, {"time": 0.2333, "x": 1.141, "y": 1.015}, {"time": 0.6, "x": 0.999, "y": 0.238}]}, "guanci001_add4": {"rotate": [{"time": 0.1, "angle": 148.2}, {"time": 0.2333, "angle": 75.75}, {"time": 0.7, "angle": 66.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.173, "y": 1.434}, {"time": 0.2333, "x": 0.998, "y": 0.907}, {"time": 0.7, "x": 0.969, "y": 0.111}]}, "guanci001_add11": {"rotate": [{"time": 0.1, "angle": 35.49}, {"time": 0.2333, "angle": -30.49}, {"time": 0.6, "angle": -54.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.167, "y": 1.427}, {"time": 0.2333, "x": 0.984, "y": 0.875}, {"time": 0.6, "x": 0.978, "y": 0.112}]}, "guanci001_add10": {"rotate": [{"time": 0.1, "angle": 148.2}, {"time": 0.2333, "angle": 75.75}, {"time": 0.7, "angle": 66.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.173, "y": 1.434}, {"time": 0.2333, "x": 0.998, "y": 0.907}, {"time": 0.7, "x": 0.969, "y": 0.111}]}, "daz1": {"translate": [{"time": 0.6667, "x": -6.88, "y": 5.21}]}}, "deform": {"default": {"light": {"light": [{"time": 1.3667, "vertices": [-18, 18, 18, 18, 18, -18, -18.00001, -18]}]}}}, "drawOrder": [{"time": 0.8667, "offsets": [{"slot": "dazhao1/qf_nanjian_jn2_cf_dg/qf_nanjian_jn2_cf_dg_00", "offset": -9}, {"slot": "yzzl_attack_d/yzzl_attack_d_00018", "offset": -50}]}, {"time": 1.4667}]}}}