import CmdMgr from "../../mgr/CmdMgr";

export enum MainCmd {
  // 相对通用的模块
  commandCmd = 0,
  // 物品/装备 模块
  itemCmd = 1,
  // 邮件 模块
  mailCmd = 2,
  // 角色模块
  playerCmd = 3,
  // 门客模块
  heroCmd = 4,

  // 游戏关卡模块
  ChapterAction = 6,
  // 水晶
  energyFactoryCmd = 7,
  // 城池
  cityCmd = 8,
  // Goods
  goodsCmd = 10,
  // 主线任务
  MainTaskCmd = 11,
  // 挚友
  FriendCmd = 12,
  // 徒弟
  PupilCmd = 13,

  // 活动模块
  ActivityCmd = 14,

  // 圣殿模块
  ShengDianCmd = 14,

  /**繁荣比拼 */
  FrbpCmd = 14,

  /**演武场 */
  CompeteAction = 15,
  // 坐骑
  HorseCmd = 16,
  /**武魂 */
  WarriorSoulCmd = 17,
  /**幸运夺宝 */
  LuckDrawActionCmd = 18,

  /** 统计模块 */
  StatisticsCmd = 19,

  /**驿站 */
  PostActionCmd = 20,
  /**狩猎 */
  HuntAction = 21,
  /**福地 */
  FarmActionCmd = 22,
  // 战盟
  ClubMain = 23,
  /**帮助事件 */
  AssistCmd = 25,

  /**事件 */
  EventCmd = 27,

  RealmsCmd = 28,
  ChallengeCmd = 29,
}

export enum CommandSubCmd {
  /**当模块解锁时，推送解锁的模块标识 */
  intList = CmdMgr.getMergeCmd(MainCmd.commandCmd, 1),

  /**权益变化时推送权益标识 */
  intList2 = CmdMgr.getMergeCmd(MainCmd.commandCmd, 2),

  errorNotice = CmdMgr.getMergeCmd(MainCmd.commandCmd, 3),
}

export enum ItemSubCmd {
  // 使用道具
  useItem = CmdMgr.getMergeCmd(MainCmd.itemCmd, 1),
  // 道具更新消息,用于消耗更新
  updateItem = CmdMgr.getMergeCmd(MainCmd.itemCmd, 2),
  // 道具奖励消息，用于获得，并更新
  awardItem = CmdMgr.getMergeCmd(MainCmd.itemCmd, 3),
}

export enum PlayerSubCmd {
  /**获取角色信息 */
  playerInfo = CmdMgr.getMergeCmd(MainCmd.playerCmd, 1),
  /**随机生成角色名称 */
  randomNickName = CmdMgr.getMergeCmd(MainCmd.playerCmd, 2),
  /**创建角色 */
  create = CmdMgr.getMergeCmd(MainCmd.playerCmd, 3),
  /**用户下线 */
  offline = CmdMgr.getMergeCmd(MainCmd.playerCmd, 4),
  /**主动获取玩家最新的气运，触发气运更新 */
  updateEnergy = CmdMgr.getMergeCmd(MainCmd.playerCmd, 5),
  /**用户提升等级 */
  levelUp = CmdMgr.getMergeCmd(MainCmd.playerCmd, 6),
  /**领取每日宝箱 */
  dailyTreasure = CmdMgr.getMergeCmd(MainCmd.playerCmd, 7),
  /**改名 */
  rename = CmdMgr.getMergeCmd(MainCmd.playerCmd, 8),

  /**获取玩家的战斗属性和战斗力 ---- 临时接口，暂时获取服务端，后续客户端自己计算 */
  getPower = CmdMgr.getMergeCmd(MainCmd.playerCmd, 9),

  /**获取系统开启情况 */
  systemOpen = CmdMgr.getMergeCmd(MainCmd.playerCmd, 10),

  /**获取指定玩家详情 */
  getDetail = CmdMgr.getMergeCmd(MainCmd.playerCmd, 11),

  /**激活皮肤 */
  activeSkin = CmdMgr.getMergeCmd(MainCmd.playerCmd, 12),

  /**升级皮肤 (升级逻辑未实现，返回当前皮肤信息 */
  levelSkin = CmdMgr.getMergeCmd(MainCmd.playerCmd, 13),

  /**上阵任意一个皮肤|装饰  皮肤|装饰,前端需要将同一类别已穿戴的状态为false，对应的装饰chosen状态置为true */
  workSkinOrDecoration = CmdMgr.getMergeCmd(MainCmd.playerCmd, 14),

  /**上阵任意一个皮肤|装饰 */
  workAll = CmdMgr.getMergeCmd(MainCmd.playerCmd, 15),

  /**获取离线的收益的大小 */
  getOfflineEnergy = CmdMgr.getMergeCmd(MainCmd.playerCmd, 17),

  /** 更新剧情引导id */
  updateGuideId = CmdMgr.getMergeCmd(MainCmd.playerCmd, 18),

  /** 更新权益 */
  updateEquity = CmdMgr.getMergeCmd(MainCmd.playerCmd, 19),

  /**获取玩家最新的皮肤列表 */
  getAllSkin = CmdMgr.getMergeCmd(MainCmd.playerCmd, 21),

  /**获取玩家最新的称号列表 */
  getAllTitle = CmdMgr.getMergeCmd(MainCmd.playerCmd, 22),

  /**获取玩家最新的头像框列表 */
  getAllHeadFrame = CmdMgr.getMergeCmd(MainCmd.playerCmd, 23),

  /**获取玩家最新的头像框列表 */
  getGetAllHeadShow = CmdMgr.getMergeCmd(MainCmd.playerCmd, 24),

  /**获取玩家最新的头像框列表 */
  getAllBubble = CmdMgr.getMergeCmd(MainCmd.playerCmd, 25),

  // 路由: 3 - 27  --- 【获取战力榜排行】 --- 【PlayerAction:573】【powerRank】
  //   方法返回值: PlayerRankMessage
  powerRank = CmdMgr.getMergeCmd(MainCmd.playerCmd, 27),

  // 路由: 3 - 28  --- 【获取繁荣度榜排行】 --- 【PlayerAction:601】【energyRank】
  //     方法返回值: PlayerRankMessage
  energyRank = CmdMgr.getMergeCmd(MainCmd.playerCmd, 28),

  /**获取前端保存在后端的数据 */
  archive = CmdMgr.getMergeCmd(MainCmd.playerCmd, 29),

  /**前端保存要用的数据在后端 */
  updateArchive = CmdMgr.getMergeCmd(MainCmd.playerCmd, 30),

  // 路由: 3 - 31  --- 【获取排行榜点赞情况】 --- 【PlayerAction:678】【rankRewardInfo】
  //   方法参数: BoolValue
  //   方法返回值: PlayerRankBoardMessage
  rankRewardInfo = CmdMgr.getMergeCmd(MainCmd.playerCmd, 31),
  // 路由: 3 - 32  --- 【点赞排行榜获取奖励】 --- 【PlayerAction:693】【takeRankReward】
  //     方法参数: BoolValue otherServer true-跨服 false-本服 目前不支持跨服
  //     方法返回值: RewardMessage
  takeRankReward = CmdMgr.getMergeCmd(MainCmd.playerCmd, 32),

  // 路由: 3 - 51  --- 广播推送: com.feamon.proto.player.PlayVipExpResponse (当发生充值活动后会推送)
  // 路由: 3 - 52  --- 广播推送: com.feamon.proto.player.SkinAddResponse (当获得新的皮肤后推送)
  // 路由: 3 - 53  --- 广播推送: com.feamon.proto.player.HeadShowAddResponse (当获得新的头像后推送)
  // 路由: 3 - 54  --- 广播推送: com.feamon.proto.player.HeadFrameAddResponse (当获得新的头像框后推送)
  // 路由: 3 - 55  --- 广播推送: com.feamon.proto.player.TitleAddResponse (当获得新的头衔后推送)
  // 路由: 3 - 56  --- 广播推送: com.feamon.proto.player.BubbleAddResponse (当获得新的气泡框后推送)
  /**
   * 当发生充值活动后会推送
   */
  vipExpNotice = CmdMgr.getMergeCmd(MainCmd.playerCmd, 51),

  /**当获得新的皮肤后推送) */
  SkinAddResponse = CmdMgr.getMergeCmd(MainCmd.playerCmd, 52),

  /**获得新的头像后推送 */
  headAddResponse = CmdMgr.getMergeCmd(MainCmd.playerCmd, 53),

  /**获得新的头像框后推送 */
  headFarmeAddResponse = CmdMgr.getMergeCmd(MainCmd.playerCmd, 54),

  /**获得新的称号后推送 */
  TitleAddResponse = CmdMgr.getMergeCmd(MainCmd.playerCmd, 55),

  /**获得新的气泡框后推送 */
  BubbleAddResponse = CmdMgr.getMergeCmd(MainCmd.playerCmd, 56),

  /** 增加战力推送 */
  notifyPlayerBattleAttr = CmdMgr.getMergeCmd(MainCmd.playerCmd, 57),

  /**当装饰过期时，前端收到消息后，要重新拉取所有的装饰 */
  upDecorateMessage = CmdMgr.getMergeCmd(MainCmd.playerCmd, 58),

  // 展示计算各模块的战力及赚速信息
  powerInfo = CmdMgr.getMergeCmd(MainCmd.playerCmd, 102),
  /**一键升级，每次升一级 */
  testLv = CmdMgr.getMergeCmd(MainCmd.playerCmd, 103),

  /**重置角色 */
  resetRole = CmdMgr.getMergeCmd(MainCmd.playerCmd, 121),
  /**删除角色 */
  delRole = CmdMgr.getMergeCmd(MainCmd.playerCmd, 122),
}

// 武将 / 英雄
export enum HeroSubCmd {
  // 升级
  levelUp = CmdMgr.getMergeCmd(MainCmd.heroCmd, 1),
  // 连升
  levelUp10 = CmdMgr.getMergeCmd(MainCmd.heroCmd, 2),
  // 升级技能
  skillLevelUp = CmdMgr.getMergeCmd(MainCmd.heroCmd, 3),
  // 武将突破
  breakTop = CmdMgr.getMergeCmd(MainCmd.heroCmd, 4),
  // 升级印记
  upgradeImprint = CmdMgr.getMergeCmd(MainCmd.heroCmd, 5),
  // 获取所有的英雄
  getAllHero = CmdMgr.getMergeCmd(MainCmd.heroCmd, 7),
  // 获取单个英雄
  getOneHero = CmdMgr.getMergeCmd(MainCmd.heroCmd, 8),

  // 获取战将图鉴信息
  getPicture = CmdMgr.getMergeCmd(MainCmd.heroCmd, 10),
  // 激活或升级图鉴，返回对应图鉴激活或升级后的等级
  lvPicture = CmdMgr.getMergeCmd(MainCmd.heroCmd, 11),

  //
  // 路由: 4 - 52  --- 广播推送: com.feamon.proto.hero.HeroMessage (当英雄加成所绑定的城池的气运赚速系数变化时，主动推送)
  // 主动推送，英雄所绑定的城池的气运赚速系数变化情况
  notifyHeroCityEnergyRate = CmdMgr.getMergeCmd(MainCmd.heroCmd, 52),
  // 路由: 4 - 51  --- 广播推送: com.feamon.proto.hero.HeroMessage (当英雄战力变更时，主动推送)
  // 主动推送，英雄战力变更情况
  notifyHeroBattleAttr = CmdMgr.getMergeCmd(MainCmd.heroCmd, 51),

  // test
  addHero1 = CmdMgr.getMergeCmd(MainCmd.heroCmd, 101),
  // test
  heroTest = CmdMgr.getMergeCmd(MainCmd.heroCmd, 102),
}

// 关卡
export enum ChapterSubCmd {
  /**获取关卡 + 宝箱列表 */
  getChapter = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 1),

  /**通过普通关卡 */
  passChildChapter = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 2),

  /**气运鼓舞 */
  energyConsumeEncourage = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 3),

  /**道具鼓舞 */
  itemConsumeEncourage = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 4),

  /**领取一个宝箱 */
  taskOneTreasure = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 5),

  /**领取全部宝箱 */
  takeAllTreasure = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 6),

  /**通关Boss关卡 */
  passBossChapter = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 7),

  takePassBossChapterReward = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 8),

  /** 特殊关卡战斗录像 */
  passDiffBossChapter = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 9),

  /**重置关卡 --- 测试 */
  resetChaperLevel = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 101),

  /**测试本次Boss关卡的属性和战力 */
  testGetUserEncouragePower = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 102),

  /**测试获取Boss关卡鼓舞的消耗 */
  testGetUserEncourageConsume = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 103),

  /** 获取所在关卡的所有普通子关卡的气运消耗 */
  testGetNonBossEnergyConsume = CmdMgr.getMergeCmd(MainCmd.ChapterAction, 104),
}

// 水晶
export enum EnergyFactorySubCmd {
  /**获取用户的族运水晶 */
  getEnergyFactory = CmdMgr.getMergeCmd(MainCmd.energyFactoryCmd, 1),
  /**用户手动升级族运水晶 */
  upgradeEnergyFactory = CmdMgr.getMergeCmd(MainCmd.energyFactoryCmd, 2),
  /** 升级族运水晶 前端需要将领取标识置为true */
  takeAutoReward = CmdMgr.getMergeCmd(MainCmd.energyFactoryCmd, 3),
}

// 城池
export enum CitySubCmd {
  /**返回List<CityMessage> */
  CityHireWorkerListMessage = CmdMgr.getMergeCmd(MainCmd.cityCmd, 1),

  /**招募雇员1位 */
  hireOneWorker = CmdMgr.getMergeCmd(MainCmd.cityCmd, 2),
  /**招募雇员10位 */
  hireTenWorker = CmdMgr.getMergeCmd(MainCmd.cityCmd, 3),
  /**创建城池 */
  create = CmdMgr.getMergeCmd(MainCmd.cityCmd, 4),
  /**升级城池 */
  upgrade = CmdMgr.getMergeCmd(MainCmd.cityCmd, 5),
  /**汇总其他的统计信息返回给前端 */
  aggregateInfo = CmdMgr.getMergeCmd(MainCmd.cityCmd, 6),

  /**获取城池绑定的通关奖励 */
  taskChapterReward = CmdMgr.getMergeCmd(MainCmd.cityCmd, 7),

  /**领取城池成就奖励 */
  achievementReward = CmdMgr.getMergeCmd(MainCmd.cityCmd, 8),

  /**记录城池已打开遮盖迷雾 */
  openFog = CmdMgr.getMergeCmd(MainCmd.cityCmd, 9),

  /**获取已有的所有城池的信息 */
  getAllCity = CmdMgr.getMergeCmd(MainCmd.cityCmd, 10),

  /**建造额外的城池，如弟子宫和福地】 */
  createOtherCity = CmdMgr.getMergeCmd(MainCmd.cityCmd, 11),

  //领取种族装饰攒齐的奖励
  getTrimAward = CmdMgr.getMergeCmd(MainCmd.cityCmd, 12),

  /**解锁或升级三界小家建筑 */
  levelUpSmallHomeBuild = CmdMgr.getMergeCmd(MainCmd.cityCmd, 13),

  /**领取三界小家建筑升级奖励 */
  takeSmallHomeBuildLvReward = CmdMgr.getMergeCmd(MainCmd.cityCmd, 14),

  /**领取三界小家所有建筑达到某个等级的奖励 */
  takeAllCityLevelReward = CmdMgr.getMergeCmd(MainCmd.cityCmd, 15),

  /** 打开一个号召宝箱 */
  getOneBoxReward = CmdMgr.getMergeCmd(MainCmd.cityCmd, 16),

  /** 打开一个号召宝箱 */
  getAllBoxReward = CmdMgr.getMergeCmd(MainCmd.cityCmd, 17),

  /** 宝箱升级 */
  lvBoxLevel = CmdMgr.getMergeCmd(MainCmd.cityCmd, 18),

  /** 建造装饰 */
  activeDecoration = CmdMgr.getMergeCmd(MainCmd.cityCmd, 19),

  //当五族荣耀解锁新装饰推送
  getNewTrim = CmdMgr.getMergeCmd(MainCmd.cityCmd, 51),

  //当五族荣耀解锁新建筑种族ID推送
  getNewTrimBuildId = CmdMgr.getMergeCmd(MainCmd.cityCmd, 52),

  // 装饰更新推送
  subTrimUpdate = CmdMgr.getMergeCmd(MainCmd.cityCmd, 53),
}

// 物品服务接口
export enum GoodsSubCmd {
  // 获得物品
  getGoods = CmdMgr.getMergeCmd(MainCmd.goodsCmd, 1),
  // 道具更新消息,用于消耗更新
  updateGoods = CmdMgr.getMergeCmd(MainCmd.goodsCmd, 2),
  /**获取兑换情况 */
  redeemInfo = CmdMgr.getMergeCmd(MainCmd.goodsCmd, 3),
  /**兑换 */
  redeemGoods = CmdMgr.getMergeCmd(MainCmd.goodsCmd, 4),

  /** 购买成功监听 */
  orderSubscriber = CmdMgr.getMergeCmd(MainCmd.goodsCmd, 5),

  /**客户端自己添加资源 */
  addRes = CmdMgr.getMergeCmd(MainCmd.goodsCmd, 101),
}

// 主线任务
export enum MainTaskSubCmd {
  /**获取当前主线任务的情况 */
  mainTaskInfo = CmdMgr.getMergeCmd(MainCmd.MainTaskCmd, 1),
  /**完成主线任务获取下一个主线任务 */
  passMainTask = CmdMgr.getMergeCmd(MainCmd.MainTaskCmd, 2),
  /**主动推送主线任务变更情况 */
  mainTaskUpdate = CmdMgr.getMergeCmd(MainCmd.MainTaskCmd, 3),
  /** 主线任务直接完成 */
  clientPassTask = CmdMgr.getMergeCmd(MainCmd.MainTaskCmd, 4),
  /**重置主线任务进度 */
  testResetMainTask = CmdMgr.getMergeCmd(MainCmd.MainTaskCmd, 101),

  /**设置主线任务进度 */
  testSetMainTaskId = CmdMgr.getMergeCmd(MainCmd.MainTaskCmd, 102),
}

export enum FriendSubCmd {
  // 赠与礼物
  giveAGift = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 1),
  // 谈心
  chat = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 2),
  // 升级美名
  levelUpFame = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 3),
  // 提升城池技能 (洗练技能)
  improveSkill = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 4),
  // 升级挚友门客技能
  levelUpHeroSkill = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 5),
  // 获取精力信息
  getVitality = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 6),
  // 使用精力丹恢复挚友的精力
  addVitality = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 7),
  // 获取所有好友信息
  getAllFriend = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 8),
  // 获取单个挚友
  getFriend = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 14),
  //获取需要主动解锁的挚友的解锁情况
  getLabel = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 15),
  //解锁获得挚友
  unLockLabelFriend = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 16),
  // 获取图鉴领取信息(返回已经领取过奖励的图鉴ID集合)
  getPicture = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 17),
  // 获领取图鉴奖励(返回道具奖励)
  takePictureReward = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 18),
  // 测试删除仙友
  testDelFriend = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 107),

  //=======================仙友推送信息
  //玩家升级时如果挚友精力值有变更，则会主动推送
  notifyVitality = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 53),
  //游历时偶遇已拥有的挚友，提升因果值，则会主动推送
  notifyKarma = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 54),
  //获得新挚友推送
  notifyObtainFriend = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 55),
  //仙友目标进度推送
  notifyFriendProcess = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 52),
  //仙友技能增加推送
  notifySkillAdd = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 51),

  //========================游历
  /**获取游历精力信息 */
  travelVitality = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 9),
  /**使用体力丹恢复游历的精力 */
  addTravelVitality = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 10),
  /**游历 */
  travel = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 11),
  /**看广告游历 */
  videoTravel = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 12),
  /**查看相关的统计数据 */
  friendUnlockStatistics = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 13),

  /**测试 --- 重置游历精力信息 */
  testReseTravelVitality = CmdMgr.getMergeCmd(MainCmd.FriendCmd, 105),
}

export enum PupilSubCmd {
  /**获取弟子培养和出阵信息 */
  getTrain = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 1),
  /**获取指定的弟子信息 */
  getPupil = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 2),
  /**获取所有的弟子信息 */
  getAllPupil = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 3),
  /**招募新的徒弟 */
  addPupil = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 4),
  /**增加活力值 */
  addVitality = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 5),
  /**培养徒弟一次 (如果资源不足以训练一次，则所有的弟子都不修炼) */
  train = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 6),
  /**弟子上阵，返回上阵的徒弟的列表 */
  work = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 7),
  /**向全服发布弟子联姻信息 */
  pushToMarket = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 8),
  /**取消结伴的信息 如果返回False，则需要前端重新获取该弟子的信息，可能弟子已经被其他玩家联姻了 */
  cancelMarket = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 9),
  /**获取全服可以联姻弟子的分页信息 */
  marryPage = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 10),
  /**结伴 */
  marry = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 11),
  /**用于己方待结伴的对象被其他对象结伴时，会得到哪个弟子被结伴的推送信息13-52，客户端需要访问本接口更新服务器的数据，并获取弟子的最新信息 */
  marryShow = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 12),
  /**分页获取徒弟排行榜的徒弟 */
  rank = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 13),
  /**下阵 */
  leaveWork = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 14),

  /**培养徒弟十次 或 弟子出师功能  如果发现有弟子培养满了要出师且未超出限制条件，就弟子出师，每次出师一位弟子；否则有体力值的弟子培养一次】 */
  trainTen = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 15),

  /**重置徒弟模块 */
  testResetPupil = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 101),
  /**获取徒弟的战力属性列表 */
  testGetAllPupilCache = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 102),
  /**返回可以申请结伴的弟子ID列表 */
  testGetAdultAndUnMarryIdList = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 103),
  /**一次性培养弟子(只培养找到的第一个徒弟)100次(不足就按资源最大培养) */
  testTrain = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 104),

  /** 返回出师的徒弟信息 */
  adult = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 51),
  /** 用于己方已申请的待结伴的对象被其他对象结伴时，会得到哪个弟子被结伴的推送信息，客户端需要访问13-12更新服务器的数据，并获取弟子的最新信息 */
  beMarried = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 52),
  /**升级后如果触发模块更新进行推送 */
  levelUpToRefresh = CmdMgr.getMergeCmd(MainCmd.PupilCmd, 53),

  // 路由: 13 - 53  --- 广播推送: com.feamon.proto.pupil.PupilTrainMessage (升级后如果触发模块更新进行推送)
  // 路由: 13 - 52  --- 广播推送: com.feamon.proto.comm.CommLongListMessage (用于己方已申请的待结伴的对象被其他对象结伴时，会得到哪个弟子被结伴的推送信息，客户端需要访问13-12更新服务器的数据，并获取弟子的最新信息)
}

/**坐骑 */
export enum HorseSubCmd {
  /**获取用户的坐骑 */
  getHorse = CmdMgr.getMergeCmd(MainCmd.HorseCmd, 1),
  /**升级或升阶 (这里没有写判断坐骑是否已经升级到极限的情况 */
  upgradeOrStage = CmdMgr.getMergeCmd(MainCmd.HorseCmd, 2),
  /**切换坐骑 */
  toggleHorse = CmdMgr.getMergeCmd(MainCmd.HorseCmd, 3),
  /**解锁坐骑 */
  unLockHorse = CmdMgr.getMergeCmd(MainCmd.HorseCmd, 4),

  /**重置坐骑 */
  testRest = CmdMgr.getMergeCmd(MainCmd.HorseCmd, 101),
  /**后端调试：展示计算各模块的战力及赚速信息（新版）102 */
  horseInfo = CmdMgr.getMergeCmd(MainCmd.HorseCmd, 102),
}

/**武魂 */
export enum WarriorSoulSubCmd {
  /**获取用户所有武魂 */
  getAll = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 1),
  /**刷新获取新的武魂 */
  refreshSoul = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 2),
  /**刷新出武魂后,选中制定槽位武魂购买 */
  buySoul = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 3),
  /**解锁新的槽位 */
  unLockSlot = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 4),
  /**升级武魂 */
  upgrade = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 5),
  /** */
  freeSoul = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 6),
  /**选择武魂参战 */
  goWork = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 7),

  //===========================武魂图鉴===========================
  /** SoulPictureSelectRequest SoulPictureActiveResponse*/
  activePicture = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 8), //激活图鉴
  /** SoulPictureSelectRequest SoulPictureMessage*/
  refreshPictureSkill = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 9), //刷新图鉴技能
  /** SoulPictureSelectRequest SoulPictureMessage*/
  useBackUpPictureSkill = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 10), //替换图鉴技能
  /**IntValue IntValue */
  workPlan = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 11), //替换方案

  /**重置武魂 */
  testReset = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 101),
  /**模块属性计算列表 */
  testGetCache = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 102),
  // 路由: 17 - 106  --- 【测试：往用户曾经获取过的武魂集合中添加武魂】 --- 【WarriorSoulAction:370】【testAddSoulTemplateId】
  //   方法参数: LongValue
  //   方法返回值: LongValueList 曾经获取过的武魂集合
  testAddSoulTemplateId = CmdMgr.getMergeCmd(MainCmd.WarriorSoulCmd, 106),
}

export enum LuckDrawActionSubCmd {
  /**获取幸运抽奖模块记录的信息 */
  getDraw = CmdMgr.getMergeCmd(MainCmd.LuckDrawActionCmd, 1),
  /**抽奖一次 */
  oneDraw = CmdMgr.getMergeCmd(MainCmd.LuckDrawActionCmd, 2),
  /**抽奖十次 */
  tenDraw = CmdMgr.getMergeCmd(MainCmd.LuckDrawActionCmd, 3),
  /**观看视频 TODO 后期接入广告 */
  video = CmdMgr.getMergeCmd(MainCmd.LuckDrawActionCmd, 4),
  /**更新guideId */
  updateGuideId = CmdMgr.getMergeCmd(MainCmd.LuckDrawActionCmd, 5),
}

export enum StatisticsSubCmd {
  /* 测试统计事件通知 */
  testStatisticsEvent = CmdMgr.getMergeCmd(MainCmd.StatisticsCmd, 101),
}

export enum CompeteActionSubCmd {
  /**返回玩家免费刷新次数,对手信息,最近一次免费刷新的时间 */
  extraInfo = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 1),
  /**获取本服玩家排行和排名信息 */
  rankInfo = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 2),
  /**获取跨服玩家排行和排名信息(目前使用的是本服的) */
  serverRankInfo = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 3),
  /**与被挑战者对战 */
  fight = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 4),
  /**刷新对手 */
  free = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 5),
  /**获取日志 */
  getLog = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 6),
  /**切磋 */
  revenge = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 7),
  /**对战后获取积分扣减、获取对手等 */
  afterFight = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 8),
  /**切磋后库存扣减，，前端需要将日志的revenge属性设为true */
  afterRevenge = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 9),
  /**同步前后端的剩余免费刷新次数的进度 */
  syncRefreshCount = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 10),

  //暂不使用
  // /**推送排行榜的变动情况 */
  // upAzstRankList = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 51),
  // /** 推送新增加的日志*/
  // upAzstLogList = CmdMgr.getMergeCmd(MainCmd.CompeteAction, 52),
}

export enum PostActionSubCmd {
  /**获取驿站的信息 */
  postInfo = CmdMgr.getMergeCmd(MainCmd.PostActionCmd, 1),
  /**升级驿站 */
  levelUp = CmdMgr.getMergeCmd(MainCmd.PostActionCmd, 2),
  /**开始挂机 */
  deliver = CmdMgr.getMergeCmd(MainCmd.PostActionCmd, 3),
  /**加速 */
  speed = CmdMgr.getMergeCmd(MainCmd.PostActionCmd, 4),
  /**领取奖励 */
  take = CmdMgr.getMergeCmd(MainCmd.PostActionCmd, 5),

  /**测试接口 ---- 重置驿站模块  ---- 解析 PostTrainMessage*/
  testRestPost = CmdMgr.getMergeCmd(MainCmd.PostActionCmd, 101),

  /**测试接口 ---- 重置每日挂机的次数 ---- 解析 PostTrainMessage */
  testResetDailyCount = CmdMgr.getMergeCmd(MainCmd.PostActionCmd, 102),

  /**测试接口 ---- 驿站提升到最大等级  ----- 解析 PostTrainMessage */
  testUpMaxLevel = CmdMgr.getMergeCmd(MainCmd.PostActionCmd, 103),
}

export enum HuntActionSubCmd {
  /**返回狩猎灵兽的基本信息 */
  getTrain = CmdMgr.getMergeCmd(MainCmd.HuntAction, 1),

  /**获取狩猎灵兽积分的总排行榜 */
  petRank = CmdMgr.getMergeCmd(MainCmd.HuntAction, 2),

  /**开始狩猎灵兽 */
  petHunt = CmdMgr.getMergeCmd(MainCmd.HuntAction, 3),

  /**狩猎灵兽结束 */
  afterPetHunt = CmdMgr.getMergeCmd(MainCmd.HuntAction, 4),

  /**获取狩猎Boss积分的总排行榜 */
  bossRank = CmdMgr.getMergeCmd(MainCmd.HuntAction, 5),

  /**开始狩猎BOSS */
  bossHunt = CmdMgr.getMergeCmd(MainCmd.HuntAction, 6),

  /**狩猎结束 */
  afterBossHunt = CmdMgr.getMergeCmd(MainCmd.HuntAction, 7),

  /**获取狩猎洪荒出现的日志 */
  getLog = CmdMgr.getMergeCmd(MainCmd.HuntAction, 8),

  /**主动推送boss信息变化 */
  bossUpdateMessage = CmdMgr.getMergeCmd(MainCmd.HuntAction, 9),

  /**重置模块 */
  testResetHunt = CmdMgr.getMergeCmd(MainCmd.HuntAction, 101),

  /**设置洪荒出现的时间为当前一个小时内 */
  testSetStart = CmdMgr.getMergeCmd(MainCmd.HuntAction, 102),

  /**后端测试数据 */
  testCache = CmdMgr.getMergeCmd(MainCmd.HuntAction, 103),

  /**复活boss, 必须是在洪荒出现的时间内才可以调用 */
  testResetBoss = CmdMgr.getMergeCmd(MainCmd.HuntAction, 113),
}

export enum FarmActionCmd {
  /**获取福地和采摘完后奖励 */
  ownFarm = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 1),
  /**获取福地奖励 */
  takeReward = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 2),
  /**采集福地 */
  collectFarm = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 3),
  /**召回蜜蜂 */
  recall = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 4),
  /**雇佣新的蜜蜂 */
  hireBee = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 5),
  /**刷新自己的福地 */
  refresh = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 6),
  /**获取周围福地 */
  findNeighbor = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 7),
  /**获取敌对福地  */
  findEnemy = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 8),
  /**获取指定用户的福地  */
  getOtherFarm = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 9),
  /**获取日志  */
  getLog = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 10),

  /**增加关注度  */
  addAttention = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 11),

  /** 召回全部的蜜蜂  */
  recallAll = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 12),

  /** 领取基金奖励  */
  takeFundReward = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 13),

  /**推送采集情况  */
  pushUpdateFarmUserId = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 51),

  /** 基金支付成功后，推送购买的基金索引和更新后的信息 */
  SubFarmFundBuy = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 52),

  /**测试：获取某人的福地信息(未触发刷新)  */
  testOwnUnUpdateFarm = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 101),
  /**测试：获取某人的福地信息(未触发刷新)  */
  testGetFarm = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 102),
  /**测试：获取某人的所有通知信息  */
  testGetNotice = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 103),
  /**后侧测试：开奖励  */
  testOpenReward = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 104),
  /**测试：重置自身福地  */
  testResetFarmTrain = CmdMgr.getMergeCmd(MainCmd.FarmActionCmd, 110),
}
export enum ClubSubCmd {
  // 获取个人已加入的仙盟信息
  ownClub = CmdMgr.getMergeCmd(MainCmd.ClubMain, 1),
  // 创建仙盟
  createClub = CmdMgr.getMergeCmd(MainCmd.ClubMain, 2),
  // 获取所有的的仙盟(已经排序好)
  listClub = CmdMgr.getMergeCmd(MainCmd.ClubMain, 3),
  // 申请加入仙盟 0 - 成功加入,1 - 待审核, 2 - 被拒绝 如果有加入仙盟，前端需要更新ClubTrainMessage的clubId属性
  joinClub = CmdMgr.getMergeCmd(MainCmd.ClubMain, 4),
  // 获取所有的申请信息
  listApply = CmdMgr.getMergeCmd(MainCmd.ClubMain, 5),
  // 审批加入仙盟的请求,前端需要将申请记录删除
  examineApply = CmdMgr.getMergeCmd(MainCmd.ClubMain, 6),
  //修改仙盟的审核配置
  updateAudit = CmdMgr.getMergeCmd(MainCmd.ClubMain, 7),
  //主动退出仙盟
  leaveBySelf = CmdMgr.getMergeCmd(MainCmd.ClubMain, 8),
  //踢出仙盟 前端需要将踢出的用户从仙盟删除掉
  kickOutClub = CmdMgr.getMergeCmd(MainCmd.ClubMain, 9),
  //委任职务 返回职务变动的集合
  adjustPosition = CmdMgr.getMergeCmd(MainCmd.ClubMain, 10),
  //查看仙盟名称是否已存在 false不存在 true存在
  clubNameExist = CmdMgr.getMergeCmd(MainCmd.ClubMain, 11),
  //通过关键字查询
  searchByKey = CmdMgr.getMergeCmd(MainCmd.ClubMain, 12),
  //一键拒绝全部入会请求
  oneClickRefuse = CmdMgr.getMergeCmd(MainCmd.ClubMain, 13),
  //领取仙盟任务奖励
  takeTaskReward = CmdMgr.getMergeCmd(MainCmd.ClubMain, 14),
  //领取活跃度奖励
  takeActiveReward = CmdMgr.getMergeCmd(MainCmd.ClubMain, 15),
  //捐献
  donate = CmdMgr.getMergeCmd(MainCmd.ClubMain, 16),
  //获取仙盟日志
  log = CmdMgr.getMergeCmd(MainCmd.ClubMain, 17),
  //号召BOSS
  callBoss = CmdMgr.getMergeCmd(MainCmd.ClubMain, 18),
  //BOSS战斗
  killBoss = CmdMgr.getMergeCmd(MainCmd.ClubMain, 19),
  //获取召唤的BOSS信息及伙伴信息
  getBossTrain = CmdMgr.getMergeCmd(MainCmd.ClubMain, 20),
  //当BOSS死亡后用于领取奖励
  takeBossDeadReward = CmdMgr.getMergeCmd(MainCmd.ClubMain, 21),
  //当BOSS伤害达成后用于领取奖励
  /**
   * 方法参数: IntValue
   * 方法返回值: @see ClubHurtRewardResponse
   */
  takeBossHurtReward = CmdMgr.getMergeCmd(MainCmd.ClubMain, 22),

  /**
   * 修改仙盟名称
   * 方法参数: StringValue
   * 方法返回值: StringValue
   */
  renameClubName = CmdMgr.getMergeCmd(MainCmd.ClubMain, 23),
  /**
   * 修改仙盟旗帜
   * 方法参数: StringValue
   * 方法返回值: StringValue
   */
  modifyAvatar = CmdMgr.getMergeCmd(MainCmd.ClubMain, 24),

  /**
   * 修改仙盟宣言
   * 方法参数: StringValue
   * 方法返回值: StringValue
   */
  modifySlogan = CmdMgr.getMergeCmd(MainCmd.ClubMain, 25),

  // 路由: 23 - 26  --- 【参与砍价】 --- 【ClubAction:727】【addBargain】
  //   方法返回值: BargainRecordMessage
  addBargain = CmdMgr.getMergeCmd(MainCmd.ClubMain, 26),

  // 路由: 23 - 27  --- 【购买砍价商品】 --- 【ClubAction:745】【buyBargain】
  //   方法返回值: RewardMessage
  buyBargain = CmdMgr.getMergeCmd(MainCmd.ClubMain, 27),
  /**
   * 显示踢出信息后通知服务端
   */
  clubLeaveShow = CmdMgr.getMergeCmd(MainCmd.ClubMain, 28),
  // 路由: 23 - 106  --- 【后端测试：补足所有战盟的砍价内容】 --- 【ClubAction:868】【testCorrectClub】
  // 方法返回值: IntValue
  // 路由: 23 - 29  --- 【仙盟战力排行榜】 --- 【ClubAction:793】【clubRank】
  //   方法返回值: ClubRankMessage
  clubRank = CmdMgr.getMergeCmd(MainCmd.ClubMain, 29),

  testCorrectClub = CmdMgr.getMergeCmd(MainCmd.ClubMain, 106),

  // 路由: 23 - 51  --- 广播推送: com.feamon.proto.club.ClubLogMessage (当有新的成员加入仙盟时,通知其他成员加入成员的信息)
  memberJoinedNotice_23_51 = CmdMgr.getMergeCmd(MainCmd.ClubMain, 51),
  // 路由: 23 - 52  --- 广播推送: com.feamon.proto.club.ClubPopUpMessage (当玩家被提出仙盟后推送信息)
  memberExitedNotice_23_52 = CmdMgr.getMergeCmd(MainCmd.ClubMain, 52),
  // 路由: 23 - 54  --- 广播推送: com.iohao.game.action.skeleton.protocol.wrapper.LongValue (当被审核加入仙盟时，通知加入仙盟的ID)
  memberApplyNotice_23_54 = CmdMgr.getMergeCmd(MainCmd.ClubMain, 54),
  // 路由: 23 - 55  --- 广播推送: com.feamon.proto.club.ClubBossTrainMessage (当BOSS血量发生变化时，推送信息)
  bossTrainNotice_23_55 = CmdMgr.getMergeCmd(MainCmd.ClubMain, 55),
  // 路由: 23 - 56  --- 广播推送: com.feamon.proto.comm.CommLongMapMessage (当职位变化时推送信息)
  positionUpdate_23_56 = CmdMgr.getMergeCmd(MainCmd.ClubMain, 56),
  // 路由: 23 - 57  --- 广播推送: com.feamon.proto.club.ClubApplyMessage (当有新的成员申请加入仙盟时,盟主和副盟主收到信息)
  applyUpdate_23_57 = CmdMgr.getMergeCmd(MainCmd.ClubMain, 57),
  // 路由: 23 - 58  --- 广播推送: com.feamon.proto.club.ClubBargainMessage (当开启了砍价任务时推送)
  bargainNotice_23_58 = CmdMgr.getMergeCmd(MainCmd.ClubMain, 58),
  // 路由: 23 - 59  --- 广播推送: com.feamon.proto.club.BargainRecordMessage (当成员砍价时触发推送)
  bargainNotice_23_59 = CmdMgr.getMergeCmd(MainCmd.ClubMain, 59),
}
export enum ActivityCmd {
  firstCharge = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 1),
  takeFirstRechargeReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 2),
  VipCard = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 3),
  takeMonthVipDailyReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 4),
  takeLifeDailyReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 5),
  fund = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 6),
  takeFundReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 7),
  /** 七日签到 */
  sevenDaySign = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 8),
  /** 获取七日签到奖励 */
  takeSevenDayReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 9),
  /**弹窗详情 方法参数: LongValue 方法返回值: WindowPackMessage */
  windowPack = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 10),
  /**记录弹窗 方法参数: WindowRecordRequest 方法返回值: WindowPackMessage */
  recordWindowUp = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 11),
  /**获取固定礼包领取情况*/
  fixedPack = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 12),

  /**消耗道具兑换或免费领取固定礼包道具 */
  buyFixedPack = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 13),

  /**看广告兑换固定礼包；VIP有跳过广告的权限，也是调用此接口 */
  watchAdFixedPack = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 14),

  /**充值好礼 */
  rechargeGoods = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 16),
  /**领取充值好礼 */
  takeRechargeGoods = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 17),

  /**1.获取累充回馈信息 */
  topUpInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 18),

  /**1.领取累充奖励 --- 这个是日 */
  topUpSign = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 19),

  /**.领取达到累充金额获得的奖励 */
  takeTopUpCostReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 20),

  /**额外自选一个礼包兑换 */
  chosenRedeem = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 21),

  /**.获取修行基金的情况 */
  leaderFundInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 23),

  /**./活动/11.修行基金.md#2.领取修行基金签到奖励 */
  takeLeaderSignPaidReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 24),

  /**领取修行基金每日任务奖励 */
  takeDayTaskReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 25),

  /**领取充值任务奖励 */
  takeLeaderRechargeReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 26),

  /**记录自选情况 */
  recordRedeemChosen = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 27),

  /**.获取山海探险信息 */
  adventureInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 28),
  /***.选择山海探险本轮选定的大奖 */
  chooseAdventureBigPrizeId = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 29),

  /***.山海探险抽奖 */
  adventureDraw = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 30),

  /***.领取探险活动任务的奖励 */
  takeAdventureDailyTaskReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 31),

  // 路由: 14 - 35  --- 【获取限时任务信息】 --- 【ActivityAction:950】【timeTaskInfo】
  //     方法参数: LongValue
  //     方法返回值: TimeTaskMessage
  timeTaskInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 35),
  // 路由: 14 - 36  --- 【领取限时任务完成奖励】 --- 【ActivityAction:970】【takeTimeTask】
  //     方法参数: TimeTaskTakeRequest
  //     方法返回值: ActivityTakeResponse
  takeTimeTask = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 36),

  // 路由: 14 - 40  --- 【曲径通幽，繁荣度冲榜，时空裂缝排行榜】 --- 【ActivityAction:1112】【simpleRankInfo】
  //   方法参数: LongValue
  //   方法返回值: SimpleRankMessage
  simpleRankInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 40),
  // 根据注释完成活动模块相关子命令枚举定义
  // 路由: 14 - 43  --- 【时空裂缝信息】 --- 【ActivityAction:1236】【fractureInfo】
  //     方法参数: LongValue
  //     方法返回值: FractureMessage
  fractureInfo = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 43),

  // 路由: 14 - 44  --- 【搜索时空裂缝】 --- 【ActivityAction:1246】【searchFracture】
  //     方法参数: LongValue
  //     方法返回值: FractureSearchResponse
  searchFracture = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 44),

  // 路由: 14 - 45  --- 【回答事件(除了路障) answer值从1开始表示配置表的回答顺序】 --- 【ActivityAction:1298】【answerEvent】
  //     方法参数: FractureAnswerRequest
  //     方法返回值: FractureEventResponse
  answerEvent = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 45),

  // 路由: 14 - 46  --- 【回答路障 answer字段，1表示付费跳过，2表示求助,其他值为达到通关的条件如值0】 --- 【ActivityAction:1332】【answerTrap】
  //     方法参数: FractureAnswerRequest
  //     方法返回值: FractureTrapResponse
  answerTrap = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 46),

  // 路由: 14 - 47  --- 【进行时空裂缝抽奖|打开各种宝箱】 --- 【ActivityAction:1374】【answerDrawOrBox】
  //     方法参数: LongValue
  //     方法返回值: FractureDrawResponse
  answerDrawOrBox = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 47),

  // 路由: 14 - 48  --- 【对战怪物、BOSS、NPC(非日志)】 --- 【ActivityAction:1418】【answerFightMonsterBossPlayer】
  //     方法参数: LongValue
  //     方法返回值: FractureFightResponse
  answerFightMonsterBossPlayer = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 48),

  // 路由: 14 - 49  --- 【绕过当前时空裂缝关卡】 --- 【ActivityAction:1463】【answerSkip】
  //     方法参数: LongValue
  //     方法返回值: FractureSkipResponse
  answerSkip = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 49),

  // 路由: 14 - 50  --- 【穿梭到指定楼层】 --- 【ActivityAction:1477】【travelFloor】
  //     方法参数: FractureTravelRequest
  //     方法返回值: IntValue
  travelFloor = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 50),

  // 路由: 14 - 51  --- 【资源消耗-曲径通幽，繁荣度冲榜，时空裂缝  注：领取排行榜奖励，要公告期后才能领取】 --- 【ActivityAction:1081】【takeBoardReward】
  //     方法参数: LongValue
  //     方法返回值: RewardMessage
  //     广播推送: com.feamon.proto.activity.MonthCardBuyResponse
  takeBoardReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 51),

  // 路由: 14 - 81  --- 【查看时空裂缝日志列表】 --- 【ActivityAction:1493】【fractureLogList】
  //     方法参数: LongValue
  //     方法返回值: ByteValueList<FractureLogMessage>
  fractureLogList = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 81),

  // 路由: 14 - 82  --- 【发起时空裂缝日志协助】 --- 【ActivityAction:1510】【assistLogRequest】
  //     方法参数: FractureLogRequest
  //     方法返回值: BoolValue
  assistLogRequest = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 82),

  // 路由: 14 - 83  --- 【与日志的对手战斗】 --- 【ActivityAction:1546】【fightFractureLog】
  //     方法参数: FractureLogRequest
  //     方法返回值: FractureLogFightResponse
  fightFractureLog = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 83),

  // 路由: 14 - 84  --- 【领取时空裂缝日志奖励】 --- 【ActivityAction:1576】【takeLogReward】
  //     方法参数: FractureLogRequest
  //     方法返回值: RewardMessage
  takeLogReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 84),
  // 路由: 14 - 86  --- 【点赞圣殿中排行榜获取奖励】 --- 【ActivityAction:950】【takeBoardReward】
  //     方法参数: BoolValue otherServer true-跨服 false-本服 目前不支持跨服
  //     方法返回值: RewardMessage
  takeMainRankReward = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 86),
  /** 月卡年卡购买推送 */
  monthCardBuyNotice = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 51),
  lifeCardBuyNotice = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 52),

  /** 首充购买推送 */
  firstCardBuyNotice = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 53),

  /** 基金购买成功后进行推送 */
  fundMessage = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 54),

  /** 条件礼包购买成功后进行推送 */
  tiaoJianLiBaoBuyNotice = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 55),
  // 路由: 14 - 70  --- 广播推送: com.feamon.proto.activity.WindowPackMessage (当弹窗信息变更时推送)
  tiaoJianLiBaoChangeNotice = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 70),
  /** 礼包购买成功后进行推送 */
  RedeemBuyMessage = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 56),

  /** .当成就关注的指标发生变化时进行推送 */
  FundTargetUp = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 57),

  /**5.充值现金后进行推送 */
  RechargeCash = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 58),

  /**.当兑换礼包的解锁条件变更时推送 */
  RedeemLimitUpdate = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 59),

  /**5.当修行基金签到活动充值后进行推送 */
  DayRechargeCash = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 60),

  /**当修行基金的日常任务完成情况变更时推送 */
  XiuXingFundTask = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 61),

  /**当修行基金的累计付费次数变化时推送 */
  xiuXingCiShuBianHua = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 62),

  /**当山海探险的日常任务完成情况变更时推送 */
  ShenHaiJingTask = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 63),

  // 路由: 14 - 65  --- 广播推送: com.feamon.proto.activity.TimeTaskResponse (限时任务更新时推送)
  TimeTaskNotice = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 65),
  /**当服务端活动配置更新时，推送变更的活动ID */
  ActivityUp = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 66),
  // 路由: 14 - 67  --- 广播推送: com.feamon.proto.activity.FractureRoadAssistResponse (当时空裂缝路障被协助后推送变更)
  FractureRoadAssist = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 67),
  // 路由: 14 - 68  --- 广播推送: com.feamon.proto.activity.FractureLogAssistResponse (当时空裂缝怪物/BOSS/NPC被协助后推送变更)
  FractureLogAssist = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 68),

  /**.测试--重置弹窗礼包 */
  testResetWindows = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 107),
}

export enum ShengDianCmd {
  // 路由: 14 - 32  --- 【圣殿信息】 --- 【ActivityAction:879】【templeInfo】
  //   方法返回值: TempleMessage
  templeInfo = CmdMgr.getMergeCmd(MainCmd.ShengDianCmd, 32),
  // 路由: 14 - 33  --- 【点赞圣殿】 --- 【ActivityAction:898】【templeLike】
  //     方法参数: LongValue
  //     方法返回值: TempleLikeResponse
  templeLike = CmdMgr.getMergeCmd(MainCmd.ShengDianCmd, 33),
  // 路由: 14 - 34  --- 【领取神殿运势数奖励】 --- 【ActivityAction:921】【takeTreeReward】
  //     方法参数: IntValue
  //     方法返回值: ActivityTakeResponse
  takeTreeReward = CmdMgr.getMergeCmd(MainCmd.ShengDianCmd, 34),

  // 路由: 14 - 64  --- 广播推送: com.iohao.game.action.skeleton.protocol.wrapper.IntValue (当圣殿逆袭之路的运势值变动后推送)
  yunShiNotice = CmdMgr.getMergeCmd(MainCmd.ShengDianCmd, 64),

  // 路由: 14 - 103  --- 【】 --- 【ActivityAction:1030】【testInsertTemplate】
  //   方法返回值: IntValue
  testInsertTemplate = CmdMgr.getMergeCmd(MainCmd.ShengDianCmd, 103),
}
export enum FrbpCmd {
  /**领取成就奖励 */
  takeAchieveReward = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 7),

  /**消耗道具兑换或免费领取礼包道具 */
  buyFixedPack = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 13),

  /**看广告兑换礼包 */
  watchAdFixedPack = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 14),

  /**额外自选一个礼包兑换 */
  buyAchieve = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 21),

  /**获取繁荣度活动详情 */
  getAchieveInfo = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 22),

  /**获取排行榜 */
  getRankList = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 40),

  /**领取排行榜奖励，要公告期后才能领取 */
  takeRankReward = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 51),

  /**成就购买成功后进行推送 */
  onAchievePayUp = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 56),

  /**当成就关注的指标发生变化时进行推送 */
  onAchieveFocusUp = CmdMgr.getMergeCmd(MainCmd.FrbpCmd, 59),

  /**当服务端活动配置更新时，推送变更的活动ID */
  ActivityUp = CmdMgr.getMergeCmd(MainCmd.ActivityCmd, 66),
}
export enum AssistSubCmd {
  /**
   * 路由: 25 - 1  --- 【获取所有的求助信息】 --- 【AssistAction:52】【allAssistList】
   * @return 方法返回值: ByteValueList<AssistMessage>
   */
  allAssistList = CmdMgr.getMergeCmd(MainCmd.AssistCmd, 1),
  /**
   * 路由: 25 - 2  --- 【】 --- 【AssistAction:65】【doAssist】
   * @param 方法参数: LongValue
   * @return 方法返回值: AssistResponse
   * */
  doAssist = CmdMgr.getMergeCmd(MainCmd.AssistCmd, 2),
}
export enum EventSubCmd {
  /**获取事件总信息 */

  eventInfo = CmdMgr.getMergeCmd(MainCmd.EventCmd, 1),

  /**驱赶贼人 */
  expel = CmdMgr.getMergeCmd(MainCmd.EventCmd, 2),

  /**当解锁新事件推送 */
  newEvent = CmdMgr.getMergeCmd(MainCmd.EventCmd, 51),

  test_申请_测试事件 = CmdMgr.getMergeCmd(MainCmd.EventCmd, 101),
}

export enum RealmsSubCmd {
  // ==================== RealmsAction  ====================
  // 路由: 28 - 1  --- 【获取驿站的信息】 --- 【RealmsAction:69】【info】
  //     方法返回值: RealmsMessage
  info = CmdMgr.getMergeCmd(MainCmd.RealmsCmd, 1),

  // 路由: 28 - 2  --- 【挑战关卡】 --- 【RealmsAction:79】【conquer】
  //     方法返回值: RealmsFightResponse
  conquer = CmdMgr.getMergeCmd(MainCmd.RealmsCmd, 2),

  // 路由: 28 - 3  --- 【扫荡关卡】 --- 【RealmsAction:108】【sweep】
  //     方法返回值: RealmsFightResponse
  sweep = CmdMgr.getMergeCmd(MainCmd.RealmsCmd, 3),

  // 路由: 28 - 5  --- 【兑换道具】 --- 【RealmsAction:159】【redeemGoods】
  //     方法参数: RealmsRedeemRequest
  //     方法返回值: RealmsRedeemResponse
  redeemGoods = CmdMgr.getMergeCmd(MainCmd.RealmsCmd, 5),
}
export enum ChallengeSubCmd {
  // ==================== ChallengeAction  ====================
  // 路由: 29 - 1  --- 【获取每日挑战的信息】 --- 【ChallengeAction:69】【info】
  //     方法返回值: ChallengeMessage
  info = CmdMgr.getMergeCmd(MainCmd.ChallengeCmd, 1),

  // 路由: 29 - 2  --- 【获取排行榜】 --- 【ChallengeAction:79】【rank】
  //     方法返回值: CommRankMessage
  rank = CmdMgr.getMergeCmd(MainCmd.ChallengeCmd, 2),

  // 路由: 29 - 3  --- 【进行每日挑战】 --- 【ChallengeAction:113】【challenge】
  //     方法返回值: ChallengeResponse
  challenge = CmdMgr.getMergeCmd(MainCmd.ChallengeCmd, 3),
}
