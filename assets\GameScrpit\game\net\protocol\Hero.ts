// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Hero.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface HeroMessage {
  /** 英雄编号 */
  heroId: number;
  /** 英雄等级 */
  level: number;
  /** 英雄突破几次 */
  breakTopLevel: number;
  /** 技能列表 */
  skillMap: { [key: number]: number };
  /** 道具增加的战斗属性 */
  itemAddBattleAttrMap: { [key: number]: number };
}

export interface HeroMessage_SkillMapEntry {
  key: number;
  value: number;
}

export interface HeroMessage_ItemAddBattleAttrMapEntry {
  key: number;
  value: number;
}

/**  */
export interface HeroSkillLvRequest {
  /** 英雄ID */
  heroId: number;
  /** 技能ID */
  skillId: number;
}

/**  */
export interface HeroSkillUpdateMessage {
  /** 变更的技能 */
  skillId: number;
  /** 涉及的英雄 */
  heroIdList: number[];
  /** 技能等级 */
  level: number;
}

function createBaseHeroMessage(): HeroMessage {
  return { heroId: 0, level: 0, breakTopLevel: 0, skillMap: {}, itemAddBattleAttrMap: {} };
}

export const HeroMessage: MessageFns<HeroMessage> = {
  encode(message: HeroMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heroId !== 0) {
      writer.uint32(8).int64(message.heroId);
    }
    if (message.level !== 0) {
      writer.uint32(16).int32(message.level);
    }
    if (message.breakTopLevel !== 0) {
      writer.uint32(24).int32(message.breakTopLevel);
    }
    Object.entries(message.skillMap).forEach(([key, value]) => {
      HeroMessage_SkillMapEntry.encode({ key: key as any, value }, writer.uint32(34).fork()).join();
    });
    Object.entries(message.itemAddBattleAttrMap).forEach(([key, value]) => {
      HeroMessage_ItemAddBattleAttrMapEntry.encode({ key: key as any, value }, writer.uint32(42).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeroMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeroMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.heroId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.breakTopLevel = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          const entry4 = HeroMessage_SkillMapEntry.decode(reader, reader.uint32());
          if (entry4.value !== undefined) {
            message.skillMap[entry4.key] = entry4.value;
          }
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          const entry5 = HeroMessage_ItemAddBattleAttrMapEntry.decode(reader, reader.uint32());
          if (entry5.value !== undefined) {
            message.itemAddBattleAttrMap[entry5.key] = entry5.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeroMessage>, I>>(base?: I): HeroMessage {
    return HeroMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeroMessage>, I>>(object: I): HeroMessage {
    const message = createBaseHeroMessage();
    message.heroId = object.heroId ?? 0;
    message.level = object.level ?? 0;
    message.breakTopLevel = object.breakTopLevel ?? 0;
    message.skillMap = Object.entries(object.skillMap ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.itemAddBattleAttrMap = Object.entries(object.itemAddBattleAttrMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseHeroMessage_SkillMapEntry(): HeroMessage_SkillMapEntry {
  return { key: 0, value: 0 };
}

export const HeroMessage_SkillMapEntry: MessageFns<HeroMessage_SkillMapEntry> = {
  encode(message: HeroMessage_SkillMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeroMessage_SkillMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeroMessage_SkillMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeroMessage_SkillMapEntry>, I>>(base?: I): HeroMessage_SkillMapEntry {
    return HeroMessage_SkillMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeroMessage_SkillMapEntry>, I>>(object: I): HeroMessage_SkillMapEntry {
    const message = createBaseHeroMessage_SkillMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseHeroMessage_ItemAddBattleAttrMapEntry(): HeroMessage_ItemAddBattleAttrMapEntry {
  return { key: 0, value: 0 };
}

export const HeroMessage_ItemAddBattleAttrMapEntry: MessageFns<HeroMessage_ItemAddBattleAttrMapEntry> = {
  encode(message: HeroMessage_ItemAddBattleAttrMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeroMessage_ItemAddBattleAttrMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeroMessage_ItemAddBattleAttrMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeroMessage_ItemAddBattleAttrMapEntry>, I>>(
    base?: I,
  ): HeroMessage_ItemAddBattleAttrMapEntry {
    return HeroMessage_ItemAddBattleAttrMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeroMessage_ItemAddBattleAttrMapEntry>, I>>(
    object: I,
  ): HeroMessage_ItemAddBattleAttrMapEntry {
    const message = createBaseHeroMessage_ItemAddBattleAttrMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseHeroSkillLvRequest(): HeroSkillLvRequest {
  return { heroId: 0, skillId: 0 };
}

export const HeroSkillLvRequest: MessageFns<HeroSkillLvRequest> = {
  encode(message: HeroSkillLvRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heroId !== 0) {
      writer.uint32(8).int64(message.heroId);
    }
    if (message.skillId !== 0) {
      writer.uint32(16).int64(message.skillId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeroSkillLvRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeroSkillLvRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.heroId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.skillId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeroSkillLvRequest>, I>>(base?: I): HeroSkillLvRequest {
    return HeroSkillLvRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeroSkillLvRequest>, I>>(object: I): HeroSkillLvRequest {
    const message = createBaseHeroSkillLvRequest();
    message.heroId = object.heroId ?? 0;
    message.skillId = object.skillId ?? 0;
    return message;
  },
};

function createBaseHeroSkillUpdateMessage(): HeroSkillUpdateMessage {
  return { skillId: 0, heroIdList: [], level: 0 };
}

export const HeroSkillUpdateMessage: MessageFns<HeroSkillUpdateMessage> = {
  encode(message: HeroSkillUpdateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.skillId !== 0) {
      writer.uint32(8).int64(message.skillId);
    }
    writer.uint32(18).fork();
    for (const v of message.heroIdList) {
      writer.int64(v);
    }
    writer.join();
    if (message.level !== 0) {
      writer.uint32(24).int32(message.level);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeroSkillUpdateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeroSkillUpdateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.skillId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.heroIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.heroIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeroSkillUpdateMessage>, I>>(base?: I): HeroSkillUpdateMessage {
    return HeroSkillUpdateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeroSkillUpdateMessage>, I>>(object: I): HeroSkillUpdateMessage {
    const message = createBaseHeroSkillUpdateMessage();
    message.skillId = object.skillId ?? 0;
    message.heroIdList = object.heroIdList?.map((e) => e) || [];
    message.level = object.level ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
