import { _decorator, director, instantiate, is<PERSON><PERSON><PERSON>, Label, ProgressBar, sp, tween, Vec3 } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { MainTaskModule } from "../../../module/mainTask/MainTaskModule";
import { CityModule } from "../../../module/city/CityModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { MainTaskAudioName } from "../../../module/mainTask/MainTaskConfig";
import { HeroModule } from "../../../module/hero/HeroModule";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import { Sleep } from "../../GameDefine";
import GuideMgr from "../../../ext_guide/GuideMgr";
import { FriendModule } from "../../../module/friend/FriendModule";
import { PetModule } from "../../../module/pet/PetModule";
import FmUtils from "../../../lib/utils/FmUtils";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { StartUp } from "../../../lib/StartUp";

const { ccclass, property } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("UITaskMainPopup")
export class UITaskMainPopup extends UINode {
  private _spineBg: sp.Skeleton;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAINTASK}?prefab/ui/UITaskMainPopup`;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(false, 3, false);
    // 设置标题
    this.getNode("task_title_lab").getComponent(Label).string = `${
      MainTaskModule.data.mainTaskMsg.showId
    }.${MainTaskModule.service.getTaskTitle()}`;

    // 设置内容
    this.getNode("task_lab").getComponent(Label).string = MainTaskModule.service.getTaskDesc(false);

    // 进度
    let cur = MainTaskModule.service.getCompleteNum();
    let need = MainTaskModule.service.getNeedCount();
    this.getNode("ProgressBar").getComponent(ProgressBar).progress = cur / need;
    let lab = this.getNode("ProgressBar").getChildByName("lab");
    lab.getComponent(Label).string = `${cur}/${need}`;

    // 奖品
    let config_taskMain = MainTaskModule.data.getConfigTaskMain(MainTaskModule.data.mainTaskMsg.taskTimelineId);
    let list = config_taskMain.rewardList;
    for (let i = 0; i < list.length; i++) {
      let node = instantiate(this.getNode("Item"));
      node.active = true;
      this.getNode("item_lay").addChild(node);
      FmUtils.setItemNode(node, list[i][0], list[i][1]);
    }

    // 播放动画
    this._spineBg = this.getNode("spine_tanchuang_action_task").getComponent(sp.Skeleton);
    SpineUtil.playSpine(this._spineBg, "tanchuang_appear", false);

    this._spineBg.setEventListener((animation, event) => {
      if (event["data"].name == "tc_1") {
        this.getNode("node_title").active = true;
      } else if (event["data"].name == "tc_2") {
        this.getNode("bg_zuoqimingzi").active = true;
      } else if (event["data"].name == "tc_3") {
        this.getNode("node_progress").active = true;
      } else if (event["data"].name == "tc_4") {
        this.getNode("TY_bg_9g_tanchuangdi").active = true;
      } else if (event["data"].name == "tc_5") {
        this.getNode("btn_huangse").active = true;
      }
    });

    const dt = SpineUtil.getSpineDuration(this._spineBg, "tanchuang_appear");
    tween(this.node)
      .delay(dt)
      .call(() => {
        this.getNode("btn_close").active = true;
        TipsMgr.setEnableTouch(true);
      })
      .start();
  }

  protected async onCloseAct(actEndCall) {
    if (!isValid(this.node)) {
      return;
    }
    this.getNode("btn_close").active = false;

    // 播放动画
    this._spineBg = this.getNode("spine_tanchuang_action_task").getComponent(sp.Skeleton);
    SpineUtil.playSpine(this._spineBg, "tanchuang_disappear", false);

    this._spineBg.setEventListener((animation, event) => {
      if (event["data"].name == "tc_1") {
        this.getNode("node_title").active = false;
      } else if (event["data"].name == "tc_2") {
        this.getNode("bg_zuoqimingzi").active = false;
      } else if (event["data"].name == "tc_3") {
        this.getNode("node_progress").active = false;
      } else if (event["data"].name == "tc_4") {
        this.getNode("TY_bg_9g_tanchuangdi").active = false;
      } else if (event["data"].name == "tc_5") {
        this.getNode("btn_huangse").active = false;
      }
    });

    const dt = SpineUtil.getSpineDuration(this._spineBg, "tanchuang_disappear");

    await Sleep(dt);
    actEndCall && actEndCall();
  }

  private on_click_btn_huangse() {
    AudioMgr.instance.playEffect(MainTaskAudioName.Effect.点击前往按钮);
    TipsMgr.setEnableTouch(false, 7);

    UIMgr.instance.back();

    let pos = this.node.getWorldPosition();

    setTimeout(() => {
      this.showTopFinger(pos, true);
    }, 500);
  }

  private showTopFinger(wPos: Vec3, isFirst = false) {
    let cur = MainTaskModule.service.getCompleteNum();
    let need = MainTaskModule.service.getNeedCount();

    // 任务特殊处理需要次数
    const id = MainTaskModule.data.mainTaskMsg.taskTimelineId;
    let configTaskMain = MainTaskModule.data.getConfigTaskMain(id);

    if (configTaskMain.taskId == 4) {
      need = MainTaskModule.data.getConfigTaskMain(id).finishList[1];
      cur = 0;
      for (let hero of HeroModule.data.ownHeroList) {
        let heroMsg = HeroModule.data.getHeroMessage(hero.id);
        if (heroMsg.level >= need) {
          cur == need;
          break;
        } else if (heroMsg.level > cur) {
          cur = heroMsg.level;
        }
      }
    }

    let redo = 1;
    if (cur < need) {
      redo = need - cur;
    }

    /**点击提示或路径跳转 */
    let args = { redo: redo };
    if ([5, 6, 8, 38, 63].includes(configTaskMain.taskId)) {
      args["buildId"] = configTaskMain.finishList[0];
    } else if (configTaskMain.taskId == 7) {
      args["buildId"] = CityModule.service.findMinCostLevelUpCityId();
    } else if ([9, 37].includes(configTaskMain.taskId)) {
      args["buildId"] = CityModule.service.findMinCostCallCityId();
    } else if (configTaskMain.taskId == 66) {
      args["homeId"] = configTaskMain.finishList[0];
    } else if ([12, 20, 29].includes(configTaskMain.taskId)) {
      let list = FriendModule.data.getFriendIds(true);
      args["friendId"] = list[0];
    } else if ([27, 28].includes(configTaskMain.taskId)) {
      let list = PetModule.data.getPetIds(true);
      args["petId"] = list[0];
    } else if (configTaskMain.taskId == 65) {
      console.log("引导至小偷界面");
      //获取当前出现小偷的节点位置 进行引导
      let node = NodeTool.findByName(StartUp.instance.uiRoot, "UIMain")
        .getChildByName("btn_map")
        .getChildByName("node_ground")
        .getChildByName("path_thief_101");
      let pos = node.getWorldPosition();
      args["pos"] = pos;
    }

    let configTask = MainTaskModule.data.getConfigTask(configTaskMain.taskId);

    if (configTaskMain.guide == 1 || isFirst) {
      GuideMgr.unshiftGuide({
        stepId: configTaskMain.guidePathId || configTask.guideType,
        args: args,
        isForce: configTaskMain.guide == 1,
        startPos: wPos,
      });
      GuideMgr.unshiftCallBack(() => {
        let id = MainTaskModule.data.mainTaskMsg.taskTimelineId;
        let configTaskMain3 = MainTaskModule.data.getConfigTaskMain(id);
        if (configTaskMain3.guide == 1) {
          TipsMgr.setEnableTouch(false, 3);

          const id = MainTaskModule.data.mainTaskMsg.taskTimelineId;
          let configTaskMain2 = MainTaskModule.data.getConfigTaskMain(id);
          if (configTaskMain2.guide == 1) {
            let needCount = MainTaskModule.service.getNeedCount();
            let complateNum = MainTaskModule.service.getCompleteNum();

            if (needCount <= complateNum) {
              log.info(" guide 2 50");
              GuideMgr.startGuide({ stepId: 50, isForce: true });
            } else {
              GuideMgr.startGuide({ stepId: 49, isForce: true });
            }
          }
        }
      });
    }
  }

  private on_click_btn_close() {
    AudioMgr.instance.playEffect(103);
    UIMgr.instance.back();
  }

  private on_click_btn_close_bg() {
    this.on_click_btn_close();
  }
}
