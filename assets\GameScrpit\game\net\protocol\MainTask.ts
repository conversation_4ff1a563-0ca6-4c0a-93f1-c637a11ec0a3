// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: MainTask.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface MainTaskMessage {
  /** 主线任务id 为-1时代表主线任务已完成，可以移除主线任务模块了 */
  taskTimelineId: number;
  showId: number;
  /** 当前完成数量 */
  completeNum: number;
  /** 总的任务完成数据 */
  totalSuccessCount: number;
}

/**  */
export interface MainTaskPassResponse {
  /** 最新的主线任务 */
  mainTask:
    | MainTaskMessage
    | undefined;
  /** 获得奖励 */
  resAddList: number[];
}

function createBaseMainTaskMessage(): MainTaskMessage {
  return { taskTimelineId: 0, showId: 0, completeNum: 0, totalSuccessCount: 0 };
}

export const MainTaskMessage: MessageFns<MainTaskMessage> = {
  encode(message: MainTaskMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.taskTimelineId !== 0) {
      writer.uint32(8).int64(message.taskTimelineId);
    }
    if (message.showId !== 0) {
      writer.uint32(16).int64(message.showId);
    }
    if (message.completeNum !== 0) {
      writer.uint32(24).int64(message.completeNum);
    }
    if (message.totalSuccessCount !== 0) {
      writer.uint32(32).int64(message.totalSuccessCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MainTaskMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMainTaskMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.taskTimelineId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.showId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.completeNum = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.totalSuccessCount = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<MainTaskMessage>, I>>(base?: I): MainTaskMessage {
    return MainTaskMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MainTaskMessage>, I>>(object: I): MainTaskMessage {
    const message = createBaseMainTaskMessage();
    message.taskTimelineId = object.taskTimelineId ?? 0;
    message.showId = object.showId ?? 0;
    message.completeNum = object.completeNum ?? 0;
    message.totalSuccessCount = object.totalSuccessCount ?? 0;
    return message;
  },
};

function createBaseMainTaskPassResponse(): MainTaskPassResponse {
  return { mainTask: undefined, resAddList: [] };
}

export const MainTaskPassResponse: MessageFns<MainTaskPassResponse> = {
  encode(message: MainTaskPassResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mainTask !== undefined) {
      MainTaskMessage.encode(message.mainTask, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MainTaskPassResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMainTaskPassResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.mainTask = MainTaskMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<MainTaskPassResponse>, I>>(base?: I): MainTaskPassResponse {
    return MainTaskPassResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MainTaskPassResponse>, I>>(object: I): MainTaskPassResponse {
    const message = createBaseMainTaskPassResponse();
    message.mainTask = (object.mainTask !== undefined && object.mainTask !== null)
      ? MainTaskMessage.fromPartial(object.mainTask)
      : undefined;
    message.resAddList = object.resAddList?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
