// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: LuckDraw.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface LuckDrawResponse {
  /** 神迹触发几次 */
  trigger: number;
  /** 偶数索引为道具的ID，奇数为数量 */
  resAddList: number[];
  /** 最新的模块信息 */
  luckTrain: LuckTrainMessage | undefined;
  recordList: LuckRecordMessage[];
}

/**  */
export interface LuckRecordMessage {
  name: string;
}

/**  */
export interface LuckTrainMessage {
  /** 剩余的观看视频的次数 */
  video: number;
  /** 已经累计的幸运值 */
  count: number;
  /** 引导ID */
  guideId: number;
}

/**  */
export interface LuckTrainResponse {
  /** 幸运抽奖模块的总体信息 */
  train:
    | LuckTrainMessage
    | undefined;
  /** 抽到大奖的记录用来滚动展示 */
  recordList: LuckRecordMessage[];
}

function createBaseLuckDrawResponse(): LuckDrawResponse {
  return { trigger: 0, resAddList: [], luckTrain: undefined, recordList: [] };
}

export const LuckDrawResponse: MessageFns<LuckDrawResponse> = {
  encode(message: LuckDrawResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.trigger !== 0) {
      writer.uint32(8).int32(message.trigger);
    }
    writer.uint32(18).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    if (message.luckTrain !== undefined) {
      LuckTrainMessage.encode(message.luckTrain, writer.uint32(26).fork()).join();
    }
    for (const v of message.recordList) {
      LuckRecordMessage.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LuckDrawResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLuckDrawResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.trigger = reader.int32();
          continue;
        }
        case 2: {
          if (tag === 17) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.luckTrain = LuckTrainMessage.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.recordList.push(LuckRecordMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LuckDrawResponse>, I>>(base?: I): LuckDrawResponse {
    return LuckDrawResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LuckDrawResponse>, I>>(object: I): LuckDrawResponse {
    const message = createBaseLuckDrawResponse();
    message.trigger = object.trigger ?? 0;
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.luckTrain = (object.luckTrain !== undefined && object.luckTrain !== null)
      ? LuckTrainMessage.fromPartial(object.luckTrain)
      : undefined;
    message.recordList = object.recordList?.map((e) => LuckRecordMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseLuckRecordMessage(): LuckRecordMessage {
  return { name: "" };
}

export const LuckRecordMessage: MessageFns<LuckRecordMessage> = {
  encode(message: LuckRecordMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LuckRecordMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLuckRecordMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LuckRecordMessage>, I>>(base?: I): LuckRecordMessage {
    return LuckRecordMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LuckRecordMessage>, I>>(object: I): LuckRecordMessage {
    const message = createBaseLuckRecordMessage();
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseLuckTrainMessage(): LuckTrainMessage {
  return { video: 0, count: 0, guideId: 0 };
}

export const LuckTrainMessage: MessageFns<LuckTrainMessage> = {
  encode(message: LuckTrainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.video !== 0) {
      writer.uint32(8).int32(message.video);
    }
    if (message.count !== 0) {
      writer.uint32(16).int32(message.count);
    }
    if (message.guideId !== 0) {
      writer.uint32(24).int64(message.guideId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LuckTrainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLuckTrainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.video = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.guideId = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LuckTrainMessage>, I>>(base?: I): LuckTrainMessage {
    return LuckTrainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LuckTrainMessage>, I>>(object: I): LuckTrainMessage {
    const message = createBaseLuckTrainMessage();
    message.video = object.video ?? 0;
    message.count = object.count ?? 0;
    message.guideId = object.guideId ?? 0;
    return message;
  },
};

function createBaseLuckTrainResponse(): LuckTrainResponse {
  return { train: undefined, recordList: [] };
}

export const LuckTrainResponse: MessageFns<LuckTrainResponse> = {
  encode(message: LuckTrainResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.train !== undefined) {
      LuckTrainMessage.encode(message.train, writer.uint32(10).fork()).join();
    }
    for (const v of message.recordList) {
      LuckRecordMessage.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LuckTrainResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLuckTrainResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.train = LuckTrainMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recordList.push(LuckRecordMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<LuckTrainResponse>, I>>(base?: I): LuckTrainResponse {
    return LuckTrainResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LuckTrainResponse>, I>>(object: I): LuckTrainResponse {
    const message = createBaseLuckTrainResponse();
    message.train = (object.train !== undefined && object.train !== null)
      ? LuckTrainMessage.fromPartial(object.train)
      : undefined;
    message.recordList = object.recordList?.map((e) => LuckRecordMessage.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
