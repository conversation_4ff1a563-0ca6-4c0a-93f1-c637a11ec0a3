import { _decorator, Component, instantiate, Label, Node } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { UIExchangeShop } from "../../../../common/src/prefab/ui/UIExchangeShop";
import { UIDailyChallengeAward } from "./UIDailyChallengeAward";
import { UIDailyChallengeRank } from "./UIDailyChallengeRank";
import { ShopTypeEnum } from "../../../../common/src/CommonConfig";
import { JsonMgr } from "db://assets/GameScrpit/game/mgr/JsonMgr";
import { ItemCtrl } from "db://assets/GameScrpit/game/common/ItemCtrl";
import { DailyChallengeModule } from "../../DailyChallengeModule";
import TipMgr from "db://assets/GameScrpit/lib/tips/TipMgr";
const { ccclass, property } = _decorator;

@ccclass("UIDailyChallenge")
@routeConfig({
  bundle: BundleEnum.BUNDLE_DAILY_CHALLENGE,
  url: "prefab/ui/UIDailyChallenge",
  nextHop: [],
  exit: "dialog_close",
})
export class UIDailyChallenge extends BaseCtrl {
  public playShowAni: boolean = true;
  start() {
    super.start();
    this.refresh();
  }
  refresh() {
    let showItemsList = JsonMgr.instance.jsonList.c_dailyChallenge[1].showItemsList;
    this.getNode("node_items").children.forEach((node) => {
      node.active = false;
    });
    for (let i = 0; i < showItemsList.length; i++) {
      let item = showItemsList[i];
      let node = this.getNode("node_items").children[i];
      if (!node) {
        node = instantiate(this.getNode("node_items").children[0]);
        this.getNode("node_items").addChild(node);
      }
      node.active = true;
      node.getComponent(ItemCtrl).setItemId(item);
    }
    let free = JsonMgr.instance.jsonList.c_dailyChallenge[1].free - DailyChallengeModule.data.challengeMessage.count;
    if (free < 0) {
      free = 0;
    }
    this.getNode("lbl_nums").getComponent(
      Label
    ).string = `${free}/${JsonMgr.instance.jsonList.c_dailyChallenge[1].free}`;
  }

  update(deltaTime: number) {}

  private on_click_btn_paihang() {
    // RouteTableManager.instance.push(BundleEnum.BUNDLE_G_MAIN_RANK, "prefab/ui/UILeaderboard");
    RouteManager.uiRouteCtrl.showRoute(UIDailyChallengeRank);
  }
  private on_click_btn_jiangli() {
    RouteManager.uiRouteCtrl.showRoute(UIDailyChallengeAward);
  }
  private on_click_btn_shangdian() {
    RouteManager.uiRouteCtrl.showRoute(UIExchangeShop, { payload: { type: ShopTypeEnum.每日挑战商店 } });
  }

  private on_click_btn_go_fight() {
    let free = JsonMgr.instance.jsonList.c_dailyChallenge[1].free - DailyChallengeModule.data.challengeMessage.count;
    if (free <= 0) {
      TipMgr.showTip("挑战次数不足");
      return;
    }

    DailyChallengeModule.api.challenge(() => {
      this.refresh();
      TipMgr.showTip("挑战成功");
    });
  }
}
