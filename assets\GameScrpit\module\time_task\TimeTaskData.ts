import { JsonMgr } from "../../game/mgr/JsonMgr";
import { TimeTaskMessage } from "../../game/net/protocol/Activity";
import { times } from "../../lib/utils/NumbersUtils";
import { TimeTaskModule } from "./TimeTaskModule";

export class TimeTaskData {
  private _timeTaskMessage: TimeTaskMessage;
  set timeTaskMessage(value: TimeTaskMessage) {
    this._timeTaskMessage = value;
  }
  get timeTaskMessage() {
    return this._timeTaskMessage;
  }
  updateTakeList(taskId: number, takeList: number[]) {
    if (!this._timeTaskMessage) {
      return;
    }
    if (!this._timeTaskMessage.completeMap) {
      return;
    }
    if (!this._timeTaskMessage.completeMap[taskId]) {
      return;
    }

    this._timeTaskMessage.completeMap[taskId].takeList = takeList;
  }
  updateTask(taskId: number, targetVal: number) {
    if (!this._timeTaskMessage) {
      return;
    }
    if (!this._timeTaskMessage.completeMap) {
      return;
    }
    if (!this._timeTaskMessage.completeMap[taskId]) {
      return;
    }

    this._timeTaskMessage.completeMap[taskId].targetVal = targetVal;
  }
}
