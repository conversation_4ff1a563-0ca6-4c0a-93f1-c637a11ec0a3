{"1": {"id": 1, "type": 1, "showType": 1, "args": "1"}, "2": {"id": 2, "type": 2, "showType": 1, "args": "2"}, "3": {"id": 3, "type": 3, "showType": 1, "args": "3"}, "4": {"id": 4, "type": 4, "showType": 1, "args": "4"}, "5": {"id": 5, "type": 5, "showType": 1, "args": "5"}, "6": {"id": 6, "type": 5, "showType": 2, "args": "{\"stepId\": 58, \"isForce\": true }"}, "7": {"id": 7, "type": 6, "showType": 1, "args": "7"}, "8": {"id": 8, "type": 6, "showType": 2, "args": "{\"stepId\": 12, \"isForce\": true }"}, "9": {"id": 9, "type": 7, "showType": 1, "args": "8"}, "10": {"id": 10, "type": 7, "showType": 2, "args": "{\"stepId\": 13, \"isForce\": true }"}, "11": {"id": 11, "type": 8, "showType": 1, "args": "10"}, "12": {"id": 12, "type": 8, "showType": 2, "args": "{\"stepId\": 37, \"isForce\": true }"}, "13": {"id": 13, "type": 9, "showType": 1, "args": "6"}, "14": {"id": 14, "type": 9, "showType": 2, "args": "{\"stepId\": 21, \"isForce\": true }"}, "15": {"id": 15, "type": 10, "showType": 1, "args": "9"}, "16": {"id": 16, "type": 10, "showType": 2, "args": "{\"stepId\": 11, \"isForce\": true }"}, "17": {"id": 17, "type": 11, "showType": 1, "args": "11"}, "18": {"id": 18, "type": 11, "showType": 2, "args": "{\"stepId\": 16, \"isForce\": true }"}, "19": {"id": 19, "type": 12, "showType": 2, "args": "{\"stepId\": 10, \"isForce\": true }"}, "20": {"id": 20, "type": 14, "showType": 2, "args": "{\"stepId\": 15, \"isForce\": true }"}, "-1": {"id": -1, "type": 0, "showType": 0, "args": ""}}