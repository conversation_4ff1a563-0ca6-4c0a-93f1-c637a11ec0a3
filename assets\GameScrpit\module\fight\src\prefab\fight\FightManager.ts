import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { BattleReplayDTO, CharacterDTO, AttrKeyEnum, BuffDTO, BattleActionDTO } from "../../FightConstant";
import { getBattleReport } from "../../utils/BattleReportUtil";

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

export class FightManager {
  private static instance: FightManager;
  public static getInstance(): FightManager {
    if (!this.instance) {
      this.instance = new FightManager();
    }
    return this.instance;
  }

  // 战报信息
  public battleReport: BattleReplayDTO;

  // 移动速度
  movingSpeed = 1600;

  // 当前回合, -1表示战斗开始前
  roundIdxNow = -1;

  // 当前回合行动顺序
  actionIdx = 0;

  // 当前队伍信息
  teamCurrentList: CharacterDTO[][];

  // 按map取队伍
  characterCurrentMap: { [key: number]: CharacterDTO } = {};

  /**
   * 战斗录像文件初始化
   */
  public init(battleReport: any) {
    this.battleReport = getBattleReport(battleReport);
    this.roundIdxNow = -1;
    this.teamCurrentList = JSON.parse(JSON.stringify(this.battleReport.teamList));

    for (let i = 0; i < this.teamCurrentList.length; i++) {
      let team = this.teamCurrentList[i];
      for (let j = 0; j < team.length; j++) {
        const key = i * 10 + j + 1;
        team[j].id = key;
        team[j].attrMap[AttrKeyEnum.血量] = team[j].attrMap[AttrKeyEnum.血量Max] || 0;
        this.characterCurrentMap[key] = team[j];
      }
    }
  }

  /**
   * 获取队伍当前信息
   * @param teamIdx 队伍信息
   * @param posIdx 位置信息
   * @returns
   */
  public getCharacter(teamIdx: number, posIdx: number): CharacterDTO {
    if (this.teamCurrentList[teamIdx]) {
      let rs = this.teamCurrentList[teamIdx][posIdx];
      if (rs) {
        return rs;
      } else {
        log.error(`队伍${teamIdx}位置${posIdx}不存在角色信息`);
      }
    } else {
      log.error(`队伍${teamIdx}不存在`);
    }
  }

  public getCharacterById(id: number) {
    return this.characterCurrentMap[id];
  }

  /**
   * 校验队伍状态，用于数据检验
   */
  public checkStatus() {}

  // /**
  //  * 从行动列表中获取行动信息
  //  * @param actionList 行动列表
  //  * @returns
  //  */
  // public fetchFromActionList(actionList: BattleActionDTO[]): BattleActionDTO {
  //   return actionList[this.actionIdx];
  //   // let action: BattleActionDTO = undefined;
  //   // for (let i = 0; i < this.actionIdxList.length; i++) {
  //   //   let idx = this.actionIdxList[i];
  //   //   let action = actionList[idx];
  //   //   if (!action) {
  //   //     if (i == 0) {
  //   //       return undefined;
  //   //     }
  //   //     this.actionIdxList.slice(0, i);
  //   //     this.actionIdxList[i - 1] = this.actionIdxList[i - 1] + 1;
  //   //     return this.fetchFromActionList(actionList);
  //   //   }
  //   // }
  //   // return action;
  // }

  /**
   * 血量变化
   */
  public hpChange(characterId: number, value: number) {
    this.getCharacterById(characterId).attrMap[AttrKeyEnum.血量] += value;
  }

  /**
   * buff 增加
   */
  public addBuff(characterId: number, buffDTO: BuffDTO) {
    let buff = this.getCharacterById(characterId);
  }
}
