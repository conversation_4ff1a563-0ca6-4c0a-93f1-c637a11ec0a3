import { _decorator, instantiate, Label, Node } from "cc";
import { ItemCost } from "db://assets/GameScrpit/game/common/ItemCost";
import { IConfigShop } from "db://assets/GameScrpit/game/JsonDefine";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { ListAdapter, ViewHolder } from "db://assets/platform/src/core/ui/adapter_view/ListAdapter";
import { ClubAudioName } from "../../../club/ClubConfig";
import { ClubModule } from "../../../club/ClubModule";
import { GoodsModule } from "../../../goods/GoodsModule";
import { PublicRouteName } from "../../../player/PlayerConstant";
import { HeroModule } from "../../../hero/HeroModule";
import { PetModule } from "../../../pet/PetModule";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import MsgEnum from "db://assets/GameScrpit/game/event/MsgEnum";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

@ccclass("ExchangeShopViewHolder")
export class ExchangeShopViewHolder extends ViewHolder {
  //
  private _data: IConfigShop;
  bindData(data: IConfigShop) {
    this._data = data;
    let buyNum = GoodsModule.data.getGoodsRedeemMsgById(this._data.id);
    this.getNode("item_cost")
      .getComponent(ItemCost)
      .setItemId(data.cointype, data.coinPrice + buyNum * (data?.priceAdd ?? 0));
    FmUtils.setItemNode(this.getNode("item_sell"), data.itemsList[0][0], data.itemsList[0][1]);
    this.checkUnLock();
    this.refreshLimit();
  }

  private checkUnLock() {
    this._data.unlockType;
    let unLockText = "";
    this.getNode("node_unlock").active = false;
    switch (this._data.unlockType) {
      case 1:
        let clubLevel = ClubModule.data.clubMessage.level;
        if (clubLevel < this._data.unlockNum) {
          this.getNode("node_unlock").active = true;
          unLockText = `战盟等级达到${this._data.unlockNum}级解锁`;
        }
        break;
      case 2:
        let isOwnedHero = HeroModule.data.getHeroMessage(this._data.unlockNum);

        if (!isOwnedHero) {
          this.getNode("node_unlock").active = true;
          let heroInfo = HeroModule.config.getHeroInfo(this._data.unlockNum);
          unLockText = `拥有战将<${heroInfo.name}>解锁`;
        }
        break;
      case 3:
        let petNum = PetModule.data.getPetNum();
        if (petNum <= this._data.unlockNum) {
          this.getNode("node_unlock").active = true;
          unLockText = `累计获得${this._data.unlockNum}只灵兽解锁`;
        }
        break;
    }
    this.getNode("lbl_unlock").getComponent(Label).string = unLockText;
  }
  private refreshLimit() {
    // 0.不限购
    // 1.每日限购
    // 2.每周限购
    // 3.每月限购
    // 4.永久限购
    // 5.活动限购
    if (this.getNode("node_unlock").active) {
      this.getNode("btn_buy").active = false;
      this.getNode("node_sell_out").active = false;
      this.getNode("lbl_limit").active = false;
      return;
    }
    let limit_num = this._data.max - GoodsModule.data.getGoodsRedeemMsgById(this._data.id);
    if (limit_num <= 0) {
      this.getNode("btn_buy").active = false;
      this.getNode("node_sell_out").active = true;
      this.getNode("lbl_limit").active = false;
      return;
    }
    this.getNode("node_sell_out").active = false;
    this.getNode("btn_buy").active = true;
    this.getNode("lbl_limit").active = true;

    switch (this._data.maxtype) {
      case 0:
        this.getNode("lbl_limit").getComponent(Label).string = "";
        break;
      case 1:
        this.getNode("lbl_limit").getComponent(Label).string = `每日限购:(${limit_num}/${this._data.max})`;
        break;
      case 2:
        this.getNode("lbl_limit").getComponent(Label).string = `每周限购:${limit_num}`;
        break;
      case 3:
        this.getNode("lbl_limit").getComponent(Label).string = `每月限购:${limit_num}`;
        break;
      case 4:
        this.getNode("lbl_limit").getComponent(Label).string = `永久限购:${limit_num}`;
        break;
      case 5:
        this.getNode("lbl_limit").getComponent(Label).string = `活动限购:${limit_num}`;
    }
  }

  private onClickBuy() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.战盟商店点击兑换);

    if (this._data.type == 11 && ClubModule.service.isInterceptOperation()) {
      return;
    }
    let numLimit = this._data.max - GoodsModule.data.getGoodsRedeemMsgById(this._data.id);
    const buyConfirm: any = {
      itemInfo: this._data.itemsList[0],
      moneyInfo: [this._data.cointype, this._data.coinPrice],
      maxNum: numLimit,
    };
    UIMgr.instance.showDialog(PublicRouteName.UIBuyConfirm, buyConfirm, (resp) => {
      if (resp.ok) {
        GoodsModule.api.redeemGoods(this._data.id, resp.num, (data: any) => {
          log.log(data);
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data });
          this.bindData(this._data);
        });
      }
    });
  }
}

export class ExchangeShopAdapter extends ListAdapter {
  private _item: Node;
  private _data: IConfigShop[];

  constructor(item: Node) {
    super();
    this._item = item;
  }
  setData(data: IConfigShop[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(ExchangeShopViewHolder).bindData(this._data[position]);
  }
  getCount(): number {
    return this._data.length;
  }
}
