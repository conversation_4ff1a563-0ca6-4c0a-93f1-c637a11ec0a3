// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Post.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface PostHarvestResponse {
  /** 偶数索引为道具的ID，奇数为数量 */
  resAddList: number[];
  /** 驿站信息 */
  postTrain: PostTrainMessage | undefined;
}

/**  */
export interface PostTrainMessage {
  /** 等级 */
  level: number;
  /** 最近一次货运结束的时间戳，如果小于当前时间，并且当日挂机次数不为0，且没有待领取的奖励，就可以随时开始下一次货运 */
  lastDeadline: number;
  /** 挂机获得奖励，需要到时间才能领取 */
  rewardList: number[];
  /** 当日已经挂机的次数 */
  dailyCount: number;
  /** 当日气运已投放的次数 */
  energyDropCount: number;
  /** 当日最大挂机次数 */
  dailyMaxCount: number;
  /** 历史货运次数 */
  historyCount: number;
}

function createBasePostHarvestResponse(): PostHarvestResponse {
  return { resAddList: [], postTrain: undefined };
}

export const PostHarvestResponse: MessageFns<PostHarvestResponse> = {
  encode(message: PostHarvestResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    if (message.postTrain !== undefined) {
      PostTrainMessage.encode(message.postTrain, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PostHarvestResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePostHarvestResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.postTrain = PostTrainMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PostHarvestResponse>, I>>(base?: I): PostHarvestResponse {
    return PostHarvestResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PostHarvestResponse>, I>>(object: I): PostHarvestResponse {
    const message = createBasePostHarvestResponse();
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.postTrain = (object.postTrain !== undefined && object.postTrain !== null)
      ? PostTrainMessage.fromPartial(object.postTrain)
      : undefined;
    return message;
  },
};

function createBasePostTrainMessage(): PostTrainMessage {
  return {
    level: 0,
    lastDeadline: 0,
    rewardList: [],
    dailyCount: 0,
    energyDropCount: 0,
    dailyMaxCount: 0,
    historyCount: 0,
  };
}

export const PostTrainMessage: MessageFns<PostTrainMessage> = {
  encode(message: PostTrainMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.level !== 0) {
      writer.uint32(8).int32(message.level);
    }
    if (message.lastDeadline !== 0) {
      writer.uint32(16).int64(message.lastDeadline);
    }
    writer.uint32(26).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    if (message.dailyCount !== 0) {
      writer.uint32(32).int32(message.dailyCount);
    }
    if (message.energyDropCount !== 0) {
      writer.uint32(40).int32(message.energyDropCount);
    }
    if (message.dailyMaxCount !== 0) {
      writer.uint32(48).int32(message.dailyMaxCount);
    }
    if (message.historyCount !== 0) {
      writer.uint32(56).int32(message.historyCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PostTrainMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePostTrainMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lastDeadline = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag === 25) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.dailyCount = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.energyDropCount = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.dailyMaxCount = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.historyCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<PostTrainMessage>, I>>(base?: I): PostTrainMessage {
    return PostTrainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PostTrainMessage>, I>>(object: I): PostTrainMessage {
    const message = createBasePostTrainMessage();
    message.level = object.level ?? 0;
    message.lastDeadline = object.lastDeadline ?? 0;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    message.dailyCount = object.dailyCount ?? 0;
    message.energyDropCount = object.energyDropCount ?? 0;
    message.dailyMaxCount = object.dailyMaxCount ?? 0;
    message.historyCount = object.historyCount ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
