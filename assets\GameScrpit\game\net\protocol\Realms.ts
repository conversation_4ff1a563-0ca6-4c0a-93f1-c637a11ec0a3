// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Realms.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RewardMessage } from "./Comm";

export const protobufPackage = "sim";

/**  */
export interface RealmsFightResponse {
  /** 是否胜利 */
  win: boolean;
  /** Boss关卡战斗回放 */
  replay: string;
  /** 已经扫荡的次数 */
  sweepCount: number;
  /** 已经击败的最高关卡ID */
  maxConquerId: number;
  /** 奖励 */
  rewardMessage: RewardMessage | undefined;
}

/**  */
export interface RealmsMessage {
  /** 已经解锁的最高关卡ID */
  maxUnlockId: number;
  /** 已经击败的最高关卡ID(也是可以扫荡的关卡) */
  maxConquerId: number;
  /** 已经扫荡的次数 */
  sweepCount: number;
  /** 是否领取本服一键点赞排行榜奖励 */
  takeCurServerRankReward: boolean;
}

/**  */
export interface RealmsRedeemRequest {
  /** 兑换的商品主键ID */
  redeemId: number;
  /** 购买数量 */
  count: number;
}

/**  */
export interface RealmsRedeemResponse {
  /** 已经兑换的道具ID 和 数量 (在对应的限制类型周期内) */
  redeemMap: { [key: number]: number };
  /**  */
  rewardMessage: RewardMessage | undefined;
}

export interface RealmsRedeemResponse_RedeemMapEntry {
  key: number;
  value: number;
}

function createBaseRealmsFightResponse(): RealmsFightResponse {
  return { win: false, replay: "", sweepCount: 0, maxConquerId: 0, rewardMessage: undefined };
}

export const RealmsFightResponse: MessageFns<RealmsFightResponse> = {
  encode(message: RealmsFightResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.win !== false) {
      writer.uint32(8).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(18).string(message.replay);
    }
    if (message.sweepCount !== 0) {
      writer.uint32(24).int32(message.sweepCount);
    }
    if (message.maxConquerId !== 0) {
      writer.uint32(32).int64(message.maxConquerId);
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RealmsFightResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRealmsFightResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.sweepCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.maxConquerId = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RealmsFightResponse>, I>>(base?: I): RealmsFightResponse {
    return RealmsFightResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RealmsFightResponse>, I>>(object: I): RealmsFightResponse {
    const message = createBaseRealmsFightResponse();
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    message.sweepCount = object.sweepCount ?? 0;
    message.maxConquerId = object.maxConquerId ?? 0;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseRealmsMessage(): RealmsMessage {
  return { maxUnlockId: 0, maxConquerId: 0, sweepCount: 0, takeCurServerRankReward: false };
}

export const RealmsMessage: MessageFns<RealmsMessage> = {
  encode(message: RealmsMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.maxUnlockId !== 0) {
      writer.uint32(8).int64(message.maxUnlockId);
    }
    if (message.maxConquerId !== 0) {
      writer.uint32(16).int64(message.maxConquerId);
    }
    if (message.sweepCount !== 0) {
      writer.uint32(24).int32(message.sweepCount);
    }
    if (message.takeCurServerRankReward !== false) {
      writer.uint32(32).bool(message.takeCurServerRankReward);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RealmsMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRealmsMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.maxUnlockId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.maxConquerId = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.sweepCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.takeCurServerRankReward = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RealmsMessage>, I>>(base?: I): RealmsMessage {
    return RealmsMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RealmsMessage>, I>>(object: I): RealmsMessage {
    const message = createBaseRealmsMessage();
    message.maxUnlockId = object.maxUnlockId ?? 0;
    message.maxConquerId = object.maxConquerId ?? 0;
    message.sweepCount = object.sweepCount ?? 0;
    message.takeCurServerRankReward = object.takeCurServerRankReward ?? false;
    return message;
  },
};

function createBaseRealmsRedeemRequest(): RealmsRedeemRequest {
  return { redeemId: 0, count: 0 };
}

export const RealmsRedeemRequest: MessageFns<RealmsRedeemRequest> = {
  encode(message: RealmsRedeemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.redeemId !== 0) {
      writer.uint32(8).int64(message.redeemId);
    }
    if (message.count !== 0) {
      writer.uint32(16).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RealmsRedeemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRealmsRedeemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.redeemId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RealmsRedeemRequest>, I>>(base?: I): RealmsRedeemRequest {
    return RealmsRedeemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RealmsRedeemRequest>, I>>(object: I): RealmsRedeemRequest {
    const message = createBaseRealmsRedeemRequest();
    message.redeemId = object.redeemId ?? 0;
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseRealmsRedeemResponse(): RealmsRedeemResponse {
  return { redeemMap: {}, rewardMessage: undefined };
}

export const RealmsRedeemResponse: MessageFns<RealmsRedeemResponse> = {
  encode(message: RealmsRedeemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.redeemMap).forEach(([key, value]) => {
      RealmsRedeemResponse_RedeemMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RealmsRedeemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRealmsRedeemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = RealmsRedeemResponse_RedeemMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.redeemMap[entry1.key] = entry1.value;
          }
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RealmsRedeemResponse>, I>>(base?: I): RealmsRedeemResponse {
    return RealmsRedeemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RealmsRedeemResponse>, I>>(object: I): RealmsRedeemResponse {
    const message = createBaseRealmsRedeemResponse();
    message.redeemMap = Object.entries(object.redeemMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

function createBaseRealmsRedeemResponse_RedeemMapEntry(): RealmsRedeemResponse_RedeemMapEntry {
  return { key: 0, value: 0 };
}

export const RealmsRedeemResponse_RedeemMapEntry: MessageFns<RealmsRedeemResponse_RedeemMapEntry> = {
  encode(message: RealmsRedeemResponse_RedeemMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RealmsRedeemResponse_RedeemMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRealmsRedeemResponse_RedeemMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<RealmsRedeemResponse_RedeemMapEntry>, I>>(
    base?: I,
  ): RealmsRedeemResponse_RedeemMapEntry {
    return RealmsRedeemResponse_RedeemMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RealmsRedeemResponse_RedeemMapEntry>, I>>(
    object: I,
  ): RealmsRedeemResponse_RedeemMapEntry {
    const message = createBaseRealmsRedeemResponse_RedeemMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
