{"0": {"id": 0, "lvNeed": 5, "expNeed": 20, "lvExpList": [[1, 5950], [2, 350], [3, 350], [4, 350], [5, 800], [6, 350], [7, 350], [8, 350], [9, 350], [10, 800]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 3]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 3850], [2, 750], [3, 300]]}, "1": {"id": 1, "lvNeed": 10, "expNeed": 40, "lvExpList": [[1, 6500], [2, 300], [3, 300], [4, 300], [5, 700], [6, 300], [7, 300], [8, 300], [9, 300], [10, 700]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 5]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 7700], [2, 1500], [3, 600]]}, "2": {"id": 2, "lvNeed": 15, "expNeed": 60, "lvExpList": [[1, 7050], [2, 250], [3, 250], [4, 250], [5, 600], [6, 250], [7, 250], [8, 250], [9, 250], [10, 600]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 10]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 11550], [2, 2250], [3, 900]]}, "3": {"id": 3, "lvNeed": 20, "expNeed": 80, "lvExpList": [[1, 7600], [2, 200], [3, 200], [4, 200], [5, 500], [6, 200], [7, 200], [8, 200], [9, 200], [10, 500]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 20]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 15400], [2, 3000], [3, 1200]]}, "4": {"id": 4, "lvNeed": 25, "expNeed": 100, "lvExpList": [[1, 8150], [2, 150], [3, 150], [4, 150], [5, 400], [6, 150], [7, 150], [8, 150], [9, 150], [10, 400]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 40]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 19250], [2, 3750], [3, 1500]]}, "5": {"id": 5, "lvNeed": 30, "expNeed": 120, "lvExpList": [[1, 8560], [2, 120], [3, 120], [4, 120], [5, 300], [6, 120], [7, 120], [8, 120], [9, 120], [10, 300]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 60]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 23100], [2, 4500], [3, 1800]]}, "6": {"id": 6, "lvNeed": 40, "expNeed": 140, "lvExpList": [[1, 8900], [2, 100], [3, 100], [4, 100], [5, 200], [6, 100], [7, 100], [8, 100], [9, 100], [10, 200]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 80]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 26950], [2, 5250], [3, 2100]]}, "7": {"id": 7, "lvNeed": 60, "expNeed": 160, "lvExpList": [[1, 9010], [2, 90], [3, 90], [4, 90], [5, 180], [6, 90], [7, 90], [8, 90], [9, 90], [10, 180]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 120]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 30800], [2, 6000], [3, 2400]]}, "8": {"id": 8, "lvNeed": 80, "expNeed": 180, "lvExpList": [[1, 9120], [2, 80], [3, 80], [4, 80], [5, 160], [6, 80], [7, 80], [8, 80], [9, 80], [10, 160]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 160]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 34650], [2, 6750], [3, 2700]]}, "9": {"id": 9, "lvNeed": 100, "expNeed": 200, "lvExpList": [[1, 9230], [2, 70], [3, 70], [4, 70], [5, 140], [6, 70], [7, 70], [8, 70], [9, 70], [10, 140]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 200]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 38500], [2, 7500], [3, 3000]]}, "10": {"id": 10, "lvNeed": 150, "expNeed": 250, "lvExpList": [[1, 9340], [2, 60], [3, 60], [4, 60], [5, 120], [6, 60], [7, 60], [8, 60], [9, 60], [10, 120]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 250]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 42350], [2, 8250], [3, 3300]]}, "11": {"id": 11, "lvNeed": 200, "expNeed": 300, "lvExpList": [[1, 9450], [2, 50], [3, 50], [4, 50], [5, 100], [6, 50], [7, 50], [8, 50], [9, 50], [10, 100]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 300]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 46200], [2, 9000], [3, 3600]]}, "12": {"id": 12, "lvNeed": 250, "expNeed": 350, "lvExpList": [[1, 9560], [2, 40], [3, 40], [4, 40], [5, 80], [6, 40], [7, 40], [8, 40], [9, 40], [10, 80]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 350]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 50050], [2, 9750], [3, 3900]]}, "13": {"id": 13, "lvNeed": 300, "expNeed": 400, "lvExpList": [[1, 9670], [2, 30], [3, 30], [4, 30], [5, 60], [6, 30], [7, 30], [8, 30], [9, 30], [10, 60]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 400]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 53900], [2, 10500], [3, 4200]]}, "14": {"id": 14, "lvNeed": 350, "expNeed": 450, "lvExpList": [[1, 9780], [2, 20], [3, 20], [4, 20], [5, 40], [6, 20], [7, 20], [8, 20], [9, 20], [10, 40]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 450]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 57750], [2, 11250], [3, 4500]]}, "15": {"id": 15, "lvNeed": 400, "expNeed": 500, "lvExpList": [[1, 9890], [2, 10], [3, 10], [4, 10], [5, 20], [6, 10], [7, 10], [8, 10], [9, 10], [10, 20]], "lvCostList": [[1075, 1]], "breakCostList": [[1076, 500]], "expAddList": [[1, 396], [2, 78], [3, 29]], "breakAddList": [[1, 61600], [2, 12000], [3, 4800]]}, "-1": {"id": -1, "lvNeed": 0, "expNeed": 0, "lvExpList": [], "lvCostList": [], "breakCostList": [], "expAddList": [], "breakAddList": [], "firstList": [[1, 800], [2, 200], [3, 100]]}}