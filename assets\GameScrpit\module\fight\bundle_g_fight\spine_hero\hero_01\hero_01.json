{"skeleton": {"hash": "dkA0kQIkQJJG+/2jqyrzUGAWaww=", "spine": "3.8.75", "x": -73.21, "y": -1.85, "width": 165.62, "height": 201.98, "images": "./images/", "audio": "D:/spine导出/Z_新主角/主角1"}, "bones": [{"name": "root"}, {"name": "qian_zj_all", "parent": "root", "length": 113.37, "rotation": 0.39, "x": -27.59, "y": -17.83}, {"name": "qian_zj_1", "parent": "qian_zj_all", "length": 49.78, "rotation": -0.09, "x": 44.92, "y": 119.15, "color": "09ff00ff"}, {"name": "qian_zj_2", "parent": "qian_zj_1", "length": 21.24, "rotation": 97.99, "x": -3.87, "y": 2.33, "color": "09ff00ff"}, {"name": "qian_zj_3", "parent": "qian_zj_2", "length": 23.12, "rotation": 4.05, "x": 21.24, "color": "09ff00ff"}, {"name": "qian_zj_4", "parent": "qian_zj_3", "length": 13.07, "rotation": -4.97, "x": 22.79, "y": -0.35, "color": "09ff00ff"}, {"name": "qian_zj_5", "parent": "qian_zj_3", "rotation": 8.92, "x": 10.51, "y": -10.47, "color": "09ff00ff"}, {"name": "qian_zj_52", "parent": "qian_zj_1", "x": -3.63, "y": 15.95, "color": "fdff00ff"}, {"name": "qian_zj_6", "parent": "qian_zj_3", "rotation": 8.92, "x": 21.69, "y": 22.62, "color": "09ff00ff"}, {"name": "qian_zj_7", "parent": "qian_zj_6", "length": 24.39, "rotation": 132.18, "x": -4.96, "y": 3.4, "color": "09ff00ff"}, {"name": "qian_zj_8", "parent": "qian_zj_7", "length": 30.97, "rotation": 39.47, "x": 24.39, "color": "09ff00ff"}, {"name": "qian_zj_9", "parent": "qian_zj_8", "length": 13.15, "rotation": 14.18, "x": 30.97, "color": "09ff00ff"}, {"name": "qian_zj_10", "parent": "qian_zj_9", "length": 28.22, "rotation": 80.75, "x": 8.88, "y": 1.88, "scaleX": 1.1113, "scaleY": 1.1113, "color": "09ff00ff"}, {"name": "qian_zj_11", "parent": "qian_zj_5", "length": 23.36, "rotation": -169.01, "x": -1.76, "y": 1.89, "color": "09ff00ff"}, {"name": "qian_zj_12", "parent": "qian_zj_11", "length": 28.96, "rotation": -4.74, "x": 23.36, "color": "09ff00ff"}, {"name": "qian_zj_13", "parent": "qian_zj_12", "length": 10.42, "rotation": -4.31, "x": 28.96, "color": "09ff00ff"}, {"name": "qian_zj_14", "parent": "qian_zj_1", "length": 12.98, "rotation": -95.67, "x": -4.87, "y": -1.92, "color": "09ff00ff"}, {"name": "qian_zj_15", "parent": "qian_zj_1", "length": 53.47, "rotation": -105.79, "x": -9.73, "y": -2.36, "color": "09ff00ff"}, {"name": "qian_zj_16", "parent": "qian_zj_15", "length": 41.75, "rotation": -5.18, "x": 53.47, "color": "09ff00ff"}, {"name": "qian_zj_17", "parent": "qian_zj_16", "length": 10.09, "rotation": -109.3, "x": 41.75, "transform": "onlyTranslation", "color": "0014ffff"}, {"name": "qian_zj_18", "parent": "qian_zj_1", "length": 48.03, "rotation": -73.42, "x": 0.76, "y": -2.1, "color": "09ff00ff"}, {"name": "qian_zj_21", "parent": "qian_zj_1", "length": 54.83, "rotation": -55.9, "x": 4.62, "y": -2.42, "color": "ff0000ff"}, {"name": "qian_zj_22", "parent": "qian_zj_21", "length": 55.24, "rotation": -75.57, "x": 54.83, "color": "ff0000ff"}, {"name": "qian_zj_31", "parent": "qian_zj_4", "rotation": -7.36, "x": 9.48, "y": -7.78, "color": "f3ff00ff"}, {"name": "qian_zj_61", "parent": "qian_zj_4", "rotation": -7.36, "x": 32.1, "y": -12.97, "color": "f3ff00ff"}, {"name": "qian_zj_23", "parent": "qian_zj_61", "length": 7.39, "rotation": 60.59, "x": 3.81, "y": 3.65, "color": "09ff00ff"}, {"name": "qian_zj_24", "parent": "qian_zj_23", "length": 8.31, "rotation": 83.93, "x": 7.43, "y": 0.16, "color": "09ff00ff"}, {"name": "qian_zj_25", "parent": "qian_zj_24", "length": 8.26, "rotation": 0.35, "x": 8.39, "y": -0.09, "color": "09ff00ff"}, {"name": "qian_zj_26", "parent": "qian_zj_25", "length": 9.22, "rotation": 5.62, "x": 8.26, "color": "09ff00ff"}, {"name": "qian_zj_27", "parent": "qian_zj_61", "length": 6.75, "rotation": -62.1, "x": 0.95, "y": -1.62, "color": "09ff00ff"}, {"name": "qian_zj_28", "parent": "qian_zj_27", "length": 5.98, "rotation": -68.13, "x": 6.75, "color": "09ff00ff"}, {"name": "qian_zj_29", "parent": "qian_zj_28", "length": 8.85, "rotation": -42.17, "x": 5.98, "color": "09ff00ff"}, {"name": "qian_zj_30", "parent": "qian_zj_29", "length": 6.3, "rotation": 14.21, "x": 8.85, "color": "09ff00ff"}, {"name": "qian_zj_32", "parent": "qian_zj_4", "rotation": -7.36, "x": 17.9, "y": -9.23, "color": "00ffd2ff"}, {"name": "qian_zj_34", "parent": "qian_zj_4", "length": 7.95, "rotation": 40.93, "x": 40.71, "y": -10.01, "color": "09ff00ff"}, {"name": "qian_zj_35", "parent": "qian_zj_34", "length": 10.58, "rotation": 127.5, "x": 12.63, "y": 12.47, "color": "ff0000ff"}, {"name": "qian_zj_36", "parent": "qian_zj_35", "length": 11.7, "rotation": -8.37, "x": 10.58, "color": "ff0000ff"}, {"name": "qian_zj_37", "parent": "qian_zj_36", "length": 13.85, "rotation": -2.14, "x": 11.7, "color": "ff0000ff"}, {"name": "qian_zj_38", "parent": "qian_zj_37", "length": 11.94, "rotation": -1.47, "x": 13.74, "y": -0.15, "color": "ff0000ff"}, {"name": "qian_zj_39", "parent": "qian_zj_1", "length": 12.44, "rotation": -135.34, "x": -19.19, "y": -0.82, "color": "0029ffff"}, {"name": "qian_zj_40", "parent": "qian_zj_39", "length": 12.44, "x": 12.44, "color": "0029ffff"}, {"name": "qian_zj_41", "parent": "qian_zj_40", "length": 12.44, "x": 12.44, "color": "0029ffff"}, {"name": "qian_zj_42", "parent": "qian_zj_41", "length": 12.44, "rotation": -1.79, "x": 12.44, "color": "0029ffff"}, {"name": "qian_zj_43", "parent": "qian_zj_1", "length": 13.74, "rotation": -113.27, "x": -7.73, "y": -3.62, "color": "0029ffff"}, {"name": "qian_zj_44", "parent": "qian_zj_43", "length": 13.74, "x": 13.74, "color": "0029ffff"}, {"name": "qian_zj_45", "parent": "qian_zj_44", "length": 13.74, "x": 13.74, "color": "0029ffff"}, {"name": "qian_zj_46", "parent": "qian_zj_45", "length": 13.74, "x": 13.74, "color": "0029ffff"}, {"name": "qian_zj_47", "parent": "qian_zj_1", "length": 10.95, "rotation": -83.37, "x": 7.51, "y": -1.91, "color": "0033ffff"}, {"name": "qian_zj_48", "parent": "qian_zj_47", "length": 10.95, "x": 10.95, "color": "0033ffff"}, {"name": "qian_zj_49", "parent": "qian_zj_48", "length": 10.95, "x": 10.95, "color": "0033ffff"}, {"name": "qian_zj_50", "parent": "qian_zj_49", "length": 10.95, "x": 10.95, "color": "0033ffff"}, {"name": "qian_zj_51", "parent": "qian_zj_50", "length": 10.95, "x": 10.95, "color": "0033ffff"}, {"name": "qian_zj_53", "parent": "qian_zj_1", "x": -3.96, "y": 23.69, "color": "0029ffff"}, {"name": "qian_zj_54", "parent": "qian_zj_1", "length": 56, "rotation": -81.08, "x": -9.38, "y": -2.45, "color": "ff0000ff"}, {"name": "qian_zj_55", "parent": "qian_zj_54", "length": 51.92, "rotation": -56.54, "x": 55.97, "y": -0.16, "color": "ff0000ff"}, {"name": "qian_zj_62", "parent": "qian_zj_1", "x": -11.77, "y": 1.85, "color": "09ff00ff"}, {"name": "L_jioall2", "parent": "qian_zj_62", "length": 48.03, "rotation": -73.29, "x": -2.29, "y": -2.22, "color": "09ff00ff"}, {"name": "qian_zj_57", "parent": "L_jioall2", "length": 43, "rotation": -38.53, "x": 49.08, "y": -0.65, "color": "09ff00ff"}, {"name": "qian_zj_58", "parent": "qian_zj_57", "length": 15.99, "rotation": 87.4, "x": 43.65, "y": 0.34, "color": "09ff00ff"}, {"name": "qian_zj_59", "parent": "qian_zj_62", "length": 54.83, "rotation": -55.9, "x": 1.31, "y": -3.52, "color": "ff0000ff"}, {"name": "qian_zj_60", "parent": "qian_zj_59", "length": 55.24, "rotation": -75.57, "x": 54.83, "color": "ff0000ff"}, {"name": "qian_zj_19", "parent": "qian_zj_18", "length": 44.44, "rotation": -38.13, "x": 47.89, "y": -0.08, "color": "09ff00ff"}, {"name": "qian_zj_20", "parent": "qian_zj_19", "length": 16.38, "rotation": 86.36, "x": 44.34, "y": -0.04, "color": "09ff00ff"}, {"name": "R_jio1", "parent": "qian_zj_all", "rotation": -0.39, "x": 59.07, "y": 71.32, "color": "ff3f00ff"}, {"name": "R_jio2", "parent": "qian_zj_all", "rotation": -0.39, "x": 41.94, "y": 29.97, "color": "ff3f00ff"}, {"name": "R_jio3", "parent": "R_jio2", "x": 14.98, "y": -3.68, "transform": "noScale", "color": "ff3f00ff"}, {"name": "L_jio1", "parent": "qian_zj_all", "rotation": -0.39, "x": 20.37, "y": 66.08, "color": "ff3f00ff"}, {"name": "L_jio2", "parent": "qian_zj_all", "rotation": -0.39, "x": 4.65, "y": 26.89, "color": "0014ffff"}, {"name": "R1_jio1", "parent": "qian_zj_all", "rotation": -0.39, "x": 44.34, "y": 71.67, "color": "ff3f00ff"}, {"name": "R1_jio2", "parent": "qian_zj_all", "rotation": -0.39, "x": 15.08, "y": 28.56, "color": "ff3f00ff"}, {"name": "R1_jio3", "parent": "R1_jio2", "x": 14.62, "y": -6.66, "transform": "noScale", "color": "ff3f00ff"}, {"name": "bone", "parent": "root", "length": 102, "rotation": 1.58, "x": -300.93, "y": -25.75, "color": "ff0000ff"}, {"name": "bone2", "parent": "bone", "length": 46.36, "rotation": 1.4, "x": 22.27, "y": 115.98, "transform": "noRotationOrReflection", "color": "ff0000ff"}, {"name": "bone4", "parent": "bone2", "length": 25.95, "rotation": 67.53, "x": -3.25, "y": 2.94, "color": "ff0000ff"}, {"name": "bone5", "parent": "bone4", "length": 24.35, "rotation": 25.41, "x": 25.95, "color": "ff0000ff"}, {"name": "bone36", "parent": "bone5", "x": 58.9, "y": -2.89, "color": "ff0000ff"}, {"name": "bone7", "parent": "bone36", "length": 16.22, "rotation": -179.36, "x": -1.12, "y": -16.08, "color": "ff0000ff"}, {"name": "bone8", "parent": "bone7", "length": 17.74, "rotation": 13.35, "x": 16.22, "color": "ff0000ff"}, {"name": "bone9", "parent": "bone8", "length": 14.7, "rotation": -18.64, "x": 17.74, "color": "ff0000ff"}, {"name": "bone10", "parent": "bone5", "x": 6.94, "y": -14.22, "color": "ff0000ff"}, {"name": "bone11", "parent": "bone5", "x": 8.67, "y": 15.95, "color": "ff0000ff"}, {"name": "bone12", "parent": "bone11", "length": 29.79, "rotation": 156.54, "x": -2.47, "y": 2.57, "color": "ff0000ff"}, {"name": "bone13", "parent": "bone12", "length": 31.8, "rotation": -16.55, "x": 29.79, "color": "ff0000ff"}, {"name": "bone14", "parent": "bone13", "length": 10.62, "rotation": 28.02, "x": 31.93, "y": -0.18, "color": "ff0000ff"}, {"name": "bone15", "parent": "bone10", "length": 23.91, "rotation": -155.25, "x": -2.81, "y": -2.67, "color": "ff0000ff"}, {"name": "bone16", "parent": "bone15", "length": 28.01, "rotation": -44.33, "x": 23.91, "color": "ff0000ff"}, {"name": "bone17", "parent": "bone16", "length": 10.93, "rotation": -16.85, "x": 28.01, "color": "ff0000ff"}, {"name": "bone18", "parent": "bone2", "length": 38.93, "rotation": -107.76, "x": -8.33, "y": -13.52, "color": "ff0000ff"}, {"name": "bone19", "parent": "bone18", "length": 48.23, "rotation": 30.67, "x": 37.61, "y": -0.17, "color": "ff0000ff"}, {"name": "bone20", "parent": "bone19", "length": 19.07, "rotation": -82.1, "x": 48.23, "color": "ff0000ff"}, {"name": "bone21", "parent": "bone2", "length": 39.1, "rotation": -86.26, "x": 7.36, "y": -16.22, "color": "ff0000ff"}, {"name": "bone22", "parent": "bone21", "length": 38.69, "rotation": 22.07, "x": 38.67, "y": 0.03, "color": "ff0000ff"}, {"name": "bone23", "parent": "bone22", "length": 9.78, "rotation": -73.68, "x": 38.69, "transform": "noRotationOrReflection", "color": "ff0000ff"}, {"name": "bone3", "parent": "bone2", "length": 12.26, "rotation": -87.78, "x": -3.72, "y": 0.91, "color": "ff0000ff"}, {"name": "qian_zj_56", "parent": "bone17", "length": 28.22, "rotation": -75.71, "x": 3.14, "y": 2.57, "scaleX": 1.1113, "scaleY": 1.1113, "color": "ff0000ff"}, {"name": "bone24", "parent": "bone2", "length": 11.34, "rotation": -48.62, "x": 13.46, "y": -7.08, "color": "ff7800ff"}, {"name": "bone25", "parent": "bone2", "length": 13.15, "rotation": -69.61, "x": 2.38, "y": -6.47, "color": "ff7800ff"}, {"name": "bone26", "parent": "bone24", "length": 11.34, "x": 11.34, "color": "ff7800ff"}, {"name": "bone27", "parent": "bone26", "length": 11.34, "rotation": 8.84, "x": 11.34, "color": "ff7800ff"}, {"name": "bone28", "parent": "bone27", "length": 11.34, "rotation": 8.93, "x": 11.34, "color": "ff7800ff"}, {"name": "bone29", "parent": "bone25", "length": 13.15, "x": 13.15, "color": "ff7800ff"}, {"name": "bone30", "parent": "bone29", "length": 13.15, "x": 13.15, "color": "ff7800ff"}, {"name": "bone31", "parent": "bone30", "length": 13.15, "x": 13.15, "color": "ff7800ff"}, {"name": "bone32", "parent": "bone2", "length": 13.65, "rotation": -111.94, "x": -14.6, "y": 0.31, "color": "ff7800ff"}, {"name": "bone33", "parent": "bone32", "length": 13.65, "rotation": 3.67, "x": 13.65, "color": "ff7800ff"}, {"name": "bone34", "parent": "bone33", "length": 13.65, "rotation": 3.75, "x": 13.65, "color": "ff7800ff"}, {"name": "bone35", "parent": "bone34", "length": 13.65, "rotation": 4.07, "x": 13.65, "color": "ff7800ff"}, {"name": "H_jio1", "parent": "bone", "rotation": -1.58, "x": 11.28, "y": 17.83, "color": "fffc00ff"}, {"name": "H_jio2", "parent": "bone", "rotation": -1.58, "x": 47.93, "y": 26.17, "color": "fffc00ff"}, {"name": "H_jio3", "parent": "H_jio1", "x": -17.88, "y": -6.72, "color": "fffc00ff"}, {"name": "qian_zj_33", "parent": "qian_zj_4", "length": 15.22, "rotation": 179.95, "x": 39.55, "y": -17.62, "color": "ff0000ff"}, {"name": "qian_zj_63", "parent": "qian_zj_33", "length": 15.18, "rotation": -1.15, "x": 15.21, "y": -0.1, "color": "ff0000ff"}, {"name": "qian_zj_64", "parent": "qian_zj_63", "length": 12.99, "rotation": 6.99, "x": 15.18, "color": "ff0000ff"}, {"name": "qian_zj_65", "parent": "qian_zj_64", "length": 13.43, "rotation": 7.75, "x": 12.99, "color": "ff0000ff"}, {"name": "qian_zj_66", "parent": "qian_zj_4", "length": 23.58, "rotation": 153.6, "x": 3.36, "y": 14.05, "color": "ffe800ff"}, {"name": "qian_zj_67", "parent": "qian_zj_66", "length": 24.73, "rotation": -30.6, "x": 23.58, "color": "ffe800ff"}, {"name": "qian_zj_68", "parent": "qian_zj_67", "length": 20.82, "rotation": -34.74, "x": 24.73, "color": "ffe800ff"}, {"name": "qian_zj_69", "parent": "qian_zj_68", "length": 16.82, "rotation": -2.95, "x": 20.82, "color": "ffe800ff"}, {"name": "qian_zj_70", "parent": "qian_zj_4", "length": 22.57, "rotation": 161.4, "x": 0.44, "y": 11.1, "color": "ffe800ff"}, {"name": "qian_zj_71", "parent": "qian_zj_70", "length": 24.26, "rotation": -12.02, "x": 22.57, "color": "ffe800ff"}, {"name": "qian_zj_72", "parent": "qian_zj_71", "length": 20.1, "rotation": -30.33, "x": 24.26, "color": "ffe800ff"}, {"name": "bone6", "parent": "bone5", "length": 23.99, "rotation": -165.9, "x": 21.4, "y": -6.71, "color": "61ff00ff"}, {"name": "bone37", "parent": "bone6", "length": 26.93, "rotation": 35.93, "x": 23.99, "color": "61ff00ff"}, {"name": "bone38", "parent": "bone37", "length": 28.14, "rotation": 27.04, "x": 26.93, "color": "61ff00ff"}, {"name": "bone39", "parent": "bone5", "length": 28.46, "rotation": -176, "x": 21.42, "y": -2.05, "color": "61ff00ff"}, {"name": "bone40", "parent": "bone39", "length": 21.06, "rotation": 19.39, "x": 28.46, "color": "61ff00ff"}, {"name": "bone41", "parent": "bone40", "length": 26.16, "rotation": 29.57, "x": 21.19, "y": 0.25, "color": "61ff00ff"}, {"name": "bone42", "parent": "bone36", "length": 19.67, "rotation": 161.38, "x": 3.56, "y": 11.08, "color": "ff0000ff"}, {"name": "bone43", "parent": "bone42", "length": 18.36, "rotation": 2.4, "x": 19.67, "color": "ff0000ff"}, {"name": "bone44", "parent": "bone43", "length": 18.61, "rotation": 5.27, "x": 18.36, "color": "ff0000ff"}, {"name": "bone45", "parent": "bone5", "length": 14.69, "rotation": 50.28, "x": 57.89, "y": 2.4, "color": "ff0000ff"}, {"name": "bone46", "parent": "bone45", "length": 13.26, "rotation": 103.9, "x": 14.8, "y": 0.08, "color": "ff0000ff"}, {"name": "bone47", "parent": "bone46", "length": 16.95, "rotation": 4.99, "x": 13.26, "color": "ff0000ff"}, {"name": "hurt", "parent": "qian_zj_all", "x": 44.24, "y": 145.05, "color": "ff0000ff"}, {"name": "status", "parent": "root", "x": 5.41, "y": 198.12, "color": "ff0000ff"}, {"name": "blood_bar", "parent": "qian_zj_all", "x": 29.23, "y": 247.68, "color": "ff0000ff"}, {"name": "spell_to", "parent": "qian_zj_all", "x": 187.05, "y": 86.79, "color": "ff0000ff"}, {"name": "attack2_to", "parent": "qian_zj_all", "x": 295.79, "y": 110.86, "color": "ff0000ff"}, {"name": "skill1_to", "parent": "qian_zj_all", "x": 229.79, "y": 112.04, "color": "ff0000ff"}, {"name": "attack1_to", "parent": "qian_zj_all", "x": 295, "y": 152.09, "color": "ff0000ff"}, {"name": "qian_zj_73", "parent": "bone17", "length": 28.22, "rotation": 161.56, "x": 3.14, "y": 2.57, "scaleX": 1.1113, "scaleY": 1.1113, "transform": "noRotationOrReflection", "color": "ff0000ff"}, {"name": "qian_zj_74", "parent": "bone5", "rotation": -3.69, "x": 44.56, "y": -21.22, "color": "f3ff00ff"}, {"name": "qian_zj_75", "parent": "qian_zj_74", "length": 7.39, "rotation": 60.59, "x": 3.81, "y": 3.65, "color": "09ff00ff"}, {"name": "qian_zj_76", "parent": "qian_zj_75", "length": 8.31, "rotation": 83.93, "x": 7.43, "y": 0.16, "color": "09ff00ff"}, {"name": "qian_zj_77", "parent": "qian_zj_76", "length": 8.26, "rotation": 0.35, "x": 8.39, "y": -0.09, "color": "09ff00ff"}, {"name": "qian_zj_78", "parent": "qian_zj_77", "length": 9.22, "rotation": 5.62, "x": 8.26, "color": "09ff00ff"}, {"name": "qian_zj_79", "parent": "qian_zj_74", "length": 6.75, "rotation": -92.89, "x": 2.38, "y": 3.59, "color": "09ff00ff"}, {"name": "qian_zj_80", "parent": "qian_zj_79", "length": 5.98, "rotation": -68.13, "x": 6.75, "color": "09ff00ff"}, {"name": "qian_zj_81", "parent": "qian_zj_80", "length": 8.85, "rotation": -42.17, "x": 5.98, "color": "09ff00ff"}, {"name": "qian_zj_82", "parent": "qian_zj_81", "length": 6.3, "rotation": 14.21, "x": 8.85, "color": "09ff00ff"}], "slots": [{"name": "rootcut", "bone": "root"}, {"name": "Q_zj_008", "bone": "qian_zj_4", "attachment": "Q_zj_008"}, {"name": "Q_zj_003_2", "bone": "root", "attachment": "Q_zj_003_2"}, {"name": "Q_zj_006_1", "bone": "root", "attachment": "Q_zj_006_1"}, {"name": "Q_zj_1", "bone": "qian_zj_11", "attachment": "Q_zj_001"}, {"name": "Q_zj_2", "bone": "qian_zj_18", "attachment": "Q_zj_002"}, {"name": "Q_zj_3", "bone": "qian_zj_18"}, {"name": "L_jioall2", "bone": "L_jioall2"}, {"name": "Q_zj_008_1", "bone": "root", "attachment": "Q_zj_008_1"}, {"name": "Q_zj_005_1", "bone": "root", "attachment": "Q_zj_005_1"}, {"name": "Q_zj_003", "bone": "qian_zj_1", "attachment": "Q_zj_003"}, {"name": "L_jioall1", "bone": "qian_zj_15", "attachment": "Q_zj_4"}, {"name": "Q_zj_006", "bone": "qian_zj_47", "attachment": "Q_zj_006"}, {"name": "Q_zj_005", "bone": "qian_zj_39", "attachment": "Q_zj_005"}, {"name": "Q_zj_007", "bone": "root", "attachment": "Q_zj_007"}, {"name": "Q_zj_0010", "bone": "qian_zj_4", "attachment": "Q_zj_0010"}, {"name": "Q_zj_0011", "bone": "qian_zj_4"}, {"name": "Q_zj_eye011", "bone": "qian_zj_4"}, {"name": "Q_zj_0012", "bone": "qian_zj_9", "attachment": "Q_zj_0012"}, {"name": "Q_zj_0013", "bone": "qian_zj_10", "attachment": "Q_zj_0013"}, {"name": "Q_zj_14", "bone": "qian_zj_10", "blend": "additive"}, {"name": "Q_zj_13", "bone": "qian_zj_56"}, {"name": "Q_zj_15", "bone": "qian_zj_73"}, {"name": "Q_zj_0014", "bone": "qian_zj_7", "attachment": "Q_zj_0014"}, {"name": "C_zj_001", "bone": "qian_zj_4"}, {"name": "Q_zj_0016", "bone": "qian_zj_27", "attachment": "Q_zj_0016"}, {"name": "Q_zj_0015", "bone": "qian_zj_23", "attachment": "Q_zj_0015"}, {"name": "Q_zj_0016_1", "bone": "root"}, {"name": "Q_zj_0016_2", "bone": "root"}, {"name": "Q_zj_003_1", "bone": "root", "attachment": "Q_zj_003_1"}, {"name": "H_zj_0010_1", "bone": "root"}, {"name": "H_zj_001_1", "bone": "root"}, {"name": "H_zj_002_1", "bone": "root"}, {"name": "H_zj_003", "bone": "bone15"}, {"name": "H_zj_004", "bone": "bone5"}, {"name": "H_zj_005", "bone": "bone2"}, {"name": "H_zj_006", "bone": "bone21"}, {"name": "H_zj_007", "bone": "bone18"}, {"name": "Q_zj_17", "bone": "qian_zj_79"}, {"name": "C_zj_1", "bone": "bone5"}, {"name": "H_zj_001", "bone": "root"}, {"name": "H_zj_002", "bone": "root"}, {"name": "H_zj_008", "bone": "bone2"}, {"name": "H_zj_009", "bone": "bone12"}, {"name": "H_zj_0012_1", "bone": "root"}, {"name": "H_zj_0010_2", "bone": "bone5"}, {"name": "H_zj_0010", "bone": "bone5"}, {"name": "H_zj_0012", "bone": "bone7"}, {"name": "Q_zj_16", "bone": "qian_zj_75"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "root"}, {"name": "fapian", "bone": "bone5"}, {"name": "H_zj_003_1", "bone": "bone16"}], "ik": [{"name": "H_jio1", "order": 14, "bones": ["bone18", "bone19"], "target": "H_jio1", "stretch": true}, {"name": "H_jio2", "order": 15, "bones": ["bone21", "bone22"], "target": "H_jio2"}, {"name": "H_jio3", "order": 16, "bones": ["bone20"], "target": "H_jio3"}, {"name": "L_jio1", "order": 7, "bones": ["qian_zj_15"], "target": "L_jio1", "compress": true, "stretch": true}, {"name": "L_jio2", "order": 8, "bones": ["qian_zj_16"], "target": "L_jio2", "compress": true, "stretch": true}, {"name": "L_jio4", "order": 5, "bones": ["qian_zj_54", "qian_zj_55"], "target": "L_jio2", "bendPositive": false}, {"name": "R1_jio1", "order": 11, "bones": ["L_jioall2"], "target": "R1_jio1", "stretch": true}, {"name": "R1_jio2", "order": 12, "bones": ["qian_zj_57"], "target": "R1_jio2", "stretch": true}, {"name": "R1_jio3", "order": 13, "bones": ["qian_zj_58"], "target": "R1_jio3"}, {"name": "R1_jio4", "order": 9, "bones": ["qian_zj_59", "qian_zj_60"], "target": "R1_jio2", "bendPositive": false}, {"name": "R_jio1", "order": 2, "bones": ["qian_zj_18"], "target": "R_jio1", "compress": true, "stretch": true}, {"name": "R_jio2", "order": 3, "bones": ["qian_zj_19"], "target": "R_jio2", "compress": true, "stretch": true}, {"name": "R_jio3", "order": 4, "bones": ["qian_zj_20"], "target": "R_jio3"}, {"name": "R_jio4", "bones": ["qian_zj_21", "qian_zj_22"], "target": "R_jio2", "bendPositive": false}], "transform": [{"name": "L_jio5", "order": 6, "bones": ["L_jio1"], "target": "qian_zj_55", "rotation": 138.08, "x": 14.17, "y": -18.92, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "R1_jio5", "order": 10, "bones": ["R1_jio1"], "target": "qian_zj_60", "rotation": 132.65, "x": 13.91, "y": -15.22, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "R_jio5", "order": 1, "bones": ["R_jio1"], "target": "qian_zj_22", "rotation": 132.15, "x": 13.19, "y": -15.34, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "body", "order": 17, "bones": ["qian_zj_53"], "target": "qian_zj_52", "x": -0.32, "y": 7.74, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}], "skins": [{"name": "default", "attachments": {"H_zj_008": {"H_zj_008": {"type": "mesh", "uvs": [0.00424, 0.05771, 0.08509, 0, 0.16594, 0, 0.26604, 0.11445, 0.47779, 0.14687, 0.73189, 0.0334, 0.87049, 0, 1, 0, 1, 0.26034, 0.99754, 0.43866, 0.97829, 0.79529, 0.84739, 0.98982, 0.62409, 1, 0.44314, 1, 0.16979, 1, 0, 0.83582, 0, 0.54403, 0.03889, 0.36571, 0.11973, 0.51161, 0.28913, 0.56024, 0.51243, 0.56834, 0.73188, 0.55213, 0.88588, 0.5035, 0.85893, 0.24413, 0.70493, 0.31708, 0.50473, 0.37382, 0.27758, 0.3495, 0.14283, 0.28466, 0.11203, 0.74666, 0.32763, 0.81961, 0.53938, 0.80339, 0.75498, 0.78718], "triangles": [27, 1, 2, 27, 17, 0, 27, 0, 1, 18, 17, 27, 18, 27, 26, 16, 17, 18, 18, 26, 19, 19, 25, 20, 28, 16, 18, 28, 18, 19, 30, 20, 21, 30, 21, 31, 29, 19, 20, 29, 20, 30, 28, 19, 29, 15, 16, 28, 14, 28, 29, 15, 28, 14, 13, 29, 30, 14, 29, 13, 12, 30, 31, 13, 30, 12, 12, 31, 11, 23, 5, 6, 6, 7, 8, 23, 6, 8, 27, 2, 3, 24, 4, 5, 24, 5, 23, 26, 3, 4, 27, 3, 26, 25, 4, 24, 26, 4, 25, 9, 23, 8, 22, 23, 9, 21, 24, 23, 22, 21, 23, 19, 26, 25, 20, 25, 24, 20, 24, 21, 31, 21, 22, 10, 22, 9, 31, 22, 10, 11, 31, 10], "vertices": [2, 73, -0.21, 19.32, 0.46582, 93, -9.88, -17.09, 0.53418, 2, 73, 2.01, 16.72, 0.48497, 93, -10.81, -13.81, 0.51503, 2, 73, 3.21, 13.71, 0.51749, 93, -10.64, -10.58, 0.48251, 2, 73, 2.67, 9.19, 0.62695, 93, -8.26, -6.69, 0.37305, 1, 73, 5.23, 1.09, 1, 2, 73, 10.99, -7.55, 0.91311, 93, -8.83, 12, 0.08689, 2, 73, 13.64, -12.47, 0.88385, 93, -9.18, 17.57, 0.11615, 2, 73, 15.55, -17.28, 0.87497, 93, -8.91, 22.74, 0.12503, 2, 73, 10.96, -19.11, 0.83358, 93, -3.97, 22.48, 0.16642, 2, 73, 7.77, -20.27, 0.76927, 93, -0.59, 22.21, 0.23073, 2, 73, 1.19, -22.07, 0.63312, 93, 6.13, 21.09, 0.36688, 2, 73, -4.18, -18.57, 0.50948, 93, 9.55, 15.67, 0.49052, 2, 73, -7.66, -10.34, 0.19556, 93, 9.28, 6.74, 0.80444, 1, 93, 8.91, -0.49, 1, 2, 73, -14.39, 6.54, 0.0428, 93, 8.34, -11.41, 0.9572, 2, 73, -14, 14, 0.12237, 93, 4.87, -18.03, 0.87763, 2, 73, -8.85, 16.05, 0.21394, 93, -0.66, -17.74, 0.78606, 2, 73, -5.13, 15.86, 0.34003, 93, -3.97, -16.01, 0.65997, 2, 73, -6.51, 11.83, 0.25547, 93, -1.03, -12.93, 0.74453, 2, 73, -4.86, 5.2, 0.20148, 93, 0.24, -6.21, 0.79852, 2, 73, -1.7, -3.16, 0.29172, 93, 0.86, 2.7, 0.70828, 2, 73, 1.84, -11.2, 0.62244, 93, 1.01, 11.49, 0.37756, 2, 73, 4.98, -16.58, 0.7232, 93, 0.41, 17.69, 0.2768, 2, 73, 9.16, -13.76, 0.84184, 93, -4.57, 16.87, 0.15816, 2, 73, 5.59, -8.55, 0.82681, 93, -3.51, 10.64, 0.17319, 2, 73, 1.62, -1.51, 0.9197, 93, -2.85, 2.59, 0.0803, 2, 73, -1.31, 7.11, 0.50958, 93, -3.78, -6.46, 0.49042, 2, 73, -2.16, 12.57, 0.42808, 93, -5.29, -11.78, 0.57192, 2, 73, -10.77, 10.47, 0.12044, 93, 3.41, -13.47, 0.87956, 2, 73, -8.87, 1.94, 0.00878, 93, 5.25, -4.93, 0.99122, 2, 73, -5.45, -5.81, 0.09402, 93, 5.38, 3.55, 0.90598, 2, 73, -1.97, -13.71, 0.47021, 93, 5.52, 12.18, 0.52979], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 36, 38, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62], "width": 40, "height": 19}}, "Q_zj_0015": {"Q_zj_0015": {"type": "mesh", "uvs": [1, 0.4423, 1, 0.31609, 0.94753, 0.18409, 0.78457, 0.09146, 0.5939, 0.01504, 0.47657, 0, 0.33968, 0, 0.26961, 0.11346, 0.1865, 0.26977, 0.10991, 0.4423, 0.03983, 0.63219, 0, 0.77693, 0, 1, 0.11969, 0.84524, 0.26798, 0.71093, 0.42279, 0.56619, 0.53035, 0.42262, 0.61672, 0.3103, 0.73568, 0.32072, 0.87909, 0.37167, 0.94753, 0.44809, 0.94265, 0.34388, 0.84161, 0.25009, 0.69658, 0.17019, 0.53362, 0.14009, 0.37717, 0.2964, 0.25332, 0.48166, 0.12946, 0.66229, 0.06916, 0.80703], "triangles": [27, 10, 26, 14, 27, 26, 15, 14, 26, 11, 10, 27, 28, 11, 27, 28, 27, 14, 13, 28, 14, 12, 11, 28, 12, 28, 13, 8, 7, 25, 26, 8, 25, 26, 25, 16, 9, 8, 26, 15, 26, 16, 10, 9, 26, 24, 5, 4, 23, 24, 4, 7, 5, 24, 5, 7, 6, 25, 7, 24, 17, 24, 23, 25, 24, 17, 16, 25, 17, 3, 23, 4, 22, 23, 3, 2, 22, 3, 18, 17, 23, 22, 2, 1, 22, 18, 23, 21, 22, 1, 19, 18, 22, 21, 19, 22, 21, 1, 0, 20, 19, 21, 0, 20, 21], "vertices": [1, 25, -6.9, 4.78, 1, 1, 25, -4.54, 0.6, 1, 1, 25, -0.85, -3.07, 1, 1, 25, 4.72, -3.98, 1, 3, 25, 10.63, -3.98, 0.30951, 26, -3.78, -3.61, 0.65604, 27, -12.19, -3.45, 0.03445, 3, 25, 13.67, -2.92, 0.05385, 26, -2.41, -6.53, 0.83857, 27, -10.83, -6.37, 0.10758, 3, 25, 16.89, -1.11, 0.00022, 26, -0.26, -9.54, 0.81234, 27, -8.71, -9.39, 0.18744, 2, 26, 4.35, -8.57, 0.60368, 27, -4.09, -8.46, 0.39632, 2, 26, 10.49, -6.95, 0.05605, 27, 2.06, -6.88, 0.94395, 2, 27, 8.61, -4.8, 0.62684, 28, -0.12, -4.81, 0.37316, 1, 28, 7.09, -2.9, 1, 1, 28, 12.41, -1.13, 1, 1, 28, 19.78, 3.05, 1, 1, 28, 13.07, 2.96, 1, 2, 27, 14.5, 4.57, 0.0005, 28, 6.66, 3.93, 0.9995, 3, 26, 15.96, 4.78, 0.03601, 27, 7.6, 4.82, 0.5717, 28, -0.18, 4.86, 0.39229, 3, 25, 4.52, 10.36, 0.0001, 26, 9.83, 3.98, 0.74721, 27, 1.47, 4.06, 0.25269, 2, 25, 4.58, 5.49, 0.20688, 26, 5, 3.4, 0.79312, 2, 25, 1.59, 4.26, 0.80155, 26, 3.46, 6.24, 0.19845, 2, 25, -2.74, 4.05, 0.99716, 26, 2.79, 10.52, 0.00284, 1, 25, -5.77, 5.67, 1, 2, 25, -3.71, 2.28, 0.99999, 26, 0.93, 11.31, 1e-05, 2, 25, 0.41, 0.52, 0.99554, 26, -0.39, 7.02, 0.00446, 1, 25, 5.32, -0.2, 1, 2, 26, 1.04, -2.18, 0.94727, 27, -7.36, -2.05, 0.05273, 2, 26, 8.32, -2.18, 0.07199, 27, -0.08, -2.08, 0.92801, 2, 27, 7.61, -0.77, 0.88549, 28, -0.73, -0.7, 0.11451, 1, 28, 6.89, -0.23, 1, 1, 28, 12.48, 1.06, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 27, "height": 38}}, "Q_zj_003_1": {"Q_zj_003_1": {"type": "mesh", "uvs": [0, 0.29892, 0.06326, 0, 0.21355, 0, 0.29852, 0.06631, 0.40434, 0.23289, 0.57407, 0.36595, 0.67478, 0.36578, 0.80803, 0.19957, 0.87292, 0.19938, 0.94254, 0.32692, 1, 0.43219, 1, 0.83682, 0.98035, 0.932, 0.94098, 1, 0.77252, 1, 0.62274, 1, 0.40555, 1, 0.2735, 1, 0.19128, 0.90068, 0.19106, 0.73304, 0.02079, 0.4673, 0.9298, 0.55017, 0.73133, 0.6763, 0.56469, 0.6587, 0.37184, 0.56484, 0.21457, 0.38297, 0.11908, 0.20111, 0.35312, 0.8083, 0.54784, 0.85524, 0.75567, 0.86697, 0.89984, 0.8523, 0.85491, 0.43284, 0.71074, 0.52964, 0.55533, 0.49737, 0.36997, 0.38297, 0.26699, 0.22751, 0.1715, 0.09551, 0.11533, 0.4123, 0.20708, 0.59124], "triangles": [31, 8, 9, 21, 31, 9, 14, 30, 13, 13, 30, 12, 15, 29, 14, 14, 29, 30, 16, 28, 15, 15, 28, 29, 17, 27, 16, 16, 27, 28, 17, 18, 27, 12, 30, 11, 18, 19, 27, 29, 28, 22, 28, 23, 22, 29, 22, 30, 28, 27, 23, 11, 30, 21, 30, 22, 21, 21, 10, 11, 19, 38, 27, 27, 24, 23, 27, 38, 24, 19, 20, 38, 23, 32, 22, 22, 31, 21, 22, 32, 31, 24, 33, 23, 23, 33, 32, 38, 25, 24, 38, 37, 25, 25, 34, 24, 24, 34, 33, 32, 33, 6, 21, 9, 10, 38, 20, 37, 0, 1, 26, 37, 0, 26, 20, 0, 37, 37, 26, 25, 32, 6, 31, 26, 36, 35, 26, 1, 36, 3, 36, 2, 6, 7, 31, 33, 5, 6, 34, 4, 5, 31, 7, 8, 35, 3, 4, 35, 36, 3, 36, 1, 2, 34, 35, 4, 25, 35, 34, 25, 26, 35, 33, 34, 5], "vertices": [2, 5, 4.23, 22.79, 0.70434, 4, 28.99, 21.98, 0.29566, 1, 5, 12.75, 18.69, 1, 1, 5, 11.84, 11.68, 1, 1, 5, 9.36, 7.98, 1, 1, 5, 3.76, 3.68, 1, 1, 5, -1.22, -3.72, 1, 1, 5, -1.82, -8.41, 1, 2, 5, 2.32, -15.26, 0.68552, 4, 23.79, -15.76, 0.31448, 2, 5, 1.94, -18.29, 0.54207, 4, 23.14, -18.74, 0.45793, 2, 5, -2.28, -21.04, 0.47909, 4, 18.7, -21.12, 0.52091, 2, 5, -5.76, -23.31, 0.06029, 4, 15.04, -23.08, 0.93971, 1, 4, 3.18, -20.49, 1, 1, 4, 0.59, -18.98, 1, 1, 4, -1.01, -16.73, 1, 1, 4, 0.68, -9, 1, 1, 4, 2.19, -2.12, 1, 1, 4, 4.37, 7.85, 1, 1, 4, 5.69, 13.91, 1, 1, 4, 9.43, 17.05, 1, 1, 4, 14.34, 15.99, 1, 2, 5, -0.9, 22.46, 0.40484, 4, 23.84, 22.1, 0.59516, 2, 5, -8.84, -19.59, 0.22702, 4, 12.29, -19.1, 0.77298, 2, 5, -11.4, -9.85, 0.16985, 4, 10.58, -9.18, 0.83015, 2, 5, -9.87, -2.15, 0.02094, 4, 12.77, -1.64, 0.97906, 1, 4, 17.46, 6.61, 1, 2, 5, 0.44, 13.11, 0.6945, 4, 24.37, 12.67, 0.3055, 1, 5, 6.43, 16.86, 1, 1, 4, 10.51, 9.03, 1, 1, 4, 7.18, 0.39, 1, 1, 4, 4.75, -9.08, 1, 1, 4, 3.73, -15.79, 1, 2, 5, -4.9, -16.55, 0.28224, 4, 16.48, -16.42, 0.71776, 2, 5, -6.91, -9.46, 0.27349, 4, 15.09, -9.18, 0.72651, 2, 5, -5.02, -2.34, 0.15324, 4, 17.59, -2.25, 0.84676, 2, 5, -0.5, 5.86, 0.63391, 4, 22.81, 5.53, 0.36609, 1, 5, 4.75, 10.07, 1, 1, 5, 9.25, 14.01, 1, 2, 5, 0.17, 17.85, 0.61666, 4, 24.5, 17.41, 0.38334, 2, 5, -5.71, 14.26, 0.1613, 4, 18.34, 14.34, 0.8387], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 20, 22, 22, 24, 24, 26, 34, 36, 36, 38, 38, 40, 32, 34, 30, 32, 26, 28, 28, 30, 20, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 38, 54, 54, 56, 56, 58, 58, 60, 16, 18, 18, 20, 18, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 40, 0, 74, 76], "width": 47, "height": 30}}, "Q_zj_0016": {"Q_zj_0016": {"type": "mesh", "uvs": [0, 0.3823, 0, 0.47314, 0, 0.52423, 0.13747, 0.5001, 0.26947, 0.44475, 0.3868, 0.61791, 0.52089, 0.78823, 0.71365, 0.93159, 0.93784, 1, 1, 1, 1, 0.92165, 0.89384, 0.82797, 0.8268, 0.69455, 0.7828, 0.51004, 0.77861, 0.3454, 0.77651, 0.15095, 0.63194, 0.04308, 0.4287, 0, 0.2548, 0.0814, 0.1228, 0.20488, 0.04737, 0.29146, 0.09556, 0.40217, 0.2108, 0.30423, 0.42032, 0.21056, 0.58794, 0.36668, 0.61308, 0.58242, 0.71156, 0.75842, 0.80794, 0.87481, 0.93994, 0.95997], "triangles": [26, 25, 12, 6, 25, 26, 26, 12, 11, 27, 26, 11, 28, 27, 11, 7, 26, 27, 6, 26, 7, 10, 28, 11, 28, 7, 27, 8, 7, 28, 28, 10, 9, 8, 28, 9, 24, 14, 13, 25, 24, 13, 4, 24, 25, 5, 4, 25, 25, 13, 12, 6, 5, 25, 23, 17, 16, 23, 16, 15, 24, 23, 15, 14, 24, 15, 23, 18, 17, 19, 18, 23, 22, 19, 23, 20, 19, 22, 21, 20, 22, 0, 20, 21, 24, 4, 22, 24, 22, 23, 21, 22, 4, 1, 0, 21, 3, 21, 4, 1, 21, 3, 2, 1, 3], "vertices": [1, 29, -2.96, 0.28, 1, 2, 29, -4.28, -2.2, 0.99996, 31, 1.47, -13.6, 4e-05, 1, 29, -5.02, -3.6, 1, 3, 29, -2.12, -4.29, 0.9796, 30, 0.68, -9.83, 0.00511, 31, 2.68, -10.85, 0.01529, 3, 29, 1.13, -4.07, 0.69656, 30, 1.69, -6.73, 0.10093, 31, 1.34, -7.87, 0.20251, 4, 29, 0.8, -9.97, 0.07696, 30, 7.04, -9.24, 0.03214, 31, 6.99, -6.14, 0.78914, 32, -3.31, -5.49, 0.10177, 3, 29, 0.81, -15.95, 0.00072, 31, 12.59, -4.04, 0.11515, 32, 2.63, -4.84, 0.88413, 1, 32, 8.26, -2.73, 1, 1, 32, 11.98, 0.85, 1, 1, 32, 12.47, 2.06, 1, 1, 32, 10.21, 2.97, 1, 1, 32, 6.69, 1.98, 1, 2, 31, 10.56, 2.71, 0.03157, 32, 2.32, 2.2, 0.96843, 1, 31, 4.77, 2.55, 1, 2, 30, 7.86, 2.52, 0.41753, 31, -0.3, 3.13, 0.58247, 2, 29, 14.8, -1.01, 0.00658, 30, 3.93, 7.1, 0.99342, 2, 29, 13.68, 3.37, 0.16167, 30, -0.54, 7.69, 0.83833, 2, 29, 10.54, 6.55, 0.48238, 30, -4.67, 5.95, 0.51762, 2, 29, 6.13, 6.03, 0.85502, 30, -5.82, 1.67, 0.14498, 1, 29, 1.89, 3.94, 1, 1, 29, -0.77, 2.31, 1, 3, 29, -1.48, -1.2, 0.99342, 30, -1.95, -8.09, 0.00143, 31, -0.45, -11.32, 0.00515, 1, 29, 2.08, 0.35, 1, 2, 29, 7.33, 0.86, 0.50937, 30, -0.58, 0.85, 0.49063, 3, 29, 8.17, -5.06, 0.00775, 30, 5.23, -0.57, 0.79168, 31, -0.17, -0.92, 0.20057, 4, 29, 5.51, -11.22, 0.00862, 30, 9.95, -5.33, 0.00298, 31, 6.53, -1.28, 0.97739, 32, -2.57, -0.67, 0.01102, 1, 32, 3.26, -0.78, 1, 1, 32, 7.36, -0.24, 1, 1, 32, 10.85, 1.35, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 2, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 21, "height": 31}}, "Q_zj_005_1": {"Q_zj_005_1": {"type": "mesh", "uvs": [0.96487, 0.01207, 1, 0.10624, 1, 0.26109, 0.97617, 0.44942, 0.90649, 0.60846, 0.81985, 0.76331, 0.69179, 0.89096, 0.53924, 0.97257, 0.38104, 1, 0.22284, 0.95792, 0.08535, 0.87422, 0, 0.7947, 0.00625, 0.65659, 0.14374, 0.58126, 0.29817, 0.49964, 0.45449, 0.39292, 0.57314, 0.27574, 0.68614, 0.13344, 0.76335, 0.02044, 0.86505, 0, 0.88577, 0.18994, 0.83492, 0.36781, 0.76147, 0.53313, 0.67672, 0.66705, 0.54865, 0.78214, 0.38292, 0.84492, 0.204, 0.80935, 0.16257, 0.67961, 0.33772, 0.67961, 0.50157, 0.57707, 0.61834, 0.44733, 0.7125, 0.3134, 0.78972, 0.13763], "triangles": [31, 32, 20, 31, 17, 32, 32, 19, 20, 20, 19, 0, 17, 18, 32, 32, 18, 19, 30, 31, 22, 15, 16, 30, 30, 16, 31, 16, 17, 31, 29, 30, 23, 14, 15, 29, 29, 15, 30, 8, 25, 7, 8, 9, 25, 7, 25, 24, 10, 26, 9, 9, 26, 25, 26, 10, 27, 26, 28, 25, 25, 28, 24, 10, 11, 27, 26, 27, 28, 11, 12, 27, 28, 29, 24, 27, 14, 28, 28, 14, 29, 12, 13, 27, 27, 13, 14, 7, 24, 6, 24, 23, 6, 6, 23, 5, 24, 29, 23, 23, 22, 5, 23, 30, 22, 5, 22, 4, 4, 22, 3, 22, 21, 3, 22, 31, 21, 3, 21, 2, 21, 20, 2, 21, 31, 20, 20, 1, 2, 20, 0, 1], "vertices": [2, 43, -5.22, -4.51, 0.60242, 39, -9.32, 3.9, 0.39758, 2, 43, -1.36, -0.59, 0.90731, 39, -7.22, 8.99, 0.09269, 1, 43, 6.34, 2.68, 1, 2, 43, 16.26, 5.33, 0.12334, 44, 2.52, 5.33, 0.87666, 2, 44, 12.06, 4.83, 0.80959, 45, -1.68, 4.83, 0.19041, 1, 45, 8.04, 3.31, 1, 3, 45, 17.39, -1.07, 0.91745, 42, -2.33, 25.85, 0.0677, 41, 10.92, 25.91, 0.01485, 3, 45, 25.02, -7.78, 0.54779, 42, 7.36, 22.8, 0.42133, 41, 20.52, 22.56, 0.03088, 3, 45, 30.09, -15.94, 0.26449, 42, 15.3, 17.39, 0.73104, 41, 28.28, 16.9, 0.00447, 2, 45, 31.7, -25.56, 0.07485, 42, 20.67, 9.24, 0.92515, 2, 45, 30.76, -34.92, 0.00418, 42, 23.6, 0.3, 0.99582, 1, 42, 24.4, -6.34, 1, 1, 42, 19.02, -11.52, 1, 1, 42, 10.22, -8.85, 1, 2, 42, 0.45, -5.72, 0.61444, 41, 12.71, -5.73, 0.38556, 2, 41, 2, -3.19, 0.91787, 40, 14.44, -3.19, 0.08213, 1, 40, 4.93, -2.63, 1, 1, 39, 7.14, -3.28, 1, 1, 39, -0.45, -4.33, 1, 2, 43, -3.48, -10.28, 0.21051, 39, -5.55, -0.8, 0.78949, 2, 43, 5.48, -5.13, 0.64618, 39, 0.82, 7.34, 0.35382, 5, 43, 15.51, -4.19, 0.21606, 44, 1.77, -4.19, 0.58972, 41, -15.11, 11.98, 0.00025, 40, -2.67, 11.98, 0.08088, 39, 9.77, 11.98, 0.11309, 5, 44, 11.71, -4.77, 0.62654, 45, -2.03, -4.77, 0.19928, 41, -5.69, 15.19, 0.04551, 40, 6.75, 15.19, 0.12583, 39, 19.19, 15.19, 0.00284, 5, 44, 20.35, -6.62, 0.03951, 45, 6.61, -6.62, 0.74023, 42, -9.94, 16.41, 0.02427, 41, 3.02, 16.71, 0.14945, 40, 15.46, 16.71, 0.04654, 5, 44, 29.07, -11.27, 2e-05, 45, 15.33, -11.27, 0.5569, 42, -0.08, 15.69, 0.28308, 41, 12.85, 15.68, 0.15783, 40, 25.29, 15.68, 0.00217, 3, 45, 22.34, -19.1, 0.22267, 42, 9.49, 11.36, 0.75111, 41, 22.28, 11.05, 0.02622, 2, 45, 24.76, -29.74, 0.01846, 42, 16.01, 2.61, 0.98154, 1, 42, 13.03, -4.2, 1, 3, 45, 15.18, -25.09, 0.03565, 42, 5.36, 2.99, 0.94942, 41, 17.89, 2.82, 0.01492, 5, 44, 19.98, -18.2, 0.01316, 45, 6.24, -18.2, 0.14807, 42, -5.6, 5.68, 0.06149, 41, 7.02, 5.85, 0.74983, 40, 19.47, 5.85, 0.02745, 4, 44, 10.8, -14.48, 0.13354, 45, -2.95, -14.48, 0.08031, 41, -2.88, 5.84, 0.19243, 40, 9.56, 5.84, 0.59373, 6, 43, 15.68, -12.1, 0.05169, 44, 1.93, -12.1, 0.15295, 45, -11.81, -12.1, 0.0016, 41, -11.99, 4.71, 7e-05, 40, 0.45, 4.71, 0.48351, 39, 12.89, 4.71, 0.31018, 3, 43, 5.13, -11.54, 0.04961, 44, -8.61, -11.54, 0.0007, 39, 2.9, 1.27, 0.94969], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 60, "height": 54}}, "Q_zj_006_1": {"Q_zj_006_1": {"type": "mesh", "uvs": [0.10655, 0, 0.2959, 0, 0.46693, 0.08553, 0.55855, 0.26916, 0.68377, 0.47161, 0.82425, 0.64818, 0.98001, 0.82003, 1, 1, 0.81509, 0.93774, 0.61047, 0.92597, 0.32339, 1, 0.20733, 0.8883, 0.08823, 0.68349, 0.01188, 0.46926, 0, 0.29034, 0, 0.10672, 0.19206, 0.12084, 0.28063, 0.29976, 0.34477, 0.4928, 0.43639, 0.70938, 0.67766, 0.77766, 0.90366, 0.87418], "triangles": [5, 20, 4, 21, 20, 5, 6, 21, 5, 9, 19, 20, 8, 9, 20, 10, 11, 19, 21, 8, 20, 9, 10, 19, 21, 6, 7, 8, 21, 7, 20, 19, 4, 12, 18, 19, 11, 12, 19, 13, 14, 17, 18, 17, 3, 18, 3, 4, 13, 17, 18, 12, 13, 18, 19, 18, 4, 16, 0, 1, 15, 0, 16, 14, 15, 16, 2, 17, 16, 2, 16, 1, 17, 2, 3, 14, 16, 17], "vertices": [2, 47, -4.35, -7.07, 0.99864, 48, -15.3, -7.07, 0.00136, 1, 47, -3.5, -0.12, 1, 2, 47, 1.34, 5.67, 0.9922, 48, -9.62, 5.67, 0.0078, 4, 47, 10.49, 7.97, 0.35917, 48, -0.46, 7.97, 0.63272, 49, -11.41, 7.97, 0.00804, 50, -22.36, 7.97, 8e-05, 3, 48, 9.75, 11.4, 0.53349, 49, -1.2, 11.4, 0.36906, 50, -12.16, 11.4, 0.09744, 3, 48, 18.79, 15.54, 0.07399, 49, 7.84, 15.54, 0.3674, 50, -3.12, 15.54, 0.55861, 3, 48, 27.67, 20.27, 0.00125, 49, 16.72, 20.27, 0.11499, 50, 5.77, 20.27, 0.88376, 2, 49, 25.38, 19.96, 0.06551, 50, 14.43, 19.96, 0.93449, 2, 49, 21.59, 13.53, 0.08205, 50, 10.64, 13.53, 0.91795, 2, 49, 20.12, 6.08, 0.03799, 50, 9.17, 6.08, 0.96201, 2, 49, 22.37, -4.89, 0.0001, 50, 11.41, -4.89, 0.9999, 3, 48, 27.48, -8.51, 0.00089, 49, 16.53, -8.51, 0.14084, 50, 5.57, -8.51, 0.85827, 3, 48, 17.19, -11.7, 0.18975, 49, 6.24, -11.7, 0.67999, 50, -4.72, -11.7, 0.13026, 4, 47, 17.59, -13.26, 0.09512, 48, 6.64, -13.26, 0.67302, 49, -4.31, -13.26, 0.23177, 50, -15.26, -13.26, 9e-05, 3, 47, 9.01, -12.66, 0.56072, 48, -1.94, -12.66, 0.41709, 49, -12.89, -12.66, 0.02219, 2, 47, 0.26, -11.6, 0.94463, 48, -10.69, -11.6, 0.05537, 2, 47, 1.79, -4.63, 0.98603, 48, -9.16, -4.63, 0.01397, 3, 47, 10.71, -2.41, 0.54956, 48, -0.24, -2.41, 0.44978, 49, -11.19, -2.41, 0.00066, 2, 48, 9.25, -1.17, 0.93682, 49, -1.71, -1.17, 0.06318, 3, 48, 19.97, 0.94, 5e-05, 49, 9.02, 0.94, 0.94083, 50, -1.93, 0.94, 0.05912, 3, 48, 24.3, 9.41, 0.01168, 49, 13.35, 9.41, 0.2314, 50, 2.4, 9.41, 0.75692, 3, 48, 29.91, 17.15, 0.00033, 49, 18.96, 17.15, 0.09631, 50, 8.01, 17.15, 0.90335], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 37, "height": 48}}, "H_zj_0012": {"H_zj_0012": {"type": "mesh", "uvs": [0.34869, 0, 0.49543, 0, 0.62806, 0.0249, 0.77198, 0.18066, 0.80302, 0.33139, 0.81995, 0.46454, 0.90179, 0.59266, 1, 0.71073, 1, 0.8489, 0.94129, 0.94688, 0.72118, 1, 0.33176, 1, 0.17091, 0.98456, 0.02981, 0.91422, 0, 0.8062, 0, 0.65295, 0, 0.55246, 0.08907, 0.49217, 0.10036, 0.35902, 0.10601, 0.17563, 0.17091, 0.07263, 0.56598, 0.19071, 0.60267, 0.39671, 0.59984, 0.61276, 0.65628, 0.79364, 0.32047, 0.20578, 0.32894, 0.38917, 0.33176, 0.5801, 0.29507, 0.8062, 0.15398, 0.8062, 0.12294, 0.64542], "triangles": [30, 17, 27, 16, 17, 30, 15, 16, 30, 14, 15, 30, 28, 29, 30, 14, 30, 29, 27, 28, 30, 13, 14, 29, 12, 29, 28, 13, 29, 12, 23, 28, 27, 11, 28, 24, 12, 28, 11, 17, 18, 26, 27, 26, 22, 17, 26, 27, 25, 20, 0, 19, 20, 25, 18, 19, 25, 21, 26, 25, 18, 25, 26, 25, 0, 1, 1, 21, 25, 23, 5, 6, 24, 23, 6, 24, 6, 7, 24, 7, 8, 9, 24, 8, 28, 23, 24, 10, 24, 9, 11, 24, 10, 22, 4, 5, 23, 27, 22, 23, 22, 5, 21, 1, 2, 21, 2, 3, 4, 22, 21, 4, 21, 3, 26, 21, 22], "vertices": [2, 76, -15.55, -22.69, 0.13359, 127, -11.42, 0.68, 0.86641, 2, 76, -14.54, -12.03, 0.47554, 127, -13.99, 11.08, 0.52446, 2, 76, -11.77, -2.53, 0.7871, 127, -14.51, 20.96, 0.2129, 1, 76, 1.76, 6.99, 1, 2, 76, 14.26, 8.31, 0.6235, 77, 0.01, 8.54, 0.3765, 3, 76, 25.24, 8.72, 0.00684, 77, 10.79, 6.4, 0.98698, 78, -8.63, 3.84, 0.00618, 2, 77, 22.61, 8.9, 0.12763, 78, 1.77, 9.99, 0.87237, 1, 78, 11.33, 17.32, 1, 1, 78, 22.66, 17.51, 1, 2, 78, 30.76, 13.36, 0.99503, 129, 22.75, 59.26, 0.00497, 2, 78, 35.39, -2.64, 0.88053, 129, 29.11, 43.87, 0.11947, 2, 78, 35.86, -31.06, 0.27123, 129, 32.7, 15.67, 0.72877, 2, 78, 34.79, -42.82, 0.07609, 129, 32.93, 3.87, 0.92391, 2, 78, 29.2, -53.22, 0.00271, 129, 28.51, -7.08, 0.99729, 1, 129, 20, -10.36, 1, 2, 128, 26.95, -11.21, 0.06712, 129, 7.53, -11.95, 0.93288, 2, 128, 18.91, -12.99, 0.33397, 129, -0.64, -12.99, 0.66603, 3, 127, 32.65, -7.18, 0.00016, 128, 12.67, -7.72, 0.82143, 129, -6.37, -7.16, 0.17842, 2, 127, 21.89, -9.2, 0.39668, 128, 1.83, -9.28, 0.60332, 2, 127, 7.26, -12.67, 0.99934, 128, -12.93, -12.14, 0.00066, 1, 127, -2.13, -10.27, 1, 3, 76, 1.45, -8.07, 0.77091, 127, -0.2, 20.09, 0.22875, 128, -19.01, 20.9, 0.00034, 6, 76, 18.49, -6.68, 0.30523, 77, 0.67, -7.02, 0.54739, 78, -13.93, -12.11, 0.01548, 127, 15.43, 27.03, 0.08693, 128, -3.1, 27.18, 0.03942, 129, -18.87, 29.04, 0.00555, 6, 76, 36.14, -8.22, 0.00016, 77, 17.49, -12.6, 0.28092, 78, 3.79, -12.02, 0.53863, 127, 32.6, 31.39, 0.01783, 128, 14.24, 30.82, 0.06621, 129, -1.27, 31.07, 0.09625, 5, 77, 32.87, -13.18, 0.00623, 78, 18.55, -7.65, 0.86399, 127, 45.87, 39.2, 6e-05, 128, 27.82, 38.06, 0.00755, 129, 12.92, 37.03, 0.12218, 3, 76, 1.32, -26.04, 0.05013, 77, -20.5, -21.89, 0.0017, 127, 5.61, 3.09, 0.94817, 6, 76, 16.37, -26.56, 0.06367, 77, -5.99, -25.87, 0.06434, 78, -14.22, -32.1, 0.01065, 127, 19.98, 7.56, 0.51738, 128, 0.63, 7.54, 0.34124, 129, -16.96, 9.14, 0.00272, 6, 76, 31.99, -27.53, 0.00614, 77, 8.99, -30.43, 0.09468, 78, 1.43, -31.63, 0.10342, 127, 35.06, 11.79, 0.04176, 128, 15.87, 11.14, 0.43678, 129, -1.46, 11.32, 0.31722, 4, 77, 25.84, -38.62, 0.00969, 78, 20.02, -34, 0.16642, 128, 34.55, 12.55, 0.00574, 129, 17.27, 11.01, 0.81816, 3, 77, 22.71, -48.43, 0.00013, 78, 20.19, -44.3, 0.00494, 129, 18.58, 0.79, 0.99494, 2, 128, 24.4, -2.58, 0.01284, 129, 5.78, -3.12, 0.98716], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 2, 42, 42, 44, 44, 46, 46, 48, 0, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 73, "height": 82}}, "H_zj_0010_2": {"H_zj_0010_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-2.98, -25.39, 0.04, 21.52, 27.99, 19.71, 24.96, -27.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 47, "height": 28}}, "H_zj_0012_1": {"H_zj_0012_1": {"type": "mesh", "uvs": [0.07009, 0, 0.13859, 0, 0.20238, 0, 0.22127, 0.09544, 0.28742, 0.23778, 0.4079, 0.39389, 0.55672, 0.4995, 0.75279, 0.53393, 0.89217, 0.50639, 1, 0.55919, 1, 0.72907, 0.99139, 0.89437, 0.92524, 0.97931, 0.70319, 1, 0.41735, 1, 0.23545, 0.92192, 0.11024, 0.77958, 0.03701, 0.56608, 0, 0.37323, 0, 0.17579, 0, 0.05412, 0.15937, 0.14602, 0.19792, 0.29193, 0.32778, 0.48912, 0.50227, 0.65081, 0.75386, 0.68236, 0.89386, 0.6863, 0.08024, 0.15391, 0.0985, 0.3294, 0.16343, 0.53644, 0.27908, 0.71982, 0.46372, 0.84799, 0.66865, 0.88348, 0.86546, 0.8756], "triangles": [27, 0, 1, 20, 0, 27, 19, 20, 27, 22, 28, 27, 19, 27, 28, 18, 19, 28, 29, 28, 22, 18, 28, 29, 29, 22, 23, 17, 18, 29, 30, 29, 23, 16, 17, 29, 16, 29, 30, 15, 16, 30, 31, 24, 32, 13, 32, 33, 13, 33, 12, 13, 14, 32, 31, 30, 24, 15, 30, 31, 14, 15, 31, 14, 31, 32, 26, 25, 7, 26, 9, 10, 33, 25, 26, 33, 26, 10, 32, 24, 25, 32, 25, 33, 11, 33, 10, 12, 33, 11, 6, 7, 25, 26, 8, 9, 26, 7, 8, 24, 23, 6, 24, 6, 25, 23, 22, 5, 6, 23, 5, 30, 23, 24, 3, 21, 1, 3, 1, 2, 27, 1, 21, 21, 3, 4, 22, 21, 4, 22, 27, 21, 5, 22, 4], "vertices": [2, 121, -4.22, -4.3, 0.09362, 124, -3.06, -0.33, 0.90638, 2, 121, -2.78, 0.2, 0.81308, 124, -2.43, 4.36, 0.18692, 1, 121, -1.43, 4.39, 1, 1, 121, 5.42, 3.56, 1, 2, 121, 16.44, 4.82, 0.94414, 122, -3.29, 8.33, 0.05586, 2, 121, 29.53, 9.35, 0.01188, 122, 9.97, 4.32, 0.98812, 2, 122, 22.69, 4.35, 0.90599, 123, -1.81, 5.8, 0.09401, 1, 123, 11.94, 5.56, 1, 1, 123, 21.12, 9.03, 1, 1, 123, 29.06, 6.52, 1, 2, 123, 31, -5.38, 0.92891, 126, 42.57, 25.83, 0.07109, 2, 123, 32.29, -17.06, 0.68584, 126, 48.52, 15.7, 0.31416, 2, 123, 28.75, -23.75, 0.56749, 126, 48.02, 8.15, 0.43251, 2, 123, 13.87, -27.65, 0.15189, 126, 36.03, -1.5, 0.84811, 2, 126, 19.56, -12.34, 0.93868, 125, 44.29, -0.83, 0.06132, 2, 126, 6.02, -14.61, 0.47309, 125, 33.64, -9.48, 0.52691, 2, 126, -6.75, -10.92, 0.00109, 125, 20.71, -12.57, 0.99891, 2, 125, 4.91, -10.17, 0.97546, 124, 36.47, -7.96, 0.02454, 2, 125, -8.44, -6.21, 0.06288, 124, 22.55, -8.66, 0.93712, 1, 124, 8.66, -6.79, 1, 1, 124, 0.1, -5.63, 1, 2, 121, 7.53, -1.6, 0.78129, 124, 8.04, 4.39, 0.21871, 2, 121, 18.21, -2.24, 0.76919, 124, 18.66, 5.64, 0.23081, 3, 122, 9.51, -4.4, 0.75387, 126, -5.55, 14.56, 0.03813, 125, 9.18, 10.18, 0.208, 3, 122, 26.01, -6.54, 0.46545, 123, -3.79, -5.4, 0.18846, 126, 10.82, 11.59, 0.34609, 2, 123, 13.7, -4.83, 0.89954, 126, 26.55, 19.26, 0.10046, 2, 123, 23.28, -3.56, 0.96103, 126, 34.78, 24.34, 0.03897, 1, 124, 7.86, -1.09, 1, 1, 124, 20.38, -1.51, 1, 1, 125, 7.01, -1.45, 1, 2, 126, 0.65, -0.97, 0.32729, 125, 22.23, -0.27, 0.67271, 2, 126, 16.3, -1.57, 0.99368, 125, 36.14, 6.93, 0.00632, 2, 123, 10.19, -19.87, 0.18349, 126, 29.49, 4.1, 0.81651, 2, 123, 23.5, -17.14, 0.60119, 126, 40.53, 12.03, 0.39881], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 2, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66], "width": 69, "height": 71}}, "L_jioall1": {"Q_zj_4": {"type": "mesh", "path": "Q_zj_004", "uvs": [0.85324, 0.0145, 0.9824, 0.06146, 1, 0.18243, 1, 0.28773, 0.97702, 0.41723, 0.94204, 0.53819, 0.91782, 0.65346, 0.76175, 0.71466, 0.61375, 0.73173, 0.54378, 0.79292, 0.46037, 0.80573, 0.41731, 0.77442, 0.41731, 0.847, 0.44153, 0.90392, 0.46844, 0.96369, 0.42807, 1, 0.27469, 1, 0.12938, 0.99785, 0.06211, 0.94946, 0.09709, 0.88258, 0.16975, 0.8285, 0.18051, 0.74881, 0.16437, 0.69473, 0.07826, 0.67339, 0.0056, 0.65631, 0, 0.56381, 0.0648, 0.52112, 0.05404, 0.45992, 0.17782, 0.34608, 0.28815, 0.21231, 0.38771, 0.10985, 0.51418, 0.02731, 0.67833, 0, 0.75385, 0.10176, 0.64497, 0.21174, 0.55272, 0.35866, 0.44977, 0.50989, 0.36826, 0.65535, 0.74444, 0.61269, 0.33949, 0.71807, 0.29442, 0.76947, 0.2793, 0.8478, 0.25623, 0.93469], "triangles": [14, 15, 42, 17, 42, 16, 14, 42, 13, 42, 15, 16, 17, 18, 42, 18, 19, 42, 42, 19, 41, 42, 41, 13, 41, 12, 13, 41, 19, 20, 12, 41, 40, 41, 20, 40, 40, 11, 12, 20, 21, 40, 10, 11, 9, 9, 11, 8, 40, 39, 11, 11, 39, 8, 40, 21, 39, 21, 22, 39, 38, 7, 8, 8, 39, 37, 39, 22, 37, 8, 37, 38, 7, 38, 6, 37, 22, 26, 26, 22, 23, 25, 26, 23, 23, 24, 25, 37, 36, 38, 37, 26, 36, 6, 38, 5, 38, 36, 5, 26, 27, 36, 27, 28, 36, 36, 35, 5, 5, 35, 4, 36, 28, 35, 4, 35, 3, 28, 29, 35, 35, 34, 3, 35, 29, 34, 34, 2, 3, 29, 30, 34, 34, 30, 33, 34, 33, 2, 33, 31, 32, 31, 33, 30, 33, 1, 2, 33, 0, 1, 33, 32, 0], "vertices": [1, 17, -2.24, 3.69, 1, 1, 17, 0.57, 11.84, 1, 1, 17, 12.44, 16.14, 1, 2, 17, 22.99, 19.06, 0.99932, 18, -32.21, 15.97, 0.00068, 2, 17, 36.31, 21.44, 0.92723, 18, -19.19, 19.65, 0.07277, 2, 17, 48.94, 22.95, 0.59552, 18, -6.77, 22.39, 0.40448, 2, 17, 60.85, 24.87, 0.24858, 18, 4.89, 25.48, 0.75142, 2, 17, 69.28, 18.3, 0.08037, 18, 13.93, 19.77, 0.91963, 2, 17, 73.16, 10.93, 0.00766, 18, 18.52, 12.81, 0.99234, 1, 18, 25.84, 11.52, 1, 1, 18, 28.74, 7.72, 1, 1, 18, 26.56, 4.34, 1, 2, 18, 33.6, 7.06, 0.95177, 19, -7.87, 7.38, 0.04823, 2, 18, 38.64, 10.44, 0.64591, 19, -2.7, 10.55, 0.35409, 2, 18, 43.9, 14.06, 0.35873, 19, 2.7, 13.97, 0.64127, 2, 18, 48.22, 13.35, 0.2754, 19, 6.99, 13.09, 0.7246, 2, 18, 51.27, 5.49, 0.05981, 19, 9.72, 5.11, 0.94019, 1, 19, 12.1, -2.52, 1, 2, 18, 50.58, -7.31, 0.007, 19, 8.54, -7.65, 0.993, 2, 18, 43.4, -8.03, 0.32797, 19, 1.33, -8.09, 0.67203, 2, 18, 36.71, -6.33, 0.93906, 19, -5.28, -6.13, 0.06094, 1, 18, 28.77, -8.77, 1, 2, 17, 76.06, -13.92, 6e-05, 18, 23.85, -11.63, 0.99994, 2, 17, 75.19, -19.07, 0.00401, 18, 23.48, -16.84, 0.99599, 2, 17, 74.54, -23.4, 0.00929, 18, 23.27, -21.21, 0.99071, 2, 17, 65.35, -26.26, 0.06735, 18, 14.41, -24.97, 0.93265, 2, 17, 60.12, -24.02, 0.17645, 18, 8.98, -23.25, 0.82355, 2, 17, 54.15, -26.29, 0.35534, 18, 3.26, -26.09, 0.64466, 2, 17, 40.92, -22.89, 0.76811, 18, -10.24, -24.01, 0.23189, 2, 17, 25.89, -20.76, 0.98541, 18, -25.4, -23.37, 0.01459, 1, 17, 14.16, -18.33, 1, 1, 17, 4.03, -13.92, 1, 1, 17, -1.12, -5.98, 1, 1, 17, 7.97, 0.85, 1, 1, 17, 20.59, -1.86, 1, 2, 17, 36.67, -2.67, 0.99701, 18, -16.46, -4.31, 0.00299, 2, 17, 53.34, -3.93, 0.37018, 18, 0.25, -3.92, 0.62982, 1, 18, 15.98, -2.64, 1, 2, 17, 59.31, 14.55, 0.22193, 18, 4.38, 15.05, 0.77807, 1, 18, 22.64, -1.77, 1, 1, 18, 28.52, -2.15, 1, 1, 18, 36.41, 0.01, 1, 2, 18, 45.3, 2.09, 0.07103, 19, 3.62, 1.95, 0.92897], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 0, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 78, 78, 80, 80, 82, 82, 84], "width": 55, "height": 104}}, "Q_zj_3": {"Q_zj_002": {"type": "mesh", "uvs": [0.48367, 0, 0.65986, 0.0166, 0.77615, 0.10073, 0.82196, 0.24561, 0.87129, 0.37492, 0.9171, 0.50734, 0.92062, 0.61951, 0.80434, 0.68026, 0.70215, 0.69896, 0.72681, 0.73012, 0.67396, 0.7753, 0.6211, 0.81113, 0.67043, 0.84852, 0.85015, 0.875, 0.96643, 0.88746, 1, 0.94822, 1, 1, 0.829, 1, 0.63872, 0.98717, 0.385, 0.9934, 0.27224, 0.94822, 0.29691, 0.89837, 0.33567, 0.86254, 0.31453, 0.78153, 0.21586, 0.75504, 0.19472, 0.68494, 0.06786, 0.65222, 0, 0.62262, 0, 0.51201, 0.03262, 0.36245, 0.09253, 0.2425, 0.14891, 0.13968, 0.21586, 0.03997, 0.34977, 0, 0.48615, 0.0993, 0.48759, 0.22644, 0.49971, 0.36142, 0.4943, 0.50583, 0.47798, 0.62041, 0.47171, 0.68947, 0.46881, 0.74441, 0.48424, 0.86056, 0.58727, 0.92802, 0.84146, 0.95465], "triangles": [42, 41, 12, 13, 42, 12, 43, 13, 14, 43, 14, 15, 43, 42, 13, 18, 42, 43, 19, 42, 18, 42, 19, 21, 20, 21, 19, 17, 18, 43, 43, 15, 16, 17, 43, 16, 37, 4, 5, 28, 29, 37, 38, 28, 37, 27, 28, 38, 26, 27, 38, 37, 6, 38, 6, 37, 5, 7, 38, 6, 25, 26, 38, 39, 25, 38, 8, 39, 38, 7, 8, 38, 39, 24, 25, 40, 39, 8, 10, 40, 8, 40, 24, 39, 9, 10, 8, 23, 24, 40, 11, 40, 10, 41, 40, 11, 41, 11, 12, 23, 40, 41, 22, 23, 41, 41, 21, 22, 42, 21, 41, 34, 0, 1, 34, 1, 2, 35, 34, 2, 33, 0, 34, 32, 33, 34, 31, 32, 34, 35, 2, 3, 36, 35, 3, 31, 34, 35, 30, 31, 35, 36, 3, 4, 36, 30, 35, 37, 36, 4, 36, 29, 30, 37, 29, 36], "vertices": [1, 56, 5.23, -1.39, 1, 1, 56, 8.45, 5.36, 1, 1, 56, 16.94, 8.03, 1, 1, 56, 30.02, 6.37, 1, 1, 56, 41.77, 5.22, 1, 2, 56, 53.76, 3.86, 0.09709, 57, 1.17, 6.4, 0.90291, 1, 57, 10.64, 10.02, 1, 1, 57, 17.6, 7.39, 1, 1, 57, 20.77, 4, 1, 1, 57, 23.03, 5.93, 1, 2, 57, 27.69, 5.28, 0.99634, 58, 7.18, 15.09, 0.00366, 2, 57, 31.55, 4.34, 0.90772, 58, 5.7, 11.4, 0.09228, 2, 57, 33.96, 7.42, 0.47474, 58, 8.4, 8.58, 0.52526, 2, 57, 33.42, 15.22, 0.03309, 58, 16.2, 7.99, 0.96691, 2, 57, 32.67, 20.13, 0.00135, 58, 21.16, 8.03, 0.99865, 1, 58, 23.65, 2.98, 1, 1, 58, 24.61, -1.6, 1, 1, 58, 17.66, -3.27, 1, 1, 58, 9.68, -4, 1, 1, 58, -0.52, -7.03, 1, 2, 57, 48.6, -4.95, 0.22273, 58, -5.94, -4.14, 0.77727, 2, 57, 43.98, -5.54, 0.74707, 58, -5.87, 0.52, 0.25293, 2, 57, 40.34, -5.15, 0.99516, 58, -4.95, 4.06, 0.00484, 2, 56, 71.56, -27.27, 0.00277, 57, 33.79, -8.49, 0.99723, 2, 56, 68.26, -30.63, 0.01138, 57, 33.08, -13.14, 0.98862, 2, 56, 61.94, -29.78, 0.05228, 57, 27.45, -16.14, 0.94772, 2, 56, 57.81, -34.13, 0.11663, 57, 26.64, -22.09, 0.88337, 2, 56, 54.55, -36.17, 0.14371, 57, 25.18, -25.64, 0.85629, 2, 56, 44.92, -33.48, 0.2965, 57, 15.79, -29.08, 0.7035, 2, 56, 32.23, -28.52, 0.65213, 57, 2.59, -32.46, 0.34787, 2, 56, 22.39, -23.17, 0.88401, 57, -8.52, -33.86, 0.11599, 2, 56, 14.01, -18.38, 0.9764, 57, -18.12, -34.87, 0.0236, 2, 56, 6, -13.23, 0.99881, 57, -27.62, -35.36, 0.00119, 1, 56, 3.88, -6.83, 1, 2, 56, 13.9, -3.71, 0.99948, 57, -26.78, -23.02, 0.00052, 2, 56, 24.98, -6.74, 0.98046, 57, -16.01, -19.02, 0.01954, 2, 56, 36.85, -9.53, 0.83377, 57, -4.74, -14.35, 0.16623, 2, 56, 49.37, -13.26, 0.23663, 57, 7.6, -10.07, 0.76337, 2, 56, 59.18, -16.71, 0.03204, 57, 17.58, -7.15, 0.96796, 2, 56, 65.13, -18.65, 0.00693, 57, 23.54, -5.24, 0.99307, 2, 56, 69.88, -20.1, 0.00121, 57, 28.25, -3.65, 0.99879, 2, 57, 37.87, 0.56, 0.97074, 58, 1.05, 5.69, 0.02926, 2, 57, 42, 6.66, 0.02998, 58, 6.49, 0.73, 0.97002, 2, 57, 40.31, 17.36, 9e-05, 58, 17.32, 0.86, 0.99991], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 68, 68, 70, 70, 72, 72, 74, 74, 76, 54, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 42, "height": 95}}, "Q_zj_1": {"Q_zj_001": {"type": "mesh", "uvs": [0, 0, 0.19922, 0, 0.32597, 0.01027, 0.45922, 0.11466, 0.60872, 0.21314, 0.71272, 0.27421, 0.76039, 0.34199, 0.79722, 0.39436, 0.76147, 0.47708, 0.79072, 0.5992, 0.83947, 0.72329, 0.94347, 0.80602, 1, 0.8592, 1, 0.94981, 0.96947, 1, 0.83622, 1, 0.68022, 1, 0.60872, 0.93996, 0.64772, 0.86905, 0.64772, 0.77057, 0.50472, 0.6839, 0.39422, 0.71148, 0.30469, 0.59709, 0.27397, 0.55784, 0.21645, 0.50032, 0.14397, 0.42784, 0.03347, 0.28208, 0, 0.16784, 0, 0.07133, 0.13098, 0.0792, 0.27398, 0.19936, 0.41373, 0.32739, 0.50148, 0.47117, 0.56647, 0.57557, 0.75822, 0.75481, 0.65467, 0.65801, 0.81672, 0.87299, 0.81997, 0.94784], "triangles": [11, 36, 34, 36, 11, 12, 18, 34, 36, 13, 37, 36, 13, 36, 12, 18, 37, 17, 37, 18, 36, 16, 17, 37, 14, 15, 37, 16, 37, 15, 13, 14, 37, 31, 6, 32, 32, 6, 7, 8, 32, 7, 32, 24, 31, 23, 24, 32, 33, 32, 8, 22, 23, 32, 33, 22, 32, 33, 8, 9, 35, 33, 9, 20, 22, 33, 20, 33, 35, 21, 22, 20, 35, 9, 10, 34, 35, 10, 19, 20, 35, 19, 35, 34, 18, 19, 34, 11, 34, 10, 29, 28, 0, 1, 29, 0, 27, 28, 29, 2, 29, 1, 30, 2, 3, 30, 29, 2, 27, 29, 30, 26, 27, 30, 4, 31, 30, 4, 30, 3, 31, 25, 26, 31, 26, 30, 5, 31, 4, 6, 31, 5, 24, 25, 31], "vertices": [1, 13, -9.57, 1.72, 1, 1, 13, -5.32, 8.46, 1, 1, 13, -2.04, 12.39, 1, 2, 13, 6.63, 13.22, 0.99686, 14, -17.77, 11.8, 0.00314, 2, 13, 15.31, 14.81, 0.8745, 14, -9.24, 14.1, 0.1255, 2, 13, 20.94, 16.18, 0.66279, 14, -3.75, 15.93, 0.33721, 2, 13, 25.74, 15.41, 0.4574, 14, 1.1, 15.55, 0.5426, 2, 13, 29.45, 14.81, 0.29873, 14, 4.85, 15.26, 0.70127, 2, 13, 33.31, 10.69, 0.10064, 14, 9.03, 11.47, 0.89936, 2, 13, 40.75, 7.38, 0.00037, 14, 16.72, 8.79, 0.99963, 2, 14, 24.88, 6.74, 0.89695, 15, -4.57, 6.41, 0.10305, 2, 14, 31.65, 7.9, 0.19246, 15, 2.08, 8.08, 0.80754, 2, 14, 35.8, 8.29, 0.02383, 15, 6.2, 8.78, 0.97617, 1, 15, 11.7, 6.42, 1, 1, 15, 14.26, 4, 1, 1, 15, 12.16, -0.9, 1, 1, 15, 9.7, -6.64, 1, 1, 15, 4.93, -7.71, 1, 2, 14, 29.87, -4.51, 0.15864, 15, 1.25, -4.43, 0.84136, 1, 14, 24.11, -1.51, 1, 1, 14, 16.39, -3.94, 1, 1, 14, 15.97, -8.7, 1, 2, 13, 30.26, -8.99, 0.10019, 14, 7.62, -8.39, 0.89981, 2, 13, 27.41, -8.65, 0.13457, 14, 4.75, -8.28, 0.86543, 2, 13, 22.97, -8.57, 0.47862, 14, 0.32, -8.57, 0.52138, 2, 13, 17.38, -8.47, 0.91218, 14, -5.26, -8.93, 0.08782, 1, 13, 6.89, -7.08, 1, 1, 13, -0.2, -4.19, 1, 1, 13, -5.59, -0.79, 1, 1, 13, -2.36, 3.37, 1, 1, 13, 7.4, 3.97, 1, 2, 13, 17.53, 4.19, 0.9643, 14, -6.16, 3.7, 0.0357, 2, 13, 27.43, 2.1, 0.0264, 14, 3.88, 2.43, 0.9736, 1, 14, 11.19, 1.55, 1, 2, 14, 25.23, 2.89, 0.96596, 15, -3.94, 2.6, 0.03404, 1, 14, 17.65, 2.17, 1, 2, 14, 33.23, 1.36, 0.00266, 15, 4.15, 1.68, 0.99734, 1, 15, 8.74, -0.15, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 50, 52, 52, 54, 54, 56, 0, 56, 0, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 70, 70, 68, 68, 72, 72, 74, 46, 48, 48, 50, 10, 12, 12, 14, 42, 44, 44, 46], "width": 40, "height": 66}}, "Q_zj_2": {"Q_zj_002": {"type": "mesh", "uvs": [0.48367, 0, 0.65986, 0.0166, 0.77615, 0.10073, 0.82196, 0.24561, 0.87129, 0.37492, 0.9171, 0.50734, 0.92062, 0.61951, 0.80434, 0.68026, 0.70215, 0.69896, 0.72681, 0.73012, 0.67396, 0.7753, 0.6211, 0.81113, 0.67043, 0.84852, 0.85015, 0.875, 0.96643, 0.88746, 1, 0.94822, 1, 1, 0.829, 1, 0.63872, 0.98717, 0.385, 0.9934, 0.27224, 0.94822, 0.29691, 0.89837, 0.33567, 0.86254, 0.31453, 0.78153, 0.21586, 0.75504, 0.19472, 0.68494, 0.06786, 0.65222, 0, 0.62262, 0, 0.51201, 0.03262, 0.36245, 0.09253, 0.2425, 0.14891, 0.13968, 0.21586, 0.03997, 0.34977, 0, 0.48615, 0.0993, 0.48759, 0.22644, 0.49971, 0.36142, 0.4943, 0.50583, 0.47798, 0.62041, 0.47171, 0.68947, 0.46881, 0.74441, 0.48424, 0.86056, 0.58727, 0.92802, 0.84146, 0.95465], "triangles": [17, 43, 16, 43, 15, 16, 17, 18, 43, 20, 21, 19, 42, 19, 21, 19, 42, 18, 18, 42, 43, 43, 42, 13, 43, 14, 15, 43, 13, 14, 13, 42, 12, 42, 41, 12, 42, 21, 41, 41, 21, 22, 22, 23, 41, 23, 40, 41, 41, 11, 12, 41, 40, 11, 11, 40, 10, 23, 24, 40, 9, 10, 8, 40, 24, 39, 10, 40, 8, 40, 39, 8, 39, 24, 25, 7, 8, 38, 8, 39, 38, 39, 25, 38, 25, 26, 38, 7, 38, 6, 6, 37, 5, 37, 6, 38, 26, 27, 38, 27, 28, 38, 38, 28, 37, 28, 29, 37, 37, 4, 5, 37, 29, 36, 36, 29, 30, 37, 36, 4, 36, 30, 35, 36, 3, 4, 30, 31, 35, 31, 34, 35, 36, 35, 3, 35, 2, 3, 31, 32, 34, 32, 33, 34, 33, 0, 34, 35, 34, 2, 34, 1, 2, 34, 0, 1], "vertices": [1, 20, 1.84, 0.81, 1, 1, 20, 5.49, 7.44, 1, 1, 20, 14.56, 9.79, 1, 1, 20, 28.29, 7.64, 1, 1, 20, 40.64, 6.06, 1, 2, 20, 53.24, 4.25, 0.06499, 61, 1.49, 6.72, 0.93501, 1, 61, 11.21, 10.77, 1, 1, 61, 18.28, 8.34, 1, 1, 61, 21.46, 5, 1, 1, 61, 23.8, 7.05, 1, 2, 61, 28.54, 6.56, 0.98238, 62, 8.53, 14.85, 0.01762, 2, 61, 32.47, 5.74, 0.83596, 62, 7.23, 11.05, 0.16404, 2, 61, 34.98, 8.97, 0.35215, 62, 10.12, 8.16, 0.64785, 2, 61, 34.56, 16.92, 0.0139, 62, 18.05, 7.58, 0.9861, 1, 62, 23.07, 7.63, 1, 1, 62, 25.87, 2.45, 1, 1, 62, 27.1, -2.25, 1, 1, 62, 20.14, -3.99, 1, 1, 62, 12.1, -4.76, 1, 2, 61, 51.95, 2.87, 0.02044, 62, 1.93, -7.91, 0.97956, 2, 61, 49.72, -3.11, 0.29195, 62, -3.72, -4.95, 0.70805, 2, 61, 45, -3.88, 0.73547, 62, -3.9, -0.17, 0.26453, 1, 61, 41.29, -3.62, 1, 2, 20, 70.82, -27.53, 0.0042, 61, 34.55, -7.27, 0.9958, 2, 20, 67.21, -30.77, 0.01579, 61, 33.74, -12.05, 0.98421, 2, 20, 60.58, -29.68, 0.06779, 61, 27.95, -15.31, 0.93221, 2, 20, 56.06, -33.88, 0.14621, 61, 27.03, -21.41, 0.85379, 2, 20, 52.54, -35.79, 0.17857, 61, 25.48, -25.09, 0.82143, 2, 20, 42.49, -32.74, 0.34789, 61, 15.83, -28.95, 0.65211, 2, 20, 29.29, -27.31, 0.70846, 61, 2.3, -32.89, 0.29154, 2, 20, 19.11, -21.59, 0.91626, 61, -9.07, -34.73, 0.08374, 2, 20, 10.45, -16.49, 0.98722, 61, -18.89, -36.11, 0.01278, 2, 20, 2.2, -11.05, 0.9999, 61, -28.6, -36.97, 0.0001, 1, 20, 0.2, -4.57, 1, 2, 20, 10.89, -1.82, 0.99998, 61, -27.53, -24.34, 2e-05, 2, 20, 22.47, -5.27, 0.99042, 61, -16.47, -19.85, 0.00958, 2, 20, 34.89, -8.51, 0.88271, 61, -4.89, -14.67, 0.11729, 2, 20, 47.95, -12.7, 0.28727, 61, 7.78, -9.85, 0.71273, 2, 20, 58.17, -16.52, 0.03978, 61, 18.02, -6.5, 0.96022, 2, 20, 64.37, -18.67, 0.00855, 61, 24.13, -4.33, 0.99145, 2, 20, 69.33, -20.31, 0.00151, 61, 28.97, -2.53, 0.99849, 2, 61, 38.86, 2.12, 0.7878, 62, 2.83, 5.17, 0.2122, 2, 61, 43.17, 8.5, 0.00159, 62, 8.61, 0.09, 0.99841, 1, 62, 19.58, 0.25, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66, 0, 68, 68, 70, 70, 72, 72, 74, 74, 76, 54, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86], "width": 42, "height": 95}}, "H_zj_004": {"H_zj_004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.51, 2.84, 16.54, 18.81, 33.51, 17.71, 32.48, 1.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 17}}, "H_zj_0010_1": {"H_zj_0010_1": {"type": "mesh", "uvs": [1, 0.4198, 1, 0.25282, 0.80824, 0.08585, 0.54343, 0, 0.33574, 0, 0.22151, 0.1498, 0.12286, 0.36651, 0.01382, 0.58677, 0, 0.71111, 0, 1, 0.2267, 0.78216, 0.36732, 0.64004, 0.45516, 0.55124, 0.62132, 0.36295, 0.67843, 0.27769, 0.87055, 0.40914, 0.86535, 0.25637, 0.63689, 0.13558, 0.44477, 0.22085, 0.28381, 0.39848, 0.17477, 0.569, 0.10727, 0.75729], "triangles": [12, 19, 13, 20, 6, 19, 20, 19, 12, 7, 6, 20, 11, 20, 12, 20, 8, 7, 10, 21, 20, 21, 8, 20, 11, 10, 20, 9, 8, 21, 9, 21, 10, 18, 4, 3, 5, 4, 18, 13, 18, 14, 19, 6, 5, 18, 19, 5, 19, 18, 13, 17, 3, 2, 18, 3, 17, 16, 2, 1, 17, 2, 16, 14, 17, 16, 18, 17, 14, 0, 15, 16, 14, 16, 15, 0, 16, 1], "vertices": [1, 130, -4.27, 2.9, 1, 1, 130, -0.54, -2.23, 1, 1, 130, 7.23, -4.43, 1, 2, 130, 14.71, -3.02, 0.87337, 131, -2.98, 0.83, 0.12663, 1, 131, -0.95, -4.17, 1, 1, 131, 5.44, -4.78, 1, 2, 131, 14.04, -4.06, 0.41796, 132, 0.42, -4.11, 0.58204, 1, 132, 9.25, -4.35, 1, 1, 132, 13.87, -3.3, 1, 1, 132, 24.36, -0.07, 1, 1, 132, 14.72, 3.13, 1, 2, 131, 21.27, 5.75, 0.00612, 132, 8.48, 5.03, 0.99388, 3, 130, 4.25, 15.28, 0.00759, 131, 17.29, 6.59, 0.12896, 132, 4.58, 6.22, 0.86345, 3, 130, 4.96, 6.95, 0.30696, 131, 9.03, 7.9, 0.59787, 132, -3.53, 8.24, 0.09517, 3, 130, 5.67, 3.45, 0.79958, 131, 5.47, 8.05, 0.19479, 132, -7.06, 8.7, 0.00563, 1, 130, -1.31, 4.56, 1, 1, 130, 2.21, -0.06, 1, 1, 130, 9.72, -0.28, 1, 2, 130, 11.85, 5.28, 0.12289, 131, 5.76, 1.61, 0.87711, 3, 130, 11.27, 13.2, 0.0006, 131, 13.59, 0.28, 0.16979, 132, 0.35, 0.25, 0.82961, 1, 132, 7.37, -0.55, 1, 1, 132, 14.73, -0.12, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 38, 40, 20, 22, 22, 24, 40, 42], "width": 26, "height": 38}}, "Q_zj_005": {"Q_zj_005": {"type": "mesh", "uvs": [0.58673, 0.05788, 0.67647, 0.04032, 0.77791, 0.02471, 0.85204, 0, 0.95348, 0.03252, 1, 0.10665, 1, 0.27247, 0.97689, 0.45389, 0.91252, 0.61776, 0.82278, 0.78553, 0.72914, 0.92599, 0.63745, 1, 0.49114, 0.98062, 0.30581, 0.9494, 0.19657, 0.85381, 0.06976, 0.73676, 0, 0.59045, 0, 0.53583, 0.14194, 0.49681, 0.30386, 0.39927, 0.43847, 0.31148, 0.53016, 0.21004, 0.58673, 0.1203, 0.85719, 0.14329, 0.82199, 0.29148, 0.76456, 0.47303, 0.67565, 0.65086, 0.58117, 0.81388, 0.53301, 0.90465, 0.72381, 0.1118, 0.64971, 0.26185, 0.56265, 0.41189, 0.44409, 0.51007, 0.26811, 0.65642, 0.14399, 0.6805, 0.33294, 0.79165, 0.42927, 0.72681, 0.55894, 0.58232, 0.68306, 0.42301, 0.74048, 0.28778, 0.79606, 0.12476], "triangles": [29, 1, 2, 22, 0, 1, 22, 1, 29, 40, 2, 3, 29, 2, 40, 30, 22, 29, 21, 22, 30, 39, 29, 40, 30, 29, 39, 31, 21, 30, 20, 21, 31, 31, 30, 38, 32, 20, 31, 19, 20, 32, 37, 32, 31, 33, 19, 32, 36, 32, 37, 16, 17, 18, 33, 18, 19, 34, 18, 33, 16, 18, 34, 36, 33, 32, 15, 16, 34, 35, 33, 36, 14, 34, 33, 14, 33, 35, 15, 34, 14, 13, 14, 35, 36, 37, 27, 28, 36, 27, 35, 36, 28, 10, 27, 9, 28, 27, 10, 13, 35, 28, 12, 13, 28, 11, 28, 10, 12, 28, 11, 26, 37, 38, 26, 25, 8, 9, 26, 8, 27, 37, 26, 27, 26, 9, 38, 30, 39, 25, 39, 24, 25, 24, 7, 38, 39, 25, 37, 31, 38, 8, 25, 7, 26, 38, 25, 23, 3, 4, 23, 4, 5, 40, 3, 23, 23, 5, 6, 24, 40, 23, 24, 23, 6, 39, 40, 24, 7, 24, 6], "vertices": [1, 39, 5.54, -6.81, 1, 1, 39, 0.91, -3.7, 1, 2, 43, -1.88, -10.07, 0.24352, 39, -4.14, 0, 0.75648, 2, 43, -5.03, -6.5, 0.62386, 39, -8.41, 2.13, 0.37614, 2, 43, -5.62, -0.02, 0.94181, 39, -11.39, 7.91, 0.05819, 1, 43, -2.57, 4.35, 1, 2, 43, 6.75, 8.3, 0.9072, 44, -7, 8.3, 0.0928, 3, 43, 17.49, 11.32, 0.15536, 44, 3.74, 11.32, 0.81276, 45, -10, 11.32, 0.03188, 3, 44, 14.48, 11.61, 0.48287, 45, 0.74, 11.61, 0.51602, 46, -13.01, 11.61, 0.00111, 3, 44, 26.04, 10.57, 0.01809, 45, 12.3, 10.57, 0.6339, 46, -1.45, 10.57, 0.34801, 2, 45, 22.41, 8.65, 0.02741, 46, 8.67, 8.65, 0.97259, 1, 46, 15.01, 5.27, 1, 2, 46, 17.41, -3.41, 0.92617, 42, 11.2, 29.27, 0.07383, 2, 46, 20.07, -14.56, 0.57762, 42, 18.15, 20.15, 0.42238, 2, 46, 17.3, -22.98, 0.29084, 42, 19.02, 11.34, 0.70916, 2, 46, 13.75, -32.88, 0.02689, 42, 19.78, 0.84, 0.97311, 1, 42, 16.78, -8.58, 1, 1, 42, 14.5, -11.01, 1, 2, 42, 6.55, -6.83, 0.99207, 41, 18.78, -7.03, 0.00793, 2, 42, -4.72, -4.41, 0.03314, 41, 7.58, -4.26, 0.96686, 2, 41, -2.01, -2.25, 0.07662, 40, 10.43, -2.25, 0.92338, 2, 40, 2.1, -2.68, 0.89991, 39, 14.54, -2.68, 0.10009, 1, 39, 8.23, -4.11, 1, 2, 43, 2.89, -2.79, 0.84135, 39, -2.46, 8.54, 0.15865, 4, 43, 12.05, -1.24, 0.94184, 44, -1.69, -1.24, 0.02344, 40, -6.99, 13.42, 0.00459, 39, 5.45, 13.42, 0.03012, 5, 44, 9.87, -0.14, 0.99776, 45, -3.87, -0.14, 0.00047, 41, -9.13, 18.78, 0.00026, 40, 3.31, 18.78, 0.00144, 39, 15.75, 18.78, 7e-05, 5, 45, 8.24, -0.9, 0.98627, 46, -5.51, -0.9, 0.0041, 42, -10.77, 22.3, 0.00118, 41, 2.38, 22.62, 0.00692, 40, 14.82, 22.62, 0.00153, 4, 45, 19.64, -2.33, 0.00317, 46, 5.9, -2.33, 0.96587, 42, 0.24, 25.61, 0.02225, 41, 13.48, 25.59, 0.00872, 3, 46, 12.14, -2.87, 0.95917, 42, 6.17, 27.64, 0.03878, 41, 19.47, 27.43, 0.00206, 3, 43, 4.3, -11.03, 0.05987, 44, -9.44, -11.03, 0.00015, 39, 1.95, 1.43, 0.93998, 5, 43, 14.49, -11.62, 0.08773, 44, 0.75, -11.62, 0.13669, 45, -12.99, -11.62, 0.00148, 40, -0.83, 4.71, 0.32011, 39, 11.61, 4.71, 0.45399, 7, 43, 24.99, -12.94, 0.00055, 44, 11.25, -12.94, 0.17996, 45, -2.49, -12.94, 0.13572, 46, -16.24, -12.94, 0.0015, 41, -3.04, 7.44, 0.16816, 40, 9.4, 7.44, 0.51224, 39, 21.84, 7.44, 0.00187, 6, 44, 19.59, -17.26, 0.01586, 45, 5.84, -17.26, 0.14859, 46, -7.9, -17.26, 0.04245, 42, -6.34, 6.37, 0.04814, 41, 6.31, 6.57, 0.70549, 40, 18.75, 6.57, 0.03947, 4, 45, 18.25, -23.66, 0.01441, 46, 4.51, -23.66, 0.10241, 42, 7.6, 5.54, 0.85749, 41, 20.21, 5.3, 0.02569, 3, 45, 22.56, -30.06, 2e-05, 46, 8.82, -30.06, 0.02109, 42, 14.13, 1.43, 0.97889, 4, 45, 24.3, -16.8, 0.0151, 46, 10.56, -16.8, 0.43138, 42, 10.36, 14.26, 0.52872, 41, 23.24, 13.93, 0.0248, 5, 45, 18.37, -12.93, 0.09771, 46, 4.63, -12.93, 0.46634, 42, 3.37, 15.4, 0.32342, 41, 16.29, 15.28, 0.1125, 40, 28.73, 15.28, 3e-05, 6, 44, 20.91, -9.09, 0.0288, 45, 7.17, -9.09, 0.54389, 46, -6.58, -9.09, 0.08973, 42, -8.43, 14.38, 0.0403, 41, 4.46, 14.64, 0.23997, 40, 16.9, 14.64, 0.0573, 6, 43, 22.75, -5.91, 0.00543, 44, 9.01, -5.91, 0.65243, 45, -4.74, -5.91, 0.09195, 41, -7.76, 13.11, 0.029, 40, 4.68, 13.11, 0.20194, 39, 17.12, 13.11, 0.01926, 4, 43, 13.79, -5.91, 0.36251, 44, 0.04, -5.91, 0.3075, 40, -3.63, 9.74, 0.09094, 39, 8.81, 9.74, 0.23905, 2, 43, 3.31, -6.67, 0.46051, 39, -0.61, 5.1, 0.53949], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80], "width": 61, "height": 61}}, "Q_zj_006": {"Q_zj_006": {"type": "mesh", "uvs": [0, 0.11512, 0.0231, 0.03979, 0.17667, 0, 0.39687, 0, 0.55913, 0.01938, 0.65474, 0.13866, 0.77354, 0.2909, 0.90103, 0.44784, 1, 0.56869, 1, 0.63617, 0.87785, 0.65815, 0.79672, 0.6864, 0.86046, 0.79626, 0.90103, 0.88729, 0.7098, 0.94535, 0.45772, 0.98459, 0.12451, 0.99244, 0.11292, 0.8606, 0.09554, 0.68954, 0.05498, 0.49492, 0.026, 0.34897, 0.01731, 0.23283, 0.22302, 0.10257, 0.25779, 0.21243, 0.29836, 0.32856, 0.35051, 0.48708, 0.39688, 0.66286, 0.46352, 0.85747, 0.69242, 0.82137, 0.61129, 0.65971, 0.54464, 0.46981, 0.48379, 0.30502, 0.44033, 0.18104, 0.42004, 0.08059, 0.78803, 0.53573, 0.70979, 0.43999, 0.63156, 0.28933], "triangles": [16, 17, 15, 15, 27, 14, 15, 17, 27, 27, 28, 14, 14, 28, 13, 28, 12, 13, 17, 26, 27, 17, 18, 26, 27, 29, 28, 27, 26, 29, 28, 11, 12, 28, 29, 11, 18, 25, 26, 10, 11, 34, 26, 30, 29, 11, 29, 34, 34, 29, 30, 9, 10, 8, 10, 34, 8, 34, 7, 8, 34, 35, 7, 18, 19, 25, 26, 25, 30, 34, 30, 35, 19, 24, 25, 25, 24, 30, 19, 20, 24, 24, 31, 30, 35, 31, 36, 35, 30, 31, 35, 6, 7, 35, 36, 6, 24, 21, 23, 24, 20, 21, 24, 23, 31, 31, 32, 36, 36, 5, 6, 23, 32, 31, 36, 32, 5, 23, 0, 22, 23, 21, 0, 23, 22, 32, 22, 33, 32, 32, 33, 5, 33, 4, 5, 0, 1, 22, 1, 2, 22, 22, 3, 33, 22, 2, 3, 33, 3, 4], "vertices": [3, 47, 1.18, -14.34, 0.89908, 48, -9.77, -14.34, 0.09237, 49, -20.72, -14.34, 0.00855, 3, 47, -4.09, -12.79, 0.9759, 48, -15.05, -12.79, 0.02389, 49, -26, -12.79, 0.00021, 2, 47, -6.22, -6.5, 0.99917, 48, -17.17, -6.5, 0.00083, 1, 47, -5.18, 2.02, 1, 2, 47, -3.03, 8.14, 0.99914, 48, -13.98, 8.14, 0.00086, 2, 47, 5.94, 10.8, 0.76707, 48, -5.01, 10.8, 0.23293, 4, 47, 17.38, 14.08, 0.08491, 48, 6.43, 14.08, 0.73235, 49, -4.52, 14.08, 0.13133, 50, -15.47, 14.08, 0.05141, 4, 48, 18.25, 17.66, 0.24475, 49, 7.3, 17.66, 0.33722, 50, -3.66, 17.66, 0.41642, 51, -14.61, 17.66, 0.00162, 4, 48, 27.35, 20.44, 0.07382, 49, 16.4, 20.44, 0.20185, 50, 5.45, 20.44, 0.67642, 51, -5.5, 20.44, 0.04791, 4, 48, 32.17, 19.85, 0.05212, 49, 21.22, 19.85, 0.16627, 50, 10.27, 19.85, 0.70176, 51, -0.68, 19.85, 0.07985, 4, 48, 33.17, 14.93, 0.03602, 49, 22.22, 14.93, 0.12411, 50, 11.27, 14.93, 0.66744, 51, 0.31, 14.93, 0.17242, 4, 48, 34.81, 11.55, 0.01248, 49, 23.86, 11.55, 0.04875, 50, 12.9, 11.55, 0.4841, 51, 1.95, 11.55, 0.45467, 3, 49, 32.01, 13.06, 0.00029, 50, 21.06, 13.06, 0.05807, 51, 10.1, 13.06, 0.94164, 2, 50, 27.75, 13.84, 0.00237, 51, 16.8, 13.84, 0.99763, 1, 51, 20.05, 5.93, 1, 1, 51, 21.67, -4.17, 1, 2, 50, 31.62, -17.13, 7e-05, 51, 20.67, -17.13, 0.99993, 3, 49, 33.09, -16.44, 0.00027, 50, 22.14, -16.44, 0.05987, 51, 11.19, -16.44, 0.93986, 3, 49, 20.78, -15.63, 0.09789, 50, 9.83, -15.63, 0.45745, 51, -1.12, -15.63, 0.44465, 5, 47, 28.59, -15.51, 0.00038, 48, 17.64, -15.51, 0.12201, 49, 6.68, -15.51, 0.62267, 50, -4.27, -15.51, 0.24076, 51, -15.22, -15.51, 0.01418, 4, 47, 18.02, -15.36, 0.10598, 48, 7.07, -15.36, 0.49171, 49, -3.88, -15.36, 0.38582, 50, -14.84, -15.36, 0.0165, 3, 47, 9.68, -14.69, 0.50702, 48, -1.27, -14.69, 0.3933, 49, -12.23, -14.69, 0.09967, 3, 47, 1.33, -5.6, 0.97579, 48, -9.62, -5.6, 0.02299, 49, -20.57, -5.6, 0.00122, 3, 47, 9.35, -5.21, 0.64927, 48, -1.6, -5.21, 0.32561, 49, -12.55, -5.21, 0.02511, 4, 47, 17.84, -4.64, 0.0183, 48, 6.89, -4.64, 0.79704, 49, -4.06, -4.64, 0.1845, 50, -15.01, -4.64, 0.00015, 4, 48, 18.46, -4, 0.00797, 49, 7.51, -4, 0.89164, 50, -3.44, -4, 0.10009, 51, -14.39, -4, 0.0003, 3, 49, 20.29, -3.73, 0.00841, 50, 9.34, -3.73, 0.74105, 51, -1.61, -3.73, 0.25055, 2, 50, 23.57, -2.84, 0.00017, 51, 12.61, -2.84, 0.99983, 2, 50, 22.06, 6.34, 0.02135, 51, 11.11, 6.34, 0.97865, 4, 48, 32.03, 4.6, 0.00321, 49, 21.08, 4.6, 0.01172, 50, 10.13, 4.6, 0.66253, 51, -0.83, 4.6, 0.32253, 3, 48, 18.14, 3.67, 0.06844, 49, 7.19, 3.67, 0.80424, 50, -3.76, 3.67, 0.12732, 3, 47, 17.03, 2.74, 0.00984, 48, 6.08, 2.74, 0.98862, 50, -15.83, 2.74, 0.00154, 2, 47, 7.96, 2.13, 0.9666, 48, -2.99, 2.13, 0.0334, 1, 47, 0.69, 2.22, 1, 4, 48, 24, 12.52, 0.10325, 49, 13.05, 12.52, 0.27027, 50, 2.09, 12.52, 0.60201, 51, -8.86, 12.52, 0.02447, 4, 47, 27.74, 10.32, 5e-05, 48, 16.79, 10.32, 0.29364, 49, 5.84, 10.32, 0.44559, 50, -5.12, 10.32, 0.26071, 4, 47, 16.6, 8.6, 0.10682, 48, 5.65, 8.6, 0.81598, 49, -5.3, 8.6, 0.05865, 50, -16.25, 8.6, 0.01854], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42, 4, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 28, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 20, 68, 68, 70, 70, 72], "width": 39, "height": 72}}, "Q_zj_007": {"Q_zj_007": {"type": "mesh", "uvs": [0.00328, 0.21788, 0.00838, 0, 0.12228, 0, 0.26338, 0.07168, 0.48438, 0.16688, 0.63568, 0.20428, 0.77848, 0.13628, 0.88728, 0, 1, 0.04448, 0.98928, 0.26888, 1, 0.45588, 1, 0.68708, 1, 0.88088, 0.87878, 1, 0.71898, 1, 0.57618, 1, 0.34328, 1, 0.15288, 0.90808, 0.01858, 0.78908, 0.02708, 0.54428, 0.05258, 0.36068, 0.19368, 0.50348, 0.37558, 0.57828, 0.56768, 0.61908, 0.74108, 0.61908, 0.90598, 0.56128, 0.87198, 0.36408, 0.74108, 0.39808, 0.56938, 0.41848, 0.39258, 0.39128, 0.20728, 0.27568, 0.07808, 0.17028, 0.17838, 0.71088, 0.37728, 0.75508, 0.58638, 0.80608, 0.72238, 0.80268, 0.90598, 0.78568], "triangles": [32, 22, 33, 18, 19, 32, 17, 18, 32, 16, 32, 33, 17, 32, 16, 15, 33, 34, 16, 33, 15, 14, 34, 35, 15, 34, 14, 31, 1, 2, 0, 1, 31, 9, 7, 8, 30, 2, 3, 31, 2, 30, 20, 0, 31, 20, 31, 30, 26, 6, 7, 26, 7, 9, 29, 3, 4, 30, 3, 29, 27, 5, 6, 27, 6, 26, 28, 4, 5, 28, 5, 27, 29, 4, 28, 26, 9, 10, 21, 20, 30, 21, 30, 29, 19, 20, 21, 25, 26, 10, 22, 21, 29, 23, 29, 28, 22, 29, 23, 24, 28, 27, 23, 28, 24, 24, 27, 26, 24, 26, 25, 25, 10, 11, 32, 19, 21, 32, 21, 22, 33, 22, 23, 36, 24, 25, 36, 25, 11, 35, 23, 24, 35, 24, 36, 34, 23, 35, 33, 23, 34, 36, 11, 12, 13, 35, 36, 13, 36, 12, 14, 35, 13], "vertices": [2, 3, 12.19, 22.3, 0.65456, 16, -10.9, -23.97, 0.34544, 2, 3, 16.47, 21.47, 0.66807, 16, -15.26, -24.18, 0.33193, 2, 3, 15.81, 16.96, 0.6787, 16, -15.69, -19.64, 0.3213, 2, 3, 13.58, 11.58, 0.73492, 16, -14.79, -13.89, 0.26508, 2, 3, 10.42, 3.11, 0.95094, 16, -13.72, -4.91, 0.04906, 2, 3, 8.81, -2.77, 0.99997, 16, -13.55, 1.19, 3e-05, 2, 3, 9.33, -8.62, 0.99999, 16, -15.44, 6.75, 1e-05, 1, 3, 11.4, -13.32, 1, 2, 3, 9.87, -17.65, 0.99916, 16, -18.09, 15.4, 0.00084, 2, 3, 5.5, -16.58, 0.98196, 16, -13.59, 15.39, 0.01804, 2, 3, 1.73, -16.47, 0.93794, 16, -9.9, 16.17, 0.06206, 2, 3, -2.84, -15.8, 0.85537, 16, -5.3, 16.6, 0.14463, 2, 3, -6.68, -15.24, 0.80279, 16, -1.44, 16.97, 0.19721, 2, 3, -8.34, -10.1, 0.7136, 16, 1.39, 12.36, 0.2864, 2, 3, -7.42, -3.78, 0.41698, 16, 1.99, 6, 0.58302, 2, 3, -6.59, 1.88, 0.00476, 16, 2.52, 0.31, 0.99524, 2, 3, -5.25, 11.09, 0.24599, 16, 3.39, -8.96, 0.75401, 2, 3, -2.34, 18.37, 0.42737, 16, 2.28, -16.72, 0.57263, 2, 3, 0.79, 23.34, 0.48324, 16, 0.41, -22.29, 0.51676, 2, 3, 5.59, 22.3, 0.53619, 16, -4.49, -22.41, 0.46381, 2, 3, 9.08, 20.76, 0.61333, 16, -8.25, -21.74, 0.38667, 2, 3, 5.44, 15.59, 0.57461, 16, -5.93, -15.85, 0.42539, 2, 3, 2.91, 8.6, 0.61044, 16, -5.12, -8.47, 0.38956, 2, 3, 0.99, 1.11, 0.94639, 16, -5.03, -0.74, 0.05361, 2, 3, -0.01, -5.75, 0.88894, 16, -5.68, 6.16, 0.11106, 2, 3, 0.19, -12.44, 0.90345, 16, -7.45, 12.62, 0.09655, 2, 3, 4.29, -11.67, 0.9716, 16, -11.25, 10.9, 0.0284, 2, 3, 4.37, -6.39, 0.98891, 16, -10.08, 5.75, 0.01109, 2, 3, 4.95, 0.47, 0.99672, 16, -9.03, -1.05, 0.00328, 2, 3, 6.51, 7.39, 0.79364, 16, -8.91, -8.14, 0.20636, 2, 3, 9.87, 14.39, 0.67663, 16, -10.52, -15.74, 0.32337, 2, 3, 12.7, 19.2, 0.66091, 16, -12.13, -21.08, 0.33909, 2, 3, 1.42, 16.79, 0.47057, 16, -1.74, -16.07, 0.52943, 2, 3, -0.6, 9.04, 0.38703, 16, -1.61, -8.07, 0.61297, 2, 3, -2.81, 0.91, 0.29244, 16, -1.38, 0.35, 0.70756, 2, 3, -3.53, -4.48, 0.62242, 16, -1.96, 5.76, 0.37758, 2, 3, -4.25, -11.8, 0.79822, 16, -2.98, 13.04, 0.20178], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 18, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 38, 64, 64, 66, 66, 68, 68, 70, 70, 72], "width": 40, "height": 20}}, "Q_zj_008": {"Q_zj_008": {"type": "mesh", "uvs": [0.51919, 0, 0.62533, 0, 0.74662, 0.0184, 0.84587, 0.12632, 0.88584, 0.29188, 0.91479, 0.45892, 0.95338, 0.51953, 1, 0.57127, 0.99749, 0.66736, 0.97406, 0.79301, 0.93408, 0.90979, 0.84725, 0.96892, 0.77557, 1, 0.65703, 1, 0.57295, 1, 0.46544, 1, 0.34138, 1, 0.1939, 0.95266, 0.05468, 0.87431, 0, 0.76788, 0.04641, 0.64371, 0.13945, 0.53682, 0.17256, 0.43172, 0.1964, 0.27264, 0.24143, 0.12209, 0.36591, 0.02551, 0.5309, 0.14026, 0.53877, 0.28505, 0.54794, 0.42985, 0.55057, 0.6351, 0.54139, 0.81785, 0.69213, 0.12198, 0.73277, 0.26818, 0.74325, 0.46218, 0.75505, 0.65759, 0.78258, 0.8305, 0.90841, 0.64072, 0.37754, 0.13463, 0.35132, 0.30614, 0.33428, 0.44531, 0.3369, 0.60136, 0.29234, 0.81925, 0.1429, 0.7335], "triangles": [21, 40, 42, 20, 21, 42, 39, 21, 22, 19, 20, 42, 41, 42, 40, 41, 40, 30, 18, 19, 42, 17, 42, 41, 18, 42, 17, 15, 16, 41, 17, 41, 16, 30, 15, 41, 39, 22, 23, 29, 40, 39, 40, 21, 39, 28, 39, 38, 29, 39, 28, 30, 40, 29, 38, 24, 37, 23, 24, 38, 27, 38, 37, 38, 39, 23, 28, 38, 27, 37, 25, 0, 24, 25, 37, 26, 0, 1, 37, 0, 26, 27, 37, 26, 36, 6, 7, 34, 33, 36, 8, 36, 7, 9, 36, 8, 30, 29, 34, 35, 34, 36, 35, 36, 9, 30, 34, 35, 10, 35, 9, 11, 35, 10, 13, 14, 30, 35, 13, 30, 15, 30, 14, 35, 12, 13, 11, 12, 35, 29, 28, 33, 36, 33, 5, 36, 5, 6, 29, 33, 34, 32, 3, 4, 28, 27, 32, 33, 32, 4, 33, 4, 5, 28, 32, 33, 31, 1, 2, 31, 2, 3, 26, 1, 31, 32, 31, 3, 27, 26, 31, 27, 31, 32], "vertices": [3, 110, -2.45, -17.89, 0.24996, 111, -17.29, -18.14, 0, 35, -2.41, 7.02, 0.75004, 2, 110, -1.45, -10.1, 0.60923, 35, -4.06, 14.7, 0.39077, 2, 110, 0.96, -1.36, 0.9704, 35, -4.71, 23.74, 0.0296, 2, 110, 9.28, 4.98, 0.98986, 111, -6.03, 4.96, 0.01014, 3, 110, 20.98, 6.46, 0.01192, 111, 5.65, 6.67, 0.98737, 112, -8.65, 7.79, 0.0007, 3, 111, 17.34, 7.56, 0.20338, 112, 3.06, 7.25, 0.7692, 113, -8.87, 8.52, 0.02743, 3, 111, 21.8, 9.95, 0.00761, 112, 7.78, 9.08, 0.7442, 113, -3.94, 9.69, 0.24818, 2, 112, 12.04, 11.62, 0.46483, 113, 0.63, 11.64, 0.53517, 2, 112, 18.45, 9.93, 0.13566, 113, 6.75, 9.1, 0.86434, 1, 113, 14.23, 4.39, 1, 3, 113, 20.71, -1.25, 0.98467, 37, 20.37, 56.6, 0.00957, 38, 5.17, 56.9, 0.00576, 4, 112, 36.18, -5.63, 0.00065, 113, 22.22, -8.71, 0.92687, 37, 26.61, 52.24, 0.04497, 38, 11.52, 52.7, 0.02751, 4, 112, 37.06, -11.29, 0.00864, 113, 22.33, -14.43, 0.85445, 37, 30.63, 48.17, 0.08258, 38, 15.64, 48.74, 0.05433, 4, 112, 35.07, -19.83, 0.03722, 113, 19.2, -22.62, 0.67888, 37, 34.01, 40.08, 0.15684, 38, 19.23, 40.73, 0.12705, 4, 112, 33.65, -25.89, 0.05279, 113, 16.98, -28.44, 0.52928, 37, 36.4, 34.34, 0.20493, 38, 21.77, 35.05, 0.21299, 4, 112, 31.84, -33.63, 0.05238, 113, 14.14, -35.87, 0.35618, 37, 39.47, 26.99, 0.22543, 38, 25.02, 27.79, 0.36601, 4, 112, 29.75, -42.57, 0.03468, 113, 10.86, -44.44, 0.20854, 37, 43, 18.52, 0.18099, 38, 28.77, 19.41, 0.57579, 4, 112, 24.08, -52.46, 0.0113, 113, 3.92, -53.47, 0.08049, 37, 44.19, 7.19, 0.0733, 38, 30.25, 8.12, 0.83492, 4, 112, 16.47, -61.26, 0.00038, 113, -4.81, -61.17, 0.01336, 37, 43.17, -4.4, 0.00382, 38, 29.53, -3.49, 0.98244, 2, 113, -13.11, -62.32, 0.00094, 38, 24.48, -10.19, 0.99906, 1, 38, 15.25, -10.55, 1, 1, 38, 5.71, -7.28, 1, 3, 36, 23.01, -8.54, 0.02592, 37, 11.63, -8.11, 0.29066, 38, -1.91, -8.01, 0.68342, 4, 35, 21, -12.38, 0.00944, 36, 12.11, -10.73, 0.62449, 37, 0.82, -10.71, 0.30201, 38, -12.65, -10.89, 0.06407, 4, 35, 10.15, -11.3, 0.34786, 36, 1.22, -11.25, 0.6507, 37, -10.05, -11.63, 0.0012, 38, -23.49, -12.09, 0.00024, 2, 35, 1.7, -3.7, 0.99361, 36, -8.25, -4.95, 0.00639, 6, 110, 7.26, -18.26, 0.26291, 111, -7.58, -18.31, 0.03965, 112, -24.82, -15.41, 0.0008, 35, 6.87, 9.9, 0.62097, 36, -5.11, 9.26, 0.07144, 37, -17.15, 8.62, 0.00423, 7, 110, 17.25, -18.96, 0.15748, 111, 2.42, -18.81, 0.1853, 112, -14.96, -17.12, 0.03208, 113, -30, -13.19, 6e-05, 35, 16.51, 12.57, 0.18965, 36, 4.04, 13.3, 0.31468, 37, -8.15, 13, 0.12076, 7, 110, 27.24, -19.55, 0.03887, 111, 12.42, -19.21, 0.2155, 112, -5.08, -18.73, 0.15999, 113, -20.43, -16.12, 0.01733, 35, 26.14, 15.34, 0.0263, 36, 13.16, 17.44, 0.16393, 37, 0.81, 17.48, 0.37808, 7, 110, 41.31, -21.17, 0.00028, 111, 26.52, -20.54, 0.04662, 112, 8.75, -21.77, 0.25139, 113, -7.13, -21, 0.19061, 36, 26.36, 22.58, 0.01817, 37, 13.8, 23.11, 0.43203, 38, -0.53, 23.25, 0.0609, 6, 111, 38.99, -22.57, 0.00246, 112, 20.88, -25.3, 0.11288, 113, 4.41, -26.13, 0.39837, 36, 38.4, 26.36, 0.00029, 37, 25.7, 27.34, 0.28541, 38, 11.25, 27.79, 0.20058, 5, 110, 7.53, -6.27, 0.82968, 111, -7.55, -6.32, 0.01626, 35, 3.13, 21.3, 0.13969, 36, -10.48, 19.99, 0.01323, 37, -22.9, 19.15, 0.00114, 6, 110, 17.92, -4.57, 0.25126, 111, 2.8, -4.41, 0.67538, 112, -12.83, -2.87, 0.00038, 35, 12.36, 26.36, 0.03327, 36, -2.08, 26.34, 0.02607, 37, -14.75, 25.81, 0.01364, 8, 110, 31.3, -5.5, 0.00393, 111, 16.19, -5.08, 0.37578, 112, 0.38, -5.16, 0.53765, 113, -13.19, -3.42, 0.00262, 35, 25.28, 29.94, 0.00279, 36, 10.19, 31.76, 0.01896, 37, -2.7, 31.68, 0.05825, 38, -17.25, 31.39, 1e-05, 6, 111, 29.69, -5.66, 0.00265, 112, 13.71, -7.39, 0.4172, 113, -0.28, -7.42, 0.48248, 36, 22.51, 37.3, 0.00294, 37, 9.41, 37.67, 0.08337, 38, -5.3, 37.7, 0.01136, 4, 112, 25.79, -8.12, 0.0271, 113, 11.59, -9.77, 0.87647, 37, 19.64, 44.15, 0.06574, 38, 4.76, 44.43, 0.03069, 2, 112, 15.16, 3.93, 0.15362, 113, 2.68, 3.6, 0.84638, 1, 35, 8.87, -1.28, 1, 6, 110, 16.92, -32.9, 0.00102, 111, 2.37, -32.76, 0.00254, 112, -16.7, -30.95, 0.00109, 113, -33.6, -26.67, 1e-05, 35, 20.85, -0.68, 0.00025, 36, 10.26, 0.82, 0.99508, 6, 110, 26.29, -35.37, 0.00061, 111, 11.78, -35.04, 0.0101, 112, -7.64, -34.37, 0.01337, 113, -25.08, -31.27, 0.00319, 36, 19.7, 3, 0.00134, 37, 7.88, 3.3, 0.9714, 6, 111, 22.51, -36.01, 0.00948, 112, 2.89, -36.63, 0.04178, 113, -14.95, -34.93, 0.03725, 36, 29.71, 6.95, 0.0002, 37, 17.75, 7.62, 0.66007, 38, 3.8, 7.87, 0.25122, 5, 111, 37.1, -40.9, 0.00013, 112, 16.78, -43.27, 0.0272, 113, -2.08, -43.38, 0.10722, 37, 32.89, 10.37, 0.16508, 38, 18.87, 11.01, 0.70038, 4, 112, 8.5, -52.69, 0.00054, 113, -11.56, -51.6, 0.00674, 37, 31.69, -2.11, 0.00342, 38, 17.99, -1.5, 0.98931], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 0, 52, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 64, 66, 66, 68, 68, 70, 74, 76, 76, 78, 78, 80, 80, 82], "width": 74, "height": 69}}, "Q_zj_0010": {"Q_zj_0010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-0.81, -20.69, 4.96, 23.93, 55.53, 17.4, 49.77, -27.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 45, "height": 51}}, "C_zj_1": {"C_zj_001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.6, -22.15, 19.5, 22.76, 69.39, 19.54, 66.49, -25.37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 45, "height": 50}}, "Q_zj_008_1": {"Q_zj_008_1": {"type": "mesh", "uvs": [0, 0.56597, 0.11624, 0.48529, 0.28234, 0.52672, 0.45754, 0.49183, 0.60544, 0.37626, 0.71921, 0.23889, 0.77609, 0.06444, 0.82615, 0, 0.93309, 0, 1, 0.0579, 1, 0.23017, 1, 0.42423, 0.94219, 0.62703, 0.86028, 0.79711, 0.74196, 0.94102, 0.62819, 1, 0.42569, 1, 0.25503, 0.99118, 0.0548, 0.96501, 0.00475, 0.832, 0, 0.69244, 0.91314, 0.12903, 0.9151, 0.23591, 0.89553, 0.35966, 0.84662, 0.55278, 0.76836, 0.69903, 0.65292, 0.81341, 0.45532, 0.86216, 0.22836, 0.85278, 0.85445, 0.10466, 0.81532, 0.23403, 0.76445, 0.33153, 0.70379, 0.45903, 0.58445, 0.57528, 0.40249, 0.65403, 0.21075, 0.67841], "triangles": [18, 28, 17, 18, 19, 28, 19, 35, 28, 19, 20, 35, 20, 0, 35, 0, 1, 35, 35, 1, 2, 28, 34, 27, 28, 35, 34, 35, 2, 34, 34, 3, 33, 34, 2, 3, 16, 27, 15, 15, 26, 14, 15, 27, 26, 16, 17, 27, 17, 28, 27, 14, 26, 13, 27, 34, 26, 34, 33, 26, 26, 25, 13, 26, 33, 25, 13, 25, 12, 25, 24, 12, 25, 32, 24, 12, 24, 11, 33, 32, 25, 33, 4, 32, 33, 3, 4, 24, 32, 23, 23, 32, 31, 32, 4, 31, 4, 5, 31, 24, 23, 11, 11, 23, 10, 23, 22, 10, 10, 22, 21, 21, 9, 10, 31, 30, 23, 23, 30, 22, 31, 5, 30, 5, 6, 30, 22, 30, 21, 30, 29, 21, 30, 6, 29, 9, 21, 8, 6, 7, 29, 21, 29, 8, 29, 7, 8], "vertices": [1, 117, 16.4, -5.95, 1, 2, 116, 28.34, -11.78, 0.03818, 117, 8.11, -11.38, 0.96182, 2, 116, 17.23, -7.69, 0.79301, 117, -3.2, -7.87, 0.20699, 2, 115, 23.66, -10.22, 0.35626, 116, 4.95, -9.01, 0.64374, 3, 114, 27.55, -13.91, 0.07265, 115, 10.5, -9.95, 0.91596, 116, -6.02, -16.29, 0.0114, 2, 114, 15.64, -9.72, 0.79071, 115, -1.89, -12.41, 0.20929, 1, 114, 2.49, -10.1, 1, 1, 114, -3.02, -8.35, 1, 2, 114, -5.43, -1.38, 0.97673, 118, -7.39, -4.35, 0.02327, 2, 114, -3, 4.35, 0.44091, 118, -4.2, 0.99, 0.55909, 1, 118, 7.97, 3.41, 1, 2, 118, 21.67, 6.13, 0.66097, 119, -2.15, 5.81, 0.33903, 1, 119, 12.84, 7.91, 1, 2, 119, 26.32, 7.55, 0.51206, 120, -2.04, 7.55, 0.48794, 1, 120, 10.68, 11.04, 1, 1, 120, 19.52, 9.8, 1, 3, 120, 30.77, 1.5, 0.82925, 116, 10.72, 27.19, 0.07788, 117, -11.49, 26.63, 0.09288, 3, 120, 39.86, -6, 0.43282, 116, 22.38, 25.4, 0.15642, 117, 0.25, 25.45, 0.41077, 3, 120, 49.86, -15.72, 0.1824, 116, 35.94, 22.17, 0.05344, 117, 13.96, 22.92, 0.76416, 3, 120, 46.95, -25.48, 0.09065, 116, 38.44, 12.31, 0.01538, 117, 16.96, 13.2, 0.89398, 2, 120, 41.25, -33.76, 0.009, 117, 16.82, 3.14, 0.991, 2, 114, 3.8, 0.35, 0.94341, 118, 1.99, -3.89, 0.05659, 2, 114, 11.03, 2.99, 0.43748, 118, 9.51, -2.26, 0.56252, 3, 114, 19.89, 4.62, 0.2536, 118, 18.52, -1.85, 0.74533, 115, -5.53, 2.1, 0.00107, 2, 115, 6.05, 10.51, 0.01068, 119, 10.53, -0.26, 0.98932, 3, 115, 16.98, 15.03, 0.02288, 119, 22.33, -1.07, 0.8834, 120, -1.13, -1.9, 0.09372, 3, 115, 28.38, 16.15, 0.00014, 120, 10.17, 0, 0.99961, 116, -6.2, 15.36, 0.00026, 3, 120, 23.23, -5.27, 0.75425, 116, 7.72, 17.51, 0.20356, 117, -13.99, 16.82, 0.04219, 3, 120, 35.43, -15.11, 0.26703, 116, 23.23, 15.31, 0.22209, 117, 1.62, 15.41, 0.51089, 1, 114, 3.46, -4.05, 1, 2, 114, 13.15, -3.56, 0.96938, 115, -7.17, -8.38, 0.03062, 2, 114, 20.93, -4.59, 0.61437, 115, 0.05, -5.3, 0.38563, 3, 114, 30.97, -5.55, 0.00398, 115, 9.19, -1.02, 0.99601, 116, -12.19, -9.69, 1e-05, 4, 115, 20.88, 0.03, 0.99789, 119, 19.16, -16.24, 0.00049, 120, 3.79, -16.6, 0.00064, 116, -3.18, -2.17, 0.00098, 2, 120, 17.26, -19.49, 0.0383, 116, 9.87, 2.24, 0.9617, 3, 120, 28.95, -25.93, 0.01997, 116, 23.21, 2.69, 0.17373, 117, 2.25, 2.81, 0.8063], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 16, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 69, "height": 72}}, "bianjie": {"bianjie": {"type": "boundingbox", "vertexCount": 4, "vertices": [-59.04, 216.18, -46.85, -6.42, 65.51, -7.48, 69.75, 219.36]}}, "C_zj_001": {"C_zj_001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-3.77, -21.03, 2, 23.6, 51.58, 17.19, 45.81, -27.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 45, "height": 50}}, "H_zj_006": {"H_zj_006": {"type": "mesh", "uvs": [0.2395, 0, 0.40584, 0.01633, 0.5466, 0.12591, 0.70441, 0.28824, 0.84943, 0.44042, 0.96886, 0.55608, 1, 0.63319, 1, 0.69406, 0.86223, 0.7387, 0.84943, 0.81378, 0.88355, 0.86857, 0.9117, 0.91182, 0.92419, 0.93102, 0.939, 0.95379, 0.8537, 1, 0.71294, 1, 0.53807, 0.99843, 0.50821, 0.9477, 0.54019, 0.91118, 0.57219, 0.87465, 0.56792, 0.81581, 0.46982, 0.80566, 0.35892, 0.73058, 0.14139, 0.70624, 0.07315, 0.60884, 0.04329, 0.44854, 0, 0.30853, 0, 0.15634, 0.10727, 0.02648, 0.29445, 0.15698, 0.3989, 0.30681, 0.46501, 0.46082, 0.54818, 0.61478, 0.68217, 0.74219, 0.73875, 0.82318, 0.70597, 0.92069], "triangles": [13, 14, 12, 16, 35, 15, 12, 14, 35, 12, 35, 11, 11, 35, 10, 15, 35, 14, 35, 34, 10, 35, 17, 18, 35, 16, 17, 18, 19, 35, 34, 9, 10, 35, 19, 34, 19, 20, 34, 8, 9, 34, 34, 20, 33, 20, 21, 33, 8, 34, 33, 21, 22, 33, 8, 33, 32, 33, 22, 32, 7, 8, 6, 32, 22, 24, 8, 32, 6, 32, 5, 6, 32, 4, 5, 32, 31, 4, 22, 23, 24, 24, 31, 32, 24, 25, 31, 25, 30, 31, 31, 3, 4, 31, 30, 3, 25, 26, 30, 26, 29, 30, 26, 27, 29, 30, 2, 3, 30, 29, 2, 27, 28, 29, 28, 0, 29, 29, 1, 2, 29, 0, 1], "vertices": [1, 90, -18.28, -1.83, 1, 1, 90, -15.96, 6.16, 1, 2, 90, -4.17, 12.15, 0.99912, 91, -34.61, 28, 0.00088, 2, 90, 13.11, 18.55, 0.86, 91, -16.21, 27.08, 0.14, 2, 90, 29.29, 24.41, 0.29974, 91, 0.97, 26.1, 0.70026, 2, 90, 41.63, 29.31, 0.03022, 91, 14.24, 25.75, 0.96978, 2, 90, 49.66, 30.21, 0.0012, 91, 21.98, 23.41, 0.9988, 1, 91, 27.53, 20.5, 1, 1, 91, 28.47, 12.39, 1, 2, 91, 35.03, 8.24, 0.92263, 92, -5.16, 7.39, 0.07737, 2, 91, 40.8, 7.1, 0.34068, 92, 0.73, 7.37, 0.65932, 2, 91, 45.39, 6.25, 0.16778, 92, 5.39, 7.41, 0.83222, 2, 91, 47.42, 5.88, 0.09103, 92, 7.46, 7.43, 0.90897, 1, 92, 9.92, 7.45, 1, 1, 92, 13.27, 2.08, 1, 1, 92, 11.29, -4.52, 1, 2, 91, 44.79, -14.1, 0.00376, 92, 8.68, -12.69, 0.99624, 2, 91, 39.48, -12.97, 0.0592, 92, 3.25, -12.59, 0.9408, 2, 91, 36.88, -9.84, 0.32576, 92, 0.1, -10, 0.67424, 2, 91, 34.27, -6.7, 0.59243, 92, -3.06, -7.42, 0.40757, 2, 91, 28.81, -4.07, 0.98496, 92, -8.92, -5.88, 0.01504, 2, 90, 65.34, 2.91, 0.00226, 91, 25.65, -7.85, 0.99774, 2, 90, 57.2, -1.9, 0.15397, 91, 16.28, -9.07, 0.84603, 2, 90, 53.86, -12.33, 0.65776, 91, 9.11, -17.34, 0.34224, 2, 90, 43.6, -14.87, 0.94952, 91, -1.33, -15.65, 0.05048, 1, 90, 27.03, -15.04, 1, 1, 90, 12.48, -16.02, 1, 1, 90, -3.14, -14.79, 1, 1, 90, -16.07, -8.5, 1, 1, 90, -1.95, -0.41, 1, 2, 90, 13.84, 3.48, 0.9881, 91, -21.47, 12.93, 0.0119, 2, 90, 29.91, 5.46, 0.71648, 91, -5.92, 8.44, 0.28352, 1, 91, 10.02, 4.68, 1, 1, 91, 24.69, 4.4, 1, 1, 91, 33.37, 2.99, 1, 2, 91, 41.52, -3.1, 0.04708, 92, 3.37, -2.5, 0.95292], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 0, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 20, 22, 34, 36, 36, 38, 22, 24, 24, 26], "width": 49, "height": 103}}, "rootcut": {"rootcut": {"type": "clipping", "end": "rootcut", "vertexCount": 4, "vertices": [-144.77, 879.44, 605.57, 879.01, 605.52, -618.96, -144.4, -619.14], "color": "ce3a3aff"}}, "Q_zj_0011": {"Q_zj_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11.23, -18.77, 14.82, 8.99, 27.71, 7.33, 24.12, -20.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 28, "height": 13}}, "Q_zj_0012": {"Q_zj_0012": {"x": 10.89, "y": 2.61, "rotation": 62.92, "width": 6, "height": 13}}, "Q_zj_0013": {"Q_zj_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [89.13, -43.79, -25.1, -7.03, -12.23, 32.95, 102, -3.81], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 42}}, "Q_zj_14": {"Q_zj_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [89.86, -43.44, -24.37, -6.68, -11.51, 33.3, 102.72, -3.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 120, "height": 42}}, "Q_zj_15": {"Q_zj_0013": {"type": "mesh", "uvs": [0.00743, 0.70941, 0, 0.82305, 0.02141, 0.97048, 0.21061, 0.81691, 0.21921, 0.94591, 0.24823, 1, 0.27618, 1, 0.30628, 0.98276, 0.31703, 0.79541, 0.92655, 0.38998, 1, 0.17805, 1, 0.07055, 0.88355, 0, 0.29231, 0.45141, 0.28801, 0.36848, 0.25898, 0.28555, 0.20093, 0.37769, 0.20093, 0.57427, 0.94048, 0.15849, 0.29971, 0.63486, 0.19675, 0.6965, 0.26026, 0.841, 0.25103, 0.66494, 0.24809, 0.46197], "triangles": [18, 12, 11, 18, 11, 10, 18, 19, 12, 9, 18, 10, 23, 16, 15, 23, 15, 14, 23, 14, 13, 17, 16, 23, 19, 13, 12, 9, 19, 18, 19, 22, 23, 19, 23, 13, 17, 23, 22, 20, 17, 22, 17, 20, 0, 8, 19, 9, 3, 20, 22, 1, 0, 20, 8, 21, 22, 8, 22, 19, 3, 22, 21, 4, 3, 21, 2, 1, 20, 2, 20, 3, 7, 21, 8, 5, 4, 21, 6, 5, 21, 7, 6, 21], "vertices": [-17.14, 7.55, -19.45, 3.28, -18.9, -3.4, 4.68, -4.21, 4.01, -9.69, 6.63, -12.92, 9.82, -13.94, 13.48, -14.36, 17.12, -7.26, 91.96, -13.46, 103.08, -7.69, 104.46, -3.39, 92.07, 3.71, 18.72, 7.4, 19.29, 10.87, 17.05, 15.25, 9.23, 13.7, 6.7, 5.84, 96.53, -4.72, 17.2, -0.21, 4.65, 1.11, 10.05, -7, 11.26, 0.38, 13.53, 8.6], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 36, 38, 40, 2, 42, 44, 44, 46], "width": 120, "height": 42}}, "Q_zj_003": {"Q_zj_003": {"type": "mesh", "uvs": [0.31508, 0.17147, 0.35519, 0.11437, 0.35519, 0.0385, 0.42692, 0.00016, 0.51931, 0, 0.62386, 0.01566, 0.67249, 0.06216, 0.66884, 0.11926, 0.66884, 0.17229, 0.77096, 0.21389, 0.89617, 0.22776, 0.9606, 0.31097, 1, 0.42763, 1, 0.52936, 0.9894, 0.63959, 0.96858, 0.72343, 1, 0.8275, 0.97163, 0.92397, 0.87986, 0.98453, 0.73304, 1, 0.55257, 0.98966, 0.39351, 0.94963, 0.30786, 0.88292, 0.30633, 0.79055, 0.30786, 0.69203, 0.20234, 0.58427, 0.10292, 0.49137, 0.00963, 0.3754, 0, 0.27072, 0.02645, 0.17424, 0.18551, 0.15782, 0.54646, 0.08393, 0.55869, 0.18246, 0.63669, 0.30459, 0.70857, 0.43493, 0.72998, 0.55603, 0.7208, 0.68535, 0.70856, 0.80543, 0.72538, 0.93064, 0.17634, 0.27379, 0.31398, 0.40003, 0.41951, 0.30048, 0.43327, 0.54987, 0.52198, 0.68124, 0.54492, 0.80132, 0.55103, 0.89985, 0.46845, 0.41953, 0.52657, 0.56417, 0.86053, 0.31018, 0.88905, 0.43908, 0.89856, 0.56033, 0.89666, 0.67648, 0.87573, 0.80156, 0.8491, 0.90877], "triangles": [49, 12, 13, 34, 48, 49, 49, 11, 12, 49, 48, 11, 34, 33, 48, 48, 10, 11, 33, 9, 48, 48, 9, 10, 25, 26, 40, 26, 27, 40, 27, 39, 40, 27, 28, 39, 28, 29, 39, 39, 30, 0, 39, 29, 30, 47, 46, 34, 42, 40, 46, 46, 33, 34, 40, 41, 46, 46, 41, 33, 40, 39, 41, 41, 32, 33, 33, 8, 9, 33, 32, 8, 39, 0, 41, 41, 0, 32, 32, 1, 31, 1, 32, 0, 31, 2, 3, 3, 4, 31, 8, 32, 7, 32, 31, 7, 7, 31, 6, 31, 1, 2, 31, 5, 6, 31, 4, 5, 43, 23, 24, 52, 15, 16, 52, 37, 36, 37, 44, 36, 52, 51, 15, 52, 36, 51, 44, 43, 36, 15, 51, 14, 24, 42, 43, 24, 25, 42, 43, 47, 36, 36, 35, 51, 36, 47, 35, 43, 42, 47, 51, 50, 14, 51, 35, 50, 14, 50, 13, 42, 25, 40, 42, 46, 47, 47, 34, 35, 13, 50, 49, 50, 35, 49, 35, 34, 49, 20, 38, 19, 18, 38, 53, 18, 19, 38, 21, 45, 20, 20, 45, 38, 18, 53, 17, 21, 22, 45, 45, 37, 38, 38, 37, 53, 17, 53, 16, 53, 52, 16, 53, 37, 52, 22, 44, 45, 45, 44, 37, 44, 23, 43, 44, 22, 23], "vertices": [2, 4, 26.22, 9.56, 0.66882, 8, 2.45, -13.61, 0.33118, 2, 4, 30.03, 6.63, 0.92757, 8, 5.75, -17.09, 0.07243, 2, 4, 35.66, 5.4, 0.99176, 8, 11.13, -19.18, 0.00824, 2, 4, 37.72, 1.2, 0.9993, 8, 12.52, -23.65, 0.0007, 1, 4, 36.73, -3.4, 1, 2, 4, 34.43, -8.36, 0.99929, 6, 23.96, -1.63, 0.00071, 2, 4, 30.45, -10.03, 0.99078, 6, 19.76, -2.66, 0.00922, 2, 4, 26.25, -8.92, 0.95132, 6, 15.79, -0.91, 0.04868, 2, 4, 22.31, -8.06, 0.77954, 6, 12.03, 0.55, 0.22046, 2, 4, 18.11, -12.47, 0.21011, 6, 7.2, -3.16, 0.78989, 3, 3, 38.22, -17.33, 0.00408, 4, 15.71, -18.48, 0.00165, 6, 3.9, -8.73, 0.99427, 2, 3, 31.48, -19.67, 0.07844, 6, -3.19, -9.5, 0.92156, 3, 3, 22.42, -20.38, 0.35685, 4, -0.26, -20.41, 0.00051, 6, -12.18, -8.16, 0.64264, 3, 3, 14.77, -19.26, 0.64597, 4, -7.81, -18.76, 0.0001, 6, -19.38, -5.35, 0.35393, 3, 16, -14.84, 16.06, 0.01815, 3, 6.56, -17.52, 0.84345, 6, -26.99, -1.81, 0.13841, 3, 16, -8.4, 15.6, 0.15422, 3, 0.41, -15.55, 0.799, 6, -32.55, 1.49, 0.04678, 3, 16, -0.67, 17.93, 0.50495, 3, -7.65, -16, 0.48885, 6, -40.5, 2.86, 0.0062, 3, 16, 6.76, 17.18, 0.75385, 3, -14.7, -13.51, 0.24598, 6, -46.81, 6.86, 0.00017, 2, 16, 11.78, 12.95, 0.88999, 3, -18.58, -8.22, 0.11001, 2, 16, 13.66, 5.61, 0.98513, 3, -18.66, -0.64, 0.01487, 1, 16, 13.74, -3.63, 1, 3, 16, 11.47, -11.99, 0.97328, 3, -12.38, 15.94, 0.02655, 8, -54.12, 4.1, 0.00018, 3, 16, 6.83, -16.82, 0.88275, 3, -6.74, 19.54, 0.11461, 8, -47.81, 6.33, 0.00264, 4, 16, -0.15, -17.55, 0.65529, 3, 0.22, 18.6, 0.32493, 4, -19.65, 20.04, 0.00495, 8, -41.24, 3.86, 0.01483, 4, 16, -7.61, -18.18, 0.26758, 3, 7.62, 17.45, 0.6, 4, -12.35, 18.36, 0.06144, 8, -34.29, 1.07, 0.07099, 4, 16, -15.26, -24.3, 0.0425, 3, 16.5, 21.59, 0.45541, 4, -3.2, 21.87, 0.23068, 8, -24.7, 3.12, 0.27141, 4, 16, -21.82, -30.01, 0.00466, 3, 24.22, 25.59, 0.21064, 4, 4.78, 25.32, 0.23789, 8, -16.29, 5.29, 0.54681, 3, 3, 33.62, 29.03, 0.04926, 4, 14.41, 28.08, 0.07139, 8, -6.35, 6.53, 0.87935, 2, 3, 41.57, 28.37, 0.00205, 8, 1.25, 4.1, 0.99795, 2, 4, 29.16, 23.98, 0.0079, 8, 7.59, 0.18, 0.9921, 2, 4, 28.65, 15.79, 0.2145, 8, 5.81, -7.83, 0.7855, 2, 4, 30.2, -3.39, 0.99651, 6, 20.55, 3.93, 0.00349, 2, 4, 22.75, -2.4, 0.96463, 6, 13.35, 6.07, 0.03537, 2, 4, 12.84, -4.31, 0.69077, 6, 3.25, 5.72, 0.30923, 3, 3, 24.01, -5.59, 0.22298, 4, 2.38, -5.77, 0.48213, 6, -7.31, 5.9, 0.29489, 3, 3, 14.75, -5.35, 0.90023, 4, -6.85, -4.87, 0.01025, 6, -16.28, 8.22, 0.08952, 2, 3, 5.09, -3.47, 0.98804, 6, -25.27, 12.21, 0.01196, 3, 16, -0.95, 2.98, 0.63226, 3, -3.85, -1.53, 0.36729, 6, -33.55, 16.1, 0.00044, 2, 16, 8.44, 4.72, 0.97282, 3, -13.39, -1.01, 0.02718, 3, 3, 40.04, 19.51, 0.00733, 4, 20.14, 18.13, 0.18323, 8, -2.23, -4.2, 0.80944, 4, 16, -29.74, -19.95, 0.00058, 3, 29.54, 13.94, 0.1139, 4, 9.26, 13.32, 0.52738, 8, -13.72, -7.26, 0.35814, 3, 3, 36.25, 7.53, 0.00531, 4, 15.51, 6.45, 0.8351, 8, -8.62, -15.02, 0.15958, 4, 16, -18.97, -12.82, 0.02678, 3, 17.39, 9.56, 0.64037, 4, -3.16, 9.81, 0.23674, 8, -26.54, -8.81, 0.0961, 4, 16, -9.45, -7.38, 0.14032, 3, 6.86, 6.52, 0.83575, 4, -13.88, 7.52, 0.00828, 8, -37.48, -9.4, 0.01565, 3, 16, -0.48, -5.36, 0.75278, 3, -2.34, 6.68, 0.24503, 8, -46.41, -7.19, 0.00219, 3, 16, 6.95, -4.35, 0.99622, 3, -9.79, 7.45, 0.00374, 8, -53.5, -4.76, 4e-05, 4, 16, -29, -11.96, 0.00019, 3, 26.94, 6.36, 0.08978, 4, 6.13, 5.94, 0.82318, 8, -17.95, -14.07, 0.08685, 4, 16, -18.34, -7.98, 0.01366, 3, 15.63, 5.01, 0.87567, 4, -5.24, 5.39, 0.08174, 8, -29.27, -12.85, 0.02894, 2, 3, 32.28, -14.63, 0.03788, 6, -1.28, -4.76, 0.96212, 3, 3, 22.38, -14.65, 0.37034, 4, 0.1, -14.7, 0.03423, 6, -10.94, -2.57, 0.59543, 3, 3, 13.19, -13.81, 0.74929, 4, -9, -13.2, 0.00356, 6, -19.7, 0.32, 0.24716, 3, 16, -11.61, 11.61, 0.04708, 3, 4.47, -12.44, 0.87676, 6, -27.89, 3.61, 0.07616, 3, 16, -2.04, 11.44, 0.42352, 3, -4.79, -10.01, 0.5678, 6, -36.37, 8.05, 0.00869, 3, 16, 6.2, 10.85, 0.82323, 3, -12.66, -7.5, 0.1767, 6, -43.47, 12.27, 6e-05], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 8, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 78, 80, 80, 84, 84, 86, 86, 88, 88, 90, 92, 94, 94, 86, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106], "width": 51, "height": 76}}, "Q_zj_17": {"Q_zj_0016": {"type": "mesh", "uvs": [0, 0.3823, 0, 0.47314, 0, 0.52423, 0.13747, 0.5001, 0.26947, 0.44475, 0.3868, 0.61791, 0.52089, 0.78823, 0.71365, 0.93159, 0.93784, 1, 1, 1, 1, 0.92165, 0.89384, 0.82797, 0.8268, 0.69455, 0.7828, 0.51004, 0.77861, 0.3454, 0.77651, 0.15095, 0.63194, 0.04308, 0.4287, 0, 0.2548, 0.0814, 0.1228, 0.20488, 0.04737, 0.29146, 0.09556, 0.40217, 0.2108, 0.30423, 0.42032, 0.21056, 0.58794, 0.36668, 0.61308, 0.58242, 0.71156, 0.75842, 0.80794, 0.87481, 0.93994, 0.95997], "triangles": [26, 25, 12, 6, 25, 26, 26, 12, 11, 27, 26, 11, 28, 27, 11, 7, 26, 27, 6, 26, 7, 10, 28, 11, 28, 7, 27, 8, 7, 28, 28, 10, 9, 8, 28, 9, 24, 14, 13, 25, 24, 13, 4, 24, 25, 5, 4, 25, 25, 13, 12, 6, 5, 25, 23, 17, 16, 23, 16, 15, 24, 23, 15, 14, 24, 15, 23, 18, 17, 19, 18, 23, 22, 19, 23, 20, 19, 22, 21, 20, 22, 0, 20, 21, 24, 4, 22, 24, 22, 23, 21, 22, 4, 1, 0, 21, 3, 21, 4, 1, 21, 3, 2, 1, 3], "vertices": [1, 146, -2.96, 0.28, 1, 2, 146, -4.28, -2.2, 0.99996, 148, 1.47, -13.6, 4e-05, 1, 146, -5.02, -3.6, 1, 3, 146, -2.12, -4.29, 0.9796, 147, 0.68, -9.83, 0.00511, 148, 2.68, -10.85, 0.01529, 3, 146, 1.13, -4.07, 0.69656, 147, 1.69, -6.73, 0.10093, 148, 1.34, -7.87, 0.20251, 4, 146, 0.8, -9.97, 0.07696, 147, 7.04, -9.24, 0.03214, 148, 6.99, -6.14, 0.78914, 149, -3.31, -5.49, 0.10177, 3, 146, 0.81, -15.95, 0.00072, 148, 12.59, -4.04, 0.11515, 149, 2.63, -4.84, 0.88413, 1, 149, 8.26, -2.73, 1, 1, 149, 11.98, 0.85, 1, 1, 149, 12.47, 2.06, 1, 1, 149, 10.21, 2.97, 1, 1, 149, 6.69, 1.98, 1, 2, 148, 10.56, 2.71, 0.03157, 149, 2.32, 2.2, 0.96843, 1, 148, 4.77, 2.55, 1, 2, 147, 7.86, 2.52, 0.41753, 148, -0.3, 3.13, 0.58247, 2, 146, 14.8, -1.01, 0.00658, 147, 3.93, 7.1, 0.99342, 2, 146, 13.68, 3.37, 0.16167, 147, -0.54, 7.69, 0.83833, 2, 146, 10.54, 6.55, 0.48238, 147, -4.67, 5.95, 0.51762, 2, 146, 6.13, 6.03, 0.85502, 147, -5.82, 1.67, 0.14498, 1, 146, 1.89, 3.94, 1, 1, 146, -0.77, 2.31, 1, 3, 146, -1.48, -1.2, 0.99342, 147, -1.95, -8.09, 0.00143, 148, -0.45, -11.32, 0.00515, 1, 146, 2.08, 0.35, 1, 2, 146, 7.33, 0.86, 0.50937, 147, -0.58, 0.85, 0.49063, 3, 146, 8.17, -5.06, 0.00775, 147, 5.23, -0.57, 0.79168, 148, -0.17, -0.92, 0.20057, 4, 146, 5.51, -11.22, 0.00862, 147, 9.95, -5.33, 0.00298, 148, 6.53, -1.28, 0.97739, 149, -2.57, -0.67, 0.01102, 1, 149, 3.26, -0.78, 1, 1, 149, 7.36, -0.24, 1, 1, 149, 10.85, 1.35, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 2, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 21, "height": 31}}, "Q_zj_13": {"Q_zj_0013": {"type": "mesh", "uvs": [0.00743, 0.70941, 0, 0.82305, 0.02141, 0.97048, 0.21061, 0.81691, 0.21921, 0.94591, 0.24823, 1, 0.27618, 1, 0.30628, 0.98276, 0.31703, 0.79541, 0.92655, 0.38998, 1, 0.17805, 1, 0.07055, 0.88355, 0, 0.29231, 0.45141, 0.28801, 0.36848, 0.25898, 0.28555, 0.20093, 0.37769, 0.20093, 0.57427, 0.94048, 0.15849, 0.29971, 0.63486, 0.19675, 0.6965, 0.26026, 0.841, 0.25103, 0.66494, 0.24809, 0.46197], "triangles": [18, 12, 11, 18, 11, 10, 18, 19, 12, 9, 18, 10, 23, 16, 15, 23, 15, 14, 23, 14, 13, 17, 16, 23, 19, 13, 12, 9, 19, 18, 19, 22, 23, 19, 23, 13, 17, 23, 22, 20, 17, 22, 17, 20, 0, 8, 19, 9, 3, 20, 22, 1, 0, 20, 8, 21, 22, 8, 22, 19, 3, 22, 21, 4, 3, 21, 2, 1, 20, 2, 20, 3, 7, 21, 8, 5, 4, 21, 6, 5, 21, 7, 6, 21], "vertices": [-17.14, 7.55, -19.45, 3.28, -18.9, -3.4, 4.68, -4.21, 4.01, -9.69, 6.63, -12.92, 9.82, -13.94, 13.48, -14.36, 17.12, -7.26, 91.96, -13.46, 103.08, -7.69, 104.46, -3.39, 92.07, 3.71, 18.72, 7.4, 19.29, 10.87, 17.05, 15.25, 9.23, 13.7, 6.7, 5.84, 96.53, -4.72, 17.2, -0.21, 4.65, 1.11, 10.05, -7, 11.26, 0.38, 13.53, 8.6], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 36, 38, 40, 2, 42, 44, 44, 46], "width": 120, "height": 42}}, "H_zj_001_1": {"H_zj_001_1": {"type": "mesh", "uvs": [0.08909, 0, 0.19522, 0, 0.29451, 0.09918, 0.39722, 0.22589, 0.52561, 0.36178, 0.6677, 0.46462, 0.84402, 0.54542, 0.957, 0.64274, 1, 0.72171, 0.98781, 0.83189, 0.85942, 0.89983, 0.74987, 0.9586, 0.63859, 1, 0.43831, 0.94391, 0.27054, 0.83556, 0.12846, 0.66111, 0.05656, 0.48298, 0, 0.30853, 0, 0.14693, 0, 0.04593, 0.14729, 0.13407, 0.26027, 0.30669, 0.40578, 0.47196, 0.53931, 0.62071, 0.71392, 0.72538, 0.82005, 0.77864, 0.0771, 0.14509, 0.09936, 0.30853, 0.17981, 0.47196, 0.27568, 0.62805, 0.39037, 0.76578, 0.54444, 0.84842, 0.65571, 0.89983], "triangles": [14, 29, 30, 30, 23, 31, 13, 30, 31, 14, 30, 13, 13, 31, 12, 29, 22, 23, 15, 28, 29, 30, 29, 23, 14, 15, 29, 28, 27, 21, 28, 21, 22, 16, 27, 28, 29, 28, 22, 15, 16, 28, 26, 19, 0, 20, 26, 0, 18, 19, 26, 27, 26, 20, 27, 20, 21, 17, 18, 26, 17, 26, 27, 16, 17, 27, 23, 5, 24, 24, 5, 6, 25, 24, 6, 7, 25, 6, 25, 7, 8, 9, 25, 8, 31, 23, 24, 10, 25, 9, 32, 31, 24, 32, 24, 25, 11, 32, 25, 10, 11, 25, 12, 31, 32, 12, 32, 11, 23, 4, 5, 22, 4, 23, 22, 3, 4, 21, 3, 22, 21, 20, 2, 21, 2, 3, 20, 0, 1, 20, 1, 2], "vertices": [2, 95, -9.89, -1.32, 0.34999, 96, -4.33, 5.41, 0.65001, 2, 95, -5.69, 3.32, 0.79677, 96, -2.07, 11.25, 0.20323, 1, 95, 2.29, 4.01, 1, 2, 95, 11.52, 3.83, 0.48528, 97, 0.18, 3.83, 0.51472, 2, 97, 10.81, 4.43, 0.53748, 98, 0.16, 4.46, 0.46252, 2, 98, 10.23, 5.34, 0.5604, 99, -0.26, 5.45, 0.4396, 1, 99, 10.97, 6.82, 1, 2, 99, 19.42, 5.54, 0.99999, 102, 7.65, 40.42, 1e-05, 2, 99, 23.79, 3.05, 0.99677, 102, 12.62, 41.22, 0.00323, 2, 99, 26.21, -2.55, 0.98299, 102, 18.01, 38.36, 0.01701, 2, 99, 21.53, -9.58, 0.90538, 102, 18.77, 29.95, 0.09462, 2, 99, 17.56, -15.62, 0.76622, 102, 19.45, 22.75, 0.23378, 3, 98, 27.44, -18.61, 0.00348, 99, 13.02, -20.88, 0.62302, 102, 19.21, 15.81, 0.37349, 3, 98, 16.32, -23.66, 0.02948, 99, 1.25, -24.14, 0.27811, 102, 12.07, 5.9, 0.69241, 2, 101, 16.09, -1.18, 0.00972, 102, 2.94, -1.18, 0.99028, 2, 100, 17.27, -5.54, 0.0428, 101, 4.12, -5.54, 0.9572, 3, 96, 19.75, -5.97, 0.00324, 100, 6.6, -5.97, 0.99233, 101, -6.55, -5.97, 0.00443, 2, 96, 9.6, -5.62, 0.79588, 100, -3.55, -5.62, 0.20412, 1, 96, 1.31, -2.41, 1, 2, 95, -11.54, -6.92, 0.03607, 96, -3.87, -0.41, 0.96393, 2, 95, -2.12, -3.72, 0.54518, 96, 3.78, 5.95, 0.45482, 4, 95, 9.4, -5.15, 0.49545, 97, -1.94, -5.15, 0.17167, 96, 15.04, 8.74, 0.05784, 100, 1.89, 8.74, 0.27504, 5, 97, 10.56, -4.88, 0.50973, 98, -1.52, -4.7, 0.28623, 100, 13.47, 13.47, 0.1031, 101, 0.32, 13.47, 0.09789, 102, -12.84, 13.47, 0.00305, 6, 97, 21.91, -4.52, 0.00086, 98, 9.75, -6.09, 0.54743, 99, -2.51, -5.77, 0.26858, 100, 23.94, 17.87, 0.00219, 101, 10.79, 17.87, 0.0836, 102, -2.36, 17.87, 0.09734, 4, 98, 21.38, -4.08, 0.00371, 99, 9.29, -5.59, 0.90435, 101, 19.87, 25.4, 0.00281, 102, 6.72, 25.4, 0.08913, 2, 99, 16.18, -4.98, 0.9402, 102, 11.71, 30.18, 0.0598, 2, 95, -4.45, -7.2, 0.10053, 96, 2.86, 1.87, 0.89947, 1, 96, 11.71, -0.15, 1, 4, 95, 12.95, -14.77, 0.00366, 97, 1.62, -14.77, 0.02003, 98, -11.87, -13.1, 0.00066, 100, 8.66, 1.04, 0.97565, 6, 97, 11.78, -16.33, 0.03427, 98, -2.07, -16.2, 0.05507, 99, -15.76, -13.92, 0.00164, 100, 18.71, 3.22, 0.05371, 101, 5.55, 3.22, 0.85283, 102, -7.6, 3.22, 0.00247, 6, 97, 21.93, -16.39, 0.00221, 98, 7.95, -17.83, 0.11107, 99, -6.11, -17.08, 0.10505, 100, 28.21, 6.79, 0.00043, 101, 15.06, 6.79, 0.24193, 102, 1.91, 6.79, 0.5393, 4, 98, 17.88, -15.63, 0.05493, 99, 4.03, -16.46, 0.46222, 101, 22.58, 13.63, 0.02074, 102, 9.43, 13.63, 0.4621, 4, 98, 24.76, -13.69, 0.00718, 99, 11.13, -15.61, 0.66964, 101, 27.58, 18.74, 0.00125, 102, 14.43, 18.74, 0.32192], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 0, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 59, "height": 55}}, "H_zj_002_1": {"H_zj_002_1": {"type": "mesh", "uvs": [0.86295, 0, 1, 0.0408, 1, 0.25938, 0.97532, 0.46526, 0.91753, 0.68892, 0.79553, 0.90497, 0.68958, 1, 0.49374, 0.93038, 0.18874, 0.9558, 0, 1, 0.01858, 0.87955, 0.19195, 0.67622, 0.30753, 0.50084, 0.44237, 0.29497, 0.55795, 0.06876, 0.70242, 0, 0.83726, 0.18568, 0.75379, 0.40426, 0.67032, 0.62792, 0.54832, 0.83126, 0.28184, 0.78297, 0.48732, 0.57709, 0.61895, 0.32547, 0.70884, 0.12468], "triangles": [7, 19, 6, 6, 19, 5, 9, 10, 8, 8, 20, 7, 8, 10, 20, 7, 20, 19, 19, 18, 5, 10, 11, 20, 20, 21, 19, 20, 11, 21, 19, 21, 18, 11, 12, 21, 5, 18, 4, 4, 18, 3, 18, 17, 3, 18, 21, 17, 12, 13, 21, 21, 22, 17, 21, 13, 22, 3, 17, 2, 17, 16, 2, 17, 22, 16, 22, 23, 16, 23, 22, 14, 22, 13, 14, 16, 1, 2, 16, 0, 1, 16, 23, 0, 14, 15, 23, 23, 15, 0], "vertices": [2, 104, -8.18, 4.2, 0.632, 103, 5.21, 3.67, 0.368, 2, 104, -7.88, 9.76, 0.632, 103, 5.16, 9.23, 0.368, 2, 104, 2.13, 12.91, 0.9331, 105, -10.65, 13.64, 0.0669, 3, 104, 11.83, 14.99, 0.51455, 105, -0.83, 15.08, 0.47174, 106, -13.37, 16.07, 0.01371, 3, 104, 22.73, 16.13, 0.06247, 105, 10.12, 15.5, 0.66932, 106, -2.42, 15.71, 0.26821, 2, 105, 21.3, 13.46, 0.16776, 106, 8.58, 12.89, 0.83224, 2, 105, 26.68, 10.64, 0.0449, 106, 13.76, 9.69, 0.9551, 2, 105, 25.21, 2.61, 0.00094, 106, 11.72, 1.79, 0.99906, 2, 105, 29.15, -8.35, 2e-05, 106, 14.87, -9.43, 0.99998, 1, 106, 18.17, -16.14, 1, 2, 105, 27.13, -15.5, 0.00351, 106, 12.35, -16.42, 0.99649, 2, 105, 16.09, -11.43, 0.29379, 106, 1.62, -11.57, 0.70621, 3, 104, 21.1, -8.69, 0.01032, 105, 6.87, -9.16, 0.91272, 106, -7.41, -8.66, 0.07696, 2, 104, 10.13, -6.78, 0.7977, 105, -3.95, -6.54, 0.2023, 2, 104, -1.54, -5.86, 0.632, 103, 12.48, -5.95, 0.368, 2, 104, -6.34, -1.62, 0.632, 103, 7.42, -2.02, 0.368, 2, 104, 0.61, 5.95, 0.99205, 105, -12.62, 6.79, 0.00795, 3, 104, 11.57, 6.08, 0.67581, 105, -1.67, 6.21, 0.32375, 106, -14.84, 7.28, 0.00045, 3, 104, 22.77, 6.29, 0.01365, 105, 9.51, 5.68, 0.85451, 106, -3.72, 5.96, 0.13184, 2, 105, 20.09, 3.5, 0.03016, 106, 6.68, 3.03, 0.96984, 2, 105, 20.25, -6.89, 0.08081, 106, 6.1, -7.34, 0.91919, 2, 105, 8.8, -1.66, 0.99298, 106, -4.96, -1.31, 0.00702, 1, 104, 9.51, 0.06, 1, 1, 104, -0.71, 0.42, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 0, 32, 32, 34, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46], "width": 38, "height": 48}}, "H_zj_003_1": {"H_zj_003_1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [41.2, -9.71, 24.2, -9.71, 24.2, 9.29, 41.2, 9.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 19}}, "fapian": {"fapian": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [25.97, -18.64, 27.91, 11.3, 54.85, 9.56, 52.92, -20.38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 30, "height": 27}}, "Q_zj_003_2": {"Q_zj_003_2": {"type": "mesh", "uvs": [0.16108, 0, 0.2299, 0, 0.31708, 0.09573, 0.48226, 0.16258, 0.69331, 0.22201, 0.82867, 0.17744, 0.95943, 0.14773, 1, 0.4003, 1, 0.70115, 0.99613, 0.91658, 0.84702, 1, 0.65661, 1, 0.42261, 0.95744, 0.20008, 0.79772, 0.06014, 0.63058, 0, 0.41515, 0, 0.12915, 0.08079, 0.02887, 0.15649, 0.31115, 0.36984, 0.49315, 0.56025, 0.56744, 0.76443, 0.59344, 0.92502, 0.58601], "triangles": [18, 17, 0, 16, 17, 18, 15, 16, 18, 19, 2, 3, 18, 0, 1, 1, 2, 18, 19, 18, 2, 19, 3, 20, 14, 15, 18, 13, 14, 18, 19, 13, 18, 8, 10, 22, 12, 19, 20, 13, 19, 12, 11, 20, 21, 12, 20, 11, 10, 21, 22, 9, 10, 8, 11, 21, 10, 20, 3, 4, 7, 22, 5, 7, 5, 6, 21, 4, 5, 21, 5, 22, 20, 4, 21, 22, 7, 8], "vertices": [2, 5, 10.47, 11.08, 0.20634, 4, 34.19, 9.78, 0.79366, 2, 5, 10.17, 8.76, 0.24356, 4, 33.69, 7.49, 0.75644, 2, 5, 7.8, 6.08, 0.35947, 4, 31.09, 5.02, 0.64053, 2, 5, 5.69, 0.69, 0.95064, 4, 28.52, -0.16, 0.04936, 2, 5, 3.53, -6.27, 0.98098, 4, 25.77, -6.91, 0.01902, 2, 5, 3.87, -10.96, 0.93399, 4, 25.7, -11.6, 0.06601, 2, 5, 3.92, -15.44, 0.91523, 4, 25.36, -16.08, 0.08477, 2, 5, -1.52, -16.13, 0.79523, 4, 19.88, -16.29, 0.20477, 2, 5, -7.79, -15.32, 0.51055, 4, 13.71, -14.95, 0.48945, 2, 5, -12.26, -14.61, 0.38473, 4, 9.32, -13.85, 0.61527, 2, 5, -13.34, -9.36, 0.26187, 4, 8.69, -8.52, 0.73813, 2, 5, -12.51, -2.94, 0.04259, 4, 10.07, -2.2, 0.95741, 1, 4, 12.65, 5.38, 1, 2, 5, -6.31, 11.91, 0.00128, 4, 17.54, 12.06, 0.99872, 2, 5, -2.22, 16.18, 0.03007, 4, 21.98, 15.96, 0.96993, 2, 5, 2.53, 17.63, 0.0765, 4, 26.84, 16.99, 0.9235, 2, 5, 8.48, 16.86, 0.13739, 4, 32.71, 15.7, 0.86261, 2, 5, 10.22, 13.86, 0.16904, 4, 34.18, 12.57, 0.83096, 2, 5, 4.01, 12.07, 0.12579, 4, 27.84, 11.32, 0.87421, 2, 5, -0.71, 5.37, 0.03207, 4, 22.55, 5.05, 0.96793, 2, 5, -3.09, -0.85, 0.08873, 4, 19.65, -0.94, 0.91127, 2, 5, -4.52, -7.67, 0.52091, 4, 17.63, -7.6, 0.47909, 2, 5, -5.06, -13.1, 0.61541, 4, 16.62, -12.97, 0.38459], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 32, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 34, "height": 21}}, "Q_zj_0014": {"Q_zj_0014": {"type": "mesh", "uvs": [0.95273, 0.01787, 1, 0.08163, 1, 0.17913, 1, 0.28963, 0.93202, 0.32538, 0.87293, 0.35951, 0.83353, 0.39688, 0.8099, 0.48463, 0.79808, 0.57238, 0.70354, 0.58376, 0.6799, 0.68938, 0.6799, 0.75601, 0.71535, 0.77876, 0.82172, 0.84213, 0.85323, 0.91851, 0.82566, 0.98188, 0.66414, 1, 0.47111, 1, 0.31354, 0.95588, 0.31748, 0.87301, 0.36475, 0.81938, 0.39232, 0.76901, 0.36081, 0.68288, 0.32141, 0.59026, 0.21899, 0.56101, 0.19929, 0.51226, 0.03384, 0.45701, 0, 0.39201, 0.03384, 0.31888, 0.20717, 0.25876, 0.29778, 0.23438, 0.36869, 0.12876, 0.47899, 0.03288, 0.60505, 0, 0.80202, 0, 0.75481, 0.07189, 0.6445, 0.15151, 0.57357, 0.26364, 0.47113, 0.36438, 0.47111, 0.49438, 0.50655, 0.59188, 0.53017, 0.69588, 0.54592, 0.77713, 0.55772, 0.85676, 0.5971, 0.92338], "triangles": [35, 33, 34, 35, 34, 0, 35, 0, 1, 35, 32, 33, 36, 32, 35, 31, 32, 36, 35, 1, 2, 36, 35, 2, 37, 31, 36, 30, 31, 37, 37, 36, 2, 3, 37, 2, 4, 37, 3, 5, 37, 4, 38, 30, 37, 38, 37, 5, 29, 30, 38, 6, 38, 5, 28, 38, 27, 38, 26, 27, 29, 38, 28, 6, 39, 38, 38, 25, 26, 7, 39, 6, 39, 25, 38, 24, 25, 39, 7, 9, 39, 8, 9, 7, 23, 24, 39, 40, 23, 39, 9, 40, 39, 22, 23, 40, 10, 40, 9, 41, 22, 40, 10, 41, 40, 41, 10, 11, 21, 22, 41, 42, 21, 41, 11, 42, 41, 12, 42, 11, 20, 21, 42, 12, 43, 42, 43, 12, 13, 20, 42, 43, 44, 43, 13, 44, 13, 14, 19, 20, 43, 15, 44, 14, 19, 44, 18, 44, 19, 43, 17, 18, 44, 16, 44, 15, 17, 44, 16], "vertices": [1, 9, -14.21, 4.8, 1, 1, 9, -10.35, 8.48, 1, 2, 10, -13.82, 26.88, 0.00021, 9, -3.37, 11.97, 0.99979, 2, 10, -5.2, 24.91, 0.02336, 9, 4.54, 15.92, 0.97664, 2, 10, -2.92, 22.08, 0.05637, 9, 8.1, 15.19, 0.94363, 2, 10, -0.69, 19.57, 0.1336, 9, 11.41, 14.67, 0.8664, 2, 10, 1.93, 17.64, 0.27594, 9, 14.67, 14.84, 0.72406, 2, 10, 8.6, 15.31, 0.66348, 9, 21.3, 17.29, 0.33652, 2, 10, 15.36, 13.36, 0.87238, 9, 27.75, 20.08, 0.12762, 2, 10, 15.55, 10.12, 0.92518, 9, 29.96, 17.69, 0.07482, 3, 11, -5.31, 9.05, 0.0077, 10, 23.61, 7.47, 0.99187, 9, 37.86, 20.78, 0.00043, 2, 11, -0.56, 6.62, 0.31619, 10, 28.81, 6.28, 0.68381, 2, 11, 1.59, 6.83, 0.63525, 10, 30.84, 7.02, 0.36475, 2, 11, 7.7, 7.65, 0.98692, 10, 36.57, 9.31, 0.01308, 1, 11, 13.62, 5.8, 1, 1, 11, 17.72, 2.68, 1, 1, 11, 16.58, -2.73, 1, 1, 11, 13.68, -8.4, 1, 1, 11, 8.17, -11.42, 1, 2, 11, 2.33, -8.29, 0.983, 10, 35.26, -7.47, 0.017, 2, 11, -0.78, -4.95, 0.69257, 10, 31.43, -4.99, 0.30743, 2, 11, -3.96, -2.3, 0.04019, 10, 27.7, -3.2, 0.95981, 1, 10, 20.75, -2.68, 1, 1, 10, 13.24, -2.29, 1, 1, 10, 10.2, -5.06, 1, 2, 10, 6.26, -4.82, 0.99471, 9, 32.28, 0.25, 0.00529, 2, 10, 0.73, -9.16, 0.65931, 9, 30.77, -6.61, 0.34069, 2, 10, -4.59, -9.09, 0.30537, 9, 26.62, -9.93, 0.69463, 2, 10, -10.04, -6.69, 0.05739, 9, 20.89, -11.55, 0.94261, 1, 9, 14.03, -8.58, 1, 1, 9, 10.95, -6.78, 1, 1, 9, 2.34, -8.47, 1, 1, 9, -6.14, -8.64, 1, 1, 9, -10.36, -6.1, 1, 1, 9, -13.26, -0.29, 1, 1, 9, -7.42, 0.89, 1, 1, 9, -0.1, 0.49, 1, 2, 10, -10.37, 11.65, 0.00473, 9, 8.97, 2.4, 0.99527, 2, 10, -3.27, 6.56, 0.07345, 9, 17.69, 2.99, 0.92655, 2, 10, 6.87, 4.24, 0.89767, 9, 26.99, 7.64, 0.10233, 2, 10, 14.73, 3.64, 0.98841, 9, 33.45, 12.17, 0.01159, 1, 10, 23.01, 2.54, 1, 2, 11, -1.07, 1.92, 0.11685, 10, 29.47, 1.59, 0.88315, 1, 11, 4.78, -0.64, 1, 1, 11, 10.12, -1.91, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88], "width": 33, "height": 80}}, "H_zj_009": {"H_zj_009": {"type": "mesh", "uvs": [0.27455, 0.48332, 0.23166, 0.41205, 0.25311, 0.33496, 0.36511, 0.21714, 0.52715, 0.1415, 0.66774, 0.05859, 0.79404, 0, 0.89412, 0, 1, 0, 1, 0.08914, 1, 0.18223, 0.96323, 0.27823, 0.88459, 0.37568, 0.79881, 0.48768, 0.69872, 0.6055, 0.63506, 0.65615, 0.56527, 0.71168, 0.51285, 0.66805, 0.43898, 0.73059, 0.41149, 0.75029, 0.38417, 0.76986, 0.38417, 0.80623, 0.43659, 0.86732, 0.42945, 0.93423, 0.33174, 0.97786, 0.20306, 0.99095, 0.0577, 0.97641, 0.00528, 0.91095, 0.01719, 0.82077, 0.10774, 0.77132, 0.17685, 0.73641, 0.20306, 0.70586, 0.21974, 0.67241, 0.2674, 0.58223, 0.27455, 0.53714, 0.89889, 0.07023, 0.81548, 0.15023, 0.71063, 0.22732, 0.632, 0.30732, 0.54859, 0.40623, 0.47949, 0.5095, 0.39132, 0.61132, 0.51047, 0.62005, 0.28647, 0.7495, 0.21259, 0.8455, 0.18638, 0.9255], "triangles": [26, 45, 25, 25, 45, 24, 26, 27, 45, 45, 27, 44, 27, 28, 44, 28, 29, 44, 23, 24, 44, 24, 45, 44, 23, 44, 22, 22, 44, 21, 29, 30, 44, 44, 43, 21, 44, 30, 43, 43, 20, 21, 20, 43, 19, 19, 43, 18, 30, 31, 43, 41, 18, 43, 43, 31, 32, 43, 32, 41, 17, 41, 42, 17, 18, 41, 16, 17, 15, 32, 33, 41, 17, 42, 15, 15, 42, 14, 14, 42, 40, 33, 34, 41, 42, 41, 40, 41, 34, 40, 14, 40, 13, 34, 0, 40, 40, 39, 13, 40, 0, 39, 39, 38, 13, 13, 38, 12, 0, 1, 39, 1, 2, 39, 2, 3, 39, 39, 3, 38, 38, 37, 12, 12, 37, 11, 3, 4, 38, 38, 4, 37, 37, 36, 11, 11, 36, 10, 4, 5, 37, 37, 5, 36, 10, 36, 9, 36, 35, 9, 36, 5, 35, 35, 6, 7, 6, 35, 5, 35, 8, 9, 35, 7, 8], "vertices": [2, 81, 32.47, -13.75, 0.37863, 82, 6.49, -12.41, 0.62137, 2, 81, 27.99, -17.5, 0.65166, 82, 3.27, -17.29, 0.34834, 2, 81, 22.07, -18.56, 0.81343, 82, -2.11, -19.99, 0.18657, 2, 81, 11.75, -16.68, 0.97378, 82, -12.54, -21.12, 0.02622, 2, 81, 3.69, -11.48, 0.99993, 82, -21.75, -18.44, 7e-05, 1, 81, -4.55, -7.42, 1, 1, 81, -10.81, -3.36, 1, 1, 81, -12.4, 1.07, 1, 1, 81, -14.08, 5.75, 1, 1, 81, -7.62, 8.07, 1, 1, 81, -0.87, 10.5, 1, 1, 81, 6.67, 11.37, 1, 1, 81, 14.98, 10.43, 1, 2, 81, 24.46, 9.55, 0.94643, 82, -7.83, 7.64, 0.05357, 2, 81, 34.59, 8.19, 0.18668, 82, 2.27, 9.22, 0.81332, 2, 81, 39.27, 6.7, 0.0137, 82, 7.18, 9.12, 0.9863, 1, 82, 12.57, 9.01, 1, 1, 82, 11.32, 5.03, 1, 1, 82, 17.26, 5.09, 1, 1, 82, 19.25, 4.95, 1, 1, 82, 21.22, 4.8, 1, 1, 82, 23.48, 6.46, 1, 2, 82, 25.81, 11.23, 0.96865, 83, -0.04, 12.95, 0.03135, 2, 82, 30.16, 14.01, 0.86077, 83, 5.1, 13.36, 0.13923, 2, 82, 35.58, 12.3, 0.59895, 83, 9.09, 9.3, 0.40105, 2, 82, 39.98, 8.03, 0.14701, 83, 10.96, 3.46, 0.85299, 1, 83, 10.84, -3.46, 1, 1, 83, 6.21, -6.63, 1, 1, 83, -0.75, -7.07, 1, 2, 82, 29, -5.6, 0.30559, 83, -5.13, -3.41, 0.69441, 2, 82, 24.92, -4.57, 0.86107, 83, -8.26, -0.59, 0.13893, 2, 82, 22.29, -4.97, 0.97725, 83, -10.76, 0.29, 0.02275, 2, 82, 19.75, -5.87, 0.99888, 83, -13.42, 0.7, 0.00112, 2, 81, 39.76, -11.49, 0.03728, 82, 12.83, -8.17, 0.96272, 2, 81, 36.37, -12.35, 0.14256, 82, 9.83, -9.96, 0.85744, 1, 81, -7.38, 3.11, 1, 1, 81, -0.26, 1.5, 1, 1, 81, 6.99, -1.13, 1, 2, 81, 14.04, -2.52, 0.99962, 82, -14.38, -6.9, 0.00038, 2, 81, 22.53, -3.64, 0.96227, 82, -5.92, -5.55, 0.03773, 2, 81, 31.11, -4, 0.2279, 82, 2.41, -3.46, 0.7721, 2, 81, 39.89, -5.25, 0.00454, 82, 11.18, -2.16, 0.99546, 1, 82, 8.41, 2.76, 1, 1, 82, 22.68, 0.18, 1, 1, 82, 30.69, 1.76, 1, 2, 82, 36.38, 4.41, 0.1557, 83, 6.09, 1.96, 0.8443], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 40, 42, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68, 16, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 28, 30, 30, 32, 82, 86, 86, 88, 88, 90], "width": 47, "height": 77}}, "H_zj_001": {"H_zj_001": {"type": "mesh", "uvs": [0.22724, 0, 0.31844, 0.01513, 0.40724, 0.06706, 0.46724, 0.19926, 0.58484, 0.32674, 0.73604, 0.42116, 0.89684, 0.50142, 1, 0.55808, 0.91604, 0.66903, 0.83444, 0.81303, 0.70724, 0.94995, 0.54164, 0.99716, 0.33284, 1, 0.21524, 0.84608, 0.10964, 0.65014, 0.02324, 0.42588, 0, 0.22995, 0, 0.06942, 0.09044, 0, 0.22883, 0.12474, 0.25745, 0.26051, 0.33657, 0.44595, 0.43083, 0.61153, 0.52005, 0.792, 0.31637, 0.09659, 0.37528, 0.24395, 0.48133, 0.38966, 0.5958, 0.53371, 0.75908, 0.68438, 0.11942, 0.10487, 0.11773, 0.25058, 0.17328, 0.44595, 0.25913, 0.64464, 0.3534, 0.83671], "triangles": [33, 22, 23, 13, 32, 33, 11, 23, 10, 33, 23, 11, 12, 13, 33, 12, 33, 11, 32, 21, 22, 14, 31, 32, 33, 32, 22, 13, 14, 32, 15, 30, 31, 31, 20, 21, 32, 31, 21, 14, 15, 31, 29, 18, 0, 19, 29, 0, 17, 18, 29, 16, 17, 29, 30, 16, 29, 30, 29, 19, 30, 19, 20, 15, 16, 30, 31, 30, 20, 28, 5, 6, 8, 6, 7, 28, 6, 8, 23, 27, 28, 9, 28, 8, 10, 23, 28, 10, 28, 9, 27, 4, 5, 27, 5, 28, 22, 26, 27, 23, 22, 27, 21, 25, 26, 26, 3, 4, 26, 4, 27, 22, 21, 26, 25, 24, 2, 21, 20, 25, 25, 2, 3, 25, 3, 26, 24, 0, 1, 24, 1, 2, 24, 19, 0, 20, 19, 24, 20, 24, 25], "vertices": [2, 95, -8.61, 2.7, 0.64047, 96, -4.58, 9.62, 0.35953, 2, 95, -4.25, 6.14, 0.94452, 96, -1.74, 14.39, 0.05548, 1, 95, 1.67, 7.97, 1, 2, 95, 10.06, 5.23, 0.73333, 97, -1.27, 5.23, 0.26667, 2, 97, 9.23, 5.24, 0.74332, 98, -1.28, 5.51, 0.25668, 2, 98, 9.4, 6.75, 0.63486, 99, -0.87, 6.96, 0.36514, 2, 98, 19.97, 9.02, 0.00313, 99, 9.93, 7.57, 0.99687, 1, 99, 17.02, 7.68, 1, 2, 99, 16.06, -0.7, 0.986, 102, 8.94, 33.45, 0.014, 2, 99, 16.23, -10.75, 0.76224, 102, 15.36, 25.71, 0.23776, 2, 99, 13.81, -21.81, 0.41977, 102, 20.4, 15.58, 0.58023, 3, 98, 22.46, -27.89, 6e-05, 99, 6.66, -29.28, 0.13213, 102, 19.5, 5.28, 0.86781, 1, 102, 15.15, -6.47, 1, 3, 100, 30.15, -9.66, 0.00023, 101, 17, -9.66, 0.22828, 102, 3.85, -9.66, 0.77148, 4, 96, 29.87, -11.26, 0.00102, 100, 16.72, -11.26, 0.30929, 101, 3.56, -11.26, 0.66013, 102, -9.59, -11.26, 0.02956, 3, 96, 15.24, -11.16, 0.37546, 100, 2.09, -11.16, 0.61701, 101, -11.06, -11.16, 0.00754, 2, 96, 3.59, -8.15, 0.98709, 100, -9.56, -8.15, 0.01291, 2, 95, -14.61, -10.25, 0.00106, 96, -5.55, -4.62, 0.99894, 2, 95, -14.12, -3.39, 0.12115, 96, -7.54, 1.97, 0.87885, 2, 95, -2.9, -2.33, 0.64847, 96, 2.55, 6.97, 0.35153, 4, 95, 4.39, -6.61, 0.43985, 97, -6.95, -6.61, 0.00649, 96, 10.9, 5.58, 0.43885, 100, -2.25, 5.58, 0.1148, 5, 95, 15.97, -10.68, 0.02118, 97, 4.63, -10.68, 0.26147, 98, -8.27, -9.52, 0.02312, 100, 10.01, 5.93, 0.51451, 101, -3.14, 5.93, 0.17972, 6, 97, 15.91, -13.26, 0.05392, 98, 2.48, -13.81, 0.21898, 99, -10.89, -12.27, 0.02574, 100, 21.47, 7.56, 0.00352, 101, 8.32, 7.56, 0.58469, 102, -4.83, 7.56, 0.11316, 4, 98, 13.58, -18.99, 0.07234, 99, -0.74, -19.11, 0.17677, 101, 20.52, 8.58, 0.04027, 102, 7.36, 8.58, 0.71062, 2, 95, -0.65, 2.72, 0.99187, 96, 2.85, 12.48, 0.00813, 4, 95, 8.39, -0.69, 0.98252, 96, 12.5, 12.54, 0.00654, 100, -0.65, 12.54, 0.01093, 101, -13.8, 12.54, 1e-05, 3, 97, 7.91, -1.94, 0.94282, 100, 9.94, 15.27, 0.02819, 101, -3.21, 15.27, 0.02898, 6, 97, 19.03, -2.74, 0.00428, 98, 7.18, -3.89, 0.85555, 99, -4.71, -3.19, 0.0404, 100, 20.61, 18.5, 0.00011, 101, 7.46, 18.5, 0.06404, 102, -5.69, 18.5, 0.03561, 4, 98, 20.58, -4.86, 0.01213, 99, 8.38, -6.23, 0.86238, 101, 19.57, 24.33, 0.00457, 102, 6.41, 24.33, 0.12092, 2, 95, -8.2, -6.39, 0.06627, 96, -0.95, 1.28, 0.93373, 2, 96, 7.31, -2.02, 0.99628, 100, -5.84, -2.02, 0.00372, 3, 96, 19.63, -3.21, 0.03662, 100, 6.48, -3.21, 0.9596, 101, -6.68, -3.21, 0.00378, 3, 100, 19.64, -2.78, 0.02182, 101, 6.49, -2.78, 0.96332, 102, -6.67, -2.78, 0.01486, 2, 101, 19.45, -1.73, 0.00561, 102, 6.3, -1.73, 0.99439], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 62, 64, 64, 66], "width": 60, "height": 61}}, "H_zj_002": {"H_zj_002": {"type": "mesh", "uvs": [0.47353, 0, 0.61597, 0, 0.76099, 0, 1, 0, 1, 0.24984, 0.97685, 0.42001, 0.95781, 0.55995, 0.92415, 0.80178, 0.87494, 0.99667, 0.51497, 0.98813, 0.22751, 0.93835, 0.07471, 0.88287, 0.14204, 0.7079, 0.19384, 0.55568, 0, 0.65526, 0, 0.55142, 0.14463, 0.41059, 0.30002, 0.23846, 0.4062, 0.11185, 0.73768, 0.17302, 0.7092, 0.4177, 0.66258, 0.55995, 0.58489, 0.79182, 0.35699, 0.41343, 0.46317, 0.24984, 0.54345, 0.13746], "triangles": [15, 16, 13, 14, 15, 13, 22, 13, 21, 12, 13, 22, 7, 22, 21, 6, 7, 21, 10, 11, 12, 22, 10, 12, 9, 10, 22, 8, 22, 7, 9, 22, 8, 16, 17, 23, 23, 24, 20, 5, 20, 4, 13, 16, 23, 21, 23, 20, 13, 23, 21, 6, 20, 5, 21, 20, 6, 18, 0, 25, 24, 18, 25, 17, 18, 24, 23, 17, 24, 19, 24, 25, 20, 19, 4, 20, 24, 19, 4, 19, 3, 25, 0, 1, 19, 1, 2, 25, 1, 19, 19, 2, 3], "vertices": [2, 103, 6.29, -7.53, 0.95059, 104, -7.82, -7.04, 0.04941, 2, 103, 4.29, -2.35, 0.99982, 104, -9.49, -1.75, 0.00018, 2, 103, 2.24, 2.92, 0.97069, 104, -11.19, 3.65, 0.02931, 3, 103, -1.12, 11.62, 0.88365, 104, -14, 12.54, 0.11633, 105, -26.76, 14.32, 2e-05, 4, 103, 15.42, 18.03, 0.20558, 104, 2.92, 17.88, 0.60175, 105, -9.54, 18.54, 0.18378, 106, -21.81, 20.14, 0.00889, 4, 103, 27.01, 21.55, 0.00792, 104, 14.71, 20.65, 0.33928, 105, 2.41, 20.54, 0.49589, 106, -9.75, 21.28, 0.1569, 3, 104, 24.41, 22.93, 0.09458, 105, 12.24, 22.18, 0.4144, 106, 0.17, 22.22, 0.49101, 3, 104, 41.18, 26.85, 0.00073, 105, 29.23, 24.99, 0.0609, 106, 17.32, 23.82, 0.93837, 2, 105, 43.13, 26.41, 0.00109, 106, 31.28, 24.26, 0.99891, 1, 106, 33.05, 10.32, 1, 1, 106, 31.45, -1.33, 1, 1, 106, 28.57, -7.87, 1, 1, 106, 15.88, -7.37, 1, 2, 105, 19.03, -6.83, 0.1622, 106, 4.89, -7.2, 0.8378, 2, 105, 27.7, -12.5, 0.15106, 106, 13.13, -13.46, 0.84894, 2, 105, 20.54, -14.25, 0.20689, 106, 5.86, -14.7, 0.79311, 3, 104, 23.84, -10.5, 0.00318, 105, 9.48, -11.15, 0.75315, 106, -4.94, -10.83, 0.24367, 2, 104, 10.36, -8.4, 0.78585, 105, -3.83, -8.17, 0.21415, 2, 103, 14.65, -7.11, 0.28324, 104, 0.54, -7.16, 0.71676, 3, 103, 14.03, 6.52, 0.33261, 104, 0.8, 6.48, 0.63545, 105, -12.4, 7.31, 0.03194, 4, 103, 30.63, 11.76, 0.00026, 104, 17.7, 10.65, 0.22782, 105, 4.74, 10.36, 0.6525, 106, -8.15, 10.96, 0.11941, 3, 104, 27.88, 11.95, 0.03284, 105, 14.98, 10.99, 0.37443, 106, 2.11, 10.87, 0.59273, 2, 105, 31.69, 11.97, 0.01728, 106, 18.85, 10.66, 0.98272, 2, 105, 7.71, -3.06, 0.98389, 106, -6.14, -2.63, 0.01611, 1, 104, 9.22, -2.09, 1, 2, 103, 14.41, -1.46, 0.15677, 104, 0.67, -1.51, 0.84323], "hull": 19, "edges": [4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 4, 38, 8, 10, 10, 12, 40, 42, 42, 44, 26, 46, 46, 48, 48, 50, 0, 2, 2, 4], "width": 39, "height": 71}}, "H_zj_003": {"H_zj_003": {"type": "mesh", "uvs": [0, 0.15071, 0.0079, 0.05752, 0.14356, 0, 0.3489, 0, 0.51756, 0.01474, 0.6569, 0.15071, 0.7779, 0.25307, 0.9209, 0.32182, 1, 0.41044, 0.99423, 0.46085, 0.88056, 0.48988, 0.7889, 0.57849, 0.6789, 0.60446, 0.63123, 0.76488, 0.7119, 0.85349, 0.7119, 0.93752, 0.65323, 0.99557, 0.4149, 1, 0.2499, 1, 0.12156, 0.95127, 0.11423, 0.87793, 0.24623, 0.81835, 0.34523, 0.77252, 0.32323, 0.58613, 0.2389, 0.56016, 0.23523, 0.43488, 0.22423, 0.3921, 0.1729, 0.35696, 0, 0.27599, 0.17655, 0.10029, 0.37089, 0.18432, 0.53955, 0.33557, 0.60188, 0.40432, 0.52488, 0.54488, 0.49188, 0.60293, 0.48088, 0.78168, 0.45888, 0.90696], "triangles": [16, 17, 36, 17, 18, 36, 18, 19, 36, 16, 36, 15, 19, 20, 36, 36, 14, 15, 20, 21, 36, 36, 21, 35, 36, 35, 14, 35, 13, 14, 35, 21, 22, 13, 35, 34, 35, 22, 34, 22, 23, 34, 13, 34, 12, 11, 12, 33, 12, 34, 33, 34, 23, 33, 23, 24, 33, 11, 33, 10, 24, 25, 33, 33, 32, 10, 33, 25, 32, 9, 10, 8, 8, 10, 32, 25, 26, 32, 32, 7, 8, 26, 31, 32, 7, 31, 6, 7, 32, 31, 26, 27, 31, 31, 27, 30, 27, 28, 30, 6, 30, 5, 6, 31, 30, 30, 0, 29, 30, 28, 0, 30, 29, 4, 30, 4, 5, 4, 29, 3, 0, 1, 29, 1, 2, 29, 29, 2, 3], "vertices": [1, 84, -4.77, -6.87, 1, 1, 84, -10.56, -3.46, 1, 1, 84, -12.26, 2.09, 1, 1, 84, -9.33, 7.5, 1, 1, 84, -5.99, 11.45, 1, 1, 84, 4.61, 10.46, 1, 1, 84, 12.82, 10.14, 1, 2, 84, 19.22, 11.56, 0.95687, 85, -11.43, 4.99, 0.04313, 2, 84, 25.96, 10.6, 0.70693, 85, -5.94, 9.02, 0.29307, 2, 84, 29.07, 8.72, 0.55736, 85, -2.41, 9.84, 0.44264, 2, 84, 29.28, 4.73, 0.28912, 85, 0.54, 7.14, 0.71088, 1, 85, 7.43, 6.24, 1, 1, 85, 10.13, 3.58, 1, 2, 85, 21.63, 5.36, 0.97784, 86, -7.66, 3.28, 0.02216, 2, 85, 27.1, 9.44, 0.53401, 86, -3.6, 8.77, 0.46599, 2, 85, 32.92, 11.09, 0.16801, 86, 1.49, 12.04, 0.83199, 2, 85, 37.42, 10.54, 0.06431, 86, 5.96, 12.82, 0.93569, 2, 85, 39.69, 3.75, 0.00177, 86, 10.09, 6.98, 0.99823, 1, 86, 12.77, 2.81, 1, 1, 86, 11.9, -2.32, 1, 1, 86, 7.58, -5.36, 1, 2, 85, 28.49, -4.69, 0.18691, 86, 1.83, -4.35, 0.81309, 2, 85, 24.51, -2.74, 0.94955, 86, -2.56, -3.64, 0.05045, 2, 84, 27.41, -13.27, 0.08514, 85, 11.78, -7.05, 0.91486, 2, 84, 24.56, -14.61, 0.1844, 85, 10.67, -9.99, 0.8156, 2, 84, 16.58, -10.41, 0.70511, 85, 2.03, -12.57, 0.29489, 2, 84, 13.71, -9.23, 0.88594, 85, -0.84, -13.73, 0.11406, 2, 84, 10.76, -9.38, 0.96992, 85, -2.86, -15.9, 0.03008, 1, 84, 3.16, -11.16, 1, 1, 84, -5.44, -0.48, 1, 1, 84, 2.65, 1.76, 1, 1, 84, 14.64, 1.03, 1, 1, 84, 19.88, 0.31, 1, 2, 84, 27.68, -6.54, 0.03946, 85, 7.27, -2.04, 0.96054, 2, 84, 30.89, -9.4, 0.00849, 85, 11.56, -1.85, 0.99151, 1, 85, 24.03, 1.35, 1, 2, 85, 32.88, 3.19, 0.05942, 86, 3.74, 4.47, 0.94058], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72], "width": 30, "height": 72}}, "H_zj_0010": {"H_zj_0010": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.83, -23.36, 20.86, 23.54, 71.75, 20.26, 68.73, -26.64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 47, "height": 51}}, "H_zj_005": {"H_zj_005": {"type": "mesh", "uvs": [0.65441, 0.01934, 0.68229, 0.13731, 0.85269, 0.14153, 0.99519, 0.16681, 1, 0.29321, 0.99519, 0.40697, 0.89296, 0.49966, 0.80931, 0.61974, 0.71947, 0.68504, 0.71018, 0.81566, 0.62653, 0.94206, 0.44684, 0.99894, 0.23308, 1, 0.06579, 0.93784, 0, 0.8304, 0, 0.67451, 0, 0.52915, 0.00383, 0.36483, 0.07818, 0.18998, 0.24547, 0.16259, 0.32602, 0.13521, 0.3539, 0.04041, 0.41586, 0, 0.54598, 0, 0.52119, 0.15206, 0.52429, 0.33323, 0.47472, 0.5123, 0.40966, 0.72928, 0.40656, 0.89571, 0.82789, 0.28899, 0.80001, 0.42171, 0.67609, 0.58814, 0.26095, 0.30374, 0.1897, 0.46384, 0.1897, 0.65344], "triangles": [29, 1, 2, 29, 2, 3, 29, 3, 4, 5, 29, 4, 30, 25, 29, 30, 29, 5, 6, 30, 5, 32, 19, 20, 18, 19, 32, 17, 18, 32, 33, 17, 32, 16, 17, 33, 24, 22, 23, 24, 23, 0, 24, 0, 1, 21, 22, 24, 20, 21, 24, 25, 24, 1, 25, 1, 29, 32, 20, 24, 32, 24, 25, 26, 32, 25, 26, 25, 30, 33, 32, 26, 31, 26, 30, 31, 30, 6, 7, 31, 6, 34, 33, 26, 16, 33, 34, 15, 16, 34, 8, 31, 7, 27, 34, 26, 27, 26, 31, 27, 31, 8, 9, 27, 8, 14, 15, 34, 27, 14, 34, 28, 27, 9, 28, 14, 27, 13, 14, 28, 10, 28, 9, 11, 28, 10, 12, 13, 28, 12, 28, 11], "vertices": [2, 74, 26.86, -5.87, 0.99822, 79, 19.92, 8.35, 0.00178, 2, 74, 17.94, -6.72, 0.78894, 79, 11, 7.5, 0.21106, 2, 74, 17.07, -15.37, 0.16066, 79, 10.13, -1.15, 0.83934, 2, 74, 14.71, -22.5, 0.00419, 79, 7.77, -8.28, 0.99581, 2, 73, 40.18, -17.75, 0.03796, 79, -1.71, -7.91, 0.96204, 3, 73, 32.16, -20.68, 0.23496, 74, -3.27, -21.34, 0.00018, 79, -10.21, -7.12, 0.76486, 4, 93, -15.91, 27.2, 0.00016, 73, 23.77, -18.41, 0.56984, 74, -9.87, -15.69, 0.00653, 79, -16.81, -1.47, 0.42347, 3, 93, -7.13, 22.47, 0.03198, 73, 13.83, -17.78, 0.83688, 79, -25.52, 3.37, 0.13113, 3, 93, -2.48, 17.64, 0.18328, 73, 7.58, -15.33, 0.77679, 79, -30.11, 8.26, 0.03993, 3, 93, 7.28, 16.66, 0.6534, 73, -1.7, -18.52, 0.3447, 79, -39.86, 9.36, 0.00189, 2, 93, 16.52, 11.91, 0.93281, 73, -12.08, -18.07, 0.06719, 2, 93, 20.31, 2.54, 0.99945, 73, -19.44, -11.13, 0.00055, 1, 93, 19.82, -8.36, 1, 3, 93, 14.72, -16.63, 0.98869, 73, -22.37, 8.62, 0.0072, 80, -48.62, 12.57, 0.00411, 3, 93, 6.5, -19.57, 0.88344, 73, -16.13, 14.72, 0.08997, 80, -40.36, 15.4, 0.02659, 3, 93, -5.17, -18.96, 0.44415, 73, -5.27, 19.05, 0.41172, 80, -28.69, 14.65, 0.14413, 3, 93, -16.06, -18.4, 0.09597, 73, 4.86, 23.08, 0.49861, 80, -17.81, 13.95, 0.40542, 3, 93, -28.36, -17.56, 0.00365, 73, 16.38, 27.46, 0.21139, 80, -5.53, 12.96, 0.78496, 3, 73, 29.97, 28.79, 0.00109, 74, 15.99, 24.28, 0.00318, 80, 7.32, 8.33, 0.99574, 2, 74, 17.49, 15.63, 0.23085, 80, 8.82, -0.32, 0.76915, 2, 74, 19.27, 11.4, 0.60714, 80, 10.6, -4.55, 0.39286, 2, 74, 26.27, 9.52, 0.91598, 80, 17.61, -6.43, 0.08402, 2, 74, 29.1, 6.18, 0.96467, 80, 20.43, -9.77, 0.03533, 2, 74, 28.67, -0.45, 0.99909, 80, 20, -16.4, 0.00091, 2, 74, 17.37, 1.55, 0.97514, 80, 8.7, -14.4, 0.02486, 3, 73, 28.41, 3.68, 0.07597, 74, 3.8, 2.27, 0.87075, 80, -4.87, -13.68, 0.05327, 3, 73, 15, 1.06, 0.98837, 74, -9.44, 5.65, 0.00038, 80, -18.11, -10.3, 0.01125, 3, 93, 0.01, 1.69, 0.61212, 73, -1.35, -1.88, 0.38788, 79, -32.41, 24.24, 0, 2, 93, 12.47, 0.89, 0.99785, 73, -13, -6.36, 0.00215, 3, 73, 37.22, -9.48, 0.01628, 74, 6.11, -13.4, 0.04784, 79, -0.83, 0.83, 0.93588, 3, 73, 27.45, -11.84, 0.44419, 74, -3.73, -11.34, 0.0859, 79, -10.67, 2.89, 0.46991, 3, 93, -9.85, 15.81, 0.03091, 73, 13.51, -10.59, 0.89125, 79, -22.72, 10, 0.07784, 3, 73, 25.49, 16.97, 0.04701, 74, 6.87, 15.53, 0.00596, 80, -1.8, -0.42, 0.94703, 3, 93, -20.45, -8.48, 0.01505, 73, 12.99, 15.9, 0.49401, 80, -13.55, 3.98, 0.49094, 3, 93, -6.25, -9.22, 0.30317, 73, -0.22, 10.64, 0.57917, 80, -27.74, 4.89, 0.11766], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 58, 60, 60, 62, 64, 66, 66, 68], "width": 51, "height": 75}}, "Q_zj_16": {"Q_zj_0015": {"type": "mesh", "uvs": [1, 0.4423, 1, 0.31609, 0.94753, 0.18409, 0.78457, 0.09146, 0.5939, 0.01504, 0.47657, 0, 0.33968, 0, 0.26961, 0.11346, 0.1865, 0.26977, 0.10991, 0.4423, 0.03983, 0.63219, 0, 0.77693, 0, 1, 0.11969, 0.84524, 0.26798, 0.71093, 0.42279, 0.56619, 0.53035, 0.42262, 0.61672, 0.3103, 0.73568, 0.32072, 0.87909, 0.37167, 0.94753, 0.44809, 0.94265, 0.34388, 0.84161, 0.25009, 0.69658, 0.17019, 0.53362, 0.14009, 0.37717, 0.2964, 0.25332, 0.48166, 0.12946, 0.66229, 0.06916, 0.80703], "triangles": [27, 10, 26, 14, 27, 26, 15, 14, 26, 11, 10, 27, 28, 11, 27, 28, 27, 14, 13, 28, 14, 12, 11, 28, 12, 28, 13, 8, 7, 25, 26, 8, 25, 26, 25, 16, 9, 8, 26, 15, 26, 16, 10, 9, 26, 24, 5, 4, 23, 24, 4, 7, 5, 24, 5, 7, 6, 25, 7, 24, 17, 24, 23, 25, 24, 17, 16, 25, 17, 3, 23, 4, 22, 23, 3, 2, 22, 3, 18, 17, 23, 22, 2, 1, 22, 18, 23, 21, 22, 1, 19, 18, 22, 21, 19, 22, 21, 1, 0, 20, 19, 21, 0, 20, 21], "vertices": [1, 142, -6.9, 4.78, 1, 1, 142, -4.54, 0.6, 1, 1, 142, -0.85, -3.07, 1, 1, 142, 4.72, -3.98, 1, 3, 142, 10.63, -3.98, 0.30951, 143, -3.78, -3.61, 0.65604, 144, -12.19, -3.45, 0.03445, 3, 142, 13.67, -2.92, 0.05385, 143, -2.41, -6.53, 0.83857, 144, -10.83, -6.37, 0.10758, 3, 142, 16.89, -1.11, 0.00022, 143, -0.26, -9.54, 0.81234, 144, -8.71, -9.39, 0.18744, 2, 143, 4.35, -8.57, 0.60368, 144, -4.09, -8.46, 0.39632, 2, 143, 10.49, -6.95, 0.05605, 144, 2.06, -6.88, 0.94395, 2, 144, 8.61, -4.8, 0.62684, 145, -0.12, -4.81, 0.37316, 1, 145, 7.09, -2.9, 1, 1, 145, 12.41, -1.13, 1, 1, 145, 19.78, 3.05, 1, 1, 145, 13.07, 2.96, 1, 2, 144, 14.5, 4.57, 0.0005, 145, 6.66, 3.93, 0.9995, 3, 143, 15.96, 4.78, 0.03601, 144, 7.6, 4.82, 0.5717, 145, -0.18, 4.86, 0.39229, 3, 142, 4.52, 10.36, 0.0001, 143, 9.83, 3.98, 0.74721, 144, 1.47, 4.06, 0.25269, 2, 142, 4.58, 5.49, 0.20688, 143, 5, 3.4, 0.79312, 2, 142, 1.59, 4.26, 0.80155, 143, 3.46, 6.24, 0.19845, 2, 142, -2.74, 4.05, 0.99716, 143, 2.79, 10.52, 0.00284, 1, 142, -5.77, 5.67, 1, 2, 142, -3.71, 2.28, 0.99999, 143, 0.93, 11.31, 1e-05, 2, 142, 0.41, 0.52, 0.99554, 143, -0.39, 7.02, 0.00446, 1, 142, 5.32, -0.2, 1, 2, 143, 1.04, -2.18, 0.94727, 144, -7.36, -2.05, 0.05273, 2, 143, 8.32, -2.18, 0.07199, 144, -0.08, -2.08, 0.92801, 2, 144, 7.61, -0.77, 0.88549, 145, -0.73, -0.7, 0.11451, 1, 145, 6.89, -0.23, 1, 1, 145, 12.48, 1.06, 1], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 27, "height": 38}}, "H_zj_007": {"H_zj_007": {"type": "mesh", "uvs": [0.64059, 0, 0.80861, 0, 0.93565, 0.10899, 1, 0.27818, 1, 0.45334, 1, 0.54615, 1, 0.65836, 0.8291, 0.71808, 0.79632, 0.78177, 0.70616, 0.79969, 0.68977, 0.88528, 0.70206, 0.98281, 0.59141, 1, 0.36602, 1, 0.14473, 1, 0.01359, 0.99077, 0.00949, 0.90518, 0.14473, 0.87532, 0.30865, 0.86935, 0.4152, 0.81362, 0.33734, 0.74594, 0.32094, 0.67827, 0.15292, 0.62054, 0.15292, 0.46927, 0.27177, 0.32595, 0.33324, 0.16472, 0.37887, 0.08889, 0.4111, 0.03534, 0.49306, 0, 0.68234, 0.09929, 0.66382, 0.19457, 0.61288, 0.36926, 0.57084, 0.52412, 0.55239, 0.69361, 0.55662, 0.76021, 0.55886, 0.81747, 0.52448, 0.91227, 0.3539, 0.92723, 0.15122, 0.94432], "triangles": [13, 36, 12, 12, 36, 11, 14, 38, 13, 38, 37, 13, 13, 37, 36, 14, 15, 38, 15, 16, 38, 36, 10, 11, 16, 17, 38, 38, 18, 37, 38, 17, 18, 37, 18, 36, 18, 19, 36, 36, 35, 10, 36, 19, 35, 10, 35, 9, 19, 34, 35, 35, 34, 9, 19, 20, 34, 8, 9, 7, 7, 9, 34, 20, 33, 34, 34, 33, 7, 20, 21, 33, 5, 6, 7, 7, 33, 32, 33, 21, 32, 21, 22, 32, 7, 32, 5, 22, 23, 32, 32, 4, 5, 32, 31, 4, 32, 23, 31, 31, 23, 24, 4, 31, 3, 31, 30, 3, 30, 2, 3, 30, 29, 2, 31, 24, 30, 30, 24, 25, 30, 25, 26, 30, 26, 29, 29, 26, 27, 29, 27, 28, 29, 28, 0, 29, 1, 2, 29, 0, 1], "vertices": [1, 87, -9.28, -2.28, 1, 1, 87, -11.29, 6.05, 1, 1, 87, -1.68, 15.03, 1, 2, 87, 14.81, 22.39, 0.93815, 88, -14.77, 28.48, 0.06185, 2, 87, 32.69, 26.71, 0.47568, 88, 3.57, 27.1, 0.52432, 2, 87, 42.17, 29, 0.18137, 88, 13.29, 26.37, 0.81863, 2, 87, 53.62, 31.76, 0.03466, 88, 25.03, 25.48, 0.96534, 2, 87, 61.76, 24.76, 0.0025, 88, 30.63, 16.32, 0.9975, 1, 88, 37.18, 14.15, 1, 1, 88, 38.71, 9.42, 1, 2, 88, 47.61, 7.92, 0.87412, 89, -7.86, 1.14, 0.12588, 2, 88, 57.87, 7.77, 0.19938, 89, -5.45, 11.12, 0.80062, 2, 88, 59.24, 2.01, 0.0609, 89, 0.47, 11.19, 0.9391, 1, 89, 11.46, 7.81, 1, 1, 89, 22.25, 4.5, 1, 1, 89, 28.36, 1.61, 1, 1, 89, 25.92, -7.04, 1, 1, 89, 18.41, -8.02, 1, 2, 88, 44.48, -11.34, 0.10088, 89, 10.23, -6.16, 0.89912, 2, 88, 39.05, -5.48, 0.84304, 89, 3.32, -10.16, 0.15696, 3, 87, 70.49, 1.07, 0.00456, 88, 31.67, -8.91, 0.9954, 89, 5.03, -18.12, 5e-05, 2, 87, 63.78, -1.41, 0.08314, 88, 24.52, -9.21, 0.91686, 2, 87, 59.9, -11.17, 0.41231, 88, 17.83, -17.3, 0.58769, 2, 87, 44.45, -14.89, 0.95987, 88, 2, -16.11, 0.04013, 2, 87, 28.4, -12.53, 0.82619, 88, -12.56, -8.94, 0.17381, 2, 87, 11.21, -13.46, 0.99501, 88, -29.2, -4.54, 0.00499, 2, 87, 2.93, -13.06, 0.99998, 88, -36.97, -1.62, 2e-05, 1, 87, -2.92, -12.79, 1, 1, 87, -7.51, -9.59, 1, 1, 87, 0.36, 2.24, 1, 1, 87, 10.3, 3.67, 1, 2, 87, 28.74, 5.45, 0.95607, 88, -6.72, 8.07, 0.04393, 2, 87, 45.05, 7.18, 0.01541, 88, 9.34, 4.71, 0.98459, 1, 88, 27.01, 2.44, 1, 1, 88, 34, 2.13, 1, 1, 88, 40.01, 1.79, 1, 1, 89, 1.03, 1.38, 1, 1, 89, 9.81, 0.33, 1, 1, 89, 20.22, -0.99, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 0, 56, 0, 58, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 8, 10, 10, 12, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76], "width": 51, "height": 105}}}}], "events": {"hit": {}}, "animations": {"attack1": {"slots": {"L_jioall1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_4"}]}, "Q_zj_008_1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4333, "name": "Q_zj_008_1"}]}, "H_zj_007": {"attachment": [{"time": 0.1667, "name": "H_zj_007"}, {"time": 0.4667, "name": null}]}, "Q_zj_003": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_003"}]}, "H_zj_0010_2": {"attachment": [{"time": 0.1667, "name": "H_zj_0010_2"}, {"time": 0.4667, "name": null}]}, "H_zj_0012": {"attachment": [{"time": 0.1667, "name": "H_zj_0012"}, {"time": 0.4667, "name": null}]}, "H_zj_0012_1": {"attachment": [{"time": 0.1667, "name": "H_zj_0012_1"}, {"time": 0.4333, "name": null}]}, "Q_zj_003_1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4333, "name": "Q_zj_003_1"}]}, "H_zj_008": {"attachment": [{"time": 0.1667, "name": "H_zj_008"}, {"time": 0.4667, "name": null}]}, "Q_zj_005": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_005"}]}, "H_zj_005": {"attachment": [{"time": 0.1667, "name": "H_zj_005"}, {"time": 0.4667, "name": null}]}, "H_zj_004": {"attachment": [{"time": 0.1667, "name": "H_zj_004"}, {"time": 0.4667, "name": null}]}, "Q_zj_003_2": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4333, "name": "Q_zj_003_2"}]}, "Q_zj_0016": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_0016"}]}, "Q_zj_1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_001"}]}, "Q_zj_13": {"attachment": [{"time": 0.1667, "name": "Q_zj_0013"}, {"time": 0.4667, "name": null}]}, "H_zj_002": {"attachment": [{"time": 0.1667, "name": "H_zj_002"}, {"time": 0.4333, "name": null}]}, "Q_zj_0013": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_0013"}]}, "H_zj_002_1": {"attachment": [{"time": 0.1667, "name": "H_zj_002_1"}, {"time": 0.4333, "name": null}]}, "Q_zj_006_1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4333, "name": "Q_zj_006_1"}]}, "Q_zj_008": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_008"}]}, "H_zj_001": {"attachment": [{"time": 0.1667, "name": "H_zj_001"}, {"time": 0.4333, "name": null}]}, "H_zj_0010_1": {"attachment": [{"time": 0.1667, "name": "H_zj_0010_1"}, {"time": 0.4333, "name": null}]}, "Q_zj_0012": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_0012"}]}, "H_zj_001_1": {"attachment": [{"time": 0.1667, "name": "H_zj_001_1"}, {"time": 0.4333, "name": null}]}, "C_zj_001": {"attachment": [{"time": 0.1333, "name": "C_zj_001"}, {"time": 0.1667, "name": null}]}, "H_zj_003": {"attachment": [{"time": 0.1667, "name": "H_zj_003"}, {"time": 0.4667, "name": null}]}, "Q_zj_005_1": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4333, "name": "Q_zj_005_1"}]}, "Q_zj_006": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_006"}]}, "Q_zj_0010": {"attachment": [{"time": 0.1333, "name": null}, {"time": 0.4667, "name": "Q_zj_0010"}]}, "Q_zj_0015": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_0015"}]}, "Q_zj_2": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_002"}]}, "H_zj_0010": {"attachment": [{"time": 0.1667, "name": "H_zj_0010"}, {"time": 0.4667, "name": null}]}, "H_zj_006": {"attachment": [{"time": 0.1667, "name": "H_zj_006"}, {"time": 0.4667, "name": null}]}, "Q_zj_007": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4333, "name": "Q_zj_007"}]}, "Q_zj_0014": {"attachment": [{"time": 0.1667, "name": null}, {"time": 0.4667, "name": "Q_zj_0014"}]}, "H_zj_009": {"attachment": [{"time": 0.1667, "name": "H_zj_009"}, {"time": 0.4667, "name": null}]}}, "bones": {"qian_zj_1": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -5.95, "y": -10.97, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 20.99, "y": -22, "curve": 0.472, "c3": 0.48}, {"time": 0.1667, "x": 37.62, "y": -22.03, "curve": 0.445, "c3": 0.744, "c4": 0.43}, {"time": 0.2, "x": 30.06, "y": -22.02, "curve": "stepped"}, {"time": 0.2333, "x": 30.06, "y": -22.02, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.3, "x": 4.7, "y": -21.98, "curve": 0.472, "c3": 0.48}, {"time": 0.4, "x": 15.51, "y": -21.99, "curve": "stepped"}, {"time": 0.4333, "x": 15.51, "y": -21.99, "curve": 0.472, "c3": 0.48}, {"time": 0.5, "x": -9.33, "y": -11.24, "curve": 0.472, "c3": 0.48}, {"time": 0.5333}]}, "qian_zj_3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -28.31, "curve": "stepped"}, {"time": 0.4, "angle": -28.31, "curve": 0.472, "c3": 0.48}, {"time": 0.5333}], "translate": [{"x": 0.69, "y": -0.24, "curve": 0.365, "c2": 0.46, "c3": 0.709, "c4": 0.84}, {"time": 0.2, "x": 0.07, "y": -0.02, "curve": "stepped"}, {"time": 0.2333, "x": 0.07, "y": -0.02, "curve": 0.356, "c2": 0.65, "c3": 0.691}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 0.69, "y": -0.24}]}, "qian_zj_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 22.69, "curve": "stepped"}, {"time": 0.4, "angle": 22.69, "curve": 0.472, "c3": 0.48}, {"time": 0.5333}], "translate": [{"x": 0.54, "y": -0.3, "curve": 0.333, "c2": 0.33, "c3": 0.705, "c4": 0.79}, {"time": 0.2, "x": 0.07, "y": -0.04, "curve": "stepped"}, {"time": 0.2333, "x": 0.07, "y": -0.04, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 0.54, "y": -0.3}]}, "qian_zj_5": {"translate": [{"x": 0.65, "y": -2.16, "curve": 0.344, "c2": 0.37, "c3": 0.708, "c4": 0.8}, {"time": 0.2, "x": 0.08, "y": -0.28, "curve": "stepped"}, {"time": 0.2333, "x": 0.08, "y": -0.28, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 0.65, "y": -2.16}]}, "qian_zj_52": {"translate": [{"x": -0.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "x": -0.02, "curve": "stepped"}, {"time": 0.2333, "x": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -0.13}]}, "qian_zj_6": {"translate": [{"x": 0.96, "y": 0.54, "curve": 0.273, "c2": 0.47, "c3": 0.592, "c4": 0.84}, {"time": 0.2, "x": 0.05, "y": 0.03, "curve": "stepped"}, {"time": 0.2333, "x": 0.05, "y": 0.03, "curve": 0.309, "c2": 0.65, "c3": 0.641}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 0.96, "y": 0.54}]}, "qian_zj_7": {"rotate": [{"angle": -7.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1333, "angle": -2.08, "curve": "stepped"}, {"time": 0.4, "angle": -2.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 22.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.45}], "translate": [{"x": 1.92, "y": -0.09}, {"time": 0.1333, "x": -4.97, "y": -0.85, "curve": "stepped"}, {"time": 0.4, "x": -4.97, "y": -0.85, "curve": 0.472, "c3": 0.48}, {"time": 0.5333}]}, "qian_zj_8": {"rotate": [{"angle": -7.05, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -52.71, "curve": "stepped"}, {"time": 0.4333, "angle": -52.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 18.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -1.69}]}, "qian_zj_9": {"rotate": [{"angle": -19.95}, {"time": 0.1333, "angle": -62.64, "curve": "stepped"}, {"time": 0.4, "angle": -62.64, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "angle": -2.04, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -4.41}]}, "qian_zj_10": {"rotate": [{"angle": -0.41, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -33.31, "curve": "stepped"}, {"time": 0.4, "angle": -33.31, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -0.41}], "shear": [{"time": 0.1333, "curve": 0.472, "c3": 0.48}, {"time": 0.1667, "y": 44.96, "curve": "stepped"}, {"time": 0.4, "y": 44.96, "curve": 0.472, "c3": 0.48}, {"time": 0.5333}]}, "qian_zj_11": {"rotate": [{"angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -28.03, "curve": "stepped"}, {"time": 0.4, "angle": -28.03, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -1.87}]}, "qian_zj_12": {"rotate": [{"angle": 1.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": 110.93, "curve": "stepped"}, {"time": 0.4, "angle": 110.93, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": 1.04}]}, "qian_zj_13": {"rotate": [{"angle": 2.95, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": 0.54, "curve": "stepped"}, {"time": 0.2333, "angle": 0.54, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": 2.95}]}, "qian_zj_23": {"rotate": [{"angle": 2.13}]}, "qian_zj_24": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1, "angle": -7.39, "curve": "stepped"}, {"time": 0.4667, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.27}]}, "qian_zj_25": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.1333, "angle": -7.39, "curve": "stepped"}, {"time": 0.5, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.31}]}, "qian_zj_26": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0667, "angle": 27.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.39, "curve": "stepped"}, {"time": 0.2333, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -8.16}]}, "qian_zj_27": {"rotate": [{"angle": 2.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": 0.39, "curve": "stepped"}, {"time": 0.2333, "angle": 0.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": 2.13}]}, "qian_zj_28": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "angle": -7.39, "curve": "stepped"}, {"time": 0.4667, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -2.7}]}, "qian_zj_29": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.1333, "angle": -7.39, "curve": "stepped"}, {"time": 0.5, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.85}]}, "qian_zj_30": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.0667, "angle": 27.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.39, "curve": "stepped"}, {"time": 0.2333, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.64}]}, "qian_zj_34": {"translate": [{"x": -2.58, "y": -2.52}]}, "qian_zj_35": {"rotate": [{"angle": -7.4, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.1333, "angle": -27.08, "curve": 0.28, "c3": 0.623, "c4": 0.39}, {"time": 0.2, "angle": -19.99, "curve": "stepped"}, {"time": 0.2333, "angle": -19.99, "curve": 0.326, "c2": 0.31, "c3": 0.757}, {"time": 0.4667, "angle": 18.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.4}]}, "qian_zj_36": {"rotate": [{"angle": -12.74, "curve": 0.304, "c2": 0.22, "c3": 0.644, "c4": 0.58}, {"time": 0.1667, "angle": -27.08, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.2, "angle": -24.71, "curve": "stepped"}, {"time": 0.2333, "angle": -24.71, "curve": 0.293, "c2": 0.18, "c3": 0.755}, {"time": 0.5, "angle": 18.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.74}]}, "qian_zj_37": {"rotate": [{"angle": -11.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 19.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -27.08, "curve": "stepped"}, {"time": 0.2333, "angle": -27.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.86}]}, "qian_zj_38": {"rotate": [{"angle": -6.23, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.0667, "angle": 19.13, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2, "angle": -21.06, "curve": "stepped"}, {"time": 0.2333, "angle": -21.06, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -27.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.23}]}, "qian_zj_39": {"rotate": [{"angle": 2.43, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "angle": 20.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": -10.8, "curve": "stepped"}, {"time": 0.2333, "angle": -10.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 20.56, "curve": "stepped"}, {"time": 0.5, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.43}]}, "qian_zj_40": {"rotate": [{"angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 20.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": 1.47, "curve": "stepped"}, {"time": 0.2333, "angle": 1.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -1.45}]}, "qian_zj_41": {"rotate": [{"angle": -4.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0333, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 20.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": 13.73, "curve": "stepped"}, {"time": 0.2333, "angle": 13.73, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3333, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.71}]}, "qian_zj_42": {"rotate": [{"angle": -5.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 20.9, "curve": "stepped"}, {"time": 0.2333, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.28}]}, "qian_zj_43": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": 20.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": 1.47, "curve": "stepped"}, {"time": 0.2333, "angle": 1.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 0.49}]}, "qian_zj_44": {"rotate": [{"angle": -3.71, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.0333, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 20.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": 13.73, "curve": "stepped"}, {"time": 0.2333, "angle": 13.73, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3333, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -3.71}]}, "qian_zj_45": {"rotate": [{"angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 20.9, "curve": "stepped"}, {"time": 0.2333, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.86}]}, "qian_zj_46": {"rotate": [{"angle": -3.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -21.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": 13.08, "curve": "stepped"}, {"time": 0.2333, "angle": 13.08, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -3.34}]}, "qian_zj_47": {"rotate": [{"angle": -1.41, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1, "angle": 20.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": -10.8, "curve": "stepped"}, {"time": 0.2333, "angle": -10.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 20.56, "curve": "stepped"}, {"time": 0.5, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -1.41}]}, "qian_zj_48": {"rotate": [{"angle": -5.28, "curve": 0.307, "c2": 0.23, "c3": 0.645, "c4": 0.58}, {"time": 0.1333, "angle": 20.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": 1.47, "curve": "stepped"}, {"time": 0.2333, "angle": 1.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.28}]}, "qian_zj_49": {"rotate": [{"angle": -5.01, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 20.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": 13.73, "curve": "stepped"}, {"time": 0.2333, "angle": 13.73, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3333, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -5.01}]}, "qian_zj_50": {"rotate": [{"angle": -1.44, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 20.9, "curve": "stepped"}, {"time": 0.2333, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -1.44}]}, "qian_zj_51": {"rotate": [{"angle": 2.17, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.1, "angle": -21.51, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": 13.08, "curve": "stepped"}, {"time": 0.2333, "angle": 13.08, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.17}]}, "L_jio2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 42.94, "y": -1.74, "curve": "stepped"}, {"time": 0.1667, "x": 42.94, "y": -1.74, "curve": 0.445, "c3": 0.744, "c4": 0.43}, {"time": 0.2, "x": 26.76, "y": -0.3, "curve": "stepped"}, {"time": 0.2333, "x": 26.76, "y": -0.3, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.3, "x": -27.45, "y": 4.54, "curve": 0.472, "c3": 0.48}, {"time": 0.4, "x": 39.83, "y": -4.43, "curve": "stepped"}, {"time": 0.4333, "x": 39.83, "y": -4.43, "curve": 0.472, "c3": 0.48}, {"time": 0.5, "x": -27.45, "y": 4.54, "curve": 0.472, "c3": 0.48}, {"time": 0.5333}]}, "bone2": {"rotate": [{"curve": 0.479, "c3": 0.744, "c4": 0.48}, {"time": 0.1667, "angle": -19.12, "curve": 0.324, "c2": 0.34, "c3": 0.644, "c4": 0.69}, {"time": 0.2, "angle": -7.89, "curve": "stepped"}, {"time": 0.2333, "angle": -7.89, "curve": 0.245, "c2": 0.56, "c3": 0.54}, {"time": 0.3}], "translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1667, "x": -20.52, "y": -5.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -6.23, "y": -8.97, "curve": "stepped"}, {"time": 0.4, "x": -6.23, "y": -8.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -45.45, "y": -1.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -69.75, "y": -0.88}]}, "bone15": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1667, "angle": 77.95, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.2, "angle": 84.71, "curve": "stepped"}, {"time": 0.2667, "angle": 84.71, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.3, "angle": -118.73, "curve": "stepped"}, {"time": 0.4, "angle": -118.73, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "angle": -12.84}], "translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1667, "x": -4.44, "y": -8.72, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.2, "x": -6.29, "y": -1.93, "curve": "stepped"}, {"time": 0.2333, "x": -6.29, "y": -1.93, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.2667, "x": -7.88, "y": 3.97, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": -2.06, "y": -4.74, "curve": "stepped"}, {"time": 0.4, "x": -2.06, "y": -4.74, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "x": 0.97, "y": 33.74}], "scale": [{"curve": 0.413, "c2": 0.01, "c3": 0.731, "c4": 0.4}, {"time": 0.2, "x": 1.079, "y": 1.301, "curve": "stepped"}, {"time": 0.4667, "x": 1.079, "y": 1.301, "curve": 0.386, "c2": 0.26, "c3": 0.657, "c4": 0.66}, {"time": 0.5333}]}, "bone12": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.2, "angle": 2.49, "curve": "stepped"}, {"time": 0.4, "angle": 2.49, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.4667, "angle": 48.29}], "translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.2, "x": -2.46, "y": 4.68, "curve": "stepped"}, {"time": 0.3, "x": -2.46, "y": 4.68, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.4, "x": -0.78, "y": 0.13, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "x": -6.3, "y": -29.23}]}, "H_jio1": {"translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1667, "x": -37.43, "y": 18.15}]}, "H_jio2": {"translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1667, "x": -37.14, "y": 5.93, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.2, "x": 1.55, "y": 4.86, "curve": "stepped"}, {"time": 0.4, "x": 1.55, "y": 4.86, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.4333, "x": -57.5, "y": 4.63, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.4667, "x": -111.85, "y": 6.13, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.5333, "x": -57.5, "y": 4.63}]}, "bone16": {"rotate": [{"time": 0.0333, "curve": 0.472, "c3": 0.48}, {"time": 0.1667, "angle": 48.94, "curve": 0.302, "c2": 0.36, "c3": 0.611, "c4": 0.73}, {"time": 0.2, "angle": 31.11, "curve": "stepped"}, {"time": 0.2667, "angle": 31.11, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.3, "angle": 41.51, "curve": "stepped"}, {"time": 0.4, "angle": 41.51, "curve": 0.472, "c3": 0.48}, {"time": 0.4333, "angle": 38.2, "curve": 0.472, "c3": 0.48}, {"time": 0.5, "angle": 1.5}]}, "qian_zj_56": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1667, "angle": 104.86, "curve": "stepped"}, {"time": 0.2, "angle": 104.86, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.2333, "angle": 31.94, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.2667, "angle": 104.86, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.3, "angle": 128.56, "curve": "stepped"}, {"time": 0.4, "angle": 128.56, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.4333, "angle": -108.87, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.4667, "angle": -25.69}], "scale": [{"time": 0.4, "curve": 0.418, "c2": 0.01, "c3": 0.733, "c4": 0.4}, {"time": 0.4333, "x": 0.205, "curve": 0.377, "c2": 0.27, "c3": 0.685, "c4": 0.64}, {"time": 0.4667}], "shear": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1667, "y": -61.41, "curve": "stepped"}, {"time": 0.2667, "y": -61.41, "curve": 0.418, "c2": 0.01, "c3": 0.733, "c4": 0.4}, {"time": 0.3, "y": 67.61, "curve": "stepped"}, {"time": 0.4, "y": 67.61, "curve": 0.315, "c2": 0.33, "c3": 0.457}, {"time": 0.5333}]}, "bone": {"translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 357.87, "y": 4.48}]}, "bone13": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.2, "angle": 59.68, "curve": "stepped"}, {"time": 0.4, "angle": 59.68, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.4667, "angle": -10.97}]}, "bone17": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1667, "angle": 2.32, "curve": "stepped"}, {"time": 0.2, "angle": 2.32, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.2333, "angle": 72.06, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.2667, "angle": 2.32, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.3, "angle": -67.25, "curve": "stepped"}, {"time": 0.4, "angle": -67.25, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "angle": -131.3}]}, "qian_zj_18": {"rotate": [{"time": 0.2, "angle": -16.98}]}, "L_jioall2": {"rotate": [{"angle": -10.22}]}, "qian_zj_15": {"rotate": [{"time": 0.2, "angle": -0.81}]}, "bone4": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.27, "curve": "stepped"}, {"time": 0.2333, "angle": -10.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3}]}, "bone5": {"rotate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "bone7": {"rotate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -28.38, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": -18.04, "curve": "stepped"}, {"time": 0.2333, "angle": -18.04, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3333, "angle": 27.68, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -20.35}]}, "bone8": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -28.38, "curve": "stepped"}, {"time": 0.2333, "angle": -28.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 27.68}]}, "bone9": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -14.19, "curve": "stepped"}, {"time": 0.2333, "angle": -14.19, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2667, "angle": -28.38, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 27.68}]}, "bone18": {"rotate": [{"angle": -0.52, "curve": 0.513, "c3": 0.647, "c4": 0.67}, {"time": 0.2, "angle": 50.07, "curve": "stepped"}, {"time": 0.2333, "angle": 50.07, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.3, "angle": -37.64, "curve": "stepped"}, {"time": 0.4, "angle": -37.64, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -15.48}], "translate": [{"curve": 0.513, "c3": 0.647, "c4": 0.67}, {"time": 0.2, "x": -3.29, "y": 9.01, "curve": "stepped"}, {"time": 0.4, "x": -3.29, "y": 9.01, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.5333}]}, "bone19": {"rotate": [{"angle": 1.34, "curve": 0.513, "c3": 0.647, "c4": 0.67}, {"time": 0.2, "angle": -98.16, "curve": "stepped"}, {"time": 0.2333, "angle": -98.16, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.3, "angle": 42.47, "curve": "stepped"}, {"time": 0.4, "angle": 42.47, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": 31.63}], "translate": [{"curve": 0.513, "c3": 0.647, "c4": 0.67}, {"time": 0.2, "x": -12.73, "y": -9.1, "curve": "stepped"}, {"time": 0.4, "x": -12.73, "y": -9.1, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.5333}]}, "bone20": {"rotate": [{"angle": -1.77, "curve": 0.513, "c3": 0.647, "c4": 0.67}, {"time": 0.2, "angle": 55.03, "curve": "stepped"}, {"time": 0.2333, "angle": 55.03, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.3, "angle": -5.77, "curve": "stepped"}, {"time": 0.4, "angle": -5.77, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -17.09}]}, "bone21": {"rotate": [{"angle": -0.38, "curve": 0.513, "c3": 0.647, "c4": 0.67}, {"time": 0.2, "angle": 56.73, "curve": "stepped"}, {"time": 0.2333, "angle": 56.73, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.3, "angle": 26.34, "curve": "stepped"}, {"time": 0.4, "angle": 26.34, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -0.94}], "translate": [{"curve": 0.513, "c3": 0.647, "c4": 0.67}, {"time": 0.2, "x": -4.73, "y": 3, "curve": "stepped"}, {"time": 0.2333, "x": -4.73, "y": 3, "curve": 0.28, "c2": 0.49, "c3": 0.603, "c4": 0.85}, {"time": 0.3, "x": -5.34, "y": 3.38, "curve": "stepped"}, {"time": 0.4, "x": -5.34, "y": 3.38, "curve": 0.314, "c2": 0.65, "c3": 0.646}, {"time": 0.5333}]}, "bone22": {"rotate": [{"angle": 1.1, "curve": 0.513, "c3": 0.647, "c4": 0.67}, {"time": 0.2, "angle": -70.19, "curve": "stepped"}, {"time": 0.2333, "angle": -70.19, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.3, "angle": -12.61, "curve": "stepped"}, {"time": 0.4, "angle": -12.61, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": 4.09}]}, "bone24": {"rotate": [{"angle": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 12.94, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 0.2, "angle": 13.76, "curve": "stepped"}, {"time": 0.2333, "angle": 13.76, "curve": 0.293, "c2": 0.18, "c3": 0.64, "c4": 0.57}, {"time": 0.4, "angle": 18.88}]}, "bone25": {"rotate": [{"time": 0.0333, "angle": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.94, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 0.2, "angle": 13.28, "curve": "stepped"}, {"time": 0.2333, "angle": 13.28, "curve": 0.282, "c2": 0.12, "c3": 0.63, "c4": 0.51}, {"time": 0.4, "angle": 19.83}]}, "bone26": {"rotate": [{"time": 0.0333, "angle": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.94, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 0.2, "angle": 13.28, "curve": "stepped"}, {"time": 0.2333, "angle": 13.28, "curve": 0.282, "c2": 0.12, "c3": 0.63, "c4": 0.51}, {"time": 0.4, "angle": 19.83}]}, "bone27": {"rotate": [{"time": 0.0667, "angle": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.94, "curve": "stepped"}, {"time": 0.2333, "angle": 12.94, "curve": 0.27, "c3": 0.618, "c4": 0.41}, {"time": 0.4, "angle": 20.75}]}, "bone28": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1333, "angle": -14.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -0.78, "curve": "stepped"}, {"time": 0.2333, "angle": -0.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 12.94, "curve": 0.288, "c3": 0.628, "c4": 0.38}, {"time": 0.4, "angle": 22.44}]}, "bone29": {"rotate": [{"time": 0.0667, "angle": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.94, "curve": "stepped"}, {"time": 0.2333, "angle": 12.94, "curve": 0.27, "c3": 0.618, "c4": 0.41}, {"time": 0.4, "angle": 20.75}]}, "bone30": {"rotate": [{"time": 0.1, "angle": -14.5, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": 7.88, "curve": "stepped"}, {"time": 0.2333, "angle": 7.88, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.2667, "angle": 12.94, "curve": 0.278, "c3": 0.622, "c4": 0.4}, {"time": 0.4, "angle": 21.64}]}, "bone31": {"rotate": [{"time": 0.0333, "curve": 0.472, "c3": 0.48}, {"time": 0.1667, "angle": -14.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "angle": -9.44, "curve": "stepped"}, {"time": 0.2333, "angle": -9.44, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3333, "angle": 12.94, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 0.4, "angle": 23.14}]}, "bone32": {"rotate": [{"angle": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 12.94, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 0.2, "angle": 13.76, "curve": "stepped"}, {"time": 0.2333, "angle": 13.76, "curve": 0.293, "c2": 0.18, "c3": 0.64, "c4": 0.57}, {"time": 0.4, "angle": 18.88}]}, "bone33": {"rotate": [{"time": 0.0333, "angle": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 12.94, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 0.2, "angle": 13.28, "curve": "stepped"}, {"time": 0.2333, "angle": 13.28, "curve": 0.282, "c2": 0.12, "c3": 0.63, "c4": 0.51}, {"time": 0.4, "angle": 19.83}]}, "bone34": {"rotate": [{"time": 0.0667, "angle": -14.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.94, "curve": "stepped"}, {"time": 0.2333, "angle": 12.94, "curve": 0.27, "c3": 0.618, "c4": 0.41}, {"time": 0.4, "angle": 20.75}]}, "bone35": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1333, "angle": -14.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -0.78, "curve": "stepped"}, {"time": 0.2333, "angle": -0.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 12.94, "curve": 0.288, "c3": 0.628, "c4": 0.38}, {"time": 0.4, "angle": 22.44}]}, "qian_zj_60": {"rotate": [{"angle": 7.13}]}, "qian_zj_59": {"rotate": [{"angle": -11.94}]}, "qian_zj_58": {"rotate": [{"angle": 10.85}]}, "qian_zj_57": {"rotate": [{"angle": 2.8}]}, "qian_zj_54": {"rotate": [{"angle": -0.46}]}, "qian_zj_21": {"rotate": [{"angle": -1.15}]}, "qian_zj_70": {"rotate": [{"angle": 4.58, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.0667, "angle": -15.82, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.2, "angle": 22.67, "curve": "stepped"}, {"time": 0.2333, "angle": 22.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.3333, "angle": 48.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.58}]}, "qian_zj_66": {"rotate": [{"angle": 2.01}, {"time": 0.0667, "angle": -15.82, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.2, "angle": 22.67, "curve": "stepped"}, {"time": 0.2333, "angle": 22.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.3333, "angle": 48.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.01}]}, "qian_zj_71": {"rotate": [{"angle": 0.7, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.1333, "angle": -15.82, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.2, "angle": -1.29, "curve": "stepped"}, {"time": 0.2333, "angle": -1.29, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.4, "angle": 48.93, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.5333, "angle": 0.7}]}, "qian_zj_67": {"rotate": [{"angle": 0.66, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -15.82, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.2, "angle": -1.29, "curve": "stepped"}, {"time": 0.2333, "angle": -1.29, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.4, "angle": 48.93, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.5333, "angle": 0.66}]}, "qian_zj_72": {"rotate": [{"angle": 1.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -15.82, "curve": "stepped"}, {"time": 0.2333, "angle": -15.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 48.93, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5333, "angle": 1.31}]}, "qian_zj_68": {"rotate": [{"angle": 4.59, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2, "angle": -15.82, "curve": "stepped"}, {"time": 0.2333, "angle": -15.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 48.93, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5333, "angle": 4.59}]}, "qian_zj_69": {"rotate": [{"angle": 8.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2, "angle": -11.33, "curve": "stepped"}, {"time": 0.2333, "angle": -11.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3, "angle": -15.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 8.73}]}, "bone43": {"rotate": [{"angle": 34.72, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": 33.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 41.94, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.5333, "angle": 34.72}]}, "bone44": {"rotate": [{"angle": 1.8, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.35, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5333, "angle": 1.8}]}, "qian_zj_64": {"rotate": [{"angle": -3.06, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "angle": -22.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.69, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5333, "angle": -3.06}]}, "qian_zj_65": {"rotate": [{"angle": -3.25, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -24.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 8, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "angle": -3.25}]}, "qian_zj_63": {"rotate": [{"angle": -1.38, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -18.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.86, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5333, "angle": -1.38}]}, "qian_zj_33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -15.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 16.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "qian_zj_16": {"rotate": [{"time": 0.2, "angle": -9.38}]}, "qian_zj_19": {"rotate": [{"time": 0.2, "angle": -4.29}]}, "qian_zj_20": {"rotate": [{"time": 0.2, "angle": 26.15}]}, "bone6": {"rotate": [{"angle": -3.75, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0333, "angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667}, {"time": 0.1667, "angle": -50.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -10.3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": -3.75}]}, "bone37": {"rotate": [{"angle": -6.48, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0333, "angle": -3.75, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0667, "angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1}, {"time": 0.2, "angle": -50.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 31.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -10.3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": -6.48}]}, "bone38": {"rotate": [{"angle": -8.98, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.0333, "angle": -6.5, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": -3.79, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333}, {"time": 0.2333, "angle": -50.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 31.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -10.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5333, "angle": -8.98}]}, "bone39": {"rotate": [{"angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333}, {"time": 0.1333, "angle": -50.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 31.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -10.3, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5333, "angle": -1.34}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": -1.25, "y": 17.71}, {"time": 0.2333}]}, "bone40": {"rotate": [{"angle": -3.75, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0333, "angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -50.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -10.3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5333, "angle": -3.75}]}, "bone41": {"rotate": [{"angle": -6.5, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0333, "angle": -3.79, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -50.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 31.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -10.3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5333, "angle": -6.5}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5333}]}, "qian_zj_75": {"rotate": [{"angle": 2.13}]}, "qian_zj_78": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0667, "angle": 27.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.39, "curve": "stepped"}, {"time": 0.2333, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -8.16}]}, "qian_zj_77": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.1333, "angle": -7.39, "curve": "stepped"}, {"time": 0.5, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.31}]}, "qian_zj_76": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1, "angle": -7.39, "curve": "stepped"}, {"time": 0.4667, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.27}]}, "qian_zj_79": {"rotate": [{"angle": 2.13, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2, "angle": 0.39, "curve": "stepped"}, {"time": 0.2333, "angle": 0.39, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": 2.13}]}, "qian_zj_82": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.0667, "angle": 27.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -7.39, "curve": "stepped"}, {"time": 0.2333, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.64}]}, "qian_zj_81": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.1333, "angle": -7.39, "curve": "stepped"}, {"time": 0.5, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.85}]}, "qian_zj_80": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "angle": -7.39, "curve": "stepped"}, {"time": 0.4667, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -2.7}]}}, "ik": {"H_jio1": [{"time": 0.2, "bendPositive": false}], "H_jio2": [{"time": 0.2, "bendPositive": false, "curve": "stepped"}, {"time": 0.4, "bendPositive": false, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.4667, "bendPositive": false}]}, "deform": {"default": {"H_zj_007": {"H_zj_007": [{"time": 0.1333}, {"time": 0.1667, "offset": 42, "vertices": [-3.6212, 2.17191, -7.86348, 1.45756, -12.05873, 1.81573, -13.42225, 2.75813, -6.86704, 2.29669, -0.04389, 1.5272, -1.52115, 0.14268, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.29401, 2.75864, -14.20587, 3.33917]}, {"time": 0.2, "offset": 42, "vertices": [-3.54614, 2.37127, -7.86348, 1.45756, -12.05873, 1.81573, -13.42225, 2.75813, -6.86704, 2.29669, -0.04389, 1.5272, -1.52115, 0.14268, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -8.38528, -1.87036, -3.46012, -7.86376, -16.95249, -5.29715, -9.93777, -15.38999, -3.91352, -3.75966, -0.15469, -5.40623, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.29401, 2.75864, -14.20587, 3.33917]}]}, "H_zj_0010_2": {"H_zj_0010_2": [{"time": 0.1333, "vertices": [-5.57702, 10.65125, 1.17133, 11.13853, 0.43538, 6.18563, -6.31297, 5.6983]}]}, "H_zj_006": {"H_zj_006": [{"time": 0.1667}, {"time": 0.2, "offset": 32, "vertices": [-1.2581, 2.42004, 0.81796, 0.59109, -4.67943, 13.83884, 2.77393, 14.02052, 3.93674, 16.7986, 6.1378, 16.12489, 7.37818, 13.49253], "curve": "stepped"}, {"time": 0.4, "offset": 32, "vertices": [-1.2581, 2.42004, 0.81796, 0.59109, -4.67943, 13.83884, 2.77393, 14.02052, 3.93674, 16.7986, 6.1378, 16.12489, 7.37818, 13.49253]}, {"time": 0.4333}]}, "C_zj_001": {"C_zj_001": [{"time": 0.1333, "vertices": [2.39761, -0.30993, 2.39761, -0.30993, 2.39761, -0.30993, 2.39761, -0.30993]}]}}}, "drawOrder": [{"time": 0.1, "offsets": [{"slot": "Q_zj_13", "offset": 11}, {"slot": "H_zj_009", "offset": -8}]}, {"time": 0.1333, "offsets": [{"slot": "Q_zj_13", "offset": 20}, {"slot": "H_zj_003", "offset": 9}, {"slot": "H_zj_009", "offset": -10}]}, {"time": 0.1667, "offsets": [{"slot": "Q_zj_13", "offset": 20}, {"slot": "H_zj_003", "offset": 9}, {"slot": "H_zj_009", "offset": -12}]}, {"time": 0.2, "offsets": [{"slot": "Q_zj_13", "offset": 11}, {"slot": "H_zj_003", "offset": 9}, {"slot": "H_zj_009", "offset": -12}]}, {"time": 0.2333, "offsets": [{"slot": "Q_zj_13", "offset": 12}, {"slot": "H_zj_009", "offset": -12}]}, {"time": 0.2667, "offsets": [{"slot": "Q_zj_13", "offset": 4}, {"slot": "H_zj_003", "offset": -7}, {"slot": "H_zj_009", "offset": -8}]}, {"time": 0.3, "offsets": [{"slot": "Q_zj_13", "offset": 2}, {"slot": "H_zj_003", "offset": -9}, {"slot": "H_zj_009", "offset": -8}]}, {"time": 0.3667, "offsets": [{"slot": "Q_zj_13", "offset": 2}, {"slot": "H_zj_003", "offset": -9}, {"slot": "H_zj_009", "offset": -9}]}, {"time": 0.4, "offsets": [{"slot": "Q_zj_13", "offset": 2}, {"slot": "H_zj_003", "offset": -9}, {"slot": "H_zj_009", "offset": -8}]}, {"time": 0.5333, "offsets": [{"slot": "Q_zj_13", "offset": 11}, {"slot": "H_zj_009", "offset": -8}]}], "events": [{"time": 0.2, "name": "hit"}]}, "attack2": {"slots": {"Q_zj_006": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_006"}]}, "H_zj_007": {"attachment": [{"time": 0.2268, "name": "H_zj_007"}, {"time": 0.7087, "name": null}]}, "Q_zj_003": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_003"}]}, "H_zj_0010_2": {"attachment": [{"time": 0.2268, "name": "H_zj_0010_2"}, {"time": 0.7087, "name": null}]}, "H_zj_0012": {"color": [{"time": 0.6236, "color": "ffffffff"}, {"time": 0.6803, "color": "ffffff00"}], "attachment": [{"time": 0.2268, "name": "H_zj_0012"}, {"time": 0.737, "name": null}]}, "H_zj_0012_1": {"color": [{"time": 0.6803, "color": "ffffffff"}, {"time": 0.7087, "color": "ffffff00"}], "attachment": [{"time": 0.2268, "name": "H_zj_0012_1"}, {"time": 0.7087, "name": null}]}, "H_zj_003_1": {"attachment": [{"time": 0.2268, "name": "H_zj_003_1"}, {"time": 0.7087, "name": null}]}, "H_zj_008": {"attachment": [{"time": 0.2268, "name": "H_zj_008"}, {"time": 0.7087, "name": null}]}, "Q_zj_005": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_005"}]}, "Q_zj_0013": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_0013"}]}, "H_zj_005": {"attachment": [{"time": 0.2268, "name": "H_zj_005"}, {"time": 0.7087, "name": null}]}, "Q_zj_003_2": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_003_2"}]}, "Q_zj_0016": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_0016"}]}, "Q_zj_1": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_001"}]}, "Q_zj_15": {"attachment": [{"time": 0.2268, "name": "Q_zj_0013"}, {"time": 0.7087, "name": null}]}, "L_jioall1": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_4"}]}, "Q_zj_17": {"attachment": [{"time": 0.2268, "name": "Q_zj_0016"}, {"time": 0.6803, "name": null}]}, "Q_zj_006_1": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_006_1"}]}, "Q_zj_008": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.6236, "name": "Q_zj_008"}]}, "H_zj_001": {"attachment": [{"time": 0.2268, "name": "H_zj_001"}, {"time": 0.7087, "name": null}]}, "H_zj_006": {"attachment": [{"time": 0.2268, "name": "H_zj_006"}, {"time": 0.7087, "name": null}]}, "Q_zj_0012": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_0012"}]}, "C_zj_001": {"attachment": [{"time": 0.1984, "name": "C_zj_001"}, {"time": 0.2268, "name": null}]}, "C_zj_1": {"attachment": [{"time": 0.2268, "name": "C_zj_001"}, {"time": 0.7087, "name": null}]}, "fapian": {"attachment": [{"time": 0.2268, "name": "fapian"}, {"time": 0.6803, "name": null}]}, "H_zj_003": {"attachment": [{"time": 0.2268, "name": "H_zj_003"}, {"time": 0.7087, "name": null}]}, "Q_zj_008_1": {"color": [{"time": 0.6803, "color": "ffffff00"}, {"time": 0.7087, "color": "ffffffff"}], "attachment": [{"time": 0.2268, "name": null}, {"time": 0.6803, "name": "Q_zj_008_1"}]}, "Q_zj_0010": {"attachment": [{"time": 0.1984, "name": null}, {"time": 0.7087, "name": "Q_zj_0010"}]}, "Q_zj_0015": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_0015"}]}, "Q_zj_005_1": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_005_1"}]}, "Q_zj_2": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_002"}]}, "Q_zj_003_1": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_003_1"}]}, "Q_zj_16": {"attachment": [{"time": 0.2268, "name": "Q_zj_0015"}, {"time": 0.7087, "name": null}]}, "Q_zj_007": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_007"}]}, "Q_zj_0014": {"attachment": [{"time": 0.2268, "name": null}, {"time": 0.7087, "name": "Q_zj_0014"}]}, "H_zj_009": {"attachment": [{"time": 0.2268, "name": "H_zj_009"}, {"time": 0.7087, "name": null}]}, "H_zj_002": {"attachment": [{"time": 0.2268, "name": "H_zj_002"}, {"time": 0.7087, "name": null}]}}, "bones": {"qian_zj_1": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1984, "x": 37.62, "y": -22.03, "curve": 0.39, "c2": 0.01, "c3": 0.716, "c4": 0.38}, {"time": 0.2268, "x": 64.2, "y": -22.07, "curve": "stepped"}, {"time": 0.2551, "x": 64.2, "y": -22.07, "curve": 0.379, "c2": 0.25, "c3": 0.702, "c4": 0.6}, {"time": 0.3118, "x": 30.06, "y": -22.02, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.3685, "x": 4.7, "y": -21.98, "curve": 0.472, "c3": 0.48}, {"time": 0.4252, "x": 15.51, "y": -21.99, "curve": "stepped"}, {"time": 0.6236, "x": 15.51, "y": -21.99, "curve": 0.472, "c3": 0.48}, {"time": 0.7087, "x": -9.33, "y": -11.24, "curve": 0.472, "c3": 0.48}, {"time": 0.737}]}, "qian_zj_3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": -28.31, "curve": "stepped"}, {"time": 0.5953, "angle": -28.31, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": -23.57, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737}], "translate": [{"x": 0.69, "y": -0.24, "curve": 0.373, "c2": 0.51, "c3": 0.721, "c4": 0.91}, {"time": 0.2551, "x": 0.02, "y": -0.01, "curve": "stepped"}, {"time": 0.3118, "x": 0.02, "y": -0.01, "curve": 0.346, "c2": 0.66, "c3": 0.68}, {"time": 0.3402, "curve": "stepped"}, {"time": 0.5953, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "x": 0.12, "y": -0.04, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "x": 0.69, "y": -0.24}]}, "qian_zj_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": 22.69, "curve": "stepped"}, {"time": 0.5953, "angle": 22.69, "curve": 0.314, "c2": 0.36, "c3": 0.645, "c4": 0.7}, {"time": 0.6236}], "translate": [{"x": 0.54, "y": -0.3, "curve": 0.335, "c2": 0.34, "c3": 0.71, "c4": 0.81}, {"time": 0.1984, "x": 0.36, "y": 0.25}]}, "qian_zj_5": {"translate": [{"x": 0.65, "y": -2.16, "curve": 0.352, "c2": 0.4, "c3": 0.729, "c4": 0.88}, {"time": 0.2551, "x": 0.03, "y": -0.09, "curve": "stepped"}, {"time": 0.3118, "x": 0.03, "y": -0.09, "curve": 0.354, "c2": 0.65, "c3": 0.689}, {"time": 0.3402, "curve": "stepped"}, {"time": 0.5953, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "x": 0.11, "y": -0.36, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "x": 0.65, "y": -2.16}]}, "qian_zj_52": {"translate": [{"x": -0.13, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.2551, "x": -0.01, "curve": "stepped"}, {"time": 0.3118, "x": -0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3402, "curve": "stepped"}, {"time": 0.5953, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "x": -0.02, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "x": -0.13}]}, "qian_zj_6": {"translate": [{"x": 0.96, "y": 0.54, "curve": 0.263, "c2": 0.52, "c3": 0.577, "c4": 0.91}, {"time": 0.2551, "x": 0.01, "y": 0.01, "curve": "stepped"}, {"time": 0.3118, "x": 0.01, "y": 0.01, "curve": 0.321, "c2": 0.66, "c3": 0.654}, {"time": 0.3402, "curve": "stepped"}, {"time": 0.5953, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "x": 0.16, "y": 0.09, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "x": 0.96, "y": 0.54}]}, "qian_zj_7": {"rotate": [{"angle": -7.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1134, "angle": -35.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1984, "angle": -33.17, "curve": 0.781, "c2": 0.03, "c3": 0.946, "c4": 0}, {"time": 0.2268, "angle": 134.87, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.2551, "angle": -30.57, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3118, "angle": 2.16, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3402, "angle": -2.08, "curve": "stepped"}, {"time": 0.5953, "angle": -2.08, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6236, "angle": 30.81, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.652, "angle": -39.09, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.7087, "angle": -14.13, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7654, "angle": -7.54}], "translate": [{"x": 1.92, "y": -0.09}, {"time": 0.1134, "x": -4.97, "y": -0.85, "curve": "stepped"}, {"time": 0.5953, "x": -4.97, "y": -0.85, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "x": -4.52, "y": -2.12, "curve": 0.358, "c2": 0.29, "c3": 0.617, "c4": 0.71}, {"time": 0.7087, "x": -2.34, "y": -8.35, "curve": 0.256, "c2": 0.59, "c3": 0.567}, {"time": 0.737, "curve": 0.25, "c3": 0.75}, {"time": 0.7654, "x": 1.92, "y": -0.09}]}, "qian_zj_8": {"rotate": [{"angle": -7.05, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.0283, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.1134, "angle": 134.82, "curve": 0.357, "c2": 0.44, "c3": 0.694, "c4": 0.79}, {"time": 0.1984, "angle": 135.61, "curve": 0.781, "c2": 0.03, "c3": 0.946, "c4": 0}, {"time": 0.2268, "angle": -30.04, "curve": 0.348, "c2": 0.66, "c3": 0.682}, {"time": 0.2551, "angle": 135.83, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3118, "angle": 102.44, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3685, "angle": -52.71, "curve": "stepped"}, {"time": 0.5953, "angle": -52.71, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6236, "angle": 57.25, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.652, "angle": 96.64, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.7087, "angle": 45.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7654, "angle": -7.05}]}, "qian_zj_9": {"rotate": [{"angle": -19.95}, {"time": 0.1984, "angle": -62.64, "curve": "stepped"}, {"time": 0.5953, "angle": -62.64, "curve": 0.465, "c3": 0.747, "c4": 0.46}, {"time": 0.6236, "angle": -41.87, "curve": 0.26, "c2": 0.45, "c3": 0.475}, {"time": 0.7087, "angle": -2.04, "curve": 0.472, "c3": 0.48}, {"time": 0.737, "angle": -4.41, "curve": 0.25, "c3": 0.75}, {"time": 0.7654, "angle": -19.95}]}, "qian_zj_10": {"rotate": [{"angle": -0.41, "curve": 0.339, "c2": 0.35, "c3": 0.689, "c4": 0.74}, {"time": 0.1134, "angle": -59.22, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.1984, "angle": -33.31, "curve": "stepped"}, {"time": 0.5953, "angle": -33.31, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": -44.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7654, "angle": -0.41}], "shear": [{"time": 0.1984, "curve": 0.497, "c3": 0.56, "c4": 0.82}, {"time": 0.4252, "y": 43.98, "curve": "stepped"}, {"time": 0.5953, "y": 43.98, "curve": 0.298, "c2": 0.64, "c3": 0.629}, {"time": 0.6236, "y": 44.96, "curve": 0.472, "c3": 0.48}, {"time": 0.7654}]}, "qian_zj_11": {"rotate": [{"angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1984, "angle": -28.03, "curve": "stepped"}, {"time": 0.5953, "angle": -28.03, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": -23.64, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "angle": -1.87}]}, "qian_zj_12": {"rotate": [{"angle": 1.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1984, "angle": 110.93, "curve": "stepped"}, {"time": 0.5953, "angle": 110.93, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": 92.52, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "angle": 1.04}]}, "qian_zj_13": {"rotate": [{"angle": 2.95, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.2551, "angle": 0.19, "curve": "stepped"}, {"time": 0.3118, "angle": 0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.3402, "curve": "stepped"}, {"time": 0.5953, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": 0.49, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "angle": 2.95}]}, "qian_zj_23": {"rotate": [{"angle": 2.13}]}, "qian_zj_24": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.085, "angle": -7.39, "curve": "stepped"}, {"time": 0.7087, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -0.27}]}, "qian_zj_25": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.1984, "angle": -7.39, "curve": "stepped"}, {"time": 0.7087, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -4.31}]}, "qian_zj_26": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0229, "angle": -7.39, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.0567, "angle": 27.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": -7.43, "curve": "stepped"}, {"time": 0.3118, "angle": -7.43, "curve": 0.283, "c2": 0.16, "c3": 0.647, "c4": 0.6}, {"time": 0.4252, "angle": -7.83, "curve": "stepped"}, {"time": 0.5953, "angle": -7.83, "curve": 0.339, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.6236, "angle": -7.96, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.737, "angle": -8.16}]}, "qian_zj_27": {"rotate": [{"angle": 2.13}]}, "qian_zj_28": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.085, "angle": -7.39, "curve": "stepped"}, {"time": 0.7087, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -2.7}]}, "qian_zj_29": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.1984, "angle": -7.39, "curve": "stepped"}, {"time": 0.7087, "angle": -7.39, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -6.85}]}, "qian_zj_30": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.0229, "angle": -7.39, "curve": 0.302, "c3": 0.638, "c4": 0.36}, {"time": 0.0567, "angle": 27.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": -7.51, "curve": "stepped"}, {"time": 0.3118, "angle": -7.51, "curve": 0.283, "c2": 0.16, "c3": 0.647, "c4": 0.6}, {"time": 0.4252, "angle": -8.67, "curve": "stepped"}, {"time": 0.5953, "angle": -8.67, "curve": 0.339, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.6236, "angle": -9.04, "curve": 0.382, "c2": 0.58, "c3": 0.733}, {"time": 0.737, "angle": -9.64}]}, "qian_zj_36": {"rotate": [{"angle": -1.55, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0283}, {"time": 0.1701, "angle": -30.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3118, "angle": 27.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4252, "angle": 5.62, "curve": "stepped"}, {"time": 0.5953, "angle": 5.62, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.6236, "angle": -8.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6803, "angle": -16.12, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.7654, "angle": -1.55}]}, "qian_zj_37": {"rotate": [{"angle": -4.47, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.0283, "angle": -1.55, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0567, "curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": -30.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3402, "angle": 27.66, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4252, "angle": 12.9, "curve": "stepped"}, {"time": 0.5953, "angle": 12.9, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.6236, "angle": -1.42, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.7087, "angle": -16.12, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7654, "angle": -4.47}]}, "qian_zj_38": {"rotate": [{"angle": -8.04, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0283, "angle": -4.57, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.085}, {"time": 0.2268, "angle": -30.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3685, "angle": 27.66, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4252, "angle": 19.65, "curve": "stepped"}, {"time": 0.5953, "angle": 19.65, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.6236, "angle": 5.77, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7087, "angle": -16.12, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7654, "angle": -8.04}]}, "qian_zj_39": {"rotate": [{"angle": 2.43, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.085, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": -17.97, "curve": "stepped"}, {"time": 0.3118, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4252, "angle": 20.56, "curve": "stepped"}, {"time": 0.7087, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": 2.43}]}, "qian_zj_40": {"rotate": [{"angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1984, "angle": 20.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2551, "angle": -10.8, "curve": "stepped"}, {"time": 0.3118, "angle": -10.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3402, "angle": -17.97, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4252, "angle": 13.45, "curve": "stepped"}, {"time": 0.5953, "angle": 13.45, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6236, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -1.45}]}, "qian_zj_41": {"rotate": [{"angle": -4.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0283, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": 20.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2551, "angle": 1.47, "curve": "stepped"}, {"time": 0.3118, "angle": 1.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3685, "angle": -17.97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4252, "angle": -3.79, "curve": "stepped"}, {"time": 0.5953, "angle": -3.79, "curve": 0.331, "c2": 0.33, "c3": 0.673, "c4": 0.69}, {"time": 0.6236, "angle": 8.32, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.7087, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -4.71}]}, "qian_zj_42": {"rotate": [{"angle": -5.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0229, "angle": 20.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0567, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": 13.73, "curve": "stepped"}, {"time": 0.3118, "angle": 13.73, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3969, "angle": -17.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4252, "angle": -10.86, "curve": "stepped"}, {"time": 0.5953, "angle": -10.86, "curve": 0.313, "c2": 0.27, "c3": 0.66, "c4": 0.65}, {"time": 0.6236, "angle": 3.72, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.7087, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -5.28}]}, "qian_zj_43": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1984, "angle": 20.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2551, "angle": -10.8, "curve": "stepped"}, {"time": 0.3118, "angle": -10.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3402, "angle": -17.97, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4252, "angle": 13.45, "curve": "stepped"}, {"time": 0.5953, "angle": 13.45, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6236, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": 0.49}]}, "qian_zj_44": {"rotate": [{"angle": -3.71, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.0283, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": 20.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2551, "angle": 1.47, "curve": "stepped"}, {"time": 0.3118, "angle": 1.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3685, "angle": -17.97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4252, "angle": -3.79, "curve": "stepped"}, {"time": 0.5953, "angle": -3.79, "curve": 0.331, "c2": 0.33, "c3": 0.673, "c4": 0.69}, {"time": 0.6236, "angle": 8.32, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.7087, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -3.71}]}, "qian_zj_45": {"rotate": [{"angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.0229, "angle": 20.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0567, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": 13.73, "curve": "stepped"}, {"time": 0.3118, "angle": 13.73, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3969, "angle": -17.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4252, "angle": -10.86, "curve": "stepped"}, {"time": 0.5953, "angle": -10.86, "curve": 0.313, "c2": 0.27, "c3": 0.66, "c4": 0.65}, {"time": 0.6236, "angle": 3.72, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.7087, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -5.86}]}, "qian_zj_46": {"rotate": [{"angle": -3.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.085, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": 20.9, "curve": "stepped"}, {"time": 0.3118, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4252, "angle": -17.97, "curve": "stepped"}, {"time": 0.5953, "angle": -17.97, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.6236, "angle": -14.68, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.737, "angle": -3.34}]}, "qian_zj_47": {"rotate": [{"angle": -1.41, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.085, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": -17.97, "curve": "stepped"}, {"time": 0.3118, "angle": -17.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4252, "angle": 20.56, "curve": "stepped"}, {"time": 0.7087, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -1.41}]}, "qian_zj_48": {"rotate": [{"angle": -5.28, "curve": 0.307, "c2": 0.23, "c3": 0.645, "c4": 0.58}, {"time": 0.1984, "angle": 20.9, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.2551, "angle": -10.8, "curve": "stepped"}, {"time": 0.3118, "angle": -10.8, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.3402, "angle": -17.97, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4252, "angle": 13.45, "curve": "stepped"}, {"time": 0.5953, "angle": 13.45, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6236, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -5.28}]}, "qian_zj_49": {"rotate": [{"angle": -5.01, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0283, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": 20.9, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2551, "angle": 1.47, "curve": "stepped"}, {"time": 0.3118, "angle": 1.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3685, "angle": -17.97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4252, "angle": -3.79, "curve": "stepped"}, {"time": 0.5953, "angle": -3.79, "curve": 0.331, "c2": 0.33, "c3": 0.673, "c4": 0.69}, {"time": 0.6236, "angle": 8.32, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.7087, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -5.01}]}, "qian_zj_50": {"rotate": [{"angle": -1.44, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0229, "angle": 20.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0567, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": 13.73, "curve": "stepped"}, {"time": 0.3118, "angle": 13.73, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3969, "angle": -17.97, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4252, "angle": -10.86, "curve": "stepped"}, {"time": 0.5953, "angle": -10.86, "curve": 0.313, "c2": 0.27, "c3": 0.66, "c4": 0.65}, {"time": 0.6236, "angle": 3.72, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.7087, "angle": 20.56, "curve": 0.25, "c3": 0.75}, {"time": 0.737, "angle": -1.44}]}, "qian_zj_51": {"rotate": [{"angle": 2.17, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.085, "angle": -21.51, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": 20.9, "curve": "stepped"}, {"time": 0.3118, "angle": 20.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4252, "angle": -17.97, "curve": "stepped"}, {"time": 0.5953, "angle": -17.97, "curve": 0.271, "c3": 0.619, "c4": 0.41}, {"time": 0.6236, "angle": -13.45, "curve": 0.342, "c2": 0.36, "c3": 0.757}, {"time": 0.737, "angle": 2.17}]}, "bone2": {"rotate": [{"curve": 0.479, "c3": 0.744, "c4": 0.48}, {"time": 0.1984, "angle": -19.12, "curve": "stepped"}, {"time": 0.2268, "angle": -19.12, "curve": 0.292, "c2": 0.38, "c3": 0.576, "c4": 0.78}, {"time": 0.2551, "angle": -1.08, "curve": 0.279, "c2": 0.62, "c3": 0.604}, {"time": 0.3402, "angle": -1.73, "curve": "stepped"}, {"time": 0.5953, "angle": -1.73, "curve": 0.311, "c2": 0.4, "c3": 0.643, "c4": 0.74}, {"time": 0.6236, "angle": -0.57, "curve": 0.299, "c2": 0.64, "c3": 0.63}, {"time": 0.7087}], "translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.0229, "x": 9.41, "y": -4.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1984, "x": -18.65, "y": -10.91, "curve": 0.759, "c2": 0.12, "c3": 0.929, "c4": 0.43}, {"time": 0.2268, "x": -8.85, "y": -11.01, "curve": 0.208, "c2": 0.69, "c3": 0.605, "c4": 0.87}, {"time": 0.3118, "x": -1.56, "y": -11.33, "curve": "stepped"}, {"time": 0.3685, "x": -1.56, "y": -11.33, "curve": 0.411, "c2": 0.17, "c3": 0.788, "c4": 0.37}, {"time": 0.5953, "x": 6.8, "y": -11.71, "curve": 0.21, "c2": 0.84, "c3": 0.799, "c4": 0.46}, {"time": 0.6236, "x": -42.86, "y": -7.07, "curve": 0.344, "c2": 0.37, "c3": 0.681, "c4": 0.71}, {"time": 0.7087, "x": -89.72, "y": -1.65}]}, "bone15": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.0229, "angle": 68.93, "curve": 0.325, "c2": 0.34, "c3": 0.657, "c4": 0.68}, {"time": 0.1984, "angle": -46.27, "curve": 0.991, "c3": 0, "c4": 1.09}, {"time": 0.2268, "angle": 86.7, "curve": 0.329, "c2": 0.34, "c3": 0.662, "c4": 0.67}, {"time": 0.2551, "angle": 81.25, "curve": 0.313, "c2": 0.36, "c3": 0.643, "c4": 0.7}, {"time": 0.3118, "angle": 75.07, "curve": 0.322, "c2": 0.35, "c3": 0.654, "c4": 0.68}, {"time": 0.3402, "angle": 72.55, "curve": 0.328, "c2": 0.34, "c3": 0.661, "c4": 0.67}, {"time": 0.4252, "angle": 69.52, "curve": "stepped"}, {"time": 0.5953, "angle": 69.52, "curve": 0.329, "c2": 0.34, "c3": 0.663, "c4": 0.67}, {"time": 0.6236, "angle": -45.25, "curve": 0.331, "c2": 0.34, "c3": 0.665, "c4": 0.67}, {"time": 0.652, "angle": -78.65, "curve": 0.313, "c2": 0.36, "c3": 0.643, "c4": 0.7}, {"time": 0.6803, "angle": -67.36}], "translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.0229, "x": -5.94, "y": 11.27, "curve": 0.326, "c2": 0.34, "c3": 0.655, "c4": 0.68}, {"time": 0.1984, "x": -3.7, "y": 19.68, "curve": 0.991, "c3": 0, "c4": 1.09}, {"time": 0.2268, "x": -0.54, "y": 10.87, "curve": "stepped"}, {"time": 0.652, "x": -0.54, "y": 10.87, "curve": 0.293, "c2": 0.4, "c3": 0.618, "c4": 0.75}, {"time": 0.6803, "x": 0.53, "y": 32.67, "curve": 0.285, "c2": 0.63, "c3": 0.612}, {"time": 0.7087, "x": -0.49, "y": 26.68}]}, "bone12": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1984, "angle": 15.66, "curve": 0.472, "c3": 0.48}, {"time": 0.2551, "angle": -49.69, "curve": "stepped"}, {"time": 0.5953, "angle": -49.69, "curve": 0.465, "c3": 0.747, "c4": 0.46}, {"time": 0.6236, "angle": -33.31, "curve": 0.26, "c2": 0.45, "c3": 0.475}, {"time": 0.7087, "angle": -1.91, "curve": 0.472, "c3": 0.48}, {"time": 0.737, "angle": 48.29}], "translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1984, "x": -5.06, "y": -4.67, "curve": 0.472, "c3": 0.48}, {"time": 0.2551, "x": -1.84, "y": 7.94, "curve": "stepped"}, {"time": 0.5953, "x": -1.84, "y": 7.94, "curve": 0.465, "c3": 0.747, "c4": 0.46}, {"time": 0.6236, "x": -3.37, "y": -4.8, "curve": 0.26, "c2": 0.45, "c3": 0.475}, {"time": 0.7087, "x": -6.3, "y": -29.23}]}, "H_jio1": {"translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.1984, "x": -44.04, "y": 16.04}]}, "H_jio2": {"translate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.0229, "x": 1.55, "y": 4.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1984, "x": -40.25, "y": 8.32, "curve": 0.897, "c3": 0.973, "c4": 0.05}, {"time": 0.2268, "x": 3.65, "y": 1.02, "curve": "stepped"}, {"time": 0.5953, "x": 3.65, "y": 1.02, "curve": 0.384, "c2": 0.01, "c3": 0.711, "c4": 0.38}, {"time": 0.6236, "x": -16.43, "y": 1.41, "curve": 0.395, "c2": 0.23, "c3": 0.71, "c4": 0.59}, {"time": 0.7087, "x": -129.4, "y": 3.51, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.7654, "x": -143.96, "y": 3.91}]}, "bone16": {"rotate": [{"time": 0.0229, "angle": 45.31, "curve": 0.355, "c2": 0.21, "c3": 0.688, "c4": 0.55}, {"time": 0.0283, "curve": 0.509, "c3": 0.618, "c4": 0.72}, {"time": 0.1984, "angle": -57.69, "curve": 0.991, "c3": 0, "c4": 1.09}, {"time": 0.2268, "angle": 43.35, "curve": 0.341, "c2": 0.31, "c3": 0.674, "c4": 0.64}, {"time": 0.2551, "angle": 28.16, "curve": "stepped"}, {"time": 0.5953, "angle": 28.16, "curve": 0.342, "c2": 0.31, "c3": 0.675, "c4": 0.65}, {"time": 0.6236, "angle": 139.87, "curve": 0.337, "c2": 0.33, "c3": 0.671, "c4": 0.66}, {"time": 0.652, "angle": 153.31, "curve": 0.361, "c2": 0.27, "c3": 0.692, "c4": 0.61}, {"time": 0.6803, "angle": 128.38}]}, "qian_zj_56": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.0229, "angle": 117.91, "curve": 0.305, "c2": 0.37, "c3": 0.631, "c4": 0.71}, {"time": 0.1984, "angle": 135.77, "curve": "stepped"}, {"time": 0.2268, "angle": 130.63, "curve": "stepped"}, {"time": 0.2551, "angle": 118.87, "curve": "stepped"}, {"time": 0.5953, "angle": 118.87, "curve": 0.263, "c2": 0.6, "c3": 0.58}, {"time": 0.6236, "angle": -108.87, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.7087, "angle": -25.69}], "scale": [{"time": 0.6236, "curve": 0.298, "c2": 0.64, "c3": 0.628}, {"time": 0.7087, "x": 0.205, "curve": 0.377, "c2": 0.27, "c3": 0.685, "c4": 0.64}, {"time": 0.7654}]}, "bone": {"translate": [{"x": 529.07, "y": 8.9, "curve": 0.472, "c3": 0.48}, {"time": 0.1984, "x": 360.64, "y": 7.4}]}, "bone13": {"rotate": [{"time": 0.1984, "curve": 0.472, "c3": 0.48}, {"time": 0.2551, "angle": 154.83, "curve": "stepped"}, {"time": 0.5953, "angle": 154.83, "curve": 0.465, "c3": 0.747, "c4": 0.46}, {"time": 0.6236, "angle": 126.71, "curve": 0.26, "c2": 0.45, "c3": 0.475}, {"time": 0.7087, "angle": 72.79, "curve": 0.472, "c3": 0.48}, {"time": 0.737, "angle": -10.97}]}, "bone17": {"rotate": [{"curve": 0.472, "c3": 0.48}, {"time": 0.0229, "angle": -24.82, "curve": 0.305, "c2": 0.37, "c3": 0.631, "c4": 0.71}, {"time": 0.1984, "angle": -40.86, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.2551, "angle": -2.09}]}, "qian_zj_18": {"rotate": [{"time": 0.0229, "angle": -16.98, "curve": "stepped"}, {"time": 0.3118, "angle": -16.98}, {"time": 0.4252, "angle": 2.71, "curve": "stepped"}, {"time": 0.6236, "angle": 2.71}, {"time": 0.7654, "angle": -0.16}]}, "L_jioall2": {"rotate": [{"angle": -10.22}]}, "qian_zj_15": {"rotate": [{"angle": -0.81}, {"time": 0.2551, "angle": 50.16, "curve": "stepped"}, {"time": 0.3118, "angle": 50.16}, {"time": 0.4252, "angle": 24.57, "curve": "stepped"}, {"time": 0.6236, "angle": 24.57}, {"time": 0.7654, "angle": -0.4}]}, "bone4": {"rotate": [{"curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.0229, "angle": -10.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1984, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": -20.21, "curve": 0.25, "c3": 0.75}, {"time": 0.2835, "angle": -15.47, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3118, "angle": -14.68, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4252}]}, "bone5": {"rotate": [{"time": 0.0229, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2551, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3118, "angle": 9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4252}]}, "bone7": {"rotate": [{"angle": -0.2, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0283}, {"time": 0.2268, "angle": 333.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3969, "angle": -44.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4536, "angle": -38.99, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.6236, "angle": -17.68, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6803, "angle": -2.07}], "translate": [{"curve": 0.511, "c3": 0.631, "c4": 0.69}, {"time": 0.2268, "x": -11.14, "y": 3.57, "curve": 0.305, "c2": 0.4, "c3": 0.635, "c4": 0.74}, {"time": 0.2551, "x": -7.95, "y": -0.17, "curve": 0.294, "c2": 0.64, "c3": 0.624}, {"time": 0.3402, "x": -6.78, "y": 2.03, "curve": 0.472, "c3": 0.48}, {"time": 0.4536, "x": -12.64, "y": 0.88, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "x": -6.06, "y": -2.46, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737}], "scale": [{"x": 1.151, "curve": 0.509, "c3": 0.705, "c4": 0.57}, {"time": 0.2268}]}, "bone8": {"rotate": [{"angle": -0.57, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.0283, "angle": -0.2, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0567, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": -26.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4536, "angle": -44.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6236, "angle": -28.89, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7087, "angle": -2.07}], "translate": [{"x": 6.57, "y": 0.26, "curve": 0.5, "c3": 0.569, "c4": 0.8}, {"time": 0.2551}]}, "bone9": {"rotate": [{"angle": -1.03, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0283, "angle": -0.59, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.085}, {"time": 0.2835, "angle": -26.26, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.4536, "angle": -42.73, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6236, "angle": -38.97, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.7087, "angle": -2.07}], "translate": [{"x": 8.43, "y": 0.58, "curve": 0.5, "c3": 0.569, "c4": 0.8}, {"time": 0.2551}]}, "bone18": {"rotate": [{"angle": -0.52, "curve": 0.5, "c3": 0.569, "c4": 0.8}, {"time": 0.2551, "angle": 13.7, "curve": "stepped"}, {"time": 0.3118, "angle": 13.7, "curve": 0.294, "c2": 0.64, "c3": 0.624}, {"time": 0.3402, "angle": -19.58, "curve": 0.294, "c2": 0.64, "c3": 0.624}, {"time": 0.4252, "angle": -16.51, "curve": "stepped"}, {"time": 0.5953, "angle": -16.51, "curve": 0.322, "c2": 0.38, "c3": 0.655, "c4": 0.72}, {"time": 0.6236, "angle": 58.75, "curve": 0.305, "c2": 0.65, "c3": 0.637}, {"time": 0.737, "angle": -15.48, "curve": 0.472, "c3": 0.48}, {"time": 0.7654, "angle": 76.35}], "translate": [{"curve": 0.509, "c3": 0.705, "c4": 0.57}, {"time": 0.1984, "x": -0.51, "y": 14.63, "curve": "stepped"}, {"time": 0.5953, "x": -0.51, "y": 14.63, "curve": 0.322, "c2": 0.38, "c3": 0.655, "c4": 0.72}, {"time": 0.6236, "x": -0.25, "y": 7.15, "curve": 0.305, "c2": 0.65, "c3": 0.637}, {"time": 0.737}]}, "bone19": {"rotate": [{"angle": 1.34, "curve": 0.5, "c3": 0.569, "c4": 0.8}, {"time": 0.2551, "angle": -67.06, "curve": "stepped"}, {"time": 0.3118, "angle": -67.06, "curve": 0.294, "c2": 0.64, "c3": 0.624}, {"time": 0.3402, "angle": 42.47, "curve": 0.472, "c3": 0.48}, {"time": 0.4252, "angle": -30.93, "curve": "stepped"}, {"time": 0.5953, "angle": -30.93, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": -104.69, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "angle": 31.63, "curve": 0.472, "c3": 0.48}, {"time": 0.7654, "angle": -74.64}]}, "bone20": {"rotate": [{"angle": -1.77, "curve": 0.5, "c3": 0.569, "c4": 0.8}, {"time": 0.2551, "angle": 54.14, "curve": "stepped"}, {"time": 0.3118, "angle": 54.14, "curve": 0.294, "c2": 0.64, "c3": 0.624}, {"time": 0.3402, "angle": -5.77, "curve": 0.472, "c3": 0.48}, {"time": 0.4252, "angle": 52.65, "curve": "stepped"}, {"time": 0.5953, "angle": 52.65, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": 45.57, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "angle": -17.09, "curve": 0.472, "c3": 0.48}, {"time": 0.7654, "angle": -2.65}]}, "bone21": {"rotate": [{"angle": -0.38, "curve": 0.5, "c3": 0.569, "c4": 0.8}, {"time": 0.2551, "angle": 53.06, "curve": "stepped"}, {"time": 0.3118, "angle": 53.06, "curve": 0.294, "c2": 0.64, "c3": 0.624}, {"time": 0.3402, "angle": 26.34, "curve": 0.472, "c3": 0.48}, {"time": 0.4252, "angle": 48.57, "curve": "stepped"}, {"time": 0.5953, "angle": 48.57, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": 32.41, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "angle": -0.94, "curve": 0.472, "c3": 0.48}, {"time": 0.7654, "angle": -23.47}], "translate": [{"curve": 0.509, "c3": 0.705, "c4": 0.57}, {"time": 0.1984, "x": -19.22, "y": 1.15, "curve": 0.276, "c2": 0.43, "c3": 0.589, "c4": 0.8}, {"time": 0.2551, "curve": "stepped"}, {"time": 0.6236, "curve": 0.367, "c2": 0.29, "c3": 0.666, "c4": 0.66}, {"time": 0.6803, "x": -4.36, "y": 6.49, "curve": 0.316, "c2": 0.35, "c3": 0.645, "c4": 0.69}, {"time": 0.7087, "x": -2.1, "y": 6.53, "curve": 0.25, "c2": 0.58, "c3": 0.555}, {"time": 0.737}]}, "bone22": {"rotate": [{"angle": 1.1, "curve": 0.5, "c3": 0.569, "c4": 0.8}, {"time": 0.2551, "angle": -76.13, "curve": "stepped"}, {"time": 0.3118, "angle": -76.13, "curve": 0.294, "c2": 0.64, "c3": 0.624}, {"time": 0.3402, "angle": -12.61, "curve": 0.472, "c3": 0.48}, {"time": 0.4252, "angle": -93.9, "curve": "stepped"}, {"time": 0.5953, "angle": -93.9, "curve": 0.43, "c3": 0.739, "c4": 0.42}, {"time": 0.6236, "angle": -22.02, "curve": 0.298, "c2": 0.36, "c3": 0.459}, {"time": 0.737, "angle": 4.09, "curve": 0.472, "c3": 0.48}, {"time": 0.7654, "angle": -36.47}]}, "bone24": {"rotate": [{"time": 0.1701, "curve": 0.25, "c3": 0.75}, {"time": 0.2268, "angle": 31.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4536, "angle": -19.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7654}]}, "bone25": {"rotate": [{"angle": -1.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0283, "curve": "stepped"}, {"time": 0.1984, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": 31.58, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.4536, "angle": -15.5, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.6236, "angle": -19.44, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.7654, "angle": -1.22}]}, "bone26": {"rotate": [{"angle": -1.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0283, "curve": "stepped"}, {"time": 0.1984, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": 31.58, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.4536, "angle": -15.5, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.6236, "angle": -19.44, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.7654, "angle": -1.22}]}, "bone27": {"rotate": [{"angle": -3.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0567, "curve": "stepped"}, {"time": 0.2268, "curve": 0.25, "c3": 0.75}, {"time": 0.2835, "angle": 31.58, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4536, "angle": -7.99, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.6236, "angle": -19.44, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7654, "angle": -3.59}]}, "bone28": {"rotate": [{"angle": -6.53, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.085, "curve": "stepped"}, {"time": 0.2551, "curve": 0.25, "c3": 0.75}, {"time": 0.3118, "angle": 31.58, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.4536, "angle": 1.25, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.652, "angle": -19.44, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7654, "angle": -6.53}]}, "bone29": {"rotate": [{"angle": -3.54, "curve": 0.352, "c2": 0.41, "c3": 0.688, "c4": 0.76}, {"time": 0.0283, "angle": -1.22, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0567, "curve": "stepped"}, {"time": 0.2268, "curve": 0.25, "c3": 0.75}, {"time": 0.2835, "angle": 31.58, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4536, "angle": -7.99, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.6236, "angle": -19.44, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.7654, "angle": -3.54}]}, "bone30": {"rotate": [{"angle": -6.49, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.0283, "angle": -3.59, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.085, "curve": "stepped"}, {"time": 0.2551, "curve": 0.25, "c3": 0.75}, {"time": 0.3118, "angle": 31.58, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.4536, "angle": 1.25, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.652, "angle": -19.44, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7654, "angle": -6.49}]}, "bone31": {"rotate": [{"angle": -9.7, "curve": 0.336, "c2": 0.34, "c3": 0.671, "c4": 0.68}, {"time": 0.0283, "angle": -6.53, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1134, "curve": "stepped"}, {"time": 0.2835, "curve": 0.25, "c3": 0.75}, {"time": 0.3402, "angle": 31.58, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.4536, "angle": 10.89, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.6803, "angle": -19.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7654, "angle": -9.7}]}, "bone33": {"rotate": [{"angle": 0.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0283, "curve": "stepped"}, {"time": 0.2268, "curve": 0.25, "c3": 0.75}, {"time": 0.3118, "angle": -36.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4819, "angle": -16.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6803, "angle": 4.23, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.7654, "angle": 0.55}]}, "bone34": {"rotate": [{"angle": 1.56, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0567, "curve": "stepped"}, {"time": 0.2551, "curve": 0.25, "c3": 0.75}, {"time": 0.3402, "angle": -36.37, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4819, "angle": -22.74, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.7087, "angle": 4.23, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7654, "angle": 1.56}]}, "bone35": {"rotate": [{"angle": 2.67, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.085, "curve": "stepped"}, {"time": 0.2835, "curve": 0.25, "c3": 0.75}, {"time": 0.3685, "angle": -36.37, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4819, "angle": -28.88, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7087, "angle": 4.23, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7654, "angle": 2.67}]}, "qian_zj_60": {"rotate": [{"angle": 7.13}]}, "qian_zj_59": {"rotate": [{"angle": -11.94}]}, "qian_zj_58": {"rotate": [{"angle": 10.85}]}, "qian_zj_57": {"rotate": [{"angle": 2.8}]}, "qian_zj_54": {"rotate": [{"angle": -0.46}]}, "qian_zj_21": {"rotate": [{"angle": -1.15}]}, "bone45": {"rotate": [{"time": 0.0229, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2551, "angle": -6.36, "curve": "stepped"}, {"time": 0.3118, "angle": -6.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3402, "angle": -12.73, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4252, "angle": -2.35, "curve": "stepped"}, {"time": 0.5953, "angle": -2.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6236}]}, "bone46": {"rotate": [{"time": 0.0229, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2551, "angle": -6.36, "curve": "stepped"}, {"time": 0.3118, "angle": -6.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3402, "angle": -12.73, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4252, "angle": -2.35, "curve": "stepped"}, {"time": 0.5953, "angle": -2.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6236}]}, "bone47": {"rotate": [{"time": 0.0229, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2551, "angle": -6.36, "curve": "stepped"}, {"time": 0.3118, "angle": -6.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3402, "angle": -12.73, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4252, "angle": -2.35, "curve": "stepped"}, {"time": 0.5953, "angle": -2.35, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6236}]}, "qian_zj_66": {"rotate": [{"angle": 2.01}, {"time": 0.0567, "angle": -12.12, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.2551, "angle": 21.71, "curve": "stepped"}, {"time": 0.3118, "angle": 21.71, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.3969, "angle": 38.81, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4252, "angle": 34.02, "curve": "stepped"}, {"time": 0.5953, "angle": 34.02, "curve": 0.309, "c2": 0.25, "c3": 0.651, "c4": 0.61}, {"time": 0.6236, "angle": 24.05, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.737, "angle": 2.01}]}, "qian_zj_70": {"rotate": [{"angle": 4.58, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.0567, "angle": -12.12, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.2551, "angle": 21.71, "curve": "stepped"}, {"time": 0.3118, "angle": 21.71, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.3969, "angle": 38.81, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4252, "angle": 34.36, "curve": "stepped"}, {"time": 0.5953, "angle": 34.36, "curve": 0.309, "c2": 0.25, "c3": 0.651, "c4": 0.61}, {"time": 0.6236, "angle": 25.08, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.737, "angle": 4.58}]}, "qian_zj_67": {"rotate": [{"angle": 0.66, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1984, "angle": -12.12, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.2551, "angle": 4.98, "curve": "stepped"}, {"time": 0.3118, "angle": 4.98, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.4252, "angle": 35.72, "curve": "stepped"}, {"time": 0.5953, "angle": 35.72, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6236, "angle": 38.81, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.737, "angle": 0.66}]}, "qian_zj_71": {"rotate": [{"angle": 0.7, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.1984, "angle": -12.12, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.2551, "angle": 4.98, "curve": "stepped"}, {"time": 0.3118, "angle": 4.98, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.4252, "angle": 35.72, "curve": "stepped"}, {"time": 0.5953, "angle": 35.72, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.6236, "angle": 38.81, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.737, "angle": 0.7}]}, "qian_zj_68": {"rotate": [{"angle": 4.59, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.0229, "angle": -12.12, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.2551, "angle": -8.92, "curve": "stepped"}, {"time": 0.3118, "angle": -8.92, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.4252, "angle": 21.49, "curve": "stepped"}, {"time": 0.5953, "angle": 21.49, "curve": 0.346, "c2": 0.38, "c3": 0.683, "c4": 0.72}, {"time": 0.6236, "angle": 30.85, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.7087, "angle": 38.81, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.737, "angle": 4.59}]}, "qian_zj_72": {"rotate": [{"angle": 1.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0229, "angle": -12.12, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 0.2551, "angle": -8.92, "curve": "stepped"}, {"time": 0.3118, "angle": -8.92, "curve": 0.285, "c2": 0.17, "c3": 0.657, "c4": 0.63}, {"time": 0.4252, "angle": 21.49, "curve": "stepped"}, {"time": 0.5953, "angle": 21.49, "curve": 0.346, "c2": 0.38, "c3": 0.683, "c4": 0.72}, {"time": 0.6236, "angle": 30.85, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.7087, "angle": 38.81, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.737, "angle": 1.31}]}, "qian_zj_69": {"rotate": [{"angle": 8.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0567, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.2551, "angle": -10.95, "curve": "stepped"}, {"time": 0.3118, "angle": -10.95, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.3402, "angle": -12.12, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.4252, "angle": -3.66, "curve": "stepped"}, {"time": 0.5953, "angle": -3.66, "curve": 0.332, "c2": 0.33, "c3": 0.669, "c4": 0.67}, {"time": 0.6236, "angle": 0.83, "curve": 0.381, "c2": 0.54, "c3": 0.743}, {"time": 0.737, "angle": 8.73}]}, "bone43": {"rotate": [{"angle": -0.2, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0283}, {"time": 0.2268, "angle": -26.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3969, "angle": -32.37, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.4536, "angle": -28.45, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.6236, "angle": -13.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6803, "angle": -2.07}], "translate": [{"x": 4.25, "y": 0.16}, {"time": 0.2551}]}, "bone44": {"rotate": [{"angle": -0.59, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0567, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": -26.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4536, "angle": -31.56, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6236, "angle": -20.71, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.7087, "angle": -2.07}], "translate": [{"x": 6.66, "y": 0.55}, {"time": 0.2551}]}, "qian_zj_64": {"rotate": [{"angle": -1.55, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0283}, {"time": 0.1701, "angle": -30.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3118, "angle": 27.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4252, "angle": 5.62, "curve": "stepped"}, {"time": 0.5953, "angle": 5.62, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.6236, "angle": -8.04, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.6803, "angle": -16.12, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.7654, "angle": -1.55}]}, "qian_zj_65": {"rotate": [{"angle": -4.57, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0567, "curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": -30.61, "curve": 0.25, "c3": 0.75}, {"time": 0.3402, "angle": 27.66, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4252, "angle": 12.9, "curve": "stepped"}, {"time": 0.5953, "angle": 12.9, "curve": 0.329, "c2": 0.32, "c3": 0.671, "c4": 0.68}, {"time": 0.6236, "angle": -1.42, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.7087, "angle": -16.12, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7654, "angle": -4.57}]}, "qian_zj_63": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1417, "angle": -30.61, "curve": 0.25, "c3": 0.75}, {"time": 0.2835, "angle": 27.66, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.4252, "angle": -1.62, "curve": "stepped"}, {"time": 0.5953, "angle": -1.62, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.6236, "angle": -13.37, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.652, "angle": -16.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7654}]}, "bone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": -26.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3685, "angle": -28.22, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4536, "angle": -18.48, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.6236, "angle": -5.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.652, "angle": -2.07}], "translate": [{"time": 0.2551}, {"time": 0.3402, "x": -7.58, "y": -7.26}], "scale": [{"x": 1.151}, {"time": 0.2551}]}, "qian_zj_20": {"rotate": [{"time": 0.2551, "angle": 26.15, "curve": "stepped"}, {"time": 0.3118, "angle": 26.15}, {"time": 0.4252, "angle": 18.48, "curve": "stepped"}, {"time": 0.6236, "angle": 18.48}, {"time": 0.7654, "angle": 10.86}]}, "bone6": {"rotate": [{"angle": -3.75, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0283, "angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0567}, {"time": 0.1984, "angle": -50.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2551, "angle": 8.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3402, "angle": 8.64, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4536, "angle": -2.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5953, "angle": -53.14, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6803, "angle": -3.75}], "translate": [{"time": 0.2551}, {"time": 0.3402, "x": -6.87, "y": 4.62, "curve": "stepped"}, {"time": 0.4536, "x": -6.87, "y": 4.62}, {"time": 0.6236, "x": -0.03, "y": 11.8}, {"time": 0.652, "x": -0.07, "y": 23.61}]}, "bone37": {"rotate": [{"angle": -6.48, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0283, "angle": -3.75, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0567, "angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.085}, {"time": 0.2268, "angle": -50.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2551, "angle": -26.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3685, "angle": 31.93, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4536, "angle": 16.39, "curve": 0.331, "c2": 0.33, "c3": 0.673, "c4": 0.69}, {"time": 0.5386, "angle": 3.11, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.652, "angle": -10.3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6803, "angle": -6.48}]}, "bone38": {"rotate": [{"angle": -8.98, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.0283, "angle": -6.5, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0567, "angle": -3.79, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1984}, {"time": 0.2551, "angle": -50.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3685, "angle": 31.93, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4536, "angle": 16.39, "curve": 0.331, "c2": 0.33, "c3": 0.673, "c4": 0.69}, {"time": 0.5386, "angle": 3.11, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.652, "angle": -10.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6803, "angle": -8.98}]}, "bone39": {"rotate": [{"angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0283}, {"time": 0.1984, "angle": -50.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": 31.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4536, "angle": -10.3, "curve": 0.279, "c3": 0.622, "c4": 0.39}, {"time": 0.5386, "angle": -51.47, "curve": 0.312, "c2": 0.26, "c3": 0.696, "c4": 0.76}, {"time": 0.6803, "angle": -1.34}], "translate": [{"x": 1.33, "y": 15.05}, {"time": 0.2551, "x": -9.15, "y": 28.56}, {"time": 0.4536, "x": 1.33, "y": 15.05}, {"time": 0.6236, "x": 1.45, "y": 19.4}, {"time": 0.652, "x": 1.56, "y": 23.76}]}, "bone40": {"rotate": [{"angle": -3.75, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0283, "angle": -1.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0567, "curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": -50.05, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.2551, "angle": 8.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3402, "angle": 31.93, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.4536, "angle": -2.51, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5386, "angle": -10.3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6803, "angle": -3.75}]}, "bone41": {"rotate": [{"angle": -6.5, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0283, "angle": -3.79, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.085, "curve": 0.25, "c3": 0.75}, {"time": 0.2268, "angle": -50.05, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2551, "angle": -26.78, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3685, "angle": 31.93, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4536, "angle": 16.39, "curve": 0.331, "c2": 0.33, "c3": 0.673, "c4": 0.69}, {"time": 0.5386, "angle": 3.11, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.652, "angle": -10.3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6803, "angle": -6.5}]}, "L_jio2": {"translate": [{"curve": 0.969, "c3": 0.022}, {"time": 0.085, "x": 48.61, "y": 34.74, "curve": 1, "c2": 0.11, "c3": 0.119, "c4": 0.97}, {"time": 0.1417, "x": 97.16, "y": 6.57, "curve": 0.292, "c2": 0.4, "c3": 0.616, "c4": 0.75}, {"time": 0.1984, "x": 120.32, "y": 2.46, "curve": 0.308, "c2": 0.42, "c3": 0.64, "c4": 0.76}, {"time": 0.2268, "x": 131.72, "y": -0.54, "curve": 0.333, "c2": 0.34, "c3": 0.666, "c4": 0.67}, {"time": 0.2551, "x": 121.73, "y": -0.74, "curve": 0.32, "c2": 0.66, "c3": 0.653}, {"time": 0.2835, "x": 109.35, "y": 5.29, "curve": 0.307, "c2": 0.65, "c3": 0.638}, {"time": 0.3118, "x": 81.13, "y": 7.45, "curve": 0.472, "c3": 0.48}, {"time": 0.3685, "x": -27.45, "y": 4.54, "curve": 0.472, "c3": 0.48}, {"time": 0.4252, "x": 39.83, "y": -4.43, "curve": "stepped"}, {"time": 0.6236, "x": 39.83, "y": -4.43, "curve": 0.472, "c3": 0.48}, {"time": 0.7087}]}, "L_jio1": {"translate": [{"time": 0.1701}, {"time": 0.1984, "x": 54.5, "y": -6.31}, {"time": 0.2551}]}, "qian_zj_16": {"rotate": [{"time": 0.0229, "angle": -9.38}, {"time": 0.2551, "angle": -7.51, "curve": "stepped"}, {"time": 0.3118, "angle": -7.51}, {"time": 0.4252, "angle": -11.71, "curve": "stepped"}, {"time": 0.6236, "angle": -11.71}, {"time": 0.7654, "angle": -0.32}]}, "qian_zj_19": {"rotate": [{"time": 0.0229, "angle": -4.29, "curve": "stepped"}, {"time": 0.3118, "angle": -4.29}, {"time": 0.4252, "angle": -19.08, "curve": "stepped"}, {"time": 0.6236, "angle": -19.08}, {"time": 0.7654, "angle": -0.26}]}, "qian_zj_73": {"rotate": [{"time": 0.2268, "angle": -158.68, "curve": "stepped"}, {"time": 0.6236, "angle": -158.68}, {"time": 0.652, "angle": -142.41}, {"time": 0.6803, "angle": -144.73}, {"time": 0.7654, "angle": -158.68}]}, "bone36": {"rotate": [{"time": 0.1984, "angle": 169.2}, {"time": 0.2268, "angle": 124.15}, {"time": 0.2551, "angle": 142.39}], "translate": [{"time": 0.1984, "x": -4.99, "y": 20}, {"time": 0.2268, "x": -9.16, "y": 7.45}, {"time": 0.2604, "x": -11.53, "y": 11.09}, {"time": 0.3969, "x": -13.41, "y": 8.59}, {"time": 0.4252, "x": -12.62, "y": 7.22, "curve": "stepped"}, {"time": 0.5953, "x": -12.62, "y": 7.22}, {"time": 0.6236, "x": -11.03, "y": 4.49}, {"time": 0.6803, "x": -12.41, "y": 1.3}, {"time": 0.7087, "x": -2.27, "y": -10.17}, {"time": 0.737, "x": 1.91, "y": -11.87}], "scale": [{"time": 0.1984, "x": -1}, {"time": 0.2268, "x": -1, "y": 0.864}]}, "qian_zj_75": {"rotate": [{"angle": 2.13}]}, "qian_zj_78": {"rotate": [{"angle": 1.52, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.0567}, {"time": 0.1701, "angle": 10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3118, "angle": -11.3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4252, "angle": 3.84, "curve": "stepped"}, {"time": 0.5953, "angle": 3.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6236, "angle": 9.83}]}, "qian_zj_77": {"rotate": [{"angle": 0.51, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0283, "curve": 0.25, "c3": 0.75}, {"time": 0.1417, "angle": 10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2835, "angle": -11.3, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.4252, "angle": 7.8, "curve": "stepped"}, {"time": 0.5953, "angle": 7.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6236, "angle": 9.83}]}, "qian_zj_76": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1134, "angle": 10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2551, "angle": -11.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4252, "angle": 9.83}]}, "qian_zj_79": {"rotate": [{"time": 0.0283, "angle": 2.13}], "translate": [{"time": 0.2268, "x": -2.56, "y": 3.97}]}, "qian_zj_82": {"rotate": [{"angle": 2.77, "curve": 0.344, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.0283, "angle": 1.52, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.085, "curve": 0.25, "c3": 0.75}, {"time": 0.1984, "angle": 10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3402, "angle": -11.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4252, "angle": -0.61, "curve": "stepped"}, {"time": 0.5953, "angle": -0.61, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.6236, "angle": 7.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.652, "angle": 9.83}]}, "qian_zj_81": {"rotate": [{"angle": 1.49, "curve": 0.352, "c2": 0.42, "c3": 0.687, "c4": 0.76}, {"time": 0.0283, "angle": 0.51, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0567}, {"time": 0.1701, "angle": 10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3118, "angle": -11.3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4252, "angle": 3.84, "curve": "stepped"}, {"time": 0.5953, "angle": 3.84, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6236, "angle": 9.83}]}, "qian_zj_80": {"rotate": [{"angle": 0.51, "curve": 0.362, "c2": 0.64, "c3": 0.698}, {"time": 0.0283}, {"time": 0.1417, "angle": 10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2835, "angle": -11.3, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.4252, "angle": 7.8, "curve": "stepped"}, {"time": 0.5953, "angle": 7.8, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.6236, "angle": 9.83}]}, "qian_zj_74": {"translate": [{"x": -3.46, "y": -7.85, "curve": "stepped"}, {"time": 0.2268, "x": -3.99, "y": 2.02, "curve": "stepped"}, {"time": 0.6236, "x": -3.99, "y": 2.02, "curve": "stepped"}, {"time": 0.7087}]}}, "ik": {"H_jio1": [{"time": 0.2268, "bendPositive": false}], "H_jio2": [{"time": 0.2268, "bendPositive": false}]}, "deform": {"default": {"L_jioall1": {"Q_zj_4": [{}, {"time": 0.085, "offset": 36, "vertices": [4.61658, 0.19868, 4.09048, 1.78536, 5.1982, 11.69373, -0.29073, 12.32989, 2.31406, 9.86804, -2.11981, 9.68361, -0.30969, 1.70579, -1.0083, 1.42692, -1.04556, 0.63708, -1.2176, 0.20932, -1.17267, 2.25939, 0.56282, 2.89161, -0.72541, 2.79745, 0.14477, 1.3287, -0.43629, 1.24581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.80964, -0.03127], "curve": "stepped"}, {"time": 0.3969, "offset": 36, "vertices": [4.61658, 0.19868, 4.09048, 1.78536, 5.1982, 11.69373, -0.29073, 12.32989, 2.31406, 9.86804, -2.11981, 9.68361, -0.30969, 1.70579, -1.0083, 1.42692, -1.04556, 0.63708, -1.2176, 0.20932, -1.17267, 2.25939, 0.56282, 2.89161, -0.72541, 2.79745, 0.14477, 1.3287, -0.43629, 1.24581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.80964, -0.03127]}, {"time": 0.4252}]}, "H_zj_007": {"H_zj_007": [{"time": 0.2268, "offset": 30, "vertices": [1.18429, -1.35783, 1.6978, -0.60315, 2.92144, -3.53409, 4.27861, -1.64881, 3.13017, -3.52052, 4.45384, -1.53476, 0, 0, -6.47031, 1.66747, -9.08325, 2.51373, -9.08325, 2.51373, -6.47031, 1.66747, -3.13844, 1.90785, -3.67063, 0.12516, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.14194, 1.08514, -6.84331, 1.82773]}]}, "H_zj_0010_2": {"H_zj_0010_2": [{"vertices": [-2.19665, 9.78828, -1.02012, 12.17934, -3.37537, 13.11946, -4.55189, 10.72839]}]}, "H_zj_003_1": {"H_zj_003_1": [{"time": 0.2268, "vertices": [0.31541, 4.75296, 5.42188, -3.15112, -4.20354, -6.48098, -3.43349, 0.5834]}, {"time": 0.2551, "vertices": [1.96243, 9.63608, 10.67833, 0.92268, -1.79443, -10.6828, -7.62684, 5.70789]}]}, "H_zj_003": {"H_zj_003": [{}, {"time": 0.2268, "offset": 46, "vertices": [-9.69027, -3.66, -3.58203, -9.71942, -7.72182, -0.02394, -5.03965, -5.85074, -6.94017, -4.21371, 0, 0, 0, 0, -0.72289, -0.99226, 0.27624, -1.19615, -0.71938, -2.11426, 1.12626, -1.92824]}, {"time": 0.2551, "offset": 46, "vertices": [-3.3075, -6.08151, -1.15435, -6.82571, -6.59715, -2.55603, -5.41026, -4.55905]}, {"time": 0.6803}]}, "H_zj_006": {"H_zj_006": [{"time": 0.2268}, {"time": 0.2414, "offset": 36, "vertices": [-3.2551, 11.72797, 3.69522, 11.59669, -2.18854, 13.669, 5.6503, 12.63733, 5.26336, 10.16235], "curve": "stepped"}, {"time": 0.3118, "offset": 36, "vertices": [-3.2551, 11.72797, 3.69522, 11.59669, -2.18854, 13.669, 5.6503, 12.63733, 5.26336, 10.16235]}, {"time": 0.4252, "offset": 32, "vertices": [0.60308, 0.3517, 0.67208, 0.18887, -3.64554, 11.07411, 3.1529, 11.06196, -1.77211, 12.76806, 5.82691, 11.66059, 5.26336, 10.16235, 1.33763, 5.62291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.01823, 0.59188, 0.16632, 0.56824, 0.31863, 0.73064, 0.49198, 0.62711], "curve": "stepped"}, {"time": 0.652, "offset": 32, "vertices": [0.60308, 0.3517, 0.67208, 0.18887, -3.64554, 11.07411, 3.1529, 11.06196, -1.77211, 12.76806, 5.82691, 11.66059, 5.26336, 10.16235, 1.33763, 5.62291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.01823, 0.59188, 0.16632, 0.56824, 0.31863, 0.73064, 0.49198, 0.62711]}, {"time": 0.6803}]}, "C_zj_001": {"C_zj_001": [{"vertices": [1.22928, -1.91909, 1.22928, -1.91909, 1.22928, -1.91909, 1.22928, -1.91909]}]}, "C_zj_1": {"C_zj_001": [{"vertices": [-2.87637, 3.64054, -2.87637, 3.64054, -2.87637, 3.64054, -2.87637, 3.64054]}]}, "fapian": {"fapian": [{"vertices": [10.56794, 12.38039, 10.56794, 12.38039, 10.56794, 12.38039, 10.56794, 12.38039]}]}, "Q_zj_2": {"Q_zj_002": [{}, {"time": 0.1701, "offset": 30, "vertices": [0.58294, -0.0965, -0.31118, -0.50232, 4.81494, -4.10679, -5.6307, -2.88885, -9.54511, -5.27283, -11.60728, -2.88685, -14.00096, -3.06302, -11.17561, -2.17309, -5.16388, -1.87137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.72916, 0.6203, 0.29596, -0.91048, 1.19762, -2.03041, -2.33347, -0.33446, -12.41011, -2.36022], "curve": "stepped"}, {"time": 0.6236, "offset": 30, "vertices": [0.58294, -0.0965, -0.31118, -0.50232, 4.81494, -4.10679, -5.6307, -2.88885, -9.54511, -5.27283, -11.60728, -2.88685, -14.00096, -3.06302, -11.17561, -2.17309, -5.16388, -1.87137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.72916, 0.6203, 0.29596, -0.91048, 1.19762, -2.03041, -2.33347, -0.33446, -12.41011, -2.36022]}, {"time": 0.737}]}}}, "drawOrder": [{"time": 0.085, "offsets": [{"slot": "Q_zj_0013", "offset": 10}, {"slot": "Q_zj_0014", "offset": 7}]}, {"time": 0.1134, "offsets": [{"slot": "Q_zj_0013", "offset": 11}, {"slot": "Q_zj_0014", "offset": 8}]}, {"time": 0.1984, "offsets": [{"slot": "Q_zj_0013", "offset": 9}, {"slot": "Q_zj_0014", "offset": 6}, {"slot": "Q_zj_0015", "offset": 0}]}, {"time": 0.2268, "offsets": [{"slot": "Q_zj_13", "offset": 15}, {"slot": "H_zj_003", "offset": 7}, {"slot": "H_zj_009", "offset": -10}]}, {"time": 0.3402, "offsets": [{"slot": "Q_zj_13", "offset": 10}, {"slot": "H_zj_002_1", "offset": 2}, {"slot": "H_zj_009", "offset": -8}]}, {"time": 0.3969, "offsets": [{"slot": "Q_zj_13", "offset": 22}, {"slot": "H_zj_003", "offset": 9}, {"slot": "H_zj_009", "offset": -10}]}, {"time": 0.6803, "offsets": [{"slot": "Q_zj_13", "offset": 23}, {"slot": "H_zj_003", "offset": 10}, {"slot": "H_zj_009", "offset": -9}, {"slot": "H_zj_0010", "offset": 2}, {"slot": "H_zj_0012", "offset": -45}]}, {"time": 0.7087, "offsets": [{"slot": "Q_zj_13", "offset": 23}, {"slot": "H_zj_003", "offset": 15}, {"slot": "H_zj_009", "offset": -8}, {"slot": "H_zj_0012_1", "offset": -16}, {"slot": "H_zj_0010_2", "offset": 4}, {"slot": "H_zj_0012", "offset": -45}]}], "events": [{"time": 0.2268, "name": "hit"}]}, "die": {"slots": {"Q_zj_0013": {"attachment": [{"time": 0.2, "name": null}]}, "Q_zj_0011": {"attachment": [{"name": "Q_zj_0011"}]}}, "bones": {"qian_zj_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 3.02, "curve": "stepped"}, {"time": 0.4, "angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -92.87}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 6.42, "y": -45.49}, {"time": 0.2667, "x": 6.01, "y": -37.52}, {"time": 0.4, "x": 6.42, "y": -45.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 34.36, "y": -83.79}]}, "qian_zj_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 2.1, "curve": "stepped"}, {"time": 0.3667, "angle": 2.1}, {"time": 0.4333, "angle": 3.71}, {"time": 0.5, "angle": 2.1}]}, "qian_zj_3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 3.37, "curve": "stepped"}, {"time": 0.3, "angle": 3.37}, {"time": 0.4, "angle": -19.53}], "translate": [{"x": 0.69, "y": -0.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667}]}, "qian_zj_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -0.19, "curve": "stepped"}, {"time": 0.2, "angle": -0.19}, {"time": 0.3667, "angle": 39.67}, {"time": 0.5, "angle": 44.84}, {"time": 0.6, "angle": 25.7}], "translate": [{"x": 0.54, "y": -0.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2667}, {"time": 0.4, "x": -3.46, "y": 2.56}]}, "qian_zj_5": {"translate": [{"x": 0.65, "y": -2.16, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4333}, {"time": 0.5, "x": 4.41, "y": -1.97}]}, "qian_zj_52": {"translate": [{"x": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "qian_zj_6": {"translate": [{"x": 0.96, "y": 0.54, "curve": 0.253, "c2": 0.58, "c3": 0.562}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4333}, {"time": 0.5, "x": -19.53, "y": -10.03}]}, "qian_zj_7": {"rotate": [{"angle": -7.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2667}, {"time": 0.6667, "angle": 46.89}], "translate": [{"x": 1.92, "y": -0.09}, {"time": 0.0667}]}, "qian_zj_8": {"rotate": [{"angle": -7.05, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.0667}]}, "qian_zj_9": {"rotate": [{"angle": -19.95}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2667}, {"time": 0.3667, "angle": -12.79}]}, "qian_zj_10": {"rotate": [{"time": 0.2667}, {"time": 0.3667, "angle": -104.17}]}, "qian_zj_11": {"rotate": [{"angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667}]}, "qian_zj_12": {"rotate": [{"angle": 1.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667}]}, "qian_zj_13": {"rotate": [{"angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "qian_zj_23": {"rotate": [{"angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "qian_zj_24": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.0667}]}, "qian_zj_25": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.0667}]}, "qian_zj_26": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0667}]}, "qian_zj_27": {"rotate": [{"angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "qian_zj_28": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.0667}]}, "qian_zj_29": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.0667}]}, "qian_zj_30": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.0667}]}, "qian_zj_35": {"rotate": [{"angle": -7.4, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2333, "angle": -8.57, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -13.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.48}]}, "qian_zj_36": {"rotate": [{"angle": -12.74, "curve": 0.304, "c2": 0.22, "c3": 0.644, "c4": 0.58}, {"time": 0.2667, "angle": -7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 4.82}]}, "qian_zj_37": {"rotate": [{"angle": -11.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": -7.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -12.22, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 4.82}]}, "qian_zj_38": {"rotate": [{"angle": -6.23, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.3667, "angle": -5.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 6.65, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -10.39, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 6.65}]}, "qian_zj_39": {"rotate": [{"angle": 2.43, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667}, {"time": 0.2, "angle": 13.01}, {"time": 0.4333, "angle": -8.31}, {"time": 0.6667, "angle": 47.99}]}, "qian_zj_40": {"rotate": [{"angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0333}, {"time": 0.1667, "angle": 13.01}, {"time": 0.4, "angle": -8.31}, {"time": 0.6333, "angle": 6.23}]}, "qian_zj_41": {"rotate": [{"angle": -4.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 13.01}, {"time": 0.3667, "angle": -8.31}, {"time": 0.6, "angle": 6.23}]}, "qian_zj_42": {"rotate": [{"angle": -5.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667}, {"time": 0.2, "angle": 13.01}, {"time": 0.4333, "angle": -8.31}, {"time": 0.6667, "angle": 6.23}]}, "qian_zj_43": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667}, {"time": 0.2, "angle": 13.01}, {"time": 0.4333, "angle": -8.31}, {"time": 0.6667, "angle": 25.98}]}, "qian_zj_44": {"rotate": [{"angle": -3.71, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.0333}, {"time": 0.1667, "angle": 13.01}, {"time": 0.4, "angle": -8.31}, {"time": 0.6333, "angle": 6.23}]}, "qian_zj_45": {"rotate": [{"angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 13.01}, {"time": 0.3667, "angle": -8.31}, {"time": 0.6, "angle": 6.23}]}, "qian_zj_46": {"rotate": [{"angle": -3.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667}, {"time": 0.2, "angle": 13.01}, {"time": 0.4333, "angle": -8.31}, {"time": 0.6667, "angle": 6.23}]}, "qian_zj_47": {"rotate": [{"angle": -1.41, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0667}, {"time": 0.2, "angle": 13.01}, {"time": 0.4333, "angle": -8.31}, {"time": 0.6667, "angle": 6.23}]}, "qian_zj_48": {"rotate": [{"angle": -5.28, "curve": 0.307, "c2": 0.23, "c3": 0.645, "c4": 0.58}, {"time": 0.0333}, {"time": 0.1667, "angle": 13.01}, {"time": 0.4, "angle": -8.31}, {"time": 0.6333, "angle": 6.23}]}, "qian_zj_49": {"rotate": [{"angle": -5.01, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333, "angle": 13.01}, {"time": 0.3667, "angle": -8.31}, {"time": 0.6, "angle": 6.23}]}, "qian_zj_50": {"rotate": [{"angle": -1.44, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0667}, {"time": 0.2, "angle": 13.01}, {"time": 0.4333, "angle": -8.31}, {"time": 0.6667, "angle": 6.23}]}, "qian_zj_51": {"rotate": [{"angle": 2.17, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.0667}, {"time": 0.2, "angle": 13.01}, {"time": 0.4333, "angle": -8.31}, {"time": 0.6667, "angle": 6.23}]}, "qian_zj_57": {"rotate": [{"angle": 2.8}, {"time": 0.0667, "angle": 2.18}, {"time": 0.2, "angle": -82.26}]}, "R_jio2": {"translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.45, "y": 0.03}]}, "qian_zj_66": {"rotate": [{"angle": 2.01}, {"time": 0.2333, "angle": 15.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 19.96}]}, "qian_zj_70": {"rotate": [{"angle": 4.58, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2333, "angle": 15.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 19.96}]}, "qian_zj_71": {"rotate": [{"angle": 0.7, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.84, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8, "angle": 18.69}]}, "qian_zj_67": {"rotate": [{"angle": 0.66, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667}, {"time": 0.3, "angle": 15.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.84, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.8, "angle": 18.69}]}, "qian_zj_72": {"rotate": [{"angle": 1.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333}, {"time": 0.3667, "angle": 15.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -6.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8, "angle": 16.53}]}, "qian_zj_68": {"rotate": [{"angle": 4.59, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 15.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -6.84, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8, "angle": 16.53}]}, "qian_zj_69": {"rotate": [{"angle": 8.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 15.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -6.84, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.8, "angle": 14.38}]}, "qian_zj_78": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0667}]}, "qian_zj_77": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.0667}]}, "qian_zj_76": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.0667}]}, "qian_zj_75": {"rotate": [{"angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "qian_zj_82": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.0667}]}, "qian_zj_81": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.0667}]}, "qian_zj_80": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.0667}]}, "qian_zj_79": {"rotate": [{"angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "qian_zj_63": {"rotate": [{"angle": -2.54}]}, "qian_zj_64": {"rotate": [{"angle": -6.4}]}, "qian_zj_65": {"rotate": [{"angle": -8.93}]}}}, "hurt": {"slots": {"Q_zj_0011": {"attachment": [{"time": 0.1333, "name": "Q_zj_0011"}]}}, "bones": {"qian_zj_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -16.29, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "qian_zj_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 17.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "qian_zj_3": {"rotate": [{"time": 0.0333, "curve": 0.679, "c3": 0.75}, {"time": 0.1667, "angle": 15.57, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 0.5}], "translate": [{"x": 0.69, "y": -0.24}]}, "qian_zj_4": {"rotate": [{"time": 0.0667, "curve": 0.661, "c2": 0.02, "c3": 0.75}, {"time": 0.2, "angle": 29.28, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.5}], "translate": [{"x": 0.54, "y": -0.3}]}, "qian_zj_5": {"translate": [{"x": 0.65, "y": -2.16}]}, "qian_zj_52": {"translate": [{"x": -0.13}]}, "qian_zj_6": {"translate": [{"x": 0.96, "y": 0.54}]}, "qian_zj_7": {"rotate": [{"angle": -7.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1667, "angle": -44.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.54}], "translate": [{"x": 1.92, "y": -0.09}]}, "qian_zj_8": {"rotate": [{"angle": -7.05, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.1667, "angle": 26.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.05}]}, "qian_zj_9": {"rotate": [{"angle": -19.95}, {"time": 0.1667, "angle": -28.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -19.95}]}, "qian_zj_11": {"rotate": [{"angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -7.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.87}]}, "qian_zj_12": {"rotate": [{"angle": 1.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 6.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -1.19, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 1.04}]}, "qian_zj_13": {"rotate": [{"angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -7.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.95}]}, "qian_zj_23": {"rotate": [{"angle": 2.13}]}, "qian_zj_24": {"rotate": [{"angle": -0.27}]}, "qian_zj_25": {"rotate": [{"angle": -4.31}]}, "qian_zj_26": {"rotate": [{"angle": -8.16}]}, "qian_zj_27": {"rotate": [{"angle": 2.13}]}, "qian_zj_28": {"rotate": [{"angle": -2.7}]}, "qian_zj_29": {"rotate": [{"angle": -6.85}]}, "qian_zj_30": {"rotate": [{"angle": -9.64}]}, "qian_zj_33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -18.74, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.5}]}, "qian_zj_35": {"rotate": [{"angle": -7.4, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.1333, "angle": -22.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.4}]}, "qian_zj_36": {"rotate": [{"angle": -12.74, "curve": 0.304, "c2": 0.22, "c3": 0.644, "c4": 0.58}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -22.77, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 0.5, "angle": -12.74}]}, "qian_zj_37": {"rotate": [{"angle": -11.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -22.77, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.5, "angle": -11.86}]}, "qian_zj_38": {"rotate": [{"angle": -6.23, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -22.77, "curve": 0.242, "c3": 0.661, "c4": 0.65}, {"time": 0.5, "angle": -6.23}]}, "qian_zj_39": {"rotate": [{"angle": 2.43, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -19.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.43}]}, "qian_zj_40": {"rotate": [{"angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667}, {"time": 0.2, "angle": -19.78, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.5, "angle": -1.45}]}, "qian_zj_41": {"rotate": [{"angle": -4.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -19.78, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.5, "angle": -4.71}]}, "qian_zj_42": {"rotate": [{"angle": -5.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.78, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "angle": -5.28}]}, "qian_zj_43": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": -19.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.49}]}, "qian_zj_44": {"rotate": [{"angle": -3.71, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -19.78, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.5, "angle": -3.71}]}, "qian_zj_45": {"rotate": [{"angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1333}, {"time": 0.2667, "angle": -19.78, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.5, "angle": -5.86}]}, "qian_zj_46": {"rotate": [{"angle": -3.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2}, {"time": 0.3333, "angle": -19.78, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "angle": -3.34}]}, "qian_zj_47": {"rotate": [{"angle": -1.41, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1333, "angle": -19.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.41}]}, "qian_zj_48": {"rotate": [{"angle": -5.28, "curve": 0.307, "c2": 0.23, "c3": 0.645, "c4": 0.58}, {"time": 0.0667}, {"time": 0.2, "angle": -19.78, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.5, "angle": -5.28}]}, "qian_zj_49": {"rotate": [{"angle": -5.01, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1333}, {"time": 0.2667, "angle": -19.78, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.5, "angle": -5.01}]}, "qian_zj_50": {"rotate": [{"angle": -1.44, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.2}, {"time": 0.3333, "angle": -19.78, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "angle": -1.44}]}, "qian_zj_51": {"rotate": [{"angle": 2.17, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -19.78, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "angle": 2.17}]}, "qian_zj_57": {"rotate": [{"angle": 2.8}]}, "qian_zj_all": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -12.43, "y": -0.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "qian_zj_63": {"rotate": [{"angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.74, "curve": 0.245, "c3": 0.714, "c4": 0.85}, {"time": 0.5, "angle": -2.54}]}, "qian_zj_64": {"rotate": [{"angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -18.74, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.5, "angle": -6.4}]}, "qian_zj_65": {"rotate": [{"angle": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -18.74, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.5, "angle": -8.93}]}, "qian_zj_66": {"rotate": [{"angle": 2.01}, {"time": 0.1, "angle": -18.36, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": 2.01}]}, "qian_zj_70": {"rotate": [{"angle": 4.58, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.1333, "angle": -18.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.58}]}, "qian_zj_71": {"rotate": [{"angle": 0.7, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.0667}, {"time": 0.2, "angle": -18.36, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.5, "angle": 0.7}]}, "qian_zj_67": {"rotate": [{"angle": 0.66, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.36, "curve": 0.243, "c3": 0.685, "c4": 0.73}, {"time": 0.4667, "angle": -2.03, "curve": 0.35, "c2": 0.42, "c3": 0.685, "c4": 0.76}, {"time": 0.5, "angle": 0.66}]}, "qian_zj_72": {"rotate": [{"angle": 1.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333}, {"time": 0.2667, "angle": -18.36, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.5, "angle": 1.31}]}, "qian_zj_68": {"rotate": [{"angle": 4.59, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -18.36, "curve": 0.244, "c3": 0.643, "c4": 0.58}, {"time": 0.4667, "angle": -5.9, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 0.5, "angle": 4.59}]}, "qian_zj_69": {"rotate": [{"angle": 8.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -18.36, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.4667, "angle": -10.28, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 8.73}]}, "qian_zj_78": {"rotate": [{"angle": -8.16}]}, "qian_zj_77": {"rotate": [{"angle": -4.31}]}, "qian_zj_76": {"rotate": [{"angle": -0.27}]}, "qian_zj_75": {"rotate": [{"angle": 2.13}]}, "qian_zj_82": {"rotate": [{"angle": -9.64}]}, "qian_zj_81": {"rotate": [{"angle": -6.85}]}, "qian_zj_80": {"rotate": [{"angle": -2.7}]}, "qian_zj_79": {"rotate": [{"angle": 2.13}]}}}, "idle": {"bones": {"qian_zj_31": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.07, "y": -1.9, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "qian_zj_23": {"rotate": [{"angle": 2.13}]}, "qian_zj_24": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -9.7, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -0.27}]}, "qian_zj_25": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.7, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -4.31}]}, "qian_zj_26": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -9.7, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -8.16}]}, "qian_zj_27": {"rotate": [{"angle": 2.13}]}, "qian_zj_28": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.1667, "angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.4333, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -9.7, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -2.7}]}, "qian_zj_29": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.1667, "angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.7, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -9.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -6.85}]}, "qian_zj_30": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.1667, "angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.9667, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -9.7, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": -9.64}]}, "qian_zj_35": {"rotate": [{"angle": -7.4, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2, "angle": -3.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5333, "curve": 0.472, "c3": 0.48}, {"time": 1.5333, "angle": -13.63, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -7.4}]}, "qian_zj_36": {"rotate": [{"angle": -12.74, "curve": 0.304, "c2": 0.22, "c3": 0.644, "c4": 0.58}, {"time": 0.2, "angle": -9.76, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.8667, "angle": -13.63, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -12.74}]}, "qian_zj_37": {"rotate": [{"angle": -11.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -13.63, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -11.86}]}, "qian_zj_38": {"rotate": [{"angle": -6.23, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.2, "angle": -9.76, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5333, "angle": -13.63, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -6.23}]}, "qian_zj_52": {"translate": [{"x": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 6.01, "y": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "qian_zj_11": {"rotate": [{"angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -3.78, "curve": 0.472, "c3": 0.48}, {"time": 1.3333, "angle": 2.95, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -1.87}]}, "qian_zj_12": {"rotate": [{"angle": 1.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "angle": -3.78, "curve": 0.472, "c3": 0.48}, {"time": 1.6667, "angle": 2.95, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 1.04}]}, "qian_zj_13": {"rotate": [{"angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.78, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 2.95}]}, "qian_zj_9": {"rotate": [{"angle": -19.95}]}, "qian_zj_39": {"rotate": [{"angle": 2.43, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": 3.01, "curve": 0.472, "c3": 0.48}, {"time": 1.1333, "angle": -5.86, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8, "angle": 0.49, "curve": 0.356, "c2": 0.42, "c3": 0.696, "c4": 0.78}, {"time": 2, "angle": 2.43}]}, "qian_zj_40": {"rotate": [{"angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 3.01, "curve": 0.472, "c3": 0.48}, {"time": 1.5, "angle": -5.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.8, "angle": -3.71, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 2, "angle": -1.45}]}, "qian_zj_41": {"rotate": [{"angle": -4.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": 3.01, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -5.86, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -4.71}]}, "qian_zj_42": {"rotate": [{"angle": -5.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 3.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.8, "angle": -3.34, "curve": 0.356, "c2": 0.42, "c3": 0.696, "c4": 0.78}, {"time": 2, "angle": -5.28}]}, "qian_zj_43": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": 3.01, "curve": 0.472, "c3": 0.48}, {"time": 1.3333, "angle": -5.86, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 0.49}]}, "qian_zj_44": {"rotate": [{"angle": -3.71, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.7, "angle": 3.01, "curve": 0.472, "c3": 0.48}, {"time": 1.7, "angle": -5.86, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -3.71}]}, "qian_zj_45": {"rotate": [{"angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.01, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -5.86}]}, "qian_zj_46": {"rotate": [{"angle": -3.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -3.34}]}, "qian_zj_47": {"rotate": [{"angle": -1.41, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "angle": 3.01, "curve": 0.472, "c3": 0.48}, {"time": 1.5, "angle": -5.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -1.41}]}, "qian_zj_48": {"rotate": [{"angle": -5.28, "curve": 0.307, "c2": 0.23, "c3": 0.645, "c4": 0.58}, {"time": 0.1667, "angle": -3.71, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8667, "angle": 3.01, "curve": 0.472, "c3": 0.48}, {"time": 1.8667, "angle": -5.86, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -5.28}]}, "qian_zj_49": {"rotate": [{"angle": -5.01, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.01, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -5.01}]}, "qian_zj_50": {"rotate": [{"angle": -1.44, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1667, "angle": -3.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.01, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -1.44}]}, "qian_zj_51": {"rotate": [{"angle": 2.17, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.1667, "angle": 0.49, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.8333, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": 3.01, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "angle": 2.17}]}, "qian_zj_2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.11, "y": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "qian_zj_3": {"translate": [{"x": 0.69, "y": -0.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.472, "c3": 0.48}, {"time": 1.3333, "x": 2.43, "y": -0.85, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 0.69, "y": -0.24}]}, "qian_zj_6": {"translate": [{"x": 0.96, "y": 0.54, "curve": 0.253, "c2": 0.58, "c3": 0.562}, {"time": 0.6, "curve": 0.472, "c3": 0.48}, {"time": 1.6, "x": 3.02, "y": 1.71, "curve": 0.311, "c2": 0.35, "c3": 0.629, "c4": 0.7}, {"time": 2, "x": 0.96, "y": 0.54}]}, "qian_zj_5": {"translate": [{"x": 0.65, "y": -2.16, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6, "curve": 0.472, "c3": 0.48}, {"time": 1.6, "x": 1.03, "y": -3.41, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 2, "x": 0.65, "y": -2.16}]}, "qian_zj_4": {"translate": [{"x": 0.54, "y": -0.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.472, "c3": 0.48}, {"time": 1.6667, "x": 0.75, "y": -0.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": 0.54, "y": -0.3}]}, "qian_zj_33": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "qian_zj_63": {"rotate": [{"angle": -2.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -8.93, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -2.54}]}, "qian_zj_64": {"rotate": [{"angle": -6.4, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.93, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -6.4}]}, "qian_zj_65": {"rotate": [{"angle": -8.93, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -8.93}]}, "qian_zj_66": {"rotate": [{"angle": 2.01}, {"time": 0.8, "angle": 10.04, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.5333, "angle": 2.03, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.8}, {"time": 2, "angle": 2.01}]}, "qian_zj_70": {"rotate": [{"angle": 4.58, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": 10.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": 4.58}]}, "qian_zj_67": {"rotate": [{"angle": 0.66, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1333}, {"time": 1.1333, "angle": 10.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.5333, "angle": 6.33, "curve": 0.332, "c2": 0.33, "c3": 0.676, "c4": 0.7}, {"time": 1.8, "angle": 2.85, "curve": 0.356, "c2": 0.42, "c3": 0.696, "c4": 0.78}, {"time": 2, "angle": 0.66}]}, "qian_zj_71": {"rotate": [{"angle": 0.7, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.8667, "angle": 10.04, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.5333, "angle": 2.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.8667, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": 0.7}]}, "qian_zj_68": {"rotate": [{"angle": 4.59, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 10.04, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1.5333, "angle": 9.84, "curve": 0.287, "c2": 0.13, "c3": 0.632, "c4": 0.52}, {"time": 1.8, "angle": 7.19, "curve": 0.324, "c2": 0.31, "c3": 0.664, "c4": 0.66}, {"time": 2, "angle": 4.59}]}, "qian_zj_72": {"rotate": [{"angle": 1.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2}, {"time": 1.2, "angle": 10.04, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1.5333, "angle": 7.19, "curve": 0.331, "c2": 0.33, "c3": 0.698, "c4": 0.77}, {"time": 2, "angle": 1.31}]}, "qian_zj_69": {"rotate": [{"angle": 8.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1.5333, "angle": 8, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.8, "angle": 10.04, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": 8.73}]}, "qian_zj_15": {"rotate": [{"time": 2, "angle": -0.4}]}, "qian_zj_16": {"rotate": [{"time": 2, "angle": -0.47}]}, "qian_zj_18": {"rotate": [{"time": 2, "angle": -0.16}]}, "qian_zj_19": {"rotate": [{"time": 2, "angle": -0.26}]}, "qian_zj_20": {"rotate": [{"time": 2, "angle": 10.86}]}, "qian_zj_78": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "angle": -9.7, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 2, "angle": -8.16}]}, "qian_zj_77": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -9.7, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -4.31}]}, "qian_zj_76": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.2667, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -9.7, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": -0.27}]}, "qian_zj_75": {"rotate": [{"angle": 2.13}]}, "qian_zj_82": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.1667, "angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.9667, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -9.7, "curve": 0.323, "c3": 0.656, "c4": 0.34}, {"time": 2, "angle": -9.64}]}, "qian_zj_81": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.1667, "angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.7, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.7, "angle": -9.7, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 2, "angle": -6.85}]}, "qian_zj_80": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.1667, "angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.4333, "angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -9.7, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 2, "angle": -2.7}]}, "qian_zj_79": {"rotate": [{"angle": 2.13}]}, "qian_zj_7": {"rotate": [{"angle": -7.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3, "angle": -5.41, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -14.21, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 2, "angle": -7.54}], "translate": [{"x": 1.92, "y": -0.09}]}, "qian_zj_8": {"rotate": [{"angle": -7.05, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.3, "angle": -6.1, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6333, "angle": -5.41, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -7.84, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 2, "angle": -7.05}]}}}, "run1": {"slots": {"L_jioall1": {"attachment": [{"name": null}]}, "Q_zj_3": {"attachment": [{"name": "Q_zj_002"}]}, "C_zj_001": {"attachment": [{"name": "C_zj_001"}]}, "Q_zj_0010": {"attachment": [{"name": null}]}}, "bones": {"qian_zj_1": {"rotate": [{"angle": -27.32}], "translate": [{"x": -0.92, "y": -9.46}, {"time": 0.1, "x": -1.83, "y": -15.49}, {"time": 0.2, "x": -0.92, "y": -9.46}, {"time": 0.3, "x": -1.83, "y": -15.49}, {"time": 0.4, "x": -0.92, "y": -9.46}]}, "qian_zj_39": {"rotate": [{"angle": 5.7}], "translate": [{"x": 2.45, "y": 2.33}]}, "qian_zj_47": {"rotate": [{"angle": 58.53}], "translate": [{"x": -4.91, "y": 4.21}]}, "qian_zj_34": {"rotate": [{"angle": -36.84}], "translate": [{"x": -4.2, "y": 0.24}]}, "qian_zj_4": {"rotate": [{"angle": 41.22}]}, "qian_zj_9": {"rotate": [{"angle": -41.17}]}, "qian_zj_10": {"rotate": [{"angle": -56.89}]}, "qian_zj_7": {"rotate": [{"angle": 14.67, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.64, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 14.67}], "translate": [{"x": 4.41, "y": 1.33}]}, "qian_zj_23": {"translate": [{"x": -4.02, "y": -7.36}]}, "qian_zj_27": {"translate": [{"x": -0.59, "y": -1.34}]}, "R1_jio2": {"translate": [{"x": -35.78, "y": 71.77, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "x": -3.69, "y": 37.09, "curve": 0.435, "c2": -0.01, "c3": 0.752, "c4": 0.42}, {"time": 0.1333, "x": 22.26, "y": 31.72, "curve": 0.468, "c2": 0.4, "c3": 0.676}, {"time": 0.2, "x": 72.81, "y": 22.33, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 0.2333, "x": 61.09, "y": 13.58, "curve": 0.32, "c2": 0.23, "c3": 0.654, "c4": 0.57}, {"time": 0.2667, "x": 21.91, "y": 8.88, "curve": 0.322, "c2": 0.28, "c3": 0.656, "c4": 0.61}, {"time": 0.3, "x": -7.03, "y": 6.96, "curve": 0.477, "c3": 0.785, "c4": 0.43}, {"time": 0.3333, "x": -30.66, "y": 14.55, "curve": 0.555, "c2": 0.4, "c3": 0.628}, {"time": 0.4, "x": -35.78, "y": 71.77}]}, "qian_zj_2": {"rotate": [{"angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -4.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.96}]}, "qian_zj_3": {"rotate": [{"angle": -15.04}]}, "qian_zj_6": {"rotate": [{"angle": -2.24}], "translate": [{"x": -16.51, "y": -10.42}]}, "qian_zj_8": {"rotate": [{"angle": -25.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -24.64, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "angle": -28.67, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -25.78}]}, "qian_zj_61": {"translate": [{"x": -2.67, "y": -5.33}]}, "qian_zj_24": {"rotate": [{"angle": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "angle": -18.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -5.26}]}, "qian_zj_25": {"rotate": [{"angle": -13.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.472, "c3": 0.48}, {"time": 0.3333, "angle": -18.53, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -13.27}]}, "qian_zj_26": {"rotate": [{"angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.53}]}, "qian_zj_28": {"rotate": [{"angle": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "angle": -18.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -5.26}], "translate": [{"x": -4.71, "y": -3.82}]}, "qian_zj_29": {"rotate": [{"angle": -13.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.472, "c3": 0.48}, {"time": 0.3333, "angle": -18.53, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -13.27}]}, "qian_zj_30": {"rotate": [{"angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.53}]}, "qian_zj_35": {"rotate": [{"angle": 3.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": 11.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -17.18, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 3.07}], "translate": [{"x": -18.57, "y": 2.56}]}, "qian_zj_36": {"rotate": [{"angle": -14.45, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.1667, "angle": 11.09, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "angle": -17.18, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.4, "angle": -14.45}]}, "qian_zj_37": {"rotate": [{"angle": -17.18, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 11.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -17.18}]}, "qian_zj_38": {"rotate": [{"angle": -9.15, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -17.18, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.09, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -9.15}]}, "qian_zj_40": {"rotate": [{"angle": -12.01, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0333, "angle": -6.83, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "angle": -24.08, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "angle": -12.01}]}, "qian_zj_41": {"rotate": [{"angle": -21.14, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.0333, "angle": -16.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1667, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "angle": -23.36, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.4, "angle": -21.14}]}, "qian_zj_42": {"rotate": [{"angle": -32.77, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -36.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.4, "angle": -32.77}]}, "qian_zj_43": {"rotate": [{"angle": 5.7}], "translate": [{"x": 1.39, "y": 3.08}]}, "qian_zj_44": {"rotate": [{"angle": -5.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "angle": -18.12, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -5.14}]}, "qian_zj_45": {"rotate": [{"angle": -18.26, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.472, "c3": 0.48}, {"time": 0.3333, "angle": -25.5, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -18.26}]}, "qian_zj_46": {"rotate": [{"angle": -31.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -31.97}]}, "qian_zj_48": {"rotate": [{"angle": -2.67, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "curve": 0.472, "c3": 0.48}, {"time": 0.2333, "angle": -28.13, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -7.98, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.4, "angle": -2.67}]}, "qian_zj_49": {"rotate": [{"angle": -9.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "angle": -18.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": -13.03, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.4, "angle": -9.12}]}, "qian_zj_50": {"rotate": [{"angle": -22.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -24.94, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.4, "angle": -22.53}]}, "qian_zj_51": {"rotate": [{"angle": -21.92, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": -24.22, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -17.35, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.4, "angle": -21.92}]}, "R_jio2": {"translate": [{"x": 39.31, "y": 16.12, "curve": 0.472, "c3": 0.48}, {"time": 0.0333, "x": 13.78, "y": -1.77, "curve": 0.313, "c2": 0.24, "c3": 0.648, "c4": 0.58}, {"time": 0.0667, "x": -41.71, "y": 3.52, "curve": 0.321, "c2": 0.29, "c3": 0.656, "c4": 0.63}, {"time": 0.1, "x": -55.32, "y": 10.91, "curve": 0.435, "c2": -0.01, "c3": 0.752, "c4": 0.42}, {"time": 0.1333, "x": -73.58, "y": 25.29, "curve": 0.422, "c2": 0.31, "c3": 0.727, "c4": 0.69}, {"time": 0.1667, "x": -62.39, "y": 34.88, "curve": 0.339, "c2": 0.57, "c3": 0.637}, {"time": 0.2, "x": -61.05, "y": 57.98, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -26.31, "y": 31.91, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "x": -5.73, "y": 27.8, "curve": 0.351, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.3667, "x": 35.51, "y": 19.34, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 0.4, "x": 39.31, "y": 16.12}]}, "R_jio3": {"translate": [{"x": 9.09, "y": 11.21, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "x": 3.1, "y": -1.9, "curve": 0.325, "c2": 0.31, "c3": 0.663, "c4": 0.65}, {"time": 0.1, "x": -4.62, "y": -4.93, "curve": 0.403, "c2": 0.12, "c3": 0.726, "c4": 0.52}, {"time": 0.1333, "x": -10.08, "y": -11.33, "curve": 0.43, "c2": 0.43, "c3": 0.702}, {"time": 0.2, "x": -33.75, "y": -8.05, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "x": -19.59, "y": -15.36, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.3667, "x": 2.17, "y": 11.87, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 0.4, "x": 9.09, "y": 11.21}]}, "R1_jio3": {"translate": [{"x": -37.74, "y": -3.33, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "x": -17.17, "y": -6.95, "curve": 0.351, "c2": 0.39, "c3": 0.701, "c4": 0.78}, {"time": 0.1667, "x": -2.68, "y": -21.2, "curve": 0.371, "c2": 0.63, "c3": 0.71}, {"time": 0.2, "x": 7.52, "y": 5.46, "curve": 0.316, "c3": 0.65, "c4": 0.35}, {"time": 0.2333, "x": 5.9, "y": 1.91, "curve": 0.32, "c2": 0.23, "c3": 0.654, "c4": 0.57}, {"time": 0.2667, "x": 3.68, "y": -7.13, "curve": 0.322, "c2": 0.28, "c3": 0.656, "c4": 0.61}, {"time": 0.3, "x": 0.77, "y": -1.93, "curve": 0.302, "c2": 0.23, "c3": 0.647, "c4": 0.6}, {"time": 0.3333, "x": -2.98, "y": -10.7, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.4, "x": -37.74, "y": -3.33}]}, "qian_zj_12": {"rotate": [{"angle": 64.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "angle": 139.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 64.71}]}, "qian_zj_11": {"rotate": [{"angle": 47.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -61.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 47.46}], "translate": [{"x": 5.15, "y": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -1.51, "y": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 5.15, "y": 7.38}]}, "qian_zj_52": {"translate": [{"x": -7.68, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 6.99, "y": 3.62, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -7.68, "y": 0.03}]}, "qian_zj_33": {"translate": [{"x": -8.09, "y": 0.17}]}, "qian_zj_63": {"rotate": [{"angle": -20.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.97, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -20.87}]}, "qian_zj_64": {"rotate": [{"angle": -13.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.97, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -13.69}]}, "qian_zj_65": {"rotate": [{"angle": -8.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -25.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -8.97}]}, "qian_zj_66": {"rotate": [{"angle": -26.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -20.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -41.45, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -26.11}], "translate": [{"x": -3.83, "y": -3.3}]}, "qian_zj_70": {"rotate": [{"angle": -20.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -41.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -20.04}], "translate": [{"x": -2.93, "y": -4.12}]}, "qian_zj_67": {"rotate": [{"angle": -8.37, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": 0.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": 6.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.47, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -8.37}]}, "qian_zj_71": {"rotate": [{"angle": 0.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": 6.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -14.47, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": 0.87}]}, "qian_zj_72": {"rotate": [{"angle": -8.39, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": 6.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -14.47, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -8.39}]}, "qian_zj_68": {"rotate": [{"angle": -14.47, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": -8.39, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "angle": 6.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -14.47}]}, "qian_zj_69": {"rotate": [{"angle": -8.39, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -14.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.94, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -8.39}]}, "qian_zj_78": {"rotate": [{"angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.53}]}, "qian_zj_77": {"rotate": [{"angle": -13.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.472, "c3": 0.48}, {"time": 0.3333, "angle": -18.53, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -13.27}]}, "qian_zj_76": {"rotate": [{"angle": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "angle": -18.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -5.26}]}, "qian_zj_82": {"rotate": [{"angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.53}]}, "qian_zj_81": {"rotate": [{"angle": -13.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "curve": 0.472, "c3": 0.48}, {"time": 0.3333, "angle": -18.53, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.4, "angle": -13.27}]}, "qian_zj_80": {"rotate": [{"angle": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "angle": -18.53, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4, "angle": -5.26}], "translate": [{"x": -4.71, "y": -3.82}]}, "qian_zj_74": {"translate": [{"x": -2.67, "y": -5.33}]}}, "deform": {"default": {"Q_zj_003_1": {"Q_zj_003_1": [{"vertices": [0.04405, -1.64376, 1.00748, -1.29936, -2.04143, -2.09389, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.35371, 0.5518, -1.41803, -0.35564, 0, 0, 0, 0, -1.49129, -1.00688, -0.60773, -1.69382, 1.80121, 3.25307, -0.47223, 3.68857, 5.63657, 2.85371, 1.25403, 4.38804, 3.88313, 8.82787, 3.62417, 6.9115, 2.34155, 5.44977, 0.37583, 6.05148, 2.28163, 11.63885, 1.57227, 5.8367, -1.29433, 3.33697, -1.63419, 0.99313, -1.90583, -0.16542, 1.92372, 3.34014, -0.42431, 3.8311, 2.36066, 1.25833, 1.15893, 2.41079, 3.24743, 1.20273, 1.90712, 2.89045, 0.08249, -0.01958, 0.10347, -0.10085, 0.14306, -0.02014, -0.71214, -1.71951, -1.38519, 5.66728, 2.79238, 3.67456, 4.56197, 3.82329, 3.93466, 3.74507, 1.4047, 0.19469, 1.01706, 0.9877, 1.2567, 1.38185, 0.19601, 1.8575, 1.81051, 2.22362, 0.14484, 2.86387, 0.10745, 0.08217, 0.03802, 0.12979, 0.0978, -0.03734, 0, 0, -0.81557, 0.30837, -0.84155, -0.23352, -2.69021, 2.07614, -3.39815, 0.08326], "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "vertices": [0.04405, -1.64376, 1.00748, -1.29936, -2.04143, -2.09389, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.35371, 0.5518, -1.41803, -0.35564, 0, 0, 0, 0, -1.49129, -1.00688, -0.60773, -1.69382, 1.80121, 3.25307, -0.47223, 3.68857, 5.47945, 2.88599, 0.98322, 4.361, 3.63889, 8.79509, 3.20943, 6.77272, 1.94372, 5.22515, 0.23779, 6.04165, 2.21723, 11.6819, 1.57227, 5.8367, -1.29433, 3.33697, -1.63419, 0.99313, -1.90583, -0.16542, 1.79919, 3.38826, -0.55319, 3.79626, 2.15509, 1.36167, 0.93204, 2.37255, 3.16638, 1.25794, 1.80911, 2.88704, 0, 0, 0, 0, 0, 0, -0.71214, -1.71951, -1.58443, 5.68047, 2.34454, 3.64332, 4.1806, 3.76165, 3.8019, 3.72787, 1.27812, 0.20448, 0.90922, 0.92075, 1.08115, 1.40679, 0.03972, 1.77381, 1.76154, 2.23877, 0.09639, 2.84712, 0, 0, 0, 0, 0, 0, 0, 0, -0.81557, 0.30837, -0.84155, -0.23352, -2.69846, 2.15783, -3.45312, 0.14426], "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "vertices": [0.04405, -1.64376, 1.00748, -1.29936, -2.04143, -2.09389, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.35371, 0.5518, -1.41803, -0.35564, 0, 0, 0, 0, -1.49129, -1.00688, -0.60773, -1.69382, 1.80121, 3.25307, -0.47223, 3.68857, 7.10849, 2.55137, 3.79084, 4.64136, 6.17117, 9.13502, 7.50922, 8.21152, 6.06835, 7.55387, 1.66898, 6.14363, 2.88494, 11.23551, 1.57227, 5.8367, -1.29433, 3.33697, -1.63419, 0.99313, -1.90583, -0.16542, 3.09024, 2.88933, 0.78301, 4.15738, 4.28638, 0.29032, 3.28439, 2.76901, 4.00662, 0.68553, 2.82526, 2.92239, 0.85527, -0.20303, 1.07269, -1.04554, 1.48322, -0.20878, -0.71214, -1.71951, 0.48119, 5.54365, 6.98762, 3.96725, 8.13447, 4.40072, 5.1783, 3.90619, 2.59039, 0.10296, 2.02728, 1.61484, 2.90121, 1.1482, 1.66009, 2.64149, 2.26933, 2.0817, 0.59869, 3.02072, 1.11395, 0.85196, 0.39418, 1.34563, 1.01393, -0.38709, 0, 0, -0.81557, 0.30837, -0.84155, -0.23352, -2.61293, 1.31092, -2.88324, -0.48815], "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.4, "vertices": [0.04405, -1.64376, 1.00748, -1.29936, -2.04143, -2.09389, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.35371, 0.5518, -1.41803, -0.35564, 0, 0, 0, 0, -1.49129, -1.00688, -0.60773, -1.69382, 1.80121, 3.25307, -0.47223, 3.68857, 5.63657, 2.85371, 1.25403, 4.38804, 3.88313, 8.82787, 3.62417, 6.9115, 2.34155, 5.44977, 0.37583, 6.05148, 2.28163, 11.63885, 1.57227, 5.8367, -1.29433, 3.33697, -1.63419, 0.99313, -1.90583, -0.16542, 1.92372, 3.34014, -0.42431, 3.8311, 2.36066, 1.25833, 1.15893, 2.41079, 3.24743, 1.20273, 1.90712, 2.89045, 0.08249, -0.01958, 0.10347, -0.10085, 0.14306, -0.02014, -0.71214, -1.71951, -1.38519, 5.66728, 2.79238, 3.67456, 4.56197, 3.82329, 3.93466, 3.74507, 1.4047, 0.19469, 1.01706, 0.9877, 1.2567, 1.38185, 0.19601, 1.8575, 1.81051, 2.22362, 0.14484, 2.86387, 0.10745, 0.08217, 0.03802, 0.12979, 0.0978, -0.03734, 0, 0, -0.81557, 0.30837, -0.84155, -0.23352, -2.69021, 2.07614, -3.39815, 0.08326]}]}, "Q_zj_003_2": {"Q_zj_003_2": [{"vertices": [-2.32906, 3.68617, -4.05822, 1.59502, -3.79605, 5.02866, -6.03562, 1.80991, -3.60193, 3.87221, -5.19475, 0.99224, -3.82669, 2.20351, -4.38918, -0.48624, -5.16969, 3.87194, -6.45885, 0.06485, -4.92897, 3.03377, -5.76902, -0.46864, -5.1929, 3.32086, -6.15153, -0.39325, -2.94164, 0.41081, -2.61519, -1.4084, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.99382, 2.09537, -2.0407, 1.10191, -0.99382, 2.09537, -2.0407, 1.10191, -1.47505, 2.20393, -2.49292, 0.90483, -3.88126, 3.53067, -5.21797, 0.55164, -3.41577, 4.3612, -5.33377, 1.49666, -4.72705, 2.58366, -5.34017, -0.71218, -4.62614, 2.35861, -5.12574, -0.83395, -3.45407, 1.61371, -3.73983, -0.74148, -3.06586, 0.8687, -2.98616, -1.11267, -1.69205, 0.45761, -1.63515, -0.63168]}]}, "C_zj_001": {"C_zj_001": [{"vertices": [-1.43182, -2.34848, -1.43182, -2.34848, -1.43182, -2.34848, -1.43182, -2.34848]}]}}}}, "run2": {"slots": {"L_jioall1": {"attachment": [{"name": null}]}, "Q_zj_3": {"attachment": [{"name": "Q_zj_002"}]}, "C_zj_001": {"attachment": [{"name": "C_zj_001"}]}, "Q_zj_0010": {"attachment": [{"name": null}]}}, "bones": {"R1_jio3": {"translate": [{"x": -37.74, "y": -3.33, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -12.21, "y": -13.7}]}, "L_jio2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -4.42, "y": 7.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -47.63, "y": 86.92}]}, "qian_zj_all": {"translate": [{"x": -0.11, "y": 19.75, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "curve": 0.076, "c2": 0.8, "c3": 0.75}, {"time": 0.2333, "x": -0.13, "y": 19.75}]}, "qian_zj_12": {"rotate": [{"angle": 64.71, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 53.52, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 59.52}]}, "qian_zj_11": {"rotate": [{"angle": 47.46, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -28.69, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": 105.19}], "translate": [{"x": 5.15, "y": 7.38, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "qian_zj_1": {"rotate": [{"angle": -34, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": -18.47, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -34}], "translate": [{"curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "x": 3.7, "y": -47.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "qian_zj_23": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": 2.13}], "translate": [{"x": -2.85, "y": -5.06, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667}]}, "qian_zj_39": {"rotate": [{"angle": -0.44, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "angle": 2.43}]}, "qian_zj_52": {"translate": [{"x": -7.68, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -0.13}]}, "qian_zj_47": {"rotate": [{"angle": -2.77, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.0667, "angle": -1.41}]}, "qian_zj_4": {"rotate": [{"angle": 41.22, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 45.85}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 0.54, "y": -0.3}]}, "qian_zj_7": {"rotate": [{"angle": 14.67, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 60.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": -21.75}], "translate": [{"x": -1.33, "y": -4.74, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -3.62, "y": -2.79}]}, "qian_zj_27": {"rotate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": 2.13}], "translate": [{"x": 0.09, "y": 2.96, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667}]}, "R1_jio2": {"translate": [{"x": -50.59, "y": 82.32, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -60.79, "y": 48.07}]}, "qian_zj_2": {"rotate": [{"angle": -11.96, "curve": 0.25, "c3": 0.75}, {"time": 0.0667}]}, "qian_zj_3": {"rotate": [{"angle": -15.04, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -58.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -26.13}], "translate": [{"time": 0.0667, "x": 0.69, "y": -0.24}]}, "qian_zj_5": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": 4.71, "y": 3.7}]}, "qian_zj_8": {"rotate": [{"angle": -25.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": 13.19, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2333, "angle": 10.16}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -0.36, "y": -0.43}]}, "qian_zj_24": {"rotate": [{"angle": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -0.27}]}, "qian_zj_25": {"rotate": [{"angle": -13.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -4.31}]}, "qian_zj_26": {"rotate": [{"angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -8.16}]}, "qian_zj_28": {"rotate": [{"angle": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -2.7}]}, "qian_zj_29": {"rotate": [{"angle": -13.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -6.85}]}, "qian_zj_30": {"rotate": [{"angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -9.64}]}, "qian_zj_35": {"rotate": [{"angle": 3.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -7.4}]}, "qian_zj_36": {"rotate": [{"angle": -14.45, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.0667, "angle": -12.74}]}, "qian_zj_37": {"rotate": [{"angle": -17.18, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -11.86}]}, "qian_zj_38": {"rotate": [{"angle": -9.15, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -6.23}]}, "qian_zj_40": {"rotate": [{"angle": -12.01, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": -1.45}]}, "qian_zj_41": {"rotate": [{"angle": -21.14, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.0667, "angle": -4.71}]}, "qian_zj_42": {"rotate": [{"angle": -32.77, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "angle": -5.28}]}, "qian_zj_43": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 0.49}]}, "qian_zj_44": {"rotate": [{"angle": -5.14, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -3.71}]}, "qian_zj_45": {"rotate": [{"angle": -18.26, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -5.86}]}, "qian_zj_46": {"rotate": [{"angle": -31.97, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -3.34}]}, "qian_zj_48": {"rotate": [{"angle": -2.67, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "angle": -5.28}]}, "qian_zj_49": {"rotate": [{"angle": -9.12, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": -5.01}]}, "qian_zj_50": {"rotate": [{"angle": -22.53, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.0667, "angle": -1.44}]}, "qian_zj_51": {"rotate": [{"angle": -21.92, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0667, "angle": 2.17}]}, "R_jio2": {"translate": [{"x": 43.4, "y": 22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 4.94, "y": 47.55}]}, "R_jio3": {"translate": [{"x": 9.09, "y": 11.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 3.38, "y": -15.1}]}, "qian_zj_9": {"rotate": [{"angle": -86.51, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -37.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -88}]}, "qian_zj_61": {"translate": [{"x": -2.67, "y": -5.33, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -7.56, "y": -1.88}]}, "qian_zj_10": {"rotate": [{"angle": -24.43, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": -0.41}]}, "qian_zj_6": {"rotate": [{"angle": -2.24, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667}], "translate": [{"x": -16.51, "y": -10.42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -16.5, "y": -9.02}]}, "qian_zj_13": {"rotate": [{"time": 0.0667, "angle": 2.95}]}, "qian_zj_57": {"rotate": [{"time": 0.5333, "angle": -5.92}]}, "bone18": {"rotate": [{"time": 0.5333, "angle": -0.52}]}, "bone19": {"rotate": [{"time": 0.5333, "angle": 1.34}]}, "bone20": {"rotate": [{"time": 0.5333, "angle": -1.77}]}, "bone21": {"rotate": [{"time": 0.5333, "angle": -0.38}]}, "bone22": {"rotate": [{"time": 0.5333, "angle": 1.1}]}, "qian_zj_33": {"rotate": [{"angle": -14.15}]}, "qian_zj_63": {"rotate": [{"angle": -14.15}]}, "qian_zj_64": {"rotate": [{"angle": -14.15}]}, "qian_zj_65": {"rotate": [{"angle": -14.15}]}, "qian_zj_78": {"rotate": [{"angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -8.16}]}, "qian_zj_77": {"rotate": [{"angle": -13.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -4.31}]}, "qian_zj_76": {"rotate": [{"angle": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -0.27}]}, "qian_zj_82": {"rotate": [{"angle": -18.53, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -9.64}]}, "qian_zj_81": {"rotate": [{"angle": -13.27, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -6.85}]}, "qian_zj_80": {"rotate": [{"angle": -5.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": -2.7}]}, "qian_zj_74": {"translate": [{"x": -2.67, "y": -5.33, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "x": -7.56, "y": -1.88}]}}, "deform": {"default": {"Q_zj_003_1": {"Q_zj_003_1": [{"vertices": [-7.79326, 5.0201, -9.25334, -0.5609, -10.96054, 8.46555, -6.31941, 9.0873, -4.5959, 6.75149, -1.19315, 3.56288, 3.03848, -0.01306, 2.90619, -0.69967, 2.32906, 1.97208, 0.71129, 2.96772, 2.16058, 1.60745, 0.79117, 2.574, 0.73769, 4.73514, -2.20574, 4.25453, -2.27948, 6.07908, -5.43385, 3.55412, 0.89004, 4.64358, 0.45335, 8.68147, 0.44843, 8.49895, 1.09241, 4.7602, 0, 0, 0, 0, -2.3424, 1.67017, -4.11858, 1.01616, -6.32494, 2.28407, -11.8748, 3.50776, -11.6508, -4.19414, 1.06372, 6.95671, -3.25673, 6.23886, 2.6752, 3.6328, 0.00862, 4.51152, -0.68341, 2.12645, -1.80875, 1.31056, -4.00623, 1.53702, -8.05701, 4.52333, -9.17239, -1.11749, -9.75731, 7.41022, -2.35999, 0.29034, 0, 0, 1.30956, 7.42532, 1.77882, 3.76384, 0.21664, 4.09801, -2.2491, 3.43259, -0.27968, 1.22625, -0.95085, 0.82349, -2.34407, 0.70587, -2.30786, -0.81702, -7.71475, 0.32455, -6.41308, -4.30081, -8.34413, 4.25938, -10.54907, 8.2716, -11.61143, 3.22657, -11.27185, -4.26519, -8.12965, 3.28018, -8.49696, -2.16243]}]}, "Q_zj_003_2": {"Q_zj_003_2": [{"vertices": [-2.32906, 3.68617, -4.05822, 1.59502, -3.79605, 5.02866, -6.03562, 1.80991, -3.60193, 3.87221, -5.19475, 0.99224, -3.82669, 2.20351, -4.38918, -0.48624, -5.16969, 3.87194, -6.45885, 0.06485, -4.92897, 3.03377, -5.76902, -0.46864, -5.1929, 3.32086, -6.15153, -0.39325, -2.94164, 0.41081, -2.61519, -1.4084, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.99382, 2.09537, -2.0407, 1.10191, -0.99382, 2.09537, -2.0407, 1.10191, -1.47505, 2.20393, -2.49292, 0.90483, -3.88126, 3.53067, -5.21797, 0.55164, -3.41577, 4.3612, -5.33377, 1.49666, -4.72705, 2.58366, -5.34017, -0.71218, -4.62614, 2.35861, -5.12574, -0.83395, -3.45407, 1.61371, -3.73983, -0.74148, -3.06586, 0.8687, -2.98616, -1.11267, -1.69205, 0.45761, -1.63515, -0.63168]}]}}}}, "run3": {"bones": {"qian_zj_7": {"rotate": [{"angle": 166.65, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0333, "angle": 129.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -18.44, "curve": "stepped"}, {"time": 0.1667, "angle": -18.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "angle": 45.5, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 177.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 63.75, "curve": "stepped"}, {"time": 0.4333, "angle": 63.75, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": 137.45}]}, "qian_zj_8": {"rotate": [{"angle": -3.81, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -26.76, "curve": "stepped"}, {"time": 0.1667, "angle": -26.76, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.2, "angle": 44.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": -26.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -17.28, "curve": "stepped"}, {"time": 0.4333, "angle": -17.28, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": -7.63}]}, "qian_zj_4": {"rotate": [{"angle": 33.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.08, "curve": "stepped"}, {"time": 0.2667, "angle": 11.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.54, "curve": "stepped"}, {"time": 0.4333, "angle": 4.54, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": 24.98}]}, "qian_zj_1": {"rotate": [{"angle": 11.4, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -36.56, "curve": "stepped"}, {"time": 0.1667, "angle": -36.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.24, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": 9.86}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 6.98, "y": -31.49, "curve": "stepped"}, {"time": 0.1667, "x": 6.98, "y": -31.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -4.47, "y": -9.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 11.64, "y": -33.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 13.71, "y": -28.56, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": 4.07, "y": -11.4}]}, "R_jio2": {"translate": [{"x": 19.65, "y": 12.19, "curve": 0.288, "c3": 0.628, "c4": 0.38}, {"time": 0.2667, "x": 19.17, "y": 8.81, "curve": 0.313, "c2": 0.26, "c3": 0.65, "c4": 0.61}, {"time": 0.5, "x": 19.65, "y": 12.19}]}, "qian_zj_11": {"rotate": [{"angle": -13.06, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -16.55, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 144.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 13.01, "curve": "stepped"}, {"time": 0.4333, "angle": 13.01, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": -5.66}]}, "qian_zj_12": {"rotate": [{"angle": 52.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.1, "angle": 50.62, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 86.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "angle": 52.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 21.61, "curve": "stepped"}, {"time": 0.4333, "angle": 21.61, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": 52.52}]}, "qian_zj_9": {"rotate": [{"angle": -15.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -99.62, "curve": "stepped"}, {"time": 0.1667, "angle": -99.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.77, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": -15.38}]}, "qian_zj_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 15.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.77, "curve": "stepped"}, {"time": 0.4333, "angle": -3.77, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": -1.07}]}, "qian_zj_3": {"rotate": [{"angle": -1.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 23.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -37.02, "curve": "stepped"}, {"time": 0.4333, "angle": -37.02, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": -11.31}]}, "qian_zj_6": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 4.94, "y": 16.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -1.17, "y": -11.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -3.66, "y": 6.39, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": -1.04, "y": 1.81}]}, "qian_zj_10": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -39.93, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": -11.33}], "scale": [{"x": 1.222, "y": 1.662}]}, "qian_zj_35": {"rotate": [{"angle": 10.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -21.25, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 29.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -23.73, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.16}]}, "qian_zj_36": {"rotate": [{"angle": 0.54, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": 10.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -21.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 29.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -23.73, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": 0.54}]}, "qian_zj_37": {"rotate": [{"angle": -14.11, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": 10.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -21.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 29.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -23.73, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": -14.11}]}, "qian_zj_38": {"rotate": [{"angle": -23.73, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 10.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -21.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 29.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -23.73}]}, "qian_zj_40": {"rotate": [{"angle": 7.18, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -20.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 7.18}]}, "qian_zj_41": {"rotate": [{"angle": -3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -20.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -3.6}]}, "qian_zj_42": {"rotate": [{"angle": -14.38, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -20.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -14.38}]}, "qian_zj_43": {"rotate": [{"angle": 7.18, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -20.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 7.18}]}, "qian_zj_44": {"rotate": [{"angle": -3.49, "curve": 0.343, "c2": 0.36, "c3": 0.686, "c4": 0.73}, {"time": 0.0333, "angle": 7.18, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -20.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -3.49}]}, "qian_zj_45": {"rotate": [{"angle": -14.43, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.0333, "angle": -3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -20.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -14.43}]}, "qian_zj_46": {"rotate": [{"angle": -20.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0333, "angle": -14.38, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1333, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -20.68}]}, "qian_zj_48": {"rotate": [{"angle": -14.33, "curve": 0.314, "c2": 0.27, "c3": 0.686, "c4": 0.73}, {"time": 0.0667, "angle": 7.18, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -20.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "angle": -14.33}]}, "qian_zj_49": {"rotate": [{"angle": -20.68, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": -3.6, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -20.68}]}, "qian_zj_50": {"rotate": [{"angle": -7.73, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -20.68, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.0667, "angle": -14.38, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1667, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 24.94, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": -7.73}]}, "qian_zj_51": {"rotate": [{"angle": 11.99, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -20.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 13.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -14.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 24.94, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 11.99}]}, "qian_zj_57": {"rotate": [{"angle": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -11.14, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": -19.94}]}, "L_jio2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -12.7, "y": 11.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 28.06, "y": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -5.59, "y": 4.15, "curve": "stepped"}, {"time": 0.4333, "x": -5.59, "y": 4.15, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": -1.59, "y": 1.18}]}, "R1_jio2": {"translate": [{"x": -50.91, "y": 34.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": -37.23, "y": 34.52}, {"time": 0.3667, "x": -34.75, "y": 21.11}]}, "R1_jio3": {"translate": [{"x": -6.55, "y": -10.83, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.1667, "x": -10.18, "y": -13.61, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.2667, "x": -9.97, "y": -11.97, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.3667, "x": -14.05, "y": -10.19, "curve": 0.343, "c2": 0.37, "c3": 0.715, "c4": 0.83}, {"time": 0.5, "x": -6.55, "y": -10.83}]}, "qian_zj_all": {"translate": [{"x": -0.13, "y": 19.75}]}, "qian_zj_39": {"rotate": [{"angle": 13.48, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 13.48}]}, "qian_zj_47": {"rotate": [{"angle": -0.3, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 13.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 22.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -14.08, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -0.3}]}, "qian_zj_66": {"rotate": [{"angle": -6.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667}, {"time": 0.2, "angle": -16.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -12.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -6.32}]}, "qian_zj_70": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -12.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "qian_zj_67": {"rotate": [{"angle": -12.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.0667, "angle": -6.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -16.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.65}]}, "qian_zj_71": {"rotate": [{"angle": -6.32, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667}, {"time": 0.2, "angle": -16.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -12.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -6.32}]}, "qian_zj_68": {"rotate": [{"angle": 4.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.0667, "angle": -12.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2}, {"time": 0.3333, "angle": -16.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 4.46}]}, "qian_zj_72": {"rotate": [{"angle": -12.65, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -16.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -12.65}]}, "qian_zj_69": {"rotate": [{"angle": 6.15, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 11.23, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.0667, "angle": 4.46, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1333, "angle": -12.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.32, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 6.15}]}}, "drawOrder": [{"offsets": [{"slot": "Q_zj_0013", "offset": -13}, {"slot": "Q_zj_0014", "offset": 9}]}]}, "skill1": {"slots": {"Q_zj_14": {"color": [{"time": 1.2, "color": "00cfffff", "curve": 0.472, "c3": 0.48}, {"time": 1.3667, "color": "00b4ffff", "curve": 0.499, "c3": 0.729, "c4": 0.53}, {"time": 1.5667, "color": "fff0d000"}], "attachment": [{"time": 1.2, "name": "Q_zj_0013"}]}, "Q_zj_16": {"attachment": [{"time": 1.9667, "name": "Q_zj_0015"}]}, "Q_zj_0013": {"color": [{"time": 1.2, "color": "00b4ffff", "curve": 0.499, "c3": 0.729, "c4": 0.53}, {"time": 1.3667, "color": "1fbcffff", "curve": 0.241, "c2": 0.52, "c3": 0.512}, {"time": 1.5667, "color": "ffffffff"}]}, "Q_zj_17": {"attachment": [{"time": 1.9667, "name": "Q_zj_0016"}]}}, "bones": {"qian_zj_1": {"rotate": [{"time": 0.5667}, {"time": 0.8667, "angle": -15.62}, {"time": 1.1667, "angle": 5.2}, {"time": 1.2, "angle": -8.27, "curve": 0.499, "c3": 0.729, "c4": 0.53}, {"time": 1.5667, "angle": -8.57, "curve": 0.241, "c2": 0.52, "c3": 0.512}, {"time": 1.8667, "angle": -8.27, "curve": 0.472, "c3": 0.48}, {"time": 1.9667}], "translate": [{"time": 0.5667}, {"time": 0.8667, "x": 1.52, "y": -28.4}, {"time": 1.1667, "x": 3.75, "y": -34.25, "curve": "stepped"}, {"time": 1.2, "x": 4.73, "y": -26.77, "curve": "stepped"}, {"time": 1.8667, "x": 4.73, "y": -26.77, "curve": 0.472, "c3": 0.48}, {"time": 1.9667}]}, "qian_zj_2": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 13.88, "curve": "stepped"}, {"time": 1.2, "angle": -15.48, "curve": "stepped"}, {"time": 1.8667, "angle": -15.48, "curve": 0.472, "c3": 0.48}, {"time": 1.9667}]}, "qian_zj_3": {"rotate": [{"time": 0.6667, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1.1333, "angle": 10.09, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 1.1667, "angle": -11.8, "curve": "stepped"}, {"time": 1.2, "angle": -4.63, "curve": "stepped"}, {"time": 1.8667, "angle": -4.63, "curve": 0.472, "c3": 0.48}, {"time": 1.9667}], "translate": [{"x": 0.69, "y": -0.24, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "x": 0.69, "y": -0.24}]}, "qian_zj_4": {"rotate": [{"time": 0.9, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1.1333, "angle": 10.75, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 1.1667, "angle": -5.36, "curve": "stepped"}, {"time": 1.8667, "angle": -5.36, "curve": 0.472, "c3": 0.48}, {"time": 1.9667}], "translate": [{"x": 0.54, "y": -0.3, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "x": 0.54, "y": -0.3}]}, "qian_zj_5": {"translate": [{"x": 0.65, "y": -2.16, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 0.65, "y": -2.16}]}, "qian_zj_52": {"translate": [{"x": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1}]}, "qian_zj_6": {"translate": [{"x": 0.96, "y": 0.54, "curve": 0.253, "c2": 0.58, "c3": 0.562}, {"time": 0.1, "curve": "stepped"}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.83, "y": -4.7, "curve": "stepped"}, {"time": 1.8667, "x": -3.83, "y": -4.7, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "x": 0.96, "y": 0.54}]}, "qian_zj_7": {"rotate": [{"angle": -7.54, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 58.44, "curve": "stepped"}, {"time": 0.5667, "angle": 58.44}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "angle": 88.35, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1333, "angle": -140.82, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 87.21, "curve": "stepped"}, {"time": 1.8667, "angle": 87.21, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": 2.45}], "translate": [{"x": 1.92, "y": -0.09}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.8667, "x": -4.02, "y": -4, "curve": 0.336, "c2": 0.34, "c3": 0.682, "c4": 0.71}, {"time": 1, "x": 3.92, "y": -10.21, "curve": 0.365, "c2": 0.48, "c3": 0.706, "c4": 0.85}, {"time": 1.1, "x": 7.05, "y": -2.18, "curve": 0.354, "c2": 0.65, "c3": 0.688}, {"time": 1.1333, "x": 10.9, "y": -6.45, "curve": 0.307, "c3": 0.642, "c4": 0.35}, {"time": 1.2, "x": 2.9, "y": -4.33, "curve": "stepped"}, {"time": 1.8667, "x": 2.9, "y": -4.33, "curve": 0.286, "c2": 0.15, "c3": 0.755}, {"time": 1.9667}]}, "qian_zj_8": {"rotate": [{"angle": -7.05, "curve": 0.329, "c2": 0.32, "c3": 0.676, "c4": 0.69}, {"time": 0.1, "angle": 15.2, "curve": "stepped"}, {"time": 0.5667, "angle": 15.2}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 31.07, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -35.16, "curve": "stepped"}, {"time": 1.8667, "angle": -35.16, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -1.69}]}, "qian_zj_9": {"rotate": [{"angle": -19.95}, {"time": 0.1, "curve": "stepped"}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -23.26, "curve": "stepped"}, {"time": 1.8667, "angle": -23.26, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -4.41}]}, "qian_zj_10": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -28.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -63.81, "curve": "stepped"}, {"time": 1.2, "angle": -26.49, "curve": "stepped"}, {"time": 1.8667, "angle": -26.49, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -0.41}], "scale": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.165, "y": 1.169, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 1.801, "y": 2.283, "curve": "stepped"}, {"time": 1.8667, "x": 1.801, "y": 2.283, "curve": 0.321, "c2": 0.32, "c3": 0.456}, {"time": 1.9667}]}, "qian_zj_11": {"rotate": [{"angle": -1.87, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -79.31, "curve": "stepped"}, {"time": 0.5667, "angle": -79.31}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -52.2, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -35.07, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 17.16, "curve": 0.472, "c3": 0.48}, {"time": 1.5667, "angle": 17.75, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -1.87}]}, "qian_zj_12": {"rotate": [{"angle": 1.04, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": 141.02, "curve": "stepped"}, {"time": 0.5667, "angle": 141.02}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 69.4, "curve": 0.472, "c3": 0.48}, {"time": 1.5667, "angle": 18.21, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": 1.04}]}, "qian_zj_13": {"rotate": [{"angle": 2.95, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": 2.95}]}, "qian_zj_23": {"rotate": [{"angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": 2.13}]}, "qian_zj_24": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -0.27}]}, "qian_zj_25": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -4.31}]}, "qian_zj_26": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -8.16}]}, "qian_zj_27": {"rotate": [{"angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": 2.13}]}, "qian_zj_28": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -2.7}]}, "qian_zj_29": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -6.85}]}, "qian_zj_30": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -9.64}]}, "qian_zj_35": {"rotate": [{"angle": -7.4, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -25.76, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -30.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": 24.58, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -7.4}]}, "qian_zj_36": {"rotate": [{"angle": -12.74, "curve": 0.304, "c2": 0.22, "c3": 0.644, "c4": 0.58}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -30.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 24.58, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -12.74}]}, "qian_zj_37": {"rotate": [{"angle": -11.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -30.48, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 24.58, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -11.86}]}, "qian_zj_38": {"rotate": [{"angle": -6.23, "curve": 0.336, "c2": 0.34, "c3": 0.676, "c4": 0.69}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -30.48, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": 24.58, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -6.23}]}, "qian_zj_39": {"rotate": [{"angle": 2.43, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -1.57, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 2.43}]}, "qian_zj_40": {"rotate": [{"angle": -1.45, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -1.45}]}, "qian_zj_41": {"rotate": [{"angle": -4.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -4.71}]}, "qian_zj_42": {"rotate": [{"angle": -5.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 20.92, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -5.28}]}, "qian_zj_43": {"rotate": [{"angle": 0.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -1.57, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 0.49}]}, "qian_zj_44": {"rotate": [{"angle": -3.71, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -3.71}]}, "qian_zj_45": {"rotate": [{"angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -5.86}]}, "qian_zj_46": {"rotate": [{"angle": -3.34, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 20.92, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -3.34}]}, "qian_zj_47": {"rotate": [{"angle": -1.41, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -1.57, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -1.41}]}, "qian_zj_48": {"rotate": [{"angle": -5.28, "curve": 0.307, "c2": 0.23, "c3": 0.645, "c4": 0.58}, {"time": 0.1, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -5.28}]}, "qian_zj_49": {"rotate": [{"angle": -5.01, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -5.01}]}, "qian_zj_50": {"rotate": [{"angle": -1.44, "curve": 0.338, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 20.92, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": -1.44}]}, "qian_zj_51": {"rotate": [{"angle": 2.17, "curve": 0.311, "c2": 0.25, "c3": 0.649, "c4": 0.6}, {"time": 0.1, "curve": "stepped"}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 20.92, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -17.68, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 2.17}]}, "R_jio2": {"translate": [{"time": 0.5667}, {"time": 1.1667, "x": 18.27, "y": -1.74, "curve": "stepped"}, {"time": 1.9, "x": 18.27, "y": -1.74, "curve": 0.344, "c2": 0.66, "c3": 0.678}, {"time": 1.9667}]}, "L_jio2": {"translate": [{"time": 0.5667}, {"time": 1.1667, "x": -27.89, "y": 6.12, "curve": "stepped"}, {"time": 1.9, "x": -27.89, "y": 6.12, "curve": 0.344, "c2": 0.66, "c3": 0.678}, {"time": 1.9667}]}, "qian_zj_18": {"rotate": [{"angle": -0.16}]}, "L_jioall2": {"rotate": [{"angle": -10.22}]}, "qian_zj_15": {"rotate": [{"angle": -0.4}]}, "root": {"translate": [{"time": 1.1, "curve": "stepped"}, {"time": 1.1667, "y": -11.06, "curve": "stepped"}, {"time": 1.2333}]}, "bone18": {"rotate": [{"time": 0.6667, "angle": -0.52}]}, "bone19": {"rotate": [{"time": 0.6667, "angle": 1.34}]}, "bone20": {"rotate": [{"time": 0.6667, "angle": -1.77}]}, "bone21": {"rotate": [{"time": 0.6667, "angle": -0.38}]}, "bone22": {"rotate": [{"time": 0.6667, "angle": 1.1}]}, "qian_zj_60": {"rotate": [{"angle": 7.13}]}, "qian_zj_59": {"rotate": [{"angle": -11.94}]}, "qian_zj_58": {"rotate": [{"angle": 10.85}]}, "qian_zj_57": {"rotate": [{"angle": 2.8}]}, "qian_zj_54": {"rotate": [{"angle": -0.46}]}, "qian_zj_21": {"rotate": [{"angle": -1.15}]}, "qian_zj_19": {"rotate": [{"angle": -0.26}]}, "qian_zj_16": {"rotate": [{"angle": -0.47}]}, "qian_zj_66": {"rotate": [{"angle": 2.01}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 25.39, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -17.55, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 2.01}]}, "qian_zj_70": {"rotate": [{"angle": 4.58, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 25.39, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -17.55, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "angle": 4.58}]}, "qian_zj_71": {"rotate": [{"angle": 0.7, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 25.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -17.55, "curve": 0.245, "c3": 0.709, "c4": 0.83}, {"time": 1.9667, "angle": 0.7}]}, "qian_zj_67": {"rotate": [{"angle": 0.66, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 25.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -17.55, "curve": 0.245, "c3": 0.709, "c4": 0.83}, {"time": 1.9667, "angle": 0.66}]}, "qian_zj_72": {"rotate": [{"angle": 1.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 25.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -17.55, "curve": 0.243, "c3": 0.677, "c4": 0.7}, {"time": 1.9667, "angle": 1.31}]}, "qian_zj_68": {"rotate": [{"angle": 4.59, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 25.39, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -17.55, "curve": 0.243, "c3": 0.677, "c4": 0.7}, {"time": 1.9667, "angle": 4.59}]}, "qian_zj_69": {"rotate": [{"angle": 8.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2, "curve": "stepped"}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -17.55, "curve": 0.243, "c3": 0.652, "c4": 0.61}, {"time": 1.9667, "angle": 8.73}]}, "qian_zj_20": {"rotate": [{"time": 1.9667, "angle": 10.86}]}, "qian_zj_64": {"rotate": [{"angle": -6.4}]}, "qian_zj_65": {"rotate": [{"angle": -8.93}]}, "qian_zj_63": {"rotate": [{"angle": -2.54}]}, "qian_zj_75": {"rotate": [{"angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": 2.13}]}, "qian_zj_78": {"rotate": [{"angle": -8.16, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -8.16}]}, "qian_zj_77": {"rotate": [{"angle": -4.31, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -4.31}]}, "qian_zj_76": {"rotate": [{"angle": -0.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -0.27}]}, "qian_zj_79": {"rotate": [{"angle": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": 2.13}]}, "qian_zj_82": {"rotate": [{"angle": -9.64, "curve": 0.295, "c2": 0.11, "c3": 0.634, "c4": 0.47}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -9.64}]}, "qian_zj_81": {"rotate": [{"angle": -6.85, "curve": 0.322, "c2": 0.3, "c3": 0.66, "c4": 0.65}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -6.85}]}, "qian_zj_80": {"rotate": [{"angle": -2.7, "curve": 0.343, "c2": 0.36, "c3": 0.68, "c4": 0.71}, {"time": 0.1, "curve": "stepped"}, {"time": 1.8667, "curve": 0.472, "c3": 0.48}, {"time": 1.9667, "angle": -2.7}]}}, "drawOrder": [{"time": 0.8333, "offsets": [{"slot": "Q_zj_0013", "offset": 10}, {"slot": "Q_zj_0014", "offset": 7}]}], "events": [{"time": 1.2333, "name": "hit"}]}, "spell": {}, "vertigo": {}}}