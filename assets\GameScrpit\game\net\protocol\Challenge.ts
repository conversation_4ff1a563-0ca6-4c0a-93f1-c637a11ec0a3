// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Challenge.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RewardMessage } from "./Comm";

export const protobufPackage = "sim";

/**  */
export interface ChallengeMessage {
  /** 今日已挑战次数 */
  count: number;
}

/**  */
export interface ChallengeResponse {
  /** 是否胜利 */
  win: boolean;
  /** 录像回放 */
  replay: string;
  /** 每日已经挑战的次数 */
  count: number;
  /** 获得的奖励 */
  rewardMessage: RewardMessage | undefined;
}

function createBaseChallengeMessage(): ChallengeMessage {
  return { count: 0 };
}

export const ChallengeMessage: MessageFns<ChallengeMessage> = {
  encode(message: ChallengeMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChallengeMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChallengeMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChallengeMessage>, I>>(base?: I): ChallengeMessage {
    return ChallengeMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChallengeMessage>, I>>(object: I): ChallengeMessage {
    const message = createBaseChallengeMessage();
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseChallengeResponse(): ChallengeResponse {
  return { win: false, replay: "", count: 0, rewardMessage: undefined };
}

export const ChallengeResponse: MessageFns<ChallengeResponse> = {
  encode(message: ChallengeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.win !== false) {
      writer.uint32(8).bool(message.win);
    }
    if (message.replay !== "") {
      writer.uint32(18).string(message.replay);
    }
    if (message.count !== 0) {
      writer.uint32(24).int32(message.count);
    }
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ChallengeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChallengeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.win = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.replay = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ChallengeResponse>, I>>(base?: I): ChallengeResponse {
    return ChallengeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChallengeResponse>, I>>(object: I): ChallengeResponse {
    const message = createBaseChallengeResponse();
    message.win = object.win ?? false;
    message.replay = object.replay ?? "";
    message.count = object.count ?? 0;
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
