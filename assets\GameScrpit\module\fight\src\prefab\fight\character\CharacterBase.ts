import { _decorator, AudioClip, AudioSource, instantiate, Node, sp, tween, UITransform, v3, Vec3 } from "cc";
import { JsonMgr } from "db://assets/GameScrpit/game/mgr/JsonMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { IConfigFightCharacter, IConfigSpineShow } from "db://assets/GameScrpit/game/JsonDefine";
import { BundleEnum } from "db://assets/platform/src/ResHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { HpStatusCtrl } from "../HpStatusCtrl";
import { ActionEnum, FaceToEnum, HpStatusType } from "../../../FightConstant";
import { SpineUtil } from "db://assets/platform/src/lib/utils/SpineUtil";
import { Sleep } from "db://assets/GameScrpit/game/GameDefine";

const { ccclass, property } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

/**
 * 角色状态枚举
 */
export enum CharacterState {
  IDLE = "idle",
  ATTACK = "attack",
  HURT = "hurt",
  JUMP1 = "JUMP1", // 前跳
  JUMP2 = "JUMP2", // 后跳
  DIE = "die",
  SKILL = "skill",
  SPELL = "spell",
  VERTIGO = "vertigo", // 眩晕
  FROZEN = "frozen", // 冰冻
  RUN = "run", // 跑步状态
  RUN_ATTACK = "run_attack", // 冲砍
  DODGE = "dodge", // 闪避
}

/**
 * 状态配置接口
 */
export interface StateConfig {
  /** 状态名称 */
  state: CharacterState;
  /** 对应的动作 */
  action: ActionEnum;
  /** 暴击 */
  critical?: boolean;
  /** 相关挂点 */
  nodeAttachment?: Node;
  /** 基础持续时间（毫秒），-1表示循环状态 */
  duration: number;
  /** 事件时间 */
  eventTime: number;
  /** 是否循环播放 */
  isLoop: number;
}

/**
 * 与美术相关的东西基本放这里控制
 */
@ccclass("CharacterBase")
export class CharacterBase extends BaseCtrl {
  /** 当前状态 */
  currentState: CharacterState;
  /** 当前状态持续时间 */
  currentStateDuration: number = 0;
  /** 状态队列 */
  stateQueue: StateConfig[] = [];
  /** 状态配置 */
  stateConfigMap: Map<CharacterState, StateConfig> = new Map();
  /** 状态函数映射 */
  stateFunctionMap: Map<CharacterState, (config: StateConfig) => void> = new Map();
  /** 普攻列表 */
  normalAttackList: ActionEnum[] = [];

  /** ======== 界面元素开始 ======== */
  /** 角色spine */
  spineCharacter: sp.Skeleton;
  /** 血量状态控制器 */
  hpStatusCtrl: HpStatusCtrl;
  /** 战斗形象所在节点 */
  node_show: Node;
  /** 特效节点池 */
  effectNodePoolMap: Map<number, Node[]> = new Map();

  /** ======== 初始化参数 ======== */
  fightCharacterId: number;
  posId: number;
  faceTo: FaceToEnum;
  cfgFightCharacter: IConfigFightCharacter;
  /** 暴击特效放大倍数 */
  criticalScale: number = 1;
  /** 暴击是否显示特效 */
  criticalEffectId: number = 0;

  public init(args: { fightCharacterId: number; posId: number }) {
    this.fightCharacterId = args.fightCharacterId;
    this.posId = args.posId;
    this.cfgFightCharacter = JsonMgr.instance.jsonList.c_fightCharacter[this.fightCharacterId];
  }

  /** 注册状态配置 */
  protected registerState(config: StateConfig, func: (config: StateConfig) => void = null) {
    this.stateConfigMap.set(config.state, config);

    let actionPlay = config.action;
    const ani = this.spineCharacter.findAnimation(actionPlay);
    if (!ani) {
      if (actionPlay == ActionEnum.run1) {
        actionPlay = ActionEnum.idle;
      } else if ([ActionEnum.run2, ActionEnum.run3, ActionEnum.jump1, ActionEnum.jump2].indexOf(actionPlay) >= 0) {
        actionPlay = ActionEnum.idle;
      } else if (
        [ActionEnum.attack2, ActionEnum.attack3, ActionEnum.skill1, ActionEnum.skill2].indexOf(actionPlay) >= 0
      ) {
        actionPlay = ActionEnum.attack1;
      } else if ([ActionEnum.vertigo, ActionEnum.spell].indexOf(actionPlay) >= 0) {
        actionPlay = ActionEnum.idle;
      }
    }
    config.action = actionPlay;

    if (config.duration == 0) {
      config.duration = SpineUtil.getSpineDuration(this.spineCharacter, config.action);
    }

    if (func) {
      this.stateFunctionMap.set(config.state, func);
    }
  }

  /** 阵亡动画 */
  public get isDie(): boolean {
    return this.hpStatusCtrl.hpNow <= 0;
  }

  /**
   * 加载战斗资源
   */
  public async loadFightRes() {
    // spine资源
    const keys = Object.keys(this.cfgFightCharacter.actionMap);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const effectCfgList = this.cfgFightCharacter.actionMap[key];
      if (!effectCfgList || effectCfgList.length == 0) {
        continue;
      }

      for (let j = 0; j < effectCfgList.length; j++) {
        const effectCfg = effectCfgList[j];
        // 加载spine
        const cfgEffect = JsonMgr.instance.jsonList.c_effect[effectCfg[0]];
        if (!cfgEffect) {
          continue;
        }
        await this.assetMgr.loadSpineSync(
          BundleEnum.BUNDLE_G_FIGHT,
          `/spine_effect/${cfgEffect.fileName}/${cfgEffect.fileName}`
        );

        // 加载音效
        const cfgAudio = JsonMgr.instance.jsonList.c_music[cfgEffect.audioId];
        if (cfgAudio) {
          await this.assetMgr.loadAudioSync(BundleEnum.BUNDLE_PUB, `/audio/${cfgAudio.musicName}`);
        }

        log.info("loadFightRes", effectCfg[0]);
      }
    }
  }

  /** 加载血条 */
  public async loadHp(type: HpStatusType, hpMax: number) {
    const pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_FIGHT, `/prefab/fight/HpStatus${type}`);
    const nodeNew = instantiate(pb);
    nodeNew.name = "node_hp";

    const nodeAttachment = this.getAttachmentByAction(ActionEnum.idle);
    nodeNew.parent = nodeAttachment;
    nodeNew.layer = nodeAttachment.layer;

    this.hpStatusCtrl = nodeNew.getComponent(HpStatusCtrl);
    this.hpStatusCtrl.initHp(hpMax);
  }

  /** 注册状态 */
  public registerAllState() {
    // idle状态
    this.registerState(
      {
        state: CharacterState.IDLE,
        action: ActionEnum.idle,
        eventTime: 0,
        duration: -1,
        isLoop: 1,
      },
      this.playAni
    );

    // 注册普攻,所有普攻参数临时生成
    this.registerState(
      {
        state: CharacterState.ATTACK,
        action: ActionEnum.attack1,
        duration: SpineUtil.getSpineDuration(this.spineCharacter, ActionEnum.attack1),
        eventTime: SpineUtil.getAnimationEventTime(this.spineCharacter, ActionEnum.attack1, "hit"),
        isLoop: 0,
      },
      this.playAni
    );

    // 注册受击
    this.registerState(
      {
        state: CharacterState.HURT,
        action: ActionEnum.hurt,
        duration: SpineUtil.getSpineDuration(this.spineCharacter, ActionEnum.hurt),
        eventTime: 0,
        isLoop: 0,
      },
      this.playAni
    );

    // 注册阵亡
    this.registerState(
      {
        state: CharacterState.DIE,
        action: ActionEnum.die,
        duration: -1,
        eventTime: 0,
        isLoop: 0,
      },
      this.playDie
    );

    // 注册向前跳
    this.registerState(
      {
        state: CharacterState.JUMP1,
        action: ActionEnum.jump1,
        duration: 0,
        eventTime: 0,
        isLoop: 0,
      },
      this.playAni
    );

    // 注册向后跳
    this.registerState(
      {
        state: CharacterState.JUMP2,
        action: ActionEnum.jump2,
        duration: 0,
        eventTime: 0,
        isLoop: 0,
      },
      this.playAni
    );

    // 晕
    this.registerState(
      {
        state: CharacterState.VERTIGO,
        action: ActionEnum.vertigo,
        duration: -1,
        eventTime: 0,
        isLoop: 1,
      },
      this.playAni
    );

    // 闪避
    this.registerState(
      {
        state: CharacterState.DODGE,
        action: ActionEnum.idle,
        duration: 0.2,
        eventTime: 0,
        isLoop: 1,
      },
      this.playDoge
    );
  }

  /**
   * cocos creator 生命周期
   */
  protected async onStart() {
    this.spineCharacter = this.getNode("node_spine").getComponent(sp.Skeleton);
    this.node_show = this.getNode("node_show");

    this.normalAttackList = [];
    for (let i = 1; i < 4; i++) {
      if (this.hasNode(`node_attack${i}`)) {
        this.normalAttackList.push(ActionEnum[`attack${i}`]);
      }
    }

    this.registerAllState();

    if (this.faceTo) {
      this.setFaceTo(this.faceTo);
    }
  }

  protected update(dt: number): void {
    if (this.stateQueue.length) {
      if (this.stateQueue[0].duration == -1) {
        return;
      }

      if (this.currentStateDuration >= this.stateQueue[0].duration) {
        this.stateQueue.shift();
        if (this.stateQueue.length) {
          this.setStateByCfg(this.stateQueue[0]);
        }
      } else {
        this.currentStateDuration += dt;
      }
    }

    if (!this.stateQueue.length && this.currentState != CharacterState.IDLE) {
      this.setStateByCfg(this.genByArgs(CharacterState.IDLE));
    }
  }

  /** 播放动画 */
  protected async playAni(config: StateConfig) {
    SpineUtil.playSpine(this.spineCharacter, config.action, config.isLoop == 1);
    const effectCfgList: number[][] = this.cfgFightCharacter.actionMap[config.action];
    if (!effectCfgList || effectCfgList.length == 0) {
      return;
    }

    // 播放启动动画
    await this.playEffectList(effectCfgList[0], this.getAttachmentByAction(config.action));

    // 播放事件特效
    await Sleep(config.eventTime);
    if (this.isValid) {
      await this.playEffectList(effectCfgList[1], this.getAttachmentByAction(config.action));
    }
  }

  /** 播放闪避 */
  protected playDoge(config: StateConfig) {
    tween(this.node_show)
      .to(0.1, { position: v3(-100 * this.node_show.getScale().x, 0, 0) })
      .to(0.1, { position: v3(0, 0, 0) })
      .start();
  }

  /** 播放吸血 */
  public playLep() {
    this.playEffect(1, 0, this.node_show);
  }

  /** 播放特效 */
  protected async playEffectList(effectInfo: number[], nodeParent: Node) {
    if (!effectInfo || effectInfo.length < 2) {
      return;
    }

    for (let i = 0; i < effectInfo.length; i += 2) {
      this.playEffect(effectInfo[i], effectInfo[i + 1], nodeParent);
    }

    // 显示暴击额外特效
    if (this.stateQueue[0].critical && this.criticalEffectId) {
      this.playEffect(this.criticalEffectId, 0, nodeParent);
    }
  }

  /** 播放单个特效 */
  private async playEffect(effectId: number, isLoop: number, nodeParent: Node) {
    const cfgEffect = JsonMgr.instance.jsonList.c_effect[effectId];
    if (!cfgEffect) {
      log.warn("特效配置不存在", effectId);
      return;
    }

    let rs: Node;
    const list = this.effectNodePoolMap.get(effectId);
    if (list && list.length > 0) {
      rs = list.pop();
    } else {
      let spineFind = await this.assetMgr.loadSpineSync(
        cfgEffect.bundle,
        `/spine_effect/${cfgEffect.fileName}/${cfgEffect.fileName}`
      );

      // 加载音效
      const cfgAudio = JsonMgr.instance.jsonList.c_music[cfgEffect.audioId];
      let audioClip: AudioClip;
      if (cfgAudio) {
        audioClip = await this.assetMgr.loadAudioSync(BundleEnum.BUNDLE_PUB, `/audio/${cfgAudio.musicName}`);
      }

      if (this.isValid) {
        rs = new Node();
        rs.layer = this.node.layer;
        let spineCmp = rs.addComponent(sp.Skeleton);
        spineCmp.skeletonData = spineFind;
        spineCmp.premultipliedAlpha = false;
        spineCmp.animation = cfgEffect.actionName;
        spineCmp.loop = isLoop == 1;
        setTimeout(() => {
          SpineUtil.playSpine(spineCmp, cfgEffect.actionName, isLoop == 1);
        }, 0.01);

        let audioCpt = rs.addComponent(AudioSource);
        audioCpt.clip = audioClip;
      }
      this.effectNodePoolMap.set(effectId, []);
    }

    // 暴击放大
    if (this.stateQueue[0].critical) {
      rs.setScale(this.criticalScale, this.criticalScale, 1);
    } else {
      rs.setScale(1, 1, 1);
    }

    const spine = rs.getComponent(sp.Skeleton);
    SpineUtil.playSpine(spine, cfgEffect.actionName, isLoop == 1);
    const audioSource = rs.getComponent(AudioSource);
    audioSource.playOneShot(audioSource.clip);
    const duration = SpineUtil.getSpineDuration(spine, cfgEffect.actionName);
    rs.parent = nodeParent;
    rs.setPosition(0, -nodeParent.getPosition().y, 0);

    setTimeout(() => {
      if (rs) {
        this.effectNodePoolMap.get(effectId).push(rs);
      }
    }, duration + 0.2);
  }

  /** 播放死亡动画 */
  protected playDie(config: StateConfig) {
    SpineUtil.playOneByOneStopLast(this.spineCharacter, ActionEnum.hurt, ActionEnum.die);
  }

  /**
   * 生成状态配置
   * @param state 状态
   * @param forceArgs 重写参数
   * @returns 持续时长
   */
  private genByArgs(state: CharacterState, forceArgs: {} = {}): StateConfig {
    const config = this.stateConfigMap.get(state);
    if (state == CharacterState.ATTACK) {
    }

    return {
      state: config.state,
      action: forceArgs.hasOwnProperty("action") ? forceArgs["action"] : config.action,
      duration: forceArgs.hasOwnProperty("duration") ? forceArgs["duration"] : config.duration,
      eventTime: forceArgs.hasOwnProperty("eventTime") ? forceArgs["eventTime"] : config.eventTime,
      isLoop: forceArgs.hasOwnProperty("isLoop") ? forceArgs["isLoop"] : config.isLoop,
    };
  }

  /**
   * 添加状态队列
   * @param state 状态
   * @param duration 持续时间 -1代表永远
   * @param forceLoop 是否强制循环，false代表走默认配置
   * @returns
   */
  public addQueueState(state: CharacterState, forceArgs?: {}): StateConfig {
    const stateConfig = this.genByArgs(state, forceArgs);
    this.stateQueue.push(stateConfig);
    return stateConfig;
  }

  /**
   * 清空队列并设置状态
   * @param state 状态
   * @param duration 设置时间
   * @returns
   */
  public clearQueueAndSetStaus(state: CharacterState, forceArgs?: {}): StateConfig {
    this.stateQueue = [];

    const stateConfig = this.genByArgs(state, forceArgs);
    this.addQueueState(state, stateConfig);
    this.setStateByCfg(stateConfig);
    return stateConfig;
  }

  /**
   * 改变血量
   * @param changeValue 改变值
   */
  public changeHp(changeValue: number) {
    this.hpStatusCtrl.changeHp(changeValue);
  }

  /** 设置状态 */
  public setStateByCfg(stateConfig: StateConfig) {
    if (this.currentState == stateConfig.state) {
      return;
    }

    this.currentState = stateConfig.state;
    const func = this.stateFunctionMap.get(stateConfig.state);
    if (func) {
      func.bind(this)(stateConfig);
    }

    this.currentStateDuration = 0;
  }

  /** 改变buff图标状态 */
  public changeBuff() {}

  /** 面向方向 */
  public setFaceTo(to: FaceToEnum) {
    log.info("CharacterCtrl setFaceTo", to);
    if (this.node_show) {
      if (to == FaceToEnum.left) {
        this.node_show.setScale(-1, 1, 1);
      } else {
        this.node_show.setScale(1, 1, 1);
      }
    } else {
      this.faceTo = to;
    }
  }

  /** 面向方向 */
  public getFaceTo(): FaceToEnum {
    return this.node_show.scale.x > 0 ? FaceToEnum.right : FaceToEnum.left;
  }

  /** 随机普攻动作 */
  public randomNormalAttack(): StateConfig {
    const action = this.normalAttackList[Math.floor(Math.random() * this.normalAttackList.length)];

    return {
      state: CharacterState.ATTACK,
      action: action,
      duration: this.getDurationByAction(action),
      eventTime: this.getEventByAction(action, "hit"),
      isLoop: 0,
    };
  }

  /** 获取动作里的附件节点 */
  public getAttachmentByAction(action: ActionEnum) {
    return this.getNode("node_" + action);
  }

  /** 获取动作持续时间 */
  public getDurationByAction(action: ActionEnum) {
    return SpineUtil.getSpineDuration(this.spineCharacter, action);
  }

  /** 获取动作里的事件触发时间 */
  public getEventByAction(action: ActionEnum, eventName: string) {
    return SpineUtil.getAnimationEventTime(this.spineCharacter, action, eventName);
  }
}
