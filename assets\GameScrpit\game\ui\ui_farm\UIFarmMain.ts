import { _decorator, instantiate, Label, Prefab, sp } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { UINode } from "../../../lib/ui/UINode";
import { FarmModule } from "../../../module/farm/FarmModule";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { FarmRouteName } from "../../../module/farm/FarmRoute";
import MsgMgr from "../../../lib/event/MsgMgr";
import { FarmEvent } from "../../../module/farm/FarmEvent";
import MsgEnum from "../../event/MsgEnum";
import { Input } from "cc";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { FarmDispatchMessage, FarmRewardMessage } from "../../net/protocol/Farm";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import { ConfirmMsg } from "../UICostConfirm";
import { FarmSlotUITool } from "../../../module/farm/FarmSlotUITool";
import FmUtils from "../../../lib/utils/FmUtils";
import { TimeUtils } from "../../../lib/utils/TimeUtils";
import { IConfigBlessLand } from "../../JsonDefine";
import { ShenjiEnum } from "../../../lib/common/ShenjiEnum";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { FarmAudioName } from "../../../module/farm/FarmConfig";
import { MainTaskModule } from "../../../module/mainTask/MainTaskModule";
import { LangMgr } from "../../mgr/LangMgr";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIExchangeShop } from "../../../module/common/src/prefab/ui/UIExchangeShop";
import { ShopTypeEnum } from "../../../module/common/src/CommonConfig";

const { ccclass, property } = _decorator;

@ccclass("UIFarmMain")
export class UIFarmMain extends UINode {
  private refreshCount = 0;
  private tickId: number = 0;
  protected _openAct = true;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FARM}?prefab/ui/UIFarmMain`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  protected onRegEvent() {
    MsgMgr.on(FarmEvent.OTHER_FARM_REFRESH, this.refreshFromServer, this);
    MsgMgr.on(FarmEvent.FARM_REFRESH, this.refresh, this);

    for (let i = 1; i <= 5; i++) {
      this.getNode("p" + i).on(Input.EventType.TOUCH_END, this.onClickHulu, this);
    }

    this.tickId = TickerMgr.setInterval(1, this.checkFarmInfo, false);
  }
  protected onDelEvent() {
    TickerMgr.clearTicker(this.tickId);

    MsgMgr.off(FarmEvent.OTHER_FARM_REFRESH, this.refreshFromServer, this);
    MsgMgr.off(FarmEvent.FARM_REFRESH, this.refresh, this);
  }

  protected onEvtShow(): void {
    // 进来先更新一下数据
    FarmModule.api.getFarmInfo(this.refresh.bind(this));

    this.refresh();
  }

  protected onEvtClose(): void {}

  private refreshFromServer() {
    FarmModule.api.getFarmInfo(this.refresh.bind(this));
  }

  private refresh() {
    // 奖励弹窗
    this.handleRewardList();

    // 更新葫芦槽位
    this.setSlotList();

    // 更新猴子状态
    let beeMetric = FarmModule.data.farmTrainMessage.beeMetric;
    this.getNode("lbl_xian").getComponent(Label).string = LangMgr.txMsgCode(480, [beeMetric.relaxBeeCnt]); // `闲 [${beeMetric.relaxBeeCnt}]`;
    this.getNode("lbl_zong").getComponent(Label).string = LangMgr.txMsgCode(481, [beeMetric.totalBeeCnt]); // `总 [${beeMetric.totalBeeCnt}]`;
    // 取通用配置
    const cfg1: IConfigBlessLand = FarmModule.data.getConfigBlessLand(1);

    // 今日可采集次数
    let remainFetch = FarmModule.data.farmTrainMessage.remainFetch;
    this.getNode("lbl_residue").getComponent(Label).string = LangMgr.txMsgCode(456, [remainFetch, cfg1.fetchMax]); // `今日可采集次数：${remainFetch}/${cfg1.fetchMax}`;

    // 刷新倒计时
    const cfg = FarmModule.data.getConfigBlessLand(1);
    let beginOfDay = TimeUtils.getDayBegin();
    let hour = TimeUtils.getHour();
    let nextRefreshTs = 0;
    for (let idx in cfg.refreshFreeList) {
      if (cfg.refreshFreeList[idx] > hour) {
        nextRefreshTs = beginOfDay + cfg.refreshFreeList[idx] * 3600000;
        break;
      }
    }
    if (nextRefreshTs == 0) {
      nextRefreshTs = beginOfDay + 86400000 + cfg.refreshFreeList[0] * 3600000;
    }
    FmUtils.setCd(this.getNode("lbl_refresh_cd"), nextRefreshTs);
    // 刷新次数，用于判断是否播放猴子入场动画
    this.refreshCount++;
  }

  /**设置葫芦槽位 */
  private async setSlotList() {
    const slotList = FarmModule.data.farmTrainMessage.slotList;

    for (let i = 0; i < 5; i++) {
      const pNode = this.getNode("p" + (i + 1));
      if (i >= slotList.length) {
        pNode.active = false;
        continue;
      }

      FarmSlotUITool.updateSlot(slotList[i], pNode, this.refreshCount);
    }
  }

  private on_click_btn_log() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.点击管理按钮);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmLog);
  }

  /**
   * 播放葫芦特效
   */
  private playRefreshHuluAni() {
    this.assetMgr.loadPrefab(BundleEnum.BUNDLE_G_FARM, "prefab/ani_hulu_refresh", (pb: Prefab) => {
      const slotList = FarmModule.data.farmTrainMessage.slotList;

      for (let i = 0; i < 5; i++) {
        const pNode = this.getNode("p" + (i + 1));
        if (i >= slotList.length) {
          pNode.active = false;
          continue;
        }

        const slot = slotList[i];
        if (slot.gourdId < 0) {
          continue;
        }

        let nodeSpine = pNode.getChildByName("ani_hulu_refresh");
        if (!nodeSpine) {
          nodeSpine = instantiate(pb);
          nodeSpine.setPosition(0, 0);
          nodeSpine.setParent(pNode);
        }

        const spine = nodeSpine.getChildByName("spine_hu_lu_shua_xin").getComponent(sp.Skeleton);

        if (slot.gourdId > 6) {
          SpineUtil.playSpine(spine, "hulu_02", false);
        } else {
          SpineUtil.playSpine(spine, "hulu_01", false);
        }
      }
    });
  }

  private doRefresh() {
    let needTip = false;
    for (let idx in FarmModule.data.farmTrainMessage.slotList) {
      let farmSlot = FarmModule.data.farmTrainMessage.slotList[idx];
      if (farmSlot.ownCollectorMessage || farmSlot.otherCollectorMessage) {
        continue;
      }

      if (farmSlot.gourdId > 0) {
        let cfg = FarmModule.data.getConfigBlessLand(farmSlot.gourdId);
        if (cfg.tip == 1) {
          needTip = true;
          break;
        }
      }
    }

    if (needTip) {
      let msg: ConfirmMsg = {
        msg: LangMgr.txMsgCode(477), // "存在高级宝箱，刷新后将消失！\n确认刷新？",
        itemList: [],
      };

      UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
        if (resp?.ok) {
          FarmModule.api.refresh(true, this.playRefreshHuluAni.bind(this));
        }
      });
    } else {
      FarmModule.api.refresh(true, this.playRefreshHuluAni.bind(this));
    }
  }

  /**普通刷新 */
  private on_click_btn_refresh() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.点击刷新按钮);
    const cfg1 = FarmModule.data.getConfigBlessLand(1);
    if (!FarmModule.data.stopHint) {
      let msg: ConfirmMsg = {
        msg: LangMgr.txMsgCode(478),
        itemList: cfg1.refreshCost1List,
        stopHintOption: true,
      };
      UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
        if (resp?.ok) {
          FarmModule.data.stopHint = resp.stopHint;

          this.doRefresh();
        }
      });
    } else {
      let lackItem = PlayerModule.service.checkitemEnought(cfg1.refreshCost1List);
      if (lackItem.length == 0) {
        this.doRefresh();
      } else {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
          itemId: lackItem[0],
          needNum: lackItem[1],
        });
      }
    }
  }

  /**高级刷新 */
  private on_click_btn_refresh_true() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.点击刷新按钮);
    FarmModule.api.refresh(true);
  }

  private onClickHulu(event) {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.点击葫芦图标);
    let rank = Number(event.target.name.replace("p", "")) - 1;

    const slotMsg = FarmModule.data.farmTrainMessage.slotList[rank];
    if (slotMsg.gourdId == -1) {
      return;
    }

    UIMgr.instance.showDialog(FarmRouteName.UIFarmCollect, {
      rank: rank,
      slotMsg: FarmModule.data.farmTrainMessage.slotList[rank],
    });
  }

  private on_click_btn_shop() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.点击商店按钮);
    // UIMgr.instance.showDialog(FarmRouteName.UIFarmShop);
    RouteManager.uiRouteCtrl.showRoute(UIExchangeShop, { payload: { type: ShopTypeEnum.福地商店 } });
  }

  private on_click_btn_find() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.点击探寻按钮);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmFind);

    const cfgTaskMain = MainTaskModule.data.getConfigTaskMain(MainTaskModule.data.mainTaskMsg.taskTimelineId);
    const cfgTask = MainTaskModule.data.getConfigTask(cfgTaskMain.taskId);
    if (cfgTask.id == 43 && cfgTask.clientDo) {
      MainTaskModule.api.clientPassTask();
    }
  }

  private on_click_btn_auto() {}

  private on_click_btn_manage() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.点击管理按钮);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmBeeManage);
  }

  private on_click_btn_cjjj() {
    AudioMgr.instance.playEffect(FarmAudioName.Effect.点击采集基金按钮);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmCjjj);
  }

  /**采集奖励弹出 */
  private handleRewardList() {
    let rewardList = FarmModule.data.farmTrainMessage.rewardGourdList;
    if (rewardList.length > 0) {
      FarmModule.api.takeReward((data: FarmRewardMessage) => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
        for (let i = 0; i < data.trigger; i++) {
          MsgMgr.emit(MsgEnum.ON_TRIGGER_SHENJI, ShenjiEnum.洞天神迹_102);
        }
      });
    }
  }

  private checkFarmInfo() {
    let canRefreshFarmInfo: boolean = false;
    // 槽位信息
    for (let slot of FarmModule.data.farmTrainMessage.slotList) {
      if (slot.otherCollectorMessage && slot.otherCollectorMessage.endTime > 0) {
        if (slot.otherCollectorMessage.endTime < TimeUtils.serverTime) {
          canRefreshFarmInfo = true;
          break;
        }
      }

      if (slot.ownCollectorMessage && slot.ownCollectorMessage.endTime > 0) {
        if (slot.ownCollectorMessage.endTime < TimeUtils.serverTime) {
          canRefreshFarmInfo = true;
          break;
        }
      }
    }

    if (!canRefreshFarmInfo) {
      // 蜜蜂情况更新
      let valueArr: FarmDispatchMessage[] = Object.values(FarmModule.data.farmTrainMessage.dispatchMap);
      for (let j = 0; j < valueArr.length; j++) {
        let colDes = valueArr[j];
        if (colDes.endTime <= TimeUtils.serverTime) {
          canRefreshFarmInfo = true;
          break;
        }
      }
    }

    if (canRefreshFarmInfo) {
      FarmModule.api.getFarmInfo((data) => {
        MsgMgr.emit(FarmEvent.FARM_REFRESH);
      });
    }
  }

  private on_click_btn_xiangxixinxi() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { desId: 12 });
  }

  on_click_btn_preview() {
    AudioMgr.instance.playEffect(1579);
    UIMgr.instance.showDialog(FarmRouteName.UIFarmPreview);
  }
}
