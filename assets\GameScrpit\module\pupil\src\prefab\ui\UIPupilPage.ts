import { _decorator, is<PERSON>alid, Label, Node, sp } from "cc";
import { UINode } from "../../../../../lib/ui/UINode";
import { BundleEnum } from "../../../../../game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../../../lib/ui/UIMgr";
import { PupilModule } from "../../PupilModule";
import { JsonMgr } from "../../../../../game/mgr/JsonMgr";
import { PlayerModule } from "../../../../player/PlayerModule";
import { EventTouch } from "cc";
import { ProgressBar } from "cc";
import TipMgr from "../../../../../lib/tips/TipMgr";
import { PupilAddResponse, PupilTrainResponse } from "../../../../../game/net/protocol/Pupil";
import { PupilRouteName } from "../../PupilRoute";
import { PupilWaitAdapter } from "../../adapter/PupilWaitAdapter";
import { ListView } from "../../../../../game/common/ListView";
import { Tween } from "cc";
import { PupilCompleteAdapter } from "../../adapter/PupilCompleteAdapter";
import { Vec3 } from "cc";
import { instantiate } from "cc";
import Tool from "../../../../../lib/common/Tool";
import { tween } from "cc";
import { v3 } from "cc";
import { UITransform } from "cc";
import MsgMgr from "../../../../../lib/event/MsgMgr";
import MsgEnum from "../../../../../game/event/MsgEnum";
import ResMgr from "../../../../../lib/common/ResMgr";
import { ItemEnum } from "../../../../../lib/common/ItemEnum";
import { PupilAni, PupilAniEnum } from "./PupilAni";
import { PlayerRouteName, PublicRouteName } from "../../../../player/PlayerConstant";
import { Prefab } from "cc";
import { BadgeMgr, BadgeType } from "../../../../../game/mgr/BadgeMgr";
import { AudioMgr, AudioName } from "../../../../../../platform/src/AudioHelper";
import { TipsMgr } from "../../../../../../platform/src/TipsHelper";
import { PupilAudioName } from "../../PupilConstant";
import { HdVipCardRouteItem } from "../../../../hd_vipcard/HdVipCardRoute";
import { RightConditionEnum, RightEnum } from "../../../../../game/GameDefine";
import { LangMgr } from "../../../../../game/mgr/LangMgr";
import { ConfirmMsg } from "../../../../../game/ui/UICostConfirm";
import { AdapterView } from "../../../../../../platform/src/core/ui/adapter_view/AdapterView";
import { ItemCost } from "../../../../../game/common/ItemCost";
import TickerMgr from "../../../../../lib/ticker/TickerMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../../../lib/utils/FmUtils";
import { AzstModule } from "../../../../azst/AzstModule";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import UIPupilAward from "./UIPupilAward";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**竖向排布拖动*/
export const SCROLL_VERTICAL: number = 2;

@ccclass("UIPupilPage")
export class UIPupilPage extends UINode {
  private _waitAdapter: PupilWaitAdapter;
  private _completeAdapter: PupilCompleteAdapter;
  protected _openAct: boolean = true;

  private _ticker_di_zi_appear: number;

  // 当前选中页
  private _tab1 = 1;

  // 自动修炼
  private _autoTrain = false;

  get autoTrain() {
    return this._autoTrain;
  }
  set autoTrain(value: boolean) {
    this._autoTrain = value;
    this.getNode("Checkmark").active = value;
  }

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_PUPIL}?prefab/ui/UIPupilPage`;
  }

  protected onRegEvent() {
    MsgMgr.on(MsgEnum.ON_PUPIL_UPDATE, this.refresh, this);
    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.refreshTrain, this);
  }

  protected onDelEvent() {
    MsgMgr.off(MsgEnum.ON_PUPIL_UPDATE, this.refresh, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.refreshTrain, this);
  }

  private refresh() {
    this.onSwitchTab(this._tab1);
  }

  protected async onEvtShow() {
    this.onSwitchTab(1);

    PupilModule.api.getTrain(() => {
      this.refreshTrain();
    });

    // 列表初始化
    this._waitAdapter = new PupilWaitAdapter(this.getNode("node_wait_item"));
    // this.getNode("list_wait").addComponent(LinearLayoutManager);
    this.getNode("list_wait").getComponent(AdapterView).setAdapter(this._waitAdapter);

    this._completeAdapter = new PupilCompleteAdapter(this.getNode("node_complete_item"));
    this.getNode("list_complete").getComponent(ListView).setAdapter(this._completeAdapter);

    this.autoTrain = false;

    BadgeMgr.instance.setBadgeId(this.getNode("btn_train"), BadgeType.UIMajorCity.btn_pupil.btn_train.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_add"), BadgeType.UIMajorCity.btn_pupil.btn_add.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_renming"), BadgeType.UIMajorCity.btn_pupil.btn_renming.id);

    // 挑战
    let ticketSize = AzstModule.data?.azstMessage?.ticketSize ?? 0;
    this.getNode("btn_to_fight").getComponent(ItemCost).setCustom(1082, "%r/%c", ticketSize);

    // 全空闲手指引导
    // const haslist = PupilModule.data.pupilTrainMsg.trainSlotList.filter((e) => {
    //   return e.pupilId > 0;
    // });
    // // 全空闲手指引导
    // if (!haslist || haslist?.length == 0) {
    //   // 全空闲
    //   TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopFinger, { stepId: 75 });
    // }

    // // 有体力
    // const tiliList = PupilModule.data.pupilTrainMsg.trainSlotList.filter((e) => {
    //   return e.vitality > 0;
    // });
    // if (haslist.length == PupilModule.data.pupilTrainMsg.trainSlotList.length) {
    //   // 满员
    //   if (tiliList.length > 0) {
    //     // 有体力
    //     TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopFinger, { stepId: 76 });
    //   }
    // }
  }

  /** 刷新界面 */
  private refreshTrain() {
    // 更新阅历
    // this.getNode("lbl_res_num_3").getComponent(Label).string = `${Formate.format(
    //   PlayerModule.data.getItemNum(ItemEnum.阅历_3)
    // )}`;
    this.getNode("item_res_3").getComponent(ItemCost).setItemId(ItemEnum.阅历_3);

    // 取默认配置
    const cfg = JsonMgr.instance.jsonList.c_pupil[1];

    // 玩家等级
    const playerLevel = PlayerModule.data.getPlayerInfo().level;

    // 训练槽
    const slotList = PupilModule.data.pupilTrainMsg.trainSlotList;

    for (let i = 0; i < 5; i++) {
      const node = this.getNode(`btn_p${i + 1}`);
      node.off(Node.EventType.TOUCH_END, this.onSlotClick, node);

      // 绑定事件
      node.on(Node.EventType.TOUCH_END, this.onSlotClick, node);

      if (playerLevel < cfg.unlockPlaceList[i] || slotList.length <= i) {
        // 未解锁
        node.getChildByName("bg").active = true;
        node.getChildByName("lbl_empty").active = false;
        node.getChildByName("PupilAni").active = false;
        node.getChildByName("bg_lock").active = true;
        node.getChildByName("progress_bar").active = false;
        node.getChildByName("lbl_jingli").active = false;
        continue;
      }

      const slotInfo = slotList[i];
      // 修炼进度
      const progress = node.getChildByName("progress_bar").getComponent(ProgressBar);

      if (slotInfo.pupilId == -1) {
        // 待招徒弟
        node.getChildByName("bg").active = true;
        node.getChildByName("lbl_empty").active = true;
        node.getChildByName("PupilAni").active = false;
        node.getChildByName("progress_bar").active = false;
        progress.progress = 0;
      } else {
        // 修炼
        node.getChildByName("bg").active = false;
        node.getChildByName("lbl_empty").active = false;
        node.getChildByName("PupilAni").active = true;
        node.getChildByName("progress_bar").active = true;

        // 修炼进度
        let pupilMsg = PupilModule.data.allPupilMap[slotInfo.pupilId];
        const cfg1 = JsonMgr.instance.jsonList.c_pupil[pupilMsg.ownInfo.talentId];
        progress.progress = (slotInfo.train * cfg1.trainAdd) / cfg1.finish;

        // 形象
        node
          .getChildByName("PupilAni")
          .getComponent(PupilAni)
          .setAniByNameId(pupilMsg.ownInfo.nameId, pupilMsg.ownInfo.adultAttrList.length);
      }

      node.getChildByName("bg_lock").active = false;
      node.getChildByName("lbl_jingli").active = true;

      // 剩余体力
      const lblJingli: Label = node.getChildByName("lbl_jingli").getComponent(Label);
      lblJingli.string = `${slotInfo.vitality}`;
    }
  }

  /** 槽点击事件 */
  private onSlotClick(event: EventTouch) {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击单个弟子图标);

    const nodeTarget: Node = event.target;
    let strIdx = nodeTarget.name.substring(5, 6);
    let idx = parseInt(strIdx) - 1;

    // 取默认配置
    const cfg = JsonMgr.instance.jsonList.c_pupil[1];

    // 玩家等级
    const playerLevel = PlayerModule.data.getPlayerInfo().level;

    if (playerLevel < cfg.unlockPlaceList[idx]) {
      TipMgr.showTip(`主角等级达到${cfg.unlockPlaceList[idx]}级解锁`);
      return;
    }

    const slotInfo = PupilModule.data.pupilTrainMsg.trainSlotList[idx];

    if (slotInfo.pupilId == -1) {
      TipMgr.showTip(`请先招徒 `);
    } else {
      UIMgr.instance.showDialog(PupilRouteName.UIPupilDetailPop, {
        pupilId: slotInfo.pupilId,
      });
    }
  }

  /** 切换页面 */
  private onSwitchTab(tab: number) {
    this._tab1 = tab;
    this.getNode("node_train").active = tab == 1;
    this.getNode("node_marry").active = tab == 2;

    this.getNode("item_res_3").active = tab == 1;

    this.getNode("btn_tab_train").getChildByName("node_check").active = tab == 1;
    this.getNode("btn_tab_train").getChildByName("node_uncheck").active = tab != 1;
    this.getNode("btn_tab_marry").getChildByName("node_check").active = tab == 2;
    this.getNode("btn_tab_marry").getChildByName("node_uncheck").active = tab != 2;

    if (tab == 2) {
      this.switchTab2(1);
    }
  }

  private on_click_btn_tab_train() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击下方页签);
    this.onSwitchTab(1);
  }

  private on_click_btn_tab_marry() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击下方页签);
    this.onSwitchTab(2);
  }

  private playFlyAni() {
    // 切换到其他地方就不自动修炼了
    if (!this.node || !this.getNode("node_train").activeInHierarchy) {
      this.autoTrain = false;
      return;
    }

    let hasPupil = this.checkPupil();
    let hasTili = this.checkTili();
    let hasAdult = this.checkCanOut();

    // 新增：校验未结伴弟子是否达最大值
    const unmarriedCount = this.getUnmarriedPupilIdList().length;
    const maxUnmarried = this.getMaxUnmarriedPupil();
    if (unmarriedCount >= maxUnmarried) {
      TipMgr.showTip(`弟子成年个数已达最大值${maxUnmarried}，请先进行结伴`);
      this.autoTrain = false;
      return;
    }

    const soltList = PupilModule.data.pupilTrainMsg.trainSlotList;
    for (let i = 0; i < soltList.length; i++) {
      let slotInfo = soltList[i];
      if (slotInfo.pupilId > 0) {
        hasPupil = true;
        if (slotInfo.vitality > 0) {
          hasTili = true;

          let nodePupil = this.getNode("btn_p" + (i + 1));
          this.flyEnergyFun(nodePupil, this.getNode("bg_item"));

          nodePupil.getChildByName("PupilAni").getComponent(PupilAni).playAni(PupilAniEnum.train);
        }
      }
    }

    if (!hasPupil) {
      TipMgr.showTip(`请先招募弟子`);
      this.autoTrain = false;
      return;
    }

    if (!hasTili && !hasAdult) {
      this.addTili();
      this.autoTrain = false;
      return;
    }

    AudioMgr.instance.playEffect(PupilAudioName.Effect.修炼成功按钮);
    if (this.autoTrain) {
      PupilModule.api.trainTen((data: PupilTrainResponse) => {
        this.refreshTrain();
        // 成年徒弟
        if (data.newAdultList.length > 0) {
          UIMgr.instance.showDialog(
            PupilRouteName.UIPupilNewAdultList,
            {
              newAdultList: [...data.newAdultList],
            },
            (resp: any) => {
              if (resp?.goMarry) {
                this.onSwitchTab(2);
              } else {
                this.playFlyAni();
              }
            }
          );
          // this.autoTrain = false;
        } else {
          if (this.autoTrain == true) {
            new Tween(this.node)
              .delay(0.05)
              .call(() => {
                this.playFlyAni();
              })
              .start();
          }
        }
        // 道具掉落
        if (data.dropItemList.length > 0) {
          this.handleDropItems(data.dropItemList);
        }
      });
    } else {
      PupilModule.api.trainTen((data: PupilTrainResponse) => {
        this.refreshTrain();
        // 成年徒弟
        if (data.newAdultList.length > 0) {
          UIMgr.instance.showDialog(
            PupilRouteName.UIPupilNewAdultList,
            {
              newAdultList: [...data.newAdultList],
            },
            (resp: any) => {
              if (resp?.goMarry) {
                this.onSwitchTab(2);
              } else {
                this.playFlyAni();
              }
            }
          );
        }
        // 道具掉落
        if (data.dropItemList.length > 0) {
          this.handleDropItems(data.dropItemList);
        }
      });
    }
  }

  private handleDropItems(dropItemList: any[]) {
    if (dropItemList.length > 0) {
      RouteManager.uiRouteCtrl.showRoute(UIPupilAward, {
        payload: {
          toWorldPosition: this.getNode("btn_to_fight").getWorldPosition(),
          itemList: dropItemList,
        },
      });
    }
  }

  // 修炼按钮事件
  private on_click_btn_train() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);

    this.playFlyAni();
  }

  private on_click_btn_to_fight() {
    //
    UIMgr.instance.showDialog("UIAzst");
  }

  /**判断是否有可以出师的弟子 */
  private checkCanOut() {
    const soltList = PupilModule.data.pupilTrainMsg.trainSlotList;
    for (let i = 0; i < soltList.length; i++) {
      let slotInfo = soltList[i];
      if (slotInfo.pupilId <= 0) continue;

      let train = slotInfo.train;
      let pupilInfo = PupilModule.data.allPupilMap[slotInfo.pupilId];
      let talentId = pupilInfo.ownInfo.talentId;

      let db = JsonMgr.instance.jsonList.c_pupil[talentId];
      let trainAdd = db.trainAdd;
      let finish = db.finish;

      let bar = Math.ceil(train * trainAdd);
      if (bar >= finish) {
        return true;
      }
    }

    return false;
  }

  /**判断是否体力不足 */
  private checkTili() {
    const soltList = PupilModule.data.pupilTrainMsg.trainSlotList;
    let bool = false;
    for (let i = 0; i < soltList.length; i++) {
      let slotInfo = soltList[i];
      if (slotInfo.pupilId <= 0) continue;

      if (slotInfo.vitality > 0) {
        bool = true;

        let nodePupil = this.getNode("btn_p" + (i + 1));
        this.flyEnergyFun(nodePupil, this.getNode("bg_item"));

        nodePupil.getChildByName("PupilAni").getComponent(PupilAni).playAni(PupilAniEnum.train);
        return true;
      }
    }

    return bool;
  }

  /**判断是否有弟子 */
  private checkPupil() {
    const soltList = PupilModule.data.pupilTrainMsg.trainSlotList;
    for (let i = 0; i < soltList.length; i++) {
      let slotInfo = soltList[i];
      if (slotInfo.pupilId > 0) {
        return true;
      }
    }

    return false;
  }

  /** 弟子招募 */
  private on_click_btn_add() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击招徒按钮);
    if (PupilModule.data.pupilTrainMsg.trainSlotList.findIndex((item) => item.pupilId == -1) == -1) {
      TipMgr.showTip(`当前槽位已满 `);
      return;
    }

    PupilModule.api.addPupil((data: PupilAddResponse) => {
      log.log("data招徒信息=======", data);
      let skt = this.getNode("di_zi_appear" + (data.index + 1)).getComponent(sp.Skeleton);
      skt.node.active = true;

      skt.setEventListener((animation, event) => {
        if (event["data"].name === "appear") {
          skt.setEventListener(null);
          this.refreshTrain();
        }
      });

      skt.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("animation" == trackEntry.animation.name) {
          skt.setCompleteListener(null);
          skt.node.active = false;
          this._ticker_di_zi_appear = TickerMgr.setTimeout(0.5, () => {
            if (isValid(this.node) == false) {
              return;
            }
            UIMgr.instance.showDialog(PupilRouteName.UIPupilAddResPop, { pupilId: data.pupilMessage.id });
          });
        }
      });

      skt.setAnimation(0, "animation", false);
    });
  }

  /**点击补充体力 */
  private on_click_btn_tili() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击补充体力按钮);
    this.addTili();
  }

  private addTili() {
    if (PupilModule.data.pupilTrainMsg.trainSlotList.findIndex((item) => item.pupilId > 0) == -1) {
      TipMgr.showTip(`请先招募弟子`);
      return;
    }
    // 跳转到弟子补充活力界面
    UIMgr.instance.showDialog(PupilRouteName.UIAddVitalityPop, {}, () => {
      this.refreshTrain();
    });
  }

  /** 弟子委任 */
  private on_click_btn_renming() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击任命按钮);
    UIMgr.instance.showDialog(PupilRouteName.UIPupilWorkSlot);
  }

  /** 弟子招募预览 */
  private on_click_btn_preview() {
    AudioMgr.instance.playEffect(1555);
    UIMgr.instance.showDialog(PupilRouteName.UIPupilRatePreview);
  }

  /** 自动修炼勾选 */
  private on_click_toggle_auto() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);

    this.autoTrain = !this.autoTrain;
    if (this.autoTrain) {
      if (!PlayerModule.service.hasRight(RightEnum.弟子一键培养)) {
        this.autoTrain = false;

        // 提示不能开启
        const msg: ConfirmMsg = {
          msg: LangMgr.txMsgCode(
            116,
            [FmUtils.getRightCondition(RightEnum.弟子一键培养, RightConditionEnum.Level)],
            "提示"
          ),
          okText: LangMgr.txMsgCode(140, [], "前往"),
        };

        UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
          if (resp?.ok) {
            UIMgr.instance.showDialog(HdVipCardRouteItem.UICardMain);
          }
        });
        return;
      }
    }

    if (this.autoTrain) {
      this.playFlyAni();
    }
  }

  private switchTab2(tab: number) {
    this.getNode("btn_tab_wait").getChildByName("node_active").active = tab == 1;
    this.getNode("btn_tab_wait").getChildByName("node_unactive").active = tab != 1;

    this.getNode("btn_tab_complete").getChildByName("node_active").active = tab == 2;
    this.getNode("btn_tab_complete").getChildByName("node_unactive").active = tab != 2;

    this.getNode("list_wait").active = tab == 1;
    this.getNode("wait_num_layer").active = tab == 1;
    this.getNode("list_complete").active = tab == 2;
    this.getNode("complete_num_layer").active = tab == 2;

    if (tab == 1) {
      // 调用封装后的方法获取未婚弟子ID列表
      const unmarriedPupilIdList = this.getUnmarriedPupilIdList();

      this._waitAdapter.setDatas(unmarriedPupilIdList);

      let str1 = unmarriedPupilIdList.length;
      let str2 = JsonMgr.instance.jsonList.c_pupil[1].max;
      this.getNode("wait_num_layer").getChildByName("Label").getComponent(Label).string = str1 + "/" + str2;
    } else if (tab == 2) {
      let list = PupilModule.service.marriedPupilIdList;
      this._completeAdapter.setDatas(list);
      this.getNode("complete_num_layer").getChildByName("Label").getComponent(Label).string = String(list.length);
    }
  }

  private on_click_btn_tab_wait() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.结伴界面上方已结伴未结伴页签);
    this.switchTab2(1);
  }

  private on_click_btn_tab_complete() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.结伴界面上方已结伴未结伴页签);
    this.switchTab2(2);
  }

  private flyEnergyFun(nodeStart: Node, nodeEnd: Node) {
    const uiTransform = this.node.getComponent(UITransform);

    let startPos: Vec3 = new Vec3(0, 0, 0);
    uiTransform.convertToNodeSpaceAR(nodeStart.worldPosition, startPos);

    let endPos: Vec3 = new Vec3(0, 0, 0);
    uiTransform.convertToNodeSpaceAR(nodeEnd.worldPosition, endPos);

    // 克隆节点
    let node = instantiate(this.getNode("energyItem"));
    this.node.addChild(node);
    node.setPosition(startPos);
    node.active = true;

    // 生成中间停顿点
    const midPos = Tool.createRandomPos(startPos, {
      area: new Vec3(300, 300, 0),
      space: new Vec3(100, 0, 0),
      offset: new Vec3(0, -0.0, 0),
    });
    const midPos2 = Tool.createRandomPos(startPos, {
      area: new Vec3(200, 300, 0),
      space: new Vec3(0, 0, 0),
      offset: new Vec3(0, 1, 0),
    });

    const tempVec3 = v3();
    const flyTime = 0.75;

    // 资源图标动画
    tween(this.getNode("bg_item_icon"))
      .delay(flyTime)
      .to(0.1, { scale: v3(1.25, 1.25, 1) }, { easing: "sineInOut" })
      .to(0.1, { scale: v3(0.75, 0.75, 1) }, { easing: "sineInOut" })
      .to(0.05, { scale: v3(1.08, 1.08, 1) }, { easing: "sineInOut" })
      .to(0.05, { scale: v3(1, 1, 1) }, { easing: "sineInOut" })
      .start();

    // 节点飞行图标
    tween(node)
      .to(
        flyTime,
        { position: endPos },
        {
          onUpdate: (target, ratio) => {
            Tool.bezierCurve(ratio, startPos, midPos, midPos2, endPos, tempVec3);
            node.setPosition(tempVec3);
          },
        }
      )
      .call(() => {
        node.getChildByName("bg_item_icon_fly").active = false;
      })
      .delay(0.3)
      .call(() => {
        node.destroy();
      })
      .start();
  }

  // 获取最大允许未结伴弟子数
  private getMaxUnmarriedPupil(): number {
    const cfg = JsonMgr.instance.jsonList.c_pupil[1];
    return cfg.max;
  }

  private on_click_btn_rank() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击弟子榜单);
    UIMgr.instance.showDialog(PupilRouteName.UIPupilRankList);
  }

  private on_click_btn_request() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    TipMgr.showTip("开发中");
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 8 });
  }

  protected onEvtClose(): void {
    if (this._ticker_di_zi_appear) {
      TickerMgr.clearTimeout(this._ticker_di_zi_appear);
    }
  }
  /** 获取未婚弟子ID列表（已封装） */
  private getUnmarriedPupilIdList(): number[] {
    let unmarriedPupilIdList: number[] = [];
    let allPupilMap = PupilModule.data.allPupilMap;

    let list = Object.keys(allPupilMap).map(Number);
    for (let i = 0; i < list.length; i++) {
      let pupilId = list[i];
      let pupilMsg = allPupilMap[pupilId];
      if (pupilMsg.ownInfo.adultAttrList.length == 0) {
        continue;
      }
      if (!pupilMsg.partnerInfo) {
        unmarriedPupilIdList.push(pupilId);
      }
    }

    // 排序
    unmarriedPupilIdList.sort((a, b) => {
      let a1 = allPupilMap[a];
      let b1 = allPupilMap[b];
      if (a1.ownInfo.adultAttrList[1] == b1.ownInfo.adultAttrList[1]) {
        return b1.addStamp - a1.addStamp;
      }
      return b1.ownInfo.adultAttrList[1] - a1.ownInfo.adultAttrList[1];
    });

    // 任命的排前端
    for (let i = 0; i < PupilModule.data.pupilTrainMsg.workSlotList.length; i++) {
      let jobPupilId = PupilModule.data.pupilTrainMsg.workSlotList[i];
      if (jobPupilId > 0) {
        let index = unmarriedPupilIdList.indexOf(jobPupilId);
        if (index != -1) {
          unmarriedPupilIdList.splice(index, 1);
          unmarriedPupilIdList.unshift(jobPupilId);
        }
      }
    }

    return unmarriedPupilIdList;
  }
}
