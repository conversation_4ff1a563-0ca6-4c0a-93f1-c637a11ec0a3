import { _decorator, instantiate } from "cc";
import { BaseCtrl } from "../platform/src/core/BaseCtrl";
import { RouteCtrl } from "../platform/src/core/RouteCtrl";
import { BundleEnum } from "../platform/src/ResHelper";
import { JsonMgr } from "../GameScrpit/game/mgr/JsonMgr";
const { ccclass, property } = _decorator;

@ccclass("SceneBattle")
export class SceneBattle extends BaseCtrl {
  routeCtrl: RouteCtrl;

  // 暴击 闪避 吸血
  // {"a":{"a":100,"b":1001,"c":10,"d":[10000,5000,2500,1250,625,313,156,78,39,20],"e":23000,"f":17000,"g":3000},"b":[[{"a":1,"b":9001,"c":"9001_100","d":2229,"e":100,"f":{32:190,1:2320,33:218,2:485,34:214,3:210,35:214,4:208,36:216,37:107,21:210,22:190,23:190,24:214,25:10182,26:190,27:100,31:216},"g":[],"h":[],"i":[]}],[{"a":2,"b":9101,"c":"9101_100","d":2708,"e":100,"f":{32:198,1:2320,33:208,2:480,34:210,3:190,35:180,4:214,36:214,37:91,21:204,22:210,23:218,24:184,25:216,26:10180,27:104,31:198},"g":[],"h":[],"i":[]}]],"c":[],"d":[{"a":0,"b":[{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-270,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{11:{101:2050}}},{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-647,2]},"c":[],"d":[],"e":[]}],"e":[],"f":2,"g":{21:{101:1673}}}]},{"a":1,"b":[{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-270,1]},"c":[],"d":[],"e":[]},{"a":21,"b":{101:[269,4]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:1942},11:{101:1780}}},{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-567,2]},"c":[],"d":[],"e":[]}],"e":[],"f":2,"g":{21:{101:1375}}}]},{"a":2,"b":[{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-270,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{11:{101:1510}}},{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-502,2]},"c":[],"d":[],"e":[]}],"e":[],"f":2,"g":{21:{101:873}}}]},{"a":3,"b":[{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-270,1]},"c":[],"d":[],"e":[]},{"a":21,"b":{101:[269,4]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:1142},11:{101:1240}}},{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-639,2]},"c":[],"d":[],"e":[]}],"e":[],"f":2,"g":{21:{101:503}}}]},{"a":4,"b":[{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-270,1]},"c":[],"d":[],"e":[]},{"a":21,"b":{101:[269,4]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:772},11:{101:970}}},{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-588,2]},"c":[],"d":[],"e":[]}],"e":[],"f":2,"g":{21:{101:184}}}]},{"a":5,"b":[{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-270,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{11:{101:700}}},{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-566,2]},"c":[],"d":[],"e":[]}],"e":[],"f":2,"g":{21:{101:0}}}]}],"e":[[{"a":1,"b":9001,"c":"9001_100","d":2229,"e":100,"f":{32:190,1:2320,33:218,2:485,34:214,3:210,35:214,4:208,36:216,37:107,21:210,22:190,23:190,24:214,25:10182,26:190,27:100,31:216,101:700,102:0,103:0,104:0,105:0},"g":[],"h":[],"i":[]}],[{"a":2,"b":9101,"c":"9101_100","d":2708,"e":100,"f":{32:198,1:2320,33:208,2:480,34:210,3:190,35:180,4:214,36:214,37:91,21:204,22:210,23:218,24:184,25:216,26:10180,27:104,31:198,101:0,102:0,103:0,104:0,105:0},"g":[],"h":[],"i":[]}]],"f":{1:1,2:0}}

  // 反击 连击
  // {"a":{"a":100,"b":1001,"c":10,"d":[10000,5000,2500,1250,625,313,156,78,39,20],"e":23000,"f":17000,"g":3000},"b":[[{"a":1,"b":9001,"c":"9001_100","d":2640,"e":100,"f":{32:190,1:2275,33:196,2:495,34:210,3:206,35:206,4:212,36:206,37:98,21:198,22:212,23:218,24:10180,25:208,26:180,27:102,31:202},"g":[],"h":[],"i":[]}],[{"a":2,"b":9101,"c":"9101_100","d":2271,"e":100,"f":{32:194,1:2260,33:214,2:550,34:212,3:194,35:206,4:196,36:204,37:99,21:192,22:188,23:10184,24:210,25:212,26:190,27:109,31:186},"g":[],"h":[],"i":[]}]],"c":[],"d":[{"a":0,"b":[{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-301,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:1959}}},{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-344,1]},"c":[],"d":[],"e":[]}],"e":[{"a":11,"b":7,"c":21,"d":[{"a":21,"b":{101:[-301,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:1658}}},{"a":21,"b":3,"c":11,"d":[{"a":11,"b":{101:[-344,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{11:{101:1587}}}],"g":{11:{101:1931}}}]},{"a":1,"b":[{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-301,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:1357}}},{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-344,1]},"c":[],"d":[],"e":[]}],"e":[{"a":11,"b":7,"c":21,"d":[{"a":21,"b":{101:[-301,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:1056}}},{"a":21,"b":3,"c":11,"d":[{"a":11,"b":{101:[-344,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{11:{101:899}}},{"a":21,"b":3,"c":11,"d":[{"a":11,"b":{101:[-344,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{11:{101:555}}}],"g":{11:{101:1243}}}]},{"a":2,"b":[{"a":11,"b":1,"c":21,"d":[{"a":21,"b":{101:[-301,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:755}}},{"a":21,"b":1,"c":11,"d":[{"a":11,"b":{101:[-344,1]},"c":[],"d":[],"e":[]}],"e":[{"a":11,"b":7,"c":21,"d":[{"a":21,"b":{101:[-301,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{21:{101:454}}},{"a":21,"b":3,"c":11,"d":[{"a":11,"b":{101:[-344,1]},"c":[],"d":[],"e":[]}],"e":[],"g":{11:{101:0}}}],"g":{11:{101:211}}}]}],"e":[[{"a":1,"b":9001,"c":"9001_100","d":2640,"e":100,"f":{32:190,1:2275,33:196,2:495,34:210,3:206,35:206,4:212,36:206,37:98,21:198,22:212,23:218,24:10180,25:208,26:180,27:102,31:202,101:0,102:0,103:0,104:0,105:0},"g":[],"h":[],"i":[]}],[{"a":2,"b":9101,"c":"9101_100","d":2271,"e":100,"f":{32:194,1:2260,33:214,2:550,34:212,3:194,35:206,4:196,36:204,37:99,21:192,22:188,23:10184,24:210,25:212,26:190,27:109,31:186,101:454,102:0,103:0,104:0,105:0},"g":[],"h":[],"i":[]}]],"f":{1:0,2:1}}

  battleData = {
    a: { a: 100, b: 1001, c: 10, d: [10000, 5000, 2500, 1250, 625, 313, 156, 78, 39, 20], e: 23000, f: 17000, g: 3000 },
    b: [
      [
        {
          a: 1,
          b: 9001,
          c: "9001_100",
          d: 2229,
          e: 100,
          f: {
            32: 190,
            1: 2320,
            33: 218,
            2: 485,
            34: 214,
            3: 210,
            35: 214,
            4: 208,
            36: 216,
            37: 107,
            21: 210,
            22: 190,
            23: 190,
            24: 214,
            25: 10182,
            26: 190,
            27: 100,
            31: 216,
          },
          g: [],
          h: [],
          i: [],
        },
      ],
      [
        {
          a: 2,
          b: 9101,
          c: "9101_100",
          d: 2708,
          e: 100,
          f: {
            32: 198,
            1: 2320,
            33: 208,
            2: 480,
            34: 210,
            3: 190,
            35: 180,
            4: 214,
            36: 214,
            37: 91,
            21: 204,
            22: 210,
            23: 218,
            24: 184,
            25: 216,
            26: 10180,
            27: 104,
            31: 198,
          },
          g: [],
          h: [],
          i: [],
        },
      ],
    ],
    c: [],
    d: [
      {
        a: 0,
        b: [
          {
            a: 21,
            b: 1,
            c: 11,
            d: [{ a: 11, b: { 101: [-270, 1] }, c: [], d: [], e: [] }],
            e: [],
            g: { 11: { 101: 2050 } },
          },
          {
            a: 11,
            b: 1,
            c: 21,
            d: [{ a: 21, b: { 101: [-647, 2] }, c: [], d: [], e: [] }],
            e: [],
            f: 2,
            g: { 21: { 101: 1673 } },
          },
        ],
      },
      {
        a: 1,
        b: [
          {
            a: 21,
            b: 1,
            c: 11,
            d: [
              { a: 11, b: { 101: [-270, 1] }, c: [], d: [], e: [] },
              { a: 21, b: { 101: [269, 4] }, c: [], d: [], e: [] },
            ],
            e: [],
            g: { 21: { 101: 1942 }, 11: { 101: 1780 } },
          },
          {
            a: 11,
            b: 1,
            c: 21,
            d: [{ a: 21, b: { 101: [-567, 2] }, c: [], d: [], e: [] }],
            e: [],
            f: 2,
            g: { 21: { 101: 1375 } },
          },
        ],
      },
      {
        a: 2,
        b: [
          {
            a: 21,
            b: 1,
            c: 11,
            d: [{ a: 11, b: { 101: [-270, 1] }, c: [], d: [], e: [] }],
            e: [],
            g: { 11: { 101: 1510 } },
          },
          {
            a: 11,
            b: 1,
            c: 21,
            d: [{ a: 21, b: { 101: [-502, 2] }, c: [], d: [], e: [] }],
            e: [],
            f: 2,
            g: { 21: { 101: 873 } },
          },
        ],
      },
      {
        a: 3,
        b: [
          {
            a: 21,
            b: 1,
            c: 11,
            d: [
              { a: 11, b: { 101: [-270, 1] }, c: [], d: [], e: [] },
              { a: 21, b: { 101: [269, 4] }, c: [], d: [], e: [] },
            ],
            e: [],
            g: { 21: { 101: 1142 }, 11: { 101: 1240 } },
          },
          {
            a: 11,
            b: 1,
            c: 21,
            d: [{ a: 21, b: { 101: [-639, 2] }, c: [], d: [], e: [] }],
            e: [],
            f: 2,
            g: { 21: { 101: 503 } },
          },
        ],
      },
      {
        a: 4,
        b: [
          {
            a: 21,
            b: 1,
            c: 11,
            d: [
              { a: 11, b: { 101: [-270, 1] }, c: [], d: [], e: [] },
              { a: 21, b: { 101: [269, 4] }, c: [], d: [], e: [] },
            ],
            e: [],
            g: { 21: { 101: 772 }, 11: { 101: 970 } },
          },
          {
            a: 11,
            b: 1,
            c: 21,
            d: [{ a: 21, b: { 101: [-588, 2] }, c: [], d: [], e: [] }],
            e: [],
            f: 2,
            g: { 21: { 101: 184 } },
          },
        ],
      },
      {
        a: 5,
        b: [
          {
            a: 21,
            b: 1,
            c: 11,
            d: [{ a: 11, b: { 101: [-270, 1] }, c: [], d: [], e: [] }],
            e: [],
            g: { 11: { 101: 700 } },
          },
          {
            a: 11,
            b: 1,
            c: 21,
            d: [{ a: 21, b: { 101: [-566, 2] }, c: [], d: [], e: [] }],
            e: [],
            f: 2,
            g: { 21: { 101: 0 } },
          },
        ],
      },
    ],
    e: [
      [
        {
          a: 1,
          b: 9001,
          c: "9001_100",
          d: 2229,
          e: 100,
          f: {
            32: 190,
            1: 2320,
            33: 218,
            2: 485,
            34: 214,
            3: 210,
            35: 214,
            4: 208,
            36: 216,
            37: 107,
            21: 210,
            22: 190,
            23: 190,
            24: 214,
            25: 10182,
            26: 190,
            27: 100,
            31: 216,
            101: 700,
            102: 0,
            103: 0,
            104: 0,
            105: 0,
          },
          g: [],
          h: [],
          i: [],
        },
      ],
      [
        {
          a: 2,
          b: 9101,
          c: "9101_100",
          d: 2708,
          e: 100,
          f: {
            32: 198,
            1: 2320,
            33: 208,
            2: 480,
            34: 210,
            3: 190,
            35: 180,
            4: 214,
            36: 214,
            37: 91,
            21: 204,
            22: 210,
            23: 218,
            24: 184,
            25: 216,
            26: 10180,
            27: 104,
            31: 198,
            101: 0,
            102: 0,
            103: 0,
            104: 0,
            105: 0,
          },
          g: [],
          h: [],
          i: [],
        },
      ],
    ],
    f: { 1: 1, 2: 0 },
  };

  async start() {
    // 加载配置
    await JsonMgr.instance.loadConfigOne("c_characterArt");
    await JsonMgr.instance.loadConfigOne("c_fightCharacter");
    await JsonMgr.instance.loadConfigOne("c_fightBullet");
    await JsonMgr.instance.loadConfigOne("c_spineShow");
    await JsonMgr.instance.loadConfigOne("c_effect");
    await JsonMgr.instance.loadConfigOne("c_music");

    let pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_FIGHT, "prefab/fight/FIghtIndex");
    let nodeBattle = instantiate(pb);

    this.routeCtrl = this.getNode("root").getComponent(RouteCtrl);

    this.routeCtrl.showNode(nodeBattle, { battleData: this.battleData });
  }
}
