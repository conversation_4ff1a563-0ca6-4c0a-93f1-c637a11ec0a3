{"skeleton": {"hash": "/mTQ/QiPc43yz/pFP4MbBiqmVmw", "spine": "3.8.99", "images": "../熊猫/首充龙卷出现/images/", "audio": "D:/spine导出/Z_新主角/熊猫/../主角1"}, "bones": [{"name": "root"}, {"name": "qian_zj_all", "parent": "root", "length": 113.37, "rotation": 0.39, "x": -27.59, "y": -17.83}, {"name": "tx", "parent": "qian_zj_all", "length": 126.03, "rotation": 0.18, "x": -57.28, "y": -19.47, "scaleX": -2.4651, "scaleY": 2.4651}, {"name": "heimun", "parent": "root", "x": 224.27, "y": 124.03, "scaleX": 45.2929, "scaleY": 70.9369}, {"name": "xuli_q", "parent": "root", "x": -37.25, "y": -47.01, "scaleX": 2.1291, "scaleY": 2.1291}, {"name": "guanci001_add", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add2", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add3", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add4", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add5", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add6", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanman001", "parent": "xuli_q"}, {"name": "guanquan", "parent": "xuli_q"}, {"name": "guanquan2", "parent": "xuli_q"}, {"name": "guanci001_add7", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add8", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add9", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add10", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add11", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add12", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanci001_add13", "parent": "xuli_q", "scaleX": 0.608, "scaleY": 0.9514}, {"name": "guanman1", "parent": "xuli_q"}, {"name": "guanquan3", "parent": "xuli_q"}, {"name": "guanquan4", "parent": "xuli_q"}, {"name": "all", "parent": "root", "x": -859.73, "y": 389.88, "color": "ff0000ff"}, {"name": "all2", "parent": "all", "x": 7.39, "y": 87.91, "color": "ff0000ff"}, {"name": "xizhen", "parent": "all2", "length": 100, "rotation": -7.34, "x": 22.57, "y": 3.46, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "xizhen2", "parent": "all2", "length": 100, "rotation": -7.34, "x": 43.06, "y": 5.14, "scaleX": 3.236, "scaleY": 0.2515}, {"name": "xizhen11", "parent": "all2", "length": 100, "rotation": -7.34, "x": 21.78, "y": -2.69, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "kuang", "parent": "all", "x": 275.06}, {"name": "xizhen4", "parent": "all", "length": 100, "rotation": -7.34, "x": 29.75, "y": 89.74, "scaleX": 3.236, "scaleY": 0.0301}, {"name": "all3", "parent": "all", "x": 14.96, "y": -101.29, "color": "ff0000ff"}, {"name": "xizhen5", "parent": "all3", "length": 100, "rotation": 5.49, "x": 12.83, "y": -1.37, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "xizhen6", "parent": "all3", "length": 100, "rotation": 5.49, "x": 35.41, "y": -2.98, "scaleX": 3.236, "scaleY": 0.2515}, {"name": "xizhen12", "parent": "all3", "length": 100, "rotation": 5.49, "x": 67.09, "y": 21.81, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "xizhen9", "parent": "all", "length": 100, "rotation": 1.51, "x": 35.33, "y": -24.12, "scaleX": 3.236, "scaleY": 1.4857}, {"name": "xizhen10", "parent": "all", "length": 100, "rotation": -3.24, "x": 46.95, "y": 35.09, "scaleX": 3.236, "scaleY": 1.2129}, {"name": "light", "parent": "all", "x": 23.8, "y": -3.31, "scaleX": 2.1882, "scaleY": 2.9235}, {"name": "lizi", "parent": "all", "rotation": -41.67, "x": 56.56, "y": -95.97, "scaleX": 4.1818, "scaleY": 4.1818, "color": "ffbc00ff"}, {"name": "xizhen13", "parent": "all", "length": 100, "rotation": -1.05, "x": -34.39, "y": 24.82, "scaleX": 3.236, "scaleY": 0.4848}, {"name": "bone36", "parent": "root", "x": -593.3, "y": 298, "color": "002cffff"}, {"name": "lonxmegjux<PERSON>ll", "parent": "root", "x": -0.74, "y": 3.02, "color": "ff0000ff"}, {"name": "lizi_zonxmeg", "parent": "lonxmegjux<PERSON>ll", "length": 1224.32, "x": -198.73, "y": 46.85}, {"name": "0", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm1", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm2", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm3", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm4", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm5", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm6", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm7", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm8", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm9", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm10", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm11", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm1xm2", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm1xm3", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xm1xm4", "parent": "lizi_zonxmeg", "length": 755.54}, {"name": "xma_zhuti", "parent": "lonxmegjux<PERSON>ll", "length": 1873.15, "x": -198.73, "y": 46.85}, {"name": "shuyxme_zonxmeg", "parent": "lonxmegjux<PERSON>ll", "length": 2022.54, "x": -198.73, "y": 46.85}, {"name": "xmbonxmexm6", "parent": "shuyxme_zonxmeg"}, {"name": "xmbonxmexm7", "parent": "shuyxme_zonxmeg"}, {"name": "xmbonxmexm8", "parent": "shuyxme_zonxmeg"}, {"name": "xmbonxmexm9", "parent": "shuyxme_zonxmeg", "x": -234.38, "y": -52.43}, {"name": "shuyxme_zonxmegxm2", "parent": "lonxmegjux<PERSON>ll", "length": 2022.54, "x": -198.73, "y": 46.85}, {"name": "xmbonxmexm1xm5", "parent": "shuyxme_zonxmegxm2", "x": -234.38, "y": -52.43}, {"name": "xmbonxmexm1xm6", "parent": "shuyxme_zonxmegxm2"}, {"name": "xmbonxmexm1xm7", "parent": "shuyxme_zonxmegxm2"}, {"name": "xmbonxmexm1xm8", "parent": "shuyxme_zonxmegxm2"}, {"name": "lonxmegjuxmanall2", "parent": "root", "x": -0.74, "y": 3.02, "color": "ff0000ff"}, {"name": "lizi_zonxmeg2", "parent": "lonxmegjuxmanall2", "length": 1224.32, "x": -198.73, "y": 46.85}, {"name": "1", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm12", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm13", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm14", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm15", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm16", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm17", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm18", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm19", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm20", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm21", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm22", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm1xm5", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm1xm6", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xm1xm7", "parent": "lizi_zonxmeg2", "length": 755.54}, {"name": "xma_zhuti2", "parent": "lonxmegjuxmanall2", "length": 1873.15, "x": -198.73, "y": 46.85}, {"name": "shuyxme_zonxmeg2", "parent": "lonxmegjuxmanall2", "length": 2022.54, "x": -198.73, "y": 46.85}, {"name": "xmbonxmexm10", "parent": "shuyxme_zonxmeg2"}, {"name": "xmbonxmexm11", "parent": "shuyxme_zonxmeg2"}, {"name": "xmbonxmexm12", "parent": "shuyxme_zonxmeg2"}, {"name": "xmbonxmexm13", "parent": "shuyxme_zonxmeg2"}, {"name": "shuyxme_zonxmegxm3", "parent": "lonxmegjuxmanall2", "length": 2022.54, "x": -198.73, "y": 46.85}, {"name": "xmbonxmexm1xm9", "parent": "shuyxme_zonxmegxm3"}, {"name": "xmbonxmexm1xm10", "parent": "shuyxme_zonxmegxm3"}, {"name": "xmbonxmexm1xm11", "parent": "shuyxme_zonxmegxm3", "x": 18.5, "y": 46.26}, {"name": "xmbonxmexm1xm12", "parent": "shuyxme_zonxmegxm3"}, {"name": "lonxmegjuxmanall3", "parent": "root", "x": -0.74, "y": 3.02, "color": "ff0000ff"}, {"name": "lizi_zonxmeg3", "parent": "lonxmegjuxmanall3", "length": 1224.32, "x": -198.73, "y": 46.85}, {"name": "2", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm23", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm24", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm25", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm26", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm27", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm28", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm29", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm30", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm31", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm32", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm33", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm1xm8", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm1xm9", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xm1xm10", "parent": "lizi_zonxmeg3", "length": 755.54}, {"name": "xma_zhuti3", "parent": "lonxmegjuxmanall3", "length": 1873.15, "x": -198.73, "y": 46.85}, {"name": "shuyxme_zonxmeg3", "parent": "lonxmegjuxmanall3", "length": 2022.54, "x": -198.73, "y": 46.85}, {"name": "xmbonxmexm14", "parent": "shuyxme_zonxmeg3"}, {"name": "xmbonxmexm15", "parent": "shuyxme_zonxmeg3"}, {"name": "xmbonxmexm16", "parent": "shuyxme_zonxmeg3"}, {"name": "xmbonxmexm17", "parent": "shuyxme_zonxmeg3", "x": 18.5, "y": 46.26}, {"name": "shuyxme_zonxmegxm4", "parent": "lonxmegjuxmanall3", "length": 2022.54, "x": -198.73, "y": 46.85}, {"name": "xmbonxmexm1xm13", "parent": "shuyxme_zonxmegxm4"}, {"name": "xmbonxmexm1xm14", "parent": "shuyxme_zonxmegxm4"}, {"name": "xmbonxmexm1xm15", "parent": "shuyxme_zonxmegxm4"}, {"name": "xmbonxmexm1xm16", "parent": "shuyxme_zonxmegxm4"}, {"name": "zi_guazai", "parent": "all", "x": 481.04, "y": -7.45}], "slots": [{"name": "img1", "bone": "heimun", "color": "0000004e"}, {"name": "texiao/skill1/guanci001", "bone": "guanci001_add", "blend": "additive"}, {"name": "texiao/skill1/guanci12", "bone": "guanci001_add13", "blend": "additive"}, {"name": "texiao/skill1/guanci2", "bone": "guanci001_add7", "blend": "additive"}, {"name": "texiao/skill1/guanci5", "bone": "guanci001_add4", "blend": "additive"}, {"name": "texiao/skill1/guanci9", "bone": "guanci001_add10", "blend": "additive"}, {"name": "texiao/skill1/guanci6", "bone": "guanci001_add5", "blend": "additive"}, {"name": "texiao/skill1/guanci10", "bone": "guanci001_add11", "blend": "additive"}, {"name": "texiao/skill1/guanci7", "bone": "guanci001_add6", "blend": "additive"}, {"name": "texiao/skill1/guanci11", "bone": "guanci001_add12", "blend": "additive"}, {"name": "texiao/skill1/guanci1", "bone": "guanci001_add2", "blend": "additive"}, {"name": "texiao/skill1/guanci3", "bone": "guanci001_add8", "blend": "additive"}, {"name": "texiao/skill1/guanci4", "bone": "guanci001_add3", "blend": "additive"}, {"name": "texiao/skill1/guanci8", "bone": "guanci001_add9", "blend": "additive"}, {"name": "texiao/skill1/guanman001", "bone": "guanman001", "blend": "additive"}, {"name": "texiao/skill1/guanman1", "bone": "guanman1", "blend": "additive"}, {"name": "texiao/skill1/guanquan", "bone": "guanquan", "blend": "additive"}, {"name": "texiao/skill1/guanquan3", "bone": "guanquan3", "blend": "additive"}, {"name": "texiao/skill1/guanquan2", "bone": "guanquan2", "blend": "additive"}, {"name": "texiao/skill1/guanquan4", "bone": "guanquan4", "blend": "additive"}, {"name": "line_fire", "bone": "kuang"}, {"name": "line_orange12", "bone": "xizhen11", "blend": "additive"}, {"name": "line_orange14", "bone": "xizhen13", "blend": "additive"}, {"name": "line_orange10", "bone": "xizhen9", "color": "ffffff6d", "blend": "additive"}, {"name": "line_orange11", "bone": "xizhen10", "color": "ffffff94", "blend": "additive"}, {"name": "line_orange5", "bone": "xizhen4", "blend": "additive"}, {"name": "line_orange3", "bone": "xizhen2"}, {"name": "line_orange2", "bone": "xizhen", "blend": "additive"}, {"name": "img", "bone": "bone36"}, {"name": "line_orange6", "bone": "xizhen5", "blend": "additive"}, {"name": "line_orange13", "bone": "xizhen12", "blend": "additive"}, {"name": "line_orange7", "bone": "xizhen6"}, {"name": "light2", "bone": "light", "color": "ff8c0094", "blend": "additive"}, {"name": "blood_bar", "bone": "qian_zj_all"}, {"name": "hit", "bone": "qian_zj_all"}, {"name": "attack1_to", "bone": "qian_zj_all", "attachment": "attack1_to"}, {"name": "attack2_to", "bone": "qian_zj_all", "attachment": "attack2_to"}, {"name": "status", "bone": "qian_zj_all"}, {"name": "skill1_to", "bone": "qian_zj_all", "attachment": "skill1_to"}, {"name": "spell_to", "bone": "qian_zj_all"}, {"name": "xmazhuti_000xm2", "bone": "xma_zhuti"}, {"name": "xmazhuti_000xm3", "bone": "xma_zhuti2"}, {"name": "xmazhuti_000xm4", "bone": "xma_zhuti3"}, {"name": "shuyxme0xm3", "bone": "xmbonxmexm14"}, {"name": "shuyxme0xm2", "bone": "xmbonxmexm10"}, {"name": "shuyxmexm16", "bone": "xmbonxmexm1xm13"}, {"name": "shuyxme0xm1", "bone": "xmbonxmexm6"}, {"name": "shuyxmexm7", "bone": "xmbonxmexm15"}, {"name": "shuyxmexm12", "bone": "xmbonxmexm1xm9"}, {"name": "shuyxmexm17", "bone": "xmbonxmexm1xm14"}, {"name": "shuyxmexm8", "bone": "xmbonxmexm16"}, {"name": "shuyxmexm9", "bone": "xmbonxmexm1xm5"}, {"name": "shuyxmexm4", "bone": "xmbonxmexm11"}, {"name": "shuyxmexm18", "bone": "xmbonxmexm1xm15"}, {"name": "shuyxmexm15", "bone": "xmbonxmexm17"}, {"name": "shuyxmexm13", "bone": "xmbonxmexm1xm10"}, {"name": "shuyxmexm1xm4", "bone": "xmbonxmexm1xm16"}, {"name": "shuyxmexm1", "bone": "xmbonxmexm7"}, {"name": "lizi3", "bone": "2", "blend": "additive"}, {"name": "shuyxmexm5", "bone": "xmbonxmexm12"}, {"name": "lizixm22", "bone": "xm23", "blend": "additive"}, {"name": "lizixm23", "bone": "xm24", "blend": "additive"}, {"name": "shuyxmexm10", "bone": "xmbonxmexm1xm6"}, {"name": "shuyxmexm14", "bone": "xmbonxmexm1xm11"}, {"name": "lizixm24", "bone": "xm25", "blend": "additive"}, {"name": "lizixm25", "bone": "xm26", "blend": "additive"}, {"name": "shuyxmexm6", "bone": "xmbonxmexm13"}, {"name": "lizixm26", "bone": "xm27", "blend": "additive"}, {"name": "shuyxmexm2", "bone": "xmbonxmexm8"}, {"name": "lizixm27", "bone": "xm28", "blend": "additive"}, {"name": "shuyxmexm1xm3", "bone": "xmbonxmexm1xm12"}, {"name": "lizixm28", "bone": "xm29", "blend": "additive"}, {"name": "lizixm29", "bone": "xm30", "blend": "additive"}, {"name": "shuyxmexm11", "bone": "xmbonxmexm1xm7"}, {"name": "lizi2", "bone": "1", "blend": "additive"}, {"name": "lizixm30", "bone": "xm31", "blend": "additive"}, {"name": "lizixm31", "bone": "xm32", "blend": "additive"}, {"name": "lizixm12", "bone": "xm12", "blend": "additive"}, {"name": "lizixm1xm10", "bone": "xm33", "blend": "additive"}, {"name": "shuyxmexm3", "bone": "xmbonxmexm9"}, {"name": "lizixm1xm11", "bone": "xm1xm8", "blend": "additive"}, {"name": "lizixm13", "bone": "xm13", "blend": "additive"}, {"name": "lizixm1xm12", "bone": "xm1xm9", "blend": "additive"}, {"name": "lizixm1xm13", "bone": "xm1xm10", "blend": "additive"}, {"name": "shuyxmexm1xm2", "bone": "xmbonxmexm1xm8"}, {"name": "lizixm14", "bone": "xm14", "blend": "additive"}, {"name": "lizixm15", "bone": "xm15", "blend": "additive"}, {"name": "lizi", "bone": "0", "blend": "additive"}, {"name": "lizixm16", "bone": "xm16", "blend": "additive"}, {"name": "lizixm2", "bone": "xm1", "blend": "additive"}, {"name": "lizixm17", "bone": "xm17", "blend": "additive"}, {"name": "lizixm18", "bone": "xm18", "blend": "additive"}, {"name": "lizixm3", "bone": "xm2", "blend": "additive"}, {"name": "lizixm19", "bone": "xm19", "blend": "additive"}, {"name": "lizixm4", "bone": "xm3", "blend": "additive"}, {"name": "lizixm20", "bone": "xm20", "blend": "additive"}, {"name": "lizixm21", "bone": "xm21", "blend": "additive"}, {"name": "lizixm5", "bone": "xm4", "blend": "additive"}, {"name": "lizixm1xm6", "bone": "xm22", "blend": "additive"}, {"name": "lizixm6", "bone": "xm5", "blend": "additive"}, {"name": "lizixm1xm7", "bone": "xm1xm5", "blend": "additive"}, {"name": "lizixm1xm8", "bone": "xm1xm6", "blend": "additive"}, {"name": "lizixm7", "bone": "xm6", "blend": "additive"}, {"name": "lizixm1xm9", "bone": "xm1xm7", "blend": "additive"}, {"name": "lizixm8", "bone": "xm7", "blend": "additive"}, {"name": "lizixm9", "bone": "xm8", "blend": "additive"}, {"name": "lizixm10", "bone": "xm9", "blend": "additive"}, {"name": "lizixm11", "bone": "xm10", "blend": "additive"}, {"name": "lizixm1xm2", "bone": "xm11", "blend": "additive"}, {"name": "lizixm1xm3", "bone": "xm1xm2", "blend": "additive"}, {"name": "lizixm1xm4", "bone": "xm1xm3", "blend": "additive"}, {"name": "lizixm1xm5", "bone": "xm1xm4", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"attack1_to": {"attack1_to": {"type": "point", "x": 217.63, "y": 152.19}}, "attack2_to": {"attack2_to": {"type": "point", "x": 205.85, "y": 110.72}}, "img": {"img": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [134.62, 3.97, -186.38, 3.97, -186.38, 368.97, 134.62, 368.97], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 192, "height": 219}}, "img1": {"img1": {"type": "mesh", "uvs": [0.145, 0.17382, 0.54606, 0.16219, 0.53444, 0.69694, 0.13338, 0.67951], "triangles": [2, 0, 1, 3, 0, 2], "vertices": [-11.53, 15.17, 10.48, 15.05, 10.36, -11.76, -11.5, -11.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 48}}, "light2": {"light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [72, -72, -72, -72, -72, 72, 72, 72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 72, "height": 72}}, "line_fire": {"line_fire": {"type": "mesh", "uvs": [0.95689, 0.26138, 0.95709, 0.41856, 0.95729, 0.58384, 0.95747, 0.7211, 0.83854, 0.82762, 0.14677, 0.8275, 0.03238, 0.72632, 0.03243, 0.5801, 0.03248, 0.40924, 0.03253, 0.25609, 0.13497, 0.1547, 0.8471, 0.15458], "triangles": [11, 0, 1, 8, 10, 11, 8, 9, 10, 11, 1, 8, 7, 8, 1, 7, 1, 2, 7, 5, 6, 7, 4, 5, 4, 7, 2, 3, 4, 2], "vertices": [403.98, 45.8, 397.61, 20.18, 402.13, -40.32, 405.87, -63.07, 387.27, -84.23, -312.14, -147.02, -335.54, -133.61, -339.48, -109.36, -338.72, 124.31, -332.47, 149.27, -310.15, 161.24, 388.84, 68.08], "hull": 12, "edges": [0, 22, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 16, 18, 0, 2, 16, 2, 12, 14, 14, 16, 2, 4, 4, 6, 14, 4], "width": 183, "height": 168}}, "line_orange2": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange3": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange5": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange6": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange7": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange10": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange11": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [206.16, -38, -49.84, -38, -49.84, 38, 206.16, 38], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange12": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [208.1, -30.78, -53.69, -167.09, -53.82, 141.14, 206.9, 29.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange13": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [181.41, -36.1, -51.04, -48.87, -50.37, 171.1, 180.78, 68.26], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "line_orange14": {"line_orange2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [208.1, -30.78, -53.69, -167.09, -53.82, 141.14, 206.9, 29.18], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 256, "height": 76}}, "lizi": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizi2": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizi3": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm2": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm3": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm4": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm5": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [263.71, 39.27, 263.71, 39.27, 263.71, 39.27, 263.71, 39.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm6": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm7": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm8": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm9": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [144.95, 39.27, 144.95, 39.27, 144.95, 39.27, 144.95, 39.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm10": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm11": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm12": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm1xm13": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-80.45, 39.27, -80.45, 39.27, -80.45, 39.27, -80.45, 39.27], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm2": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm3": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm4": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm5": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm6": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm7": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm8": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm9": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm10": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm11": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm12": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm13": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm14": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm15": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm16": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm17": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm18": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm19": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm20": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm21": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm22": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm23": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm24": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm25": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm26": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm27": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm28": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm29": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm30": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "lizixm31": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.5, -18.5, -17.5, -18.5, -17.5, 18.5, 17.5, 18.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 37}}, "shuyxme0xm1": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm1": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm1xm2": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm1xm3": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm1xm4": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxme0xm2": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm2": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxme0xm3": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm3": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm4": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm5": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm6": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm7": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm8": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm9": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm10": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm11": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm12": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm13": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm14": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm15": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm16": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm17": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "shuyxmexm18": {"shuye01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28, -27.5, -28, -27.5, -28, 27.5, 28, 27.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 55}}, "skill1_to": {"skill1_to": {"type": "point", "x": 582.94, "y": 111.94}}, "texiao/skill1/guanci001": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.26, -21.56, -150.73, -20.35, -150.33, 31.65, 5.67, 30.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci1": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.95, -21.18, -151.05, -20.73, -150.9, 31.27, 5.1, 30.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci2": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.26, -21.56, -150.73, -20.35, -150.33, 31.65, 5.67, 30.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci3": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.95, -21.18, -151.05, -20.73, -150.9, 31.27, 5.1, 30.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci4": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.73, -20.88, -151.27, -21.03, -151.32, 30.97, 4.68, 31.12], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci5": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.31, -21.18, -151.69, -20.73, -151.54, 31.27, 4.46, 30.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci6": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.76, -20.78, -151.24, -21.13, -151.36, 30.87, 4.64, 31.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci7": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.61, -20.28, -151.39, -21.63, -151.84, 30.36, 4.15, 31.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci8": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.73, -20.88, -151.27, -21.03, -151.32, 30.97, 4.68, 31.12], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci9": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.31, -21.18, -151.69, -20.73, -151.54, 31.27, 4.46, 30.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci10": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.76, -20.78, -151.24, -21.13, -151.36, 30.87, 4.64, 31.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci11": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.61, -20.28, -151.39, -21.63, -151.84, 30.36, 4.15, 31.72], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanci12": {"texiao/skill1/guanci001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.26, -21.56, -150.73, -20.35, -150.33, 31.65, 5.67, 30.44], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 156, "height": 52}}, "texiao/skill1/guanman001": {"texiao/skill1/guanman001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [61.5, -58, -61.5, -58, -61.5, 58, 61.5, 58], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 116}}, "texiao/skill1/guanman1": {"texiao/skill1/guanman001": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [61.5, -58, -61.5, -58, -61.5, 58, 61.5, 58], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 123, "height": 116}}, "texiao/skill1/guanquan": {"texiao/skill1/guanquan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.5, -48.5, -58.5, -48.5, -58.5, 48.5, 58.5, 48.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 97}}, "texiao/skill1/guanquan2": {"texiao/skill1/guanquan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.5, -48.5, -58.5, -48.5, -58.5, 48.5, 58.5, 48.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 97}}, "texiao/skill1/guanquan3": {"texiao/skill1/guanquan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.5, -48.5, -58.5, -48.5, -58.5, 48.5, 58.5, 48.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 97}}, "texiao/skill1/guanquan4": {"texiao/skill1/guanquan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [58.5, -48.5, -58.5, -48.5, -58.5, 48.5, 58.5, 48.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 117, "height": 97}}, "xmazhuti_000xm2": {"azhuti_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}}, "xmazhuti_000xm3": {"azhuti_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}}, "xmazhuti_000xm4": {"azhuti_0002": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0006": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0007": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0009": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0011": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0012": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0013": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0014": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0015": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0016": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0019": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}, "azhuti_0021": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [232, -271.5, -232, -271.5, -232, 271.5, 232, 271.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 232, "height": 271}}}}], "events": {"hit": {}}, "animations": {"ultimate01": {"slots": {"img": {"attachment": [{"name": "img"}, {"time": 0.7, "name": null}]}, "img1": {"color": [{"color": "00000000", "curve": 0.472, "c3": 0.48}, {"time": 0.1, "color": "0000008b", "curve": "stepped"}, {"time": 0.5333, "color": "0000008b", "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "color": "00000000"}], "attachment": [{"name": "img1"}]}, "light2": {"color": [{"color": "00aaff94"}], "attachment": [{"name": "light"}, {"time": 0.7, "name": null}]}, "line_fire": {"attachment": [{"name": "line_fire"}, {"time": 0.7, "name": null}]}, "line_orange2": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange3": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange5": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange6": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange7": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange10": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange11": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange12": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange13": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "line_orange14": {"attachment": [{"name": "line_orange2"}, {"time": 0.7, "name": null}]}, "lizi": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizi2": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizi3": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm1xm2": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.6, "name": null}]}, "lizixm1xm3": {"attachment": [{"time": 1.3, "name": "lizi"}, {"time": 2.3333, "name": null}]}, "lizixm1xm4": {"attachment": [{"time": 1.2667, "name": "lizi"}, {"time": 2.2333, "name": null}]}, "lizixm1xm5": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.6667, "name": null}]}, "lizixm1xm6": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.6, "name": null}]}, "lizixm1xm7": {"attachment": [{"time": 1.3, "name": "lizi"}, {"time": 2.3333, "name": null}]}, "lizixm1xm8": {"attachment": [{"time": 1.2667, "name": "lizi"}, {"time": 2.2333, "name": null}]}, "lizixm1xm9": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.6667, "name": null}]}, "lizixm1xm10": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.6, "name": null}]}, "lizixm1xm11": {"attachment": [{"time": 1.3, "name": "lizi"}, {"time": 2.3333, "name": null}]}, "lizixm1xm12": {"attachment": [{"time": 1.2667, "name": "lizi"}, {"time": 2.2333, "name": null}]}, "lizixm1xm13": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.6667, "name": null}]}, "lizixm2": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm3": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm4": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm5": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm6": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm7": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 2.0667, "name": null}]}, "lizixm8": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.8667, "name": null}]}, "lizixm9": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.7667, "name": null}]}, "lizixm10": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.7333, "name": null}]}, "lizixm11": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm12": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm13": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm14": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm15": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm16": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm17": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 2.0667, "name": null}]}, "lizixm18": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.8667, "name": null}]}, "lizixm19": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.7667, "name": null}]}, "lizixm20": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.7333, "name": null}]}, "lizixm21": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm22": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm23": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm24": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm25": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm26": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "lizixm27": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 2.0667, "name": null}]}, "lizixm28": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.8667, "name": null}]}, "lizixm29": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.7667, "name": null}]}, "lizixm30": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.7333, "name": null}]}, "lizixm31": {"attachment": [{"time": 1.0333, "name": "lizi"}, {"time": 1.5667, "name": null}]}, "shuyxme0xm1": {"attachment": [{"time": 1.0333, "name": "shuye01"}, {"time": 1.9333, "name": null}]}, "shuyxmexm1": {"attachment": [{"time": 1.2, "name": "shuye01"}, {"time": 2.3667, "name": null}]}, "shuyxmexm1xm2": {"color": [{"time": 1.8, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}], "attachment": [{"time": 1.2, "name": "shuye01"}, {"time": 2.0667, "name": null}]}, "shuyxmexm1xm3": {"color": [{"time": 1.2667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}], "attachment": [{"time": 0.7333, "name": "shuye01"}, {"time": 1.5333, "name": null}]}, "shuyxmexm1xm4": {"color": [{"time": 1.8, "color": "ffffffff"}, {"time": 2.0333, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "shuye01"}, {"time": 2.0667, "name": null}]}, "shuyxme0xm2": {"attachment": [{"time": 1.1333, "name": "shuye01"}, {"time": 2.0333, "name": null}]}, "shuyxmexm2": {"attachment": [{"time": 1.1667, "name": "shuye01"}, {"time": 1.8, "name": null}]}, "shuyxme0xm3": {"attachment": [{"time": 1.3, "name": "shuye01"}, {"time": 2.2, "name": null}]}, "shuyxmexm3": {"attachment": [{"time": 1.3, "name": "shuye01"}, {"time": 2.2667, "name": null}]}, "shuyxmexm4": {"attachment": [{"time": 1.3667, "name": "shuye01"}, {"time": 2.5333, "name": null}]}, "shuyxmexm5": {"attachment": [{"time": 1.3, "name": "shuye01"}, {"time": 1.9333, "name": null}]}, "shuyxmexm6": {"attachment": [{"time": 1.4, "name": "shuye01"}, {"time": 2.3667, "name": null}]}, "shuyxmexm7": {"attachment": [{"time": 1.4667, "name": "shuye01"}, {"time": 2.6333, "name": null}]}, "shuyxmexm8": {"attachment": [{"time": 1.5667, "name": "shuye01"}, {"time": 2.2, "name": null}]}, "shuyxmexm9": {"color": [{"time": 1.7333, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}], "attachment": [{"time": 1.1667, "name": "shuye01"}, {"time": 2.1, "name": null}]}, "shuyxmexm10": {"color": [{"time": 1.6667, "color": "ffffffff"}, {"time": 1.9, "color": "ffffff00"}], "attachment": [{"time": 1.5, "name": "shuye01"}, {"time": 1.9333, "name": null}]}, "shuyxmexm11": {"color": [{"time": 1.8, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "shuye01"}, {"time": 2.1333, "name": null}]}, "shuyxmexm12": {"color": [{"time": 1.5333, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff00"}], "attachment": [{"time": 1.2, "name": "shuye01"}, {"time": 1.9, "name": null}]}, "shuyxmexm13": {"color": [{"time": 1.5, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff00"}], "attachment": [{"time": 1.1333, "name": "shuye01"}, {"time": 1.7667, "name": null}]}, "shuyxmexm14": {"color": [{"time": 1.4333, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "shuye01"}, {"time": 1.7667, "name": null}]}, "shuyxmexm15": {"attachment": [{"time": 1.5667, "name": "shuye01"}, {"time": 2.5333, "name": null}]}, "shuyxmexm16": {"color": [{"time": 1.4, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff00"}], "attachment": [{"time": 1.2333, "name": "shuye01"}, {"time": 1.7667, "name": null}]}, "shuyxmexm17": {"color": [{"time": 1.8333, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}], "attachment": [{"time": 1.4667, "name": "shuye01"}, {"time": 2.1, "name": null}]}, "shuyxmexm18": {"color": [{"time": 1.7333, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff00"}], "attachment": [{"time": 1.1333, "name": "shuye01"}, {"time": 2, "name": null}]}, "texiao/skill1/guanci001": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "texiao/skill1/guanci1": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "texiao/skill1/guanci2": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "texiao/skill1/guanci3": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "texiao/skill1/guanci4": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "texiao/skill1/guanci5": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.7, "name": null}]}, "texiao/skill1/guanci6": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "texiao/skill1/guanci7": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "texiao/skill1/guanci8": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.5333, "name": null}]}, "texiao/skill1/guanci9": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.7, "name": null}]}, "texiao/skill1/guanci10": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "texiao/skill1/guanci11": {"attachment": [{"time": 0.1, "name": "texiao/skill1/guanci001"}, {"time": 0.6, "name": null}]}, "texiao/skill1/guanci12": {"attachment": [{"time": 0.0333, "name": "texiao/skill1/guanci001"}, {"time": 0.6333, "name": null}]}, "texiao/skill1/guanman001": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanman001"}, {"time": 0.7, "name": null}]}, "texiao/skill1/guanman1": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanman001"}, {"time": 0.7, "name": null}]}, "texiao/skill1/guanquan": {"color": [{"time": 0.0333, "color": "0070ffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanquan"}, {"time": 0.2333, "name": null}]}, "texiao/skill1/guanquan2": {"color": [{"time": 0.0333, "color": "006fff00"}, {"time": 0.1667, "color": "0070ffff"}, {"time": 0.4, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "texiao/skill1/guanquan"}, {"time": 0.4, "name": null}]}, "texiao/skill1/guanquan3": {"color": [{"time": 0.0333, "color": "0070ffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "texiao/skill1/guanquan"}, {"time": 0.2333, "name": null}]}, "texiao/skill1/guanquan4": {"color": [{"time": 0.0333, "color": "006fff00"}, {"time": 0.1667, "color": "0070ffff"}, {"time": 0.4, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "texiao/skill1/guanquan"}, {"time": 0.4, "name": null}]}, "xmazhuti_000xm2": {"attachment": [{"time": 1.2333, "name": "azhuti_0002"}, {"time": 1.2667, "name": "azhuti_0003"}, {"time": 1.3333, "name": "azhuti_0004"}, {"time": 1.4333, "name": "azhuti_0006"}, {"time": 1.5333, "name": "azhuti_0007"}, {"time": 1.6333, "name": "azhuti_0009"}, {"time": 1.7333, "name": "azhuti_0011"}, {"time": 1.8333, "name": "azhuti_0012"}, {"time": 1.9333, "name": "azhuti_0013"}, {"time": 2.0333, "name": "azhuti_0014"}, {"time": 2.1333, "name": "azhu<PERSON>_0015"}, {"time": 2.2333, "name": "azhuti_0016"}, {"time": 2.3, "name": "az<PERSON><PERSON>_0017"}, {"time": 2.4, "name": "azhuti_0019"}, {"time": 2.5, "name": "azhuti_0021"}, {"time": 2.5667, "name": null}]}, "xmazhuti_000xm3": {"attachment": [{"time": 1.3667, "name": "azhuti_0002"}, {"time": 1.4, "name": "azhuti_0003"}, {"time": 1.4667, "name": "azhuti_0004"}, {"time": 1.5667, "name": "azhuti_0006"}, {"time": 1.6667, "name": "azhuti_0007"}, {"time": 1.7667, "name": "azhuti_0009"}, {"time": 1.8667, "name": "azhuti_0011"}, {"time": 1.9667, "name": "azhuti_0012"}, {"time": 2.0667, "name": "azhuti_0013"}, {"time": 2.1667, "name": "azhuti_0014"}, {"time": 2.2667, "name": "azhu<PERSON>_0015"}, {"time": 2.3667, "name": "azhuti_0016"}, {"time": 2.4333, "name": "az<PERSON><PERSON>_0017"}, {"time": 2.5333, "name": "azhuti_0019"}, {"time": 2.6333, "name": "azhuti_0021"}, {"time": 2.7, "name": null}]}, "xmazhuti_000xm4": {"attachment": [{"time": 1.5, "name": "azhuti_0002"}, {"time": 1.5333, "name": "azhuti_0003"}, {"time": 1.6, "name": "azhuti_0004"}, {"time": 1.7, "name": "azhuti_0006"}, {"time": 1.8, "name": "azhuti_0007"}, {"time": 1.9, "name": "azhuti_0009"}, {"time": 2, "name": "azhuti_0011"}, {"time": 2.1, "name": "azhuti_0012"}, {"time": 2.2, "name": "azhuti_0013"}, {"time": 2.3, "name": "azhuti_0014"}, {"time": 2.4, "name": "azhu<PERSON>_0015"}, {"time": 2.5, "name": "azhuti_0016"}, {"time": 2.5667, "name": "az<PERSON><PERSON>_0017"}, {"time": 2.6667, "name": "azhuti_0019"}, {"time": 2.7667, "name": "azhuti_0021"}, {"time": 2.8333, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0.3, "angle": 0.07}]}, "guanman001": {"rotate": [{"time": 0.0333}, {"time": 0.4, "angle": -69.64}, {"time": 0.7, "angle": -133.93}], "translate": [{"time": 0.0333, "x": 36.42, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.695, "y": 0.695}]}, "guanman1": {"rotate": [{"time": 0.0333}, {"time": 0.4, "angle": -69.64}, {"time": 0.7, "angle": -133.93}], "translate": [{"time": 0.0333, "x": 36.42, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.695, "y": 0.695}]}, "guanci001_add3": {"rotate": [{"time": 0.0333, "angle": -98.24}, {"time": 0.1667, "angle": -179.34}, {"time": 0.5333, "angle": 166.16}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.013}, {"time": 0.1667, "x": 1.989, "y": 1.683}, {"time": 0.5333, "x": 1.97, "y": 0.165}]}, "guanquan4": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94, "curve": "stepped"}, {"time": 0.1667, "x": 0.94, "y": 0.94}, {"time": 0.2333, "x": 1.266, "y": 1.266}, {"time": 0.4, "x": 1.35, "y": 1.35}]}, "guanci001_add7": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "guanquan": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94}, {"time": 0.1, "x": 1.266, "y": 1.266}, {"time": 0.2333, "x": 1.35, "y": 1.35}]}, "guanquan2": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94, "curve": "stepped"}, {"time": 0.1667, "x": 0.94, "y": 0.94}, {"time": 0.2333, "x": 1.266, "y": 1.266}, {"time": 0.4, "x": 1.35, "y": 1.35}]}, "guanci001_add2": {"rotate": [{"time": 0.0333, "angle": 23.4}, {"time": 0.1667, "angle": -52.87, "curve": "stepped"}, {"time": 0.2333, "angle": -52.87, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -75.64}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 2.001, "y": 2.696}, {"time": 0.1667, "x": 1.997, "y": 2.052, "curve": "stepped"}, {"time": 0.2333, "x": 1.997, "y": 2.052, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 1.971, "y": 0.181}]}, "guanci001_add8": {"rotate": [{"time": 0.0333, "angle": 23.4}, {"time": 0.1667, "angle": -52.87, "curve": "stepped"}, {"time": 0.2333, "angle": -52.87, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "angle": -75.64}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 2.001, "y": 2.696}, {"time": 0.1667, "x": 1.997, "y": 2.052, "curve": "stepped"}, {"time": 0.2333, "x": 1.997, "y": 2.052, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": 1.971, "y": 0.181}]}, "guanci001_add": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "guanci001_add13": {"rotate": [{"time": 0.0333, "angle": 133.02}, {"time": 0.1667, "angle": 53.51}, {"time": 0.6333, "angle": 36.73}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.644}, {"time": 0.1667, "x": 2.002, "y": 1.82}, {"time": 0.6333, "x": 1.989, "y": 0.079}]}, "xuli_q": {"translate": [{"time": 0.0333, "x": -8.98, "y": 60.23}], "scale": [{"time": 0.0333, "x": 0.631, "y": 0.631}]}, "guanci001_add9": {"rotate": [{"time": 0.0333, "angle": -98.24}, {"time": 0.1667, "angle": -179.34}, {"time": 0.5333, "angle": 166.16}], "translate": [{"time": 0.0333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.0333, "x": 1.963, "y": 2.013}, {"time": 0.1667, "x": 1.989, "y": 1.683}, {"time": 0.5333, "x": 1.97, "y": 0.165}]}, "guanquan3": {"translate": [{"time": 0.0333, "x": 37.04, "y": 69.91}], "scale": [{"time": 0.0333, "x": 0.94, "y": 0.94}, {"time": 0.1, "x": 1.266, "y": 1.266}, {"time": 0.2333, "x": 1.35, "y": 1.35}]}, "guanci001_add12": {"rotate": [{"time": 0.1, "angle": -83.31}, {"time": 0.2333, "angle": -152.37}, {"time": 0.6, "angle": -168.29}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.025, "y": 1.253}, {"time": 0.2333, "x": 1.141, "y": 1.015}, {"time": 0.6, "x": 0.999, "y": 0.238}]}, "guanci001_add6": {"rotate": [{"time": 0.1, "angle": -83.31}, {"time": 0.2333, "angle": -152.37}, {"time": 0.6, "angle": -168.29}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.025, "y": 1.253}, {"time": 0.2333, "x": 1.141, "y": 1.015}, {"time": 0.6, "x": 0.999, "y": 0.238}]}, "guanci001_add4": {"rotate": [{"time": 0.1, "angle": 148.2}, {"time": 0.2333, "angle": 75.75}, {"time": 0.7, "angle": 66.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.173, "y": 1.434}, {"time": 0.2333, "x": 0.998, "y": 0.907}, {"time": 0.7, "x": 0.969, "y": 0.111}]}, "guanci001_add11": {"rotate": [{"time": 0.1, "angle": 35.49}, {"time": 0.2333, "angle": -30.49}, {"time": 0.6, "angle": -54.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.167, "y": 1.427}, {"time": 0.2333, "x": 0.984, "y": 0.875}, {"time": 0.6, "x": 0.978, "y": 0.112}]}, "guanci001_add10": {"rotate": [{"time": 0.1, "angle": 148.2}, {"time": 0.2333, "angle": 75.75}, {"time": 0.7, "angle": 66.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.173, "y": 1.434}, {"time": 0.2333, "x": 0.998, "y": 0.907}, {"time": 0.7, "x": 0.969, "y": 0.111}]}, "guanci001_add5": {"rotate": [{"time": 0.1, "angle": 35.49}, {"time": 0.2333, "angle": -30.49}, {"time": 0.6, "angle": -54.3}], "translate": [{"time": 0.1, "x": 38.29, "y": 67.62}, {"time": 0.2333, "x": 39.24, "y": 69.05}], "scale": [{"time": 0.1, "x": 1.167, "y": 1.427}, {"time": 0.2333, "x": 0.984, "y": 0.875}, {"time": 0.6, "x": 0.978, "y": 0.112}]}, "xizhen10": {"translate": [{"x": -61.76, "y": 3.49, "curve": "stepped"}, {"time": 0.1667, "x": -61.76, "y": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -114.91, "y": 6.5, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -61.76, "y": 3.49, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -86.23, "y": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7, "x": -86.23, "y": 4.88}], "scale": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.552, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "all3": {"translate": [{"y": 4.05, "curve": "stepped"}, {"time": 0.1667, "y": 4.05, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.472, "c3": 0.48}, {"time": 0.3333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7, "y": 4.05}]}, "xizhen5": {"translate": [{"x": -61.57, "y": -5.92, "curve": "stepped"}, {"time": 0.1667, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333, "curve": 0.472, "c3": 0.48}, {"time": 0.6, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6333, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7, "x": -61.57, "y": -5.92}]}, "xizhen": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "xizhen12": {"translate": [{"x": -14.15, "y": -1.36, "curve": "stepped"}, {"time": 0.1667, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.2, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2667, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": -85.97, "y": -8.26, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3667, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.4333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4667, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5667, "curve": 0.445, "c3": 0.744, "c4": 0.43}, {"time": 0.6, "x": -14.15, "y": -1.36, "curve": 0.28, "c2": 0.4, "c3": 0.464}, {"time": 0.6333, "x": -61.57, "y": -5.92, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667}], "scale": [{"x": 0.944, "curve": "stepped"}, {"time": 0.1667, "x": 0.944, "curve": 0.377, "c2": 0.27, "c3": 0.685, "c4": 0.64}, {"time": 0.2, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.2333, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.3333, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.3667, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.4333, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.4667, "x": 0.75, "curve": "stepped"}, {"time": 0.5333, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.6, "curve": 0.49, "c3": 0.739, "c4": 0.5}, {"time": 0.6333, "x": 0.75, "curve": 0.243, "c2": 0.5, "c3": 0.498}, {"time": 0.6667, "curve": 0.418, "c2": 0.01, "c3": 0.733, "c4": 0.4}, {"time": 0.7, "x": 0.944}]}, "all2": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "xizhen13": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -63.65, "y": 8.2, "curve": "stepped"}, {"time": 0.6, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -63.65, "y": 8.2}]}, "xizhen4": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "xizhen6": {"translate": [{"x": -61.57, "y": -5.92, "curve": "stepped"}, {"time": 0.1667, "x": -61.57, "y": -5.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -61.57, "y": -5.92, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "x": -85.97, "y": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7, "x": -85.97, "y": -8.26}]}, "xizhen9": {"translate": [{"x": -61.83, "y": -1.63, "curve": "stepped"}, {"time": 0.1667, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333, "curve": 0.472, "c3": 0.48}, {"time": 0.4667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5333, "curve": 0.472, "c3": 0.48}, {"time": 0.6, "x": -61.83, "y": -1.63, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6333, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -86.33, "y": -2.28, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.7, "x": -61.83, "y": -1.63}]}, "xizhen2": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "curve": 0.472, "c3": 0.48}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3, "curve": 0.472, "c3": 0.48}, {"time": 0.3667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.4, "curve": 0.472, "c3": 0.48}, {"time": 0.4333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5667, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.6, "curve": 0.472, "c3": 0.48}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.7}]}, "xizhen11": {"translate": [{"x": -24.31, "y": 3.13, "curve": "stepped"}, {"time": 0.1667, "x": -24.31, "y": 3.13, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -85.66, "y": 11.03, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.472, "c3": 0.48}, {"time": 0.5333, "x": -63.65, "y": 8.2, "curve": "stepped"}, {"time": 0.6, "x": -63.65, "y": 8.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -85.66, "y": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -63.65, "y": 8.2}]}, "all": {"translate": [{"x": 41.63, "curve": "stepped"}, {"time": 0.1667, "x": 41.63, "curve": 0.472, "c3": 0.48}, {"time": 0.3, "x": 647.52}]}, "bone36": {"translate": [{"time": 0.1667, "curve": 0.472, "c3": 0.172, "c4": 0.89}, {"time": 0.4667, "x": 590.32}]}, "xmbonxmexm1xm5": {"rotate": [{"time": 1.1667, "angle": 115.56}, {"time": 2.0667, "angle": 150}], "translate": [{"time": 1.1667, "x": 97.63, "y": 254.93, "curve": 0.159, "c2": 0.29, "c3": 0.75}, {"time": 2.0667, "x": -166.31, "y": 653.78}], "scale": [{"time": 1.1667}, {"time": 2.0667, "x": 2.789, "y": 2.789}]}, "xmbonxmexm1xm6": {"rotate": [{"time": 1.5, "angle": 134.45}, {"time": 1.9, "angle": 90}], "translate": [{"time": 1.5, "x": 320.02, "y": 273.92, "curve": 0.226, "c2": 0.35, "c3": 0.75}, {"time": 1.9, "x": 420, "y": 435.21}]}, "xmbonxmexm1xm7": {"rotate": [{"time": 1.0333, "angle": 93.22}, {"time": 2.1, "angle": 160}], "translate": [{"time": 1.0333, "x": 97.63, "y": 132.89, "curve": 0.198, "c2": 0.38, "c3": 0.75}, {"time": 2.1, "x": -359.87, "y": 241.4}], "scale": [{"time": 1.0333}, {"time": 2.1, "x": 1.6, "y": 1.6}]}, "xmbonxmexm1xm8": {"rotate": [{"time": 1.2}, {"time": 2.0333, "angle": 180}], "translate": [{"time": 1.2, "x": 108.48, "y": 54.24, "curve": 0.15, "c2": 0.24, "c3": 0.75}, {"time": 2.0333, "x": -71.03, "y": -412.23}], "scale": [{"time": 1.2}, {"time": 2.0333, "x": 2.347, "y": 2.347}]}, "xmbonxmexm1xm9": {"rotate": [{"time": 1.2, "angle": 115.56}, {"time": 1.8667, "angle": 150}], "translate": [{"time": 1.2, "x": 97.63, "y": 254.93, "curve": 0.159, "c2": 0.29, "c3": 0.75}, {"time": 1.8667, "x": -166.31, "y": 653.78}], "scale": [{"time": 1.2}, {"time": 1.8667, "x": 2.789, "y": 2.789}]}, "xmbonxmexm1xm10": {"rotate": [{"time": 1.1333, "angle": 134.45}, {"time": 1.7333, "angle": 90}], "translate": [{"time": 1.1333, "x": 320.02, "y": 273.92, "curve": 0.226, "c2": 0.35, "c3": 0.75}, {"time": 1.7333, "x": 420, "y": 435.21}]}, "xmbonxmexm1xm11": {"rotate": [{"time": 0.6667, "angle": 93.22}, {"time": 1.7333, "angle": 160}], "translate": [{"time": 0.6667, "x": 97.63, "y": 132.89, "curve": 0.198, "c2": 0.38, "c3": 0.75}, {"time": 1.7333, "x": -359.87, "y": 241.4}], "scale": [{"time": 0.6667}, {"time": 1.7333, "x": 1.6, "y": 1.6}]}, "xmbonxmexm1xm12": {"rotate": [{"time": 0.7333}, {"time": 1.5, "angle": 180}], "translate": [{"time": 0.7333, "x": 108.48, "y": 54.24, "curve": 0.15, "c2": 0.24, "c3": 0.75}, {"time": 1.5, "x": -71.03, "y": -412.23}], "scale": [{"time": 0.7333}, {"time": 1.5, "x": 2.347, "y": 2.347}]}, "xmbonxmexm1xm13": {"rotate": [{"time": 1.2333, "angle": 115.56}, {"time": 1.7333, "angle": 150}], "translate": [{"time": 1.2333, "x": 97.63, "y": 254.93, "curve": 0.159, "c2": 0.29, "c3": 0.75}, {"time": 1.7333, "x": -166.31, "y": 653.78}], "scale": [{"time": 1.2333}, {"time": 1.7333, "x": 2.789, "y": 2.789}]}, "xmbonxmexm1xm14": {"rotate": [{"time": 1.4667, "angle": 134.45}, {"time": 2.0667, "angle": 90}], "translate": [{"time": 1.4667, "x": 320.02, "y": 273.92, "curve": 0.226, "c2": 0.35, "c3": 0.75}, {"time": 2.0667, "x": 420, "y": 435.21}]}, "xmbonxmexm1xm15": {"rotate": [{"time": 1.1333, "angle": 93.22}, {"time": 1.9667, "angle": 160}], "translate": [{"time": 1.1333, "x": 97.63, "y": 132.89, "curve": 0.198, "c2": 0.38, "c3": 0.75}, {"time": 1.9667, "x": -359.87, "y": 241.4}], "scale": [{"time": 1.1333}, {"time": 1.9667, "x": 1.6, "y": 1.6}]}, "xmbonxmexm1xm16": {"rotate": [{"time": 1.0333}, {"time": 2.0333, "angle": 180}], "translate": [{"time": 1.0333, "x": 108.48, "y": 54.24, "curve": 0.15, "c2": 0.24, "c3": 0.75}, {"time": 2.0333, "x": -71.03, "y": -412.23}], "scale": [{"time": 1.0333}, {"time": 2.0333, "x": 2.347, "y": 2.347}]}, "0": {"translate": [{"time": 1.0333, "x": -128.64, "y": 123.28, "curve": 0.159, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": -341.04, "y": 217.29}], "scale": [{"time": 1.0333}, {"time": 1.5333, "x": 0.438, "y": 0.438}]}, "xm1": {"translate": [{"time": 1.0333, "x": -91.12}, {"time": 1.5333, "x": -360.97}]}, "xm2": {"translate": [{"time": 1.0333, "x": 101.84, "y": -77.72}, {"time": 1.5333, "x": 349.06, "y": -42.9}]}, "xm3": {"translate": [{"time": 1.0333, "x": 58.2, "y": -46.45, "curve": 0.264, "c2": 0.47, "c3": 0.75}, {"time": 1.5333, "x": 339.2, "y": -125.48}]}, "xm4": {"translate": [{"time": 1.0333, "x": -5.36, "y": -80.4, "curve": 0.174, "c2": 0.4, "c3": 0.75}, {"time": 1.5333, "x": -214.28, "y": -334.75}]}, "xm5": {"translate": [{"time": 1.0333, "x": 96.48, "y": -91.12, "curve": 0.164, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": 338.51, "y": -312.8}]}, "xm6": {"translate": [{"time": 1.0333, "x": -37.52, "y": 155.44, "curve": 0.15, "c2": 0.35, "c3": 0.75}, {"time": 2.0333, "x": -339.46, "y": 276.76}], "scale": [{"time": 1.0333}, {"time": 2.0333, "x": 0.282, "y": 0.282}]}, "xm7": {"translate": [{"time": 1.0333, "x": 77.72, "y": -120.6, "curve": 0.231, "c2": 0.4, "c3": 0.75}, {"time": 1.8333, "x": 367.26, "y": 42.26}], "scale": [{"time": 1.0333}, {"time": 1.8333, "x": 0.367, "y": 0.367}]}, "xm8": {"translate": [{"time": 1.0333, "x": -115.58, "y": -102.08, "curve": 0.24, "c2": 0.43, "c3": 0.75}, {"time": 1.7333, "x": -400.6, "y": -29.7}], "scale": [{"time": 1.0333}, {"time": 1.7333, "x": 1.2, "y": 1.2}]}, "xm9": {"translate": [{"time": 1.0333, "x": 91.12, "y": -134}, {"time": 1.7, "x": 281.13, "y": -231.27}]}, "xm10": {"translate": [{"time": 1.0333, "x": -128.64, "y": 238.52, "curve": 0.088, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": -357.79, "y": 402.97}], "scale": [{"time": 1.0333}, {"time": 1.5333, "x": 0.164, "y": 0.164}]}, "xm11": {"translate": [{"time": 1.0333, "x": -40.2, "y": -99.16}, {"time": 1.5667, "x": -239.26, "y": -180.59}], "scale": [{"time": 1.0333}, {"time": 1.5667, "x": 0.188, "y": 0.188}]}, "xm1xm4": {"translate": [{"time": 1.0333, "curve": 0.207, "c2": 0.44, "c3": 0.75}, {"time": 1.6333, "x": -321.2, "y": -131.2}], "scale": [{"time": 1.0333}, {"time": 1.6333, "x": 0, "y": 0}]}, "xmbonxmexm6": {"rotate": [{"time": 1.0333}, {"time": 1.9, "angle": 177.61}], "translate": [{"time": 1.0333, "x": -456.92, "y": 255.61, "curve": 0.193, "c2": 0.4, "c3": 0.75}, {"time": 1.9, "x": 291.49, "y": -56.76}], "scale": [{"time": 1.0333}, {"time": 1.9, "x": 0.444, "y": 0.444}]}, "lizi_zonxmeg": {"translate": [{"time": 1.0333, "x": 171.52, "y": 120.6}]}, "lonxmegjuxmanall": {"translate": [{"time": 1.2667, "curve": 0.183, "c2": 0.31, "c3": 0.75}, {"time": 2.0333, "x": 923.69}]}, "1": {"translate": [{"time": 1.0333, "x": -128.64, "y": 123.28, "curve": 0.159, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": -341.04, "y": 217.29}], "scale": [{"time": 1.0333}, {"time": 1.5333, "x": 0.438, "y": 0.438}]}, "xm12": {"translate": [{"time": 1.0333, "x": -91.12}, {"time": 1.5333, "x": -360.97}]}, "xm13": {"translate": [{"time": 1.0333, "x": 101.84, "y": -77.72}, {"time": 1.5333, "x": 349.06, "y": -42.9}]}, "xm14": {"translate": [{"time": 1.0333, "x": 58.2, "y": -46.45, "curve": 0.264, "c2": 0.47, "c3": 0.75}, {"time": 1.5333, "x": 339.2, "y": -125.48}]}, "xm15": {"translate": [{"time": 1.0333, "x": -5.36, "y": -80.4, "curve": 0.174, "c2": 0.4, "c3": 0.75}, {"time": 1.5333, "x": -214.28, "y": -334.75}]}, "xm16": {"translate": [{"time": 1.0333, "x": 96.48, "y": -91.12, "curve": 0.164, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": 338.51, "y": -312.8}]}, "xm17": {"translate": [{"time": 1.0333, "x": -37.52, "y": 155.44, "curve": 0.15, "c2": 0.35, "c3": 0.75}, {"time": 2.0333, "x": -339.46, "y": 276.76}], "scale": [{"time": 1.0333}, {"time": 2.0333, "x": 0.282, "y": 0.282}]}, "xm18": {"translate": [{"time": 1.0333, "x": 77.72, "y": -120.6, "curve": 0.231, "c2": 0.4, "c3": 0.75}, {"time": 1.8333, "x": 367.26, "y": 42.26}], "scale": [{"time": 1.0333}, {"time": 1.8333, "x": 0.367, "y": 0.367}]}, "xm19": {"translate": [{"time": 1.0333, "x": -115.58, "y": -102.08, "curve": 0.24, "c2": 0.43, "c3": 0.75}, {"time": 1.7333, "x": -400.6, "y": -29.7}], "scale": [{"time": 1.0333}, {"time": 1.7333, "x": 1.2, "y": 1.2}]}, "xm20": {"translate": [{"time": 1.0333, "x": 91.12, "y": -134}, {"time": 1.7, "x": 281.13, "y": -231.27}]}, "xm21": {"translate": [{"time": 1.0333, "x": -128.64, "y": 238.52, "curve": 0.088, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": -357.79, "y": 402.97}], "scale": [{"time": 1.0333}, {"time": 1.5333, "x": 0.164, "y": 0.164}]}, "xm22": {"translate": [{"time": 1.0333, "x": -40.2, "y": -99.16}, {"time": 1.5667, "x": -239.26, "y": -180.59}], "scale": [{"time": 1.0333}, {"time": 1.5667, "x": 0.188, "y": 0.188}]}, "xm1xm7": {"translate": [{"time": 1.0333, "curve": 0.207, "c2": 0.44, "c3": 0.75}, {"time": 1.6333, "x": -321.2, "y": -131.2}], "scale": [{"time": 1.0333}, {"time": 1.6333, "x": 0, "y": 0}]}, "2": {"translate": [{"time": 1.0333, "x": -128.64, "y": 123.28, "curve": 0.159, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": -341.04, "y": 217.29}], "scale": [{"time": 1.0333}, {"time": 1.5333, "x": 0.438, "y": 0.438}]}, "xm23": {"translate": [{"time": 1.0333, "x": -91.12}, {"time": 1.5333, "x": -360.97}]}, "xm24": {"translate": [{"time": 1.0333, "x": 101.84, "y": -77.72}, {"time": 1.5333, "x": 349.06, "y": -42.9}]}, "xm25": {"translate": [{"time": 1.0333, "x": 58.2, "y": -46.45, "curve": 0.264, "c2": 0.47, "c3": 0.75}, {"time": 1.5333, "x": 339.2, "y": -125.48}]}, "xm26": {"translate": [{"time": 1.0333, "x": -5.36, "y": -80.4, "curve": 0.174, "c2": 0.4, "c3": 0.75}, {"time": 1.5333, "x": -214.28, "y": -334.75}]}, "xm27": {"translate": [{"time": 1.0333, "x": 96.48, "y": -91.12, "curve": 0.164, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": 338.51, "y": -312.8}]}, "xm28": {"translate": [{"time": 1.0333, "x": -37.52, "y": 155.44, "curve": 0.15, "c2": 0.35, "c3": 0.75}, {"time": 2.0333, "x": -339.46, "y": 276.76}], "scale": [{"time": 1.0333}, {"time": 2.0333, "x": 0.282, "y": 0.282}]}, "xm29": {"translate": [{"time": 1.0333, "x": 77.72, "y": -120.6, "curve": 0.231, "c2": 0.4, "c3": 0.75}, {"time": 1.8333, "x": 367.26, "y": 42.26}], "scale": [{"time": 1.0333}, {"time": 1.8333, "x": 0.367, "y": 0.367}]}, "xm30": {"translate": [{"time": 1.0333, "x": -115.58, "y": -102.08, "curve": 0.24, "c2": 0.43, "c3": 0.75}, {"time": 1.7333, "x": -400.6, "y": -29.7}], "scale": [{"time": 1.0333}, {"time": 1.7333, "x": 1.2, "y": 1.2}]}, "xm31": {"translate": [{"time": 1.0333, "x": 91.12, "y": -134}, {"time": 1.7, "x": 281.13, "y": -231.27}]}, "xm32": {"translate": [{"time": 1.0333, "x": -128.64, "y": 238.52, "curve": 0.088, "c2": 0.32, "c3": 0.75}, {"time": 1.5333, "x": -357.79, "y": 402.97}], "scale": [{"time": 1.0333}, {"time": 1.5333, "x": 0.164, "y": 0.164}]}, "xm33": {"translate": [{"time": 1.0333, "x": -40.2, "y": -99.16}, {"time": 1.5667, "x": -239.26, "y": -180.59}], "scale": [{"time": 1.0333}, {"time": 1.5667, "x": 0.188, "y": 0.188}]}, "xm1xm10": {"translate": [{"time": 1.0333, "curve": 0.207, "c2": 0.44, "c3": 0.75}, {"time": 1.6333, "x": -321.2, "y": -131.2}], "scale": [{"time": 1.0333}, {"time": 1.6333, "x": 0, "y": 0}]}, "xmbonxmexm10": {"rotate": [{"time": 1.1333}, {"time": 2, "angle": 177.61}], "translate": [{"time": 1.1333, "x": -456.92, "y": 255.61, "curve": 0.193, "c2": 0.4, "c3": 0.75}, {"time": 2, "x": 291.49, "y": -56.76}], "scale": [{"time": 1.1333}, {"time": 2, "x": 0.444, "y": 0.444}]}, "xmbonxmexm8": {"translate": [{"time": 1.1667, "x": 732.99, "y": 362.19}, {"time": 1.7667, "x": 291.04, "y": 133.66}], "scale": [{"time": 1.1667}, {"time": 1.7667, "x": 0.567, "y": 0.567}]}, "lizi_zonxmeg2": {"translate": [{"time": 1.1667, "x": 171.52, "y": 120.6}]}, "lonxmegjuxmanall2": {"translate": [{"time": 1.4, "curve": 0.183, "c2": 0.31, "c3": 0.75}, {"time": 2.0333, "x": 890.9}]}, "xmbonxmexm7": {"translate": [{"time": 1.2, "x": -49.58, "y": 756.71, "curve": 0.388, "c2": 0.02, "c3": 0.723, "c4": 0.45}, {"time": 1.6333, "x": -162.36, "y": 579.98, "curve": 0.387, "c2": 0.32, "c3": 0.722, "c4": 0.67}, {"time": 2, "x": -98.92, "y": 281.6, "curve": 0.418, "c2": 0.41, "c3": 0.753, "c4": 0.77}, {"time": 2.3333, "x": 243.61, "y": 53.9}], "scale": [{"time": 1.2}, {"time": 2.3333, "x": 0.488, "y": 0.488}]}, "xma_zhuti": {"translate": [{"time": 1.2333, "x": 123.05, "y": 265.66}], "scale": [{"time": 1.2333, "x": 1.5, "y": 1.5}]}, "xm1xm3": {"translate": [{"time": 1.2667, "x": -54.29, "y": 22.62, "curve": 0.183, "c2": 0.31, "c3": 0.75}, {"time": 2.2, "x": -160.6, "y": 126.67}], "scale": [{"time": 1.2667}, {"time": 2.2, "x": 0.224, "y": 0.224}]}, "xm1xm6": {"translate": [{"time": 1.2667, "x": -54.29, "y": 22.62, "curve": 0.183, "c2": 0.31, "c3": 0.75}, {"time": 2.2, "x": -160.6, "y": 126.67}], "scale": [{"time": 1.2667}, {"time": 2.2, "x": 0.224, "y": 0.224}]}, "xm1xm9": {"translate": [{"time": 1.2667, "x": -54.29, "y": 22.62, "curve": 0.183, "c2": 0.31, "c3": 0.75}, {"time": 2.2, "x": -160.6, "y": 126.67}], "scale": [{"time": 1.2667}, {"time": 2.2, "x": 0.224, "y": 0.224}]}, "xm1xm2": {"translate": [{"time": 1.3, "y": 122.15, "curve": 0.142, "c2": 0.3, "c3": 0.594, "c4": 0.73}, {"time": 1.7333, "x": 29.41, "y": 344.23, "curve": 0.39, "c2": 0.6, "c3": 0.756}, {"time": 2.3, "y": 395.85}], "scale": [{"time": 1.3}, {"time": 2.3, "x": 0.255, "y": 0.255}]}, "xmbonxmexm9": {"rotate": [{"time": 1.3}, {"time": 2.2333, "angle": 180}], "translate": [{"time": 1.3, "x": -213.92, "y": -415.79}, {"time": 2.2333, "x": 304.31, "y": -18.08}], "scale": [{"time": 1.3, "x": 2.181, "y": 2.181}, {"time": 2.2333, "x": 0.778, "y": 0.778}]}, "xm1xm5": {"translate": [{"time": 1.3, "y": 122.15, "curve": 0.142, "c2": 0.3, "c3": 0.594, "c4": 0.73}, {"time": 1.7333, "x": 29.41, "y": 344.23, "curve": 0.39, "c2": 0.6, "c3": 0.756}, {"time": 2.3, "y": 395.85}], "scale": [{"time": 1.3}, {"time": 2.3, "x": 0.255, "y": 0.255}]}, "xmbonxmexm12": {"translate": [{"time": 1.3, "x": 732.99, "y": 362.19}, {"time": 1.9, "x": 291.04, "y": 133.66}], "scale": [{"time": 1.3}, {"time": 1.9, "x": 0.567, "y": 0.567}]}, "xm1xm8": {"translate": [{"time": 1.3, "y": 122.15, "curve": 0.142, "c2": 0.3, "c3": 0.594, "c4": 0.73}, {"time": 1.7333, "x": 29.41, "y": 344.23, "curve": 0.39, "c2": 0.6, "c3": 0.756}, {"time": 2.3, "y": 395.85}], "scale": [{"time": 1.3}, {"time": 2.3, "x": 0.255, "y": 0.255}]}, "lizi_zonxmeg3": {"translate": [{"time": 1.3, "x": 171.52, "y": 120.6}]}, "xmbonxmexm14": {"rotate": [{"time": 1.3}, {"time": 2.1667, "angle": 177.61}], "translate": [{"time": 1.3, "x": -456.92, "y": 255.61, "curve": 0.193, "c2": 0.4, "c3": 0.75}, {"time": 2.1667, "x": 291.49, "y": -56.76}], "scale": [{"time": 1.3}, {"time": 2.1667, "x": 0.444, "y": 0.444}]}, "lonxmegjuxmanall3": {"translate": [{"time": 1.5333, "curve": 0.183, "c2": 0.31, "c3": 0.75}, {"time": 2.1667, "x": 893.63}]}, "xma_zhuti2": {"translate": [{"time": 1.3667, "x": 123.05, "y": 265.66}], "scale": [{"time": 1.3667, "x": 1.5, "y": 1.5}]}, "xmbonxmexm11": {"translate": [{"time": 1.3667, "x": -49.58, "y": 756.71, "curve": 0.388, "c2": 0.02, "c3": 0.723, "c4": 0.45}, {"time": 1.8, "x": -162.36, "y": 579.98, "curve": 0.387, "c2": 0.32, "c3": 0.722, "c4": 0.67}, {"time": 2.1667, "x": -98.92, "y": 281.6, "curve": 0.418, "c2": 0.41, "c3": 0.753, "c4": 0.77}, {"time": 2.5, "x": 243.61, "y": 53.9}], "scale": [{"time": 1.3667}, {"time": 2.5, "x": 0.488, "y": 0.488}]}, "xmbonxmexm13": {"rotate": [{"time": 1.4}, {"time": 2.3333, "angle": 180}], "translate": [{"time": 1.4, "x": -213.92, "y": -415.79}, {"time": 2.3333, "x": 304.31, "y": -18.08}], "scale": [{"time": 1.4, "x": 2.181, "y": 2.181}, {"time": 2.3333, "x": 0.778, "y": 0.778}]}, "xmbonxmexm15": {"translate": [{"time": 1.4667, "x": -49.58, "y": 756.71, "curve": 0.388, "c2": 0.02, "c3": 0.723, "c4": 0.45}, {"time": 1.9, "x": -162.36, "y": 579.98, "curve": 0.387, "c2": 0.32, "c3": 0.722, "c4": 0.67}, {"time": 2.2667, "x": -98.92, "y": 281.6, "curve": 0.418, "c2": 0.41, "c3": 0.753, "c4": 0.77}, {"time": 2.6, "x": 243.61, "y": 53.9}], "scale": [{"time": 1.4667}, {"time": 2.6, "x": 0.488, "y": 0.488}]}, "xma_zhuti3": {"translate": [{"time": 1.5, "x": 123.05, "y": 265.66}], "scale": [{"time": 1.5, "x": 1.5, "y": 1.5}]}, "xmbonxmexm16": {"translate": [{"time": 1.5667, "x": 732.99, "y": 362.19}, {"time": 2.1667, "x": 291.04, "y": 133.66}], "scale": [{"time": 1.5667}, {"time": 2.1667, "x": 0.567, "y": 0.567}]}, "xmbonxmexm17": {"rotate": [{"time": 1.5667}, {"time": 2.5, "angle": 180}], "translate": [{"time": 1.5667, "x": -213.92, "y": -415.79}, {"time": 2.5, "x": 304.31, "y": -18.08}], "scale": [{"time": 1.5667, "x": 2.181, "y": 2.181}, {"time": 2.5, "x": 0.778, "y": 0.778}]}}, "events": [{"time": 1.5333, "name": "hit"}, {"time": 1.6, "name": "hit"}, {"time": 1.6667, "name": "hit"}]}}}