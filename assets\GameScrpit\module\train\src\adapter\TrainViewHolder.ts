import { instantiate, Node, _decorator, Label } from "cc";
import { ItemCtrl } from "db://assets/GameScrpit/game/common/ItemCtrl";
import { IConfigTrain } from "db://assets/GameScrpit/game/JsonDefine";
import { ListAdapter, ViewHolder } from "db://assets/platform/src/core/ui/adapter_view/ListAdapter";
import { TrainModule } from "../TrainModule";
import { PlayerModule } from "../../../player/PlayerModule";
import { ItemCost } from "db://assets/GameScrpit/game/common/ItemCost";
import { ItemEnum } from "db://assets/GameScrpit/lib/common/ItemEnum";
import { StateButton } from "db://assets/platform/src/core/ui/components/StateButton";
import TipMgr from "db://assets/GameScrpit/lib/tips/TipMgr";
import { RealmsFightResponse } from "db://assets/GameScrpit/game/net/protocol/Realms";
import MsgEnum from "db://assets/GameScrpit/game/event/MsgEnum";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
const { ccclass, property } = _decorator;

enum StateButtonState {
  FIGHT,
  SWEEP,
  FREE,
}
@ccclass("TrainViewHolder")
export class TrainViewHolder extends ViewHolder {
  private node_list: Node;
  private _data: IConfigTrain;
  bindData(data: IConfigTrain) {
    this._data = data;
    //
    let realmsMessage = TrainModule.data.realmsMessage;
    this.getNode("item_layout").children.forEach((child) => {
      child.active = false;
    });
    for (let i = 0; i < data.reward01List.length; i++) {
      let node = this.getNode("item_layout").children[i];
      if (!node) {
        node = instantiate(this.getNode("item_layout").children[0]);
      }
      node.active = true;
      let item = node.getComponent(ItemCtrl);
      item.setItemId(data.reward01List[i][0], data.reward01List[i][1]);
      node.active = true;
    }
    let leaderData = PlayerModule.data.getConfigLeaderData(data.unlockLv);
    this.getNode("lbl_level").getComponent(Label).string = leaderData.jingjie;
    this.getNode("lbl_unlock").getComponent(Label).string = `${leaderData.jingjie}解锁`;
    this.getNode("btn_fight").active = false;
    this.getNode("ItemCost").active = false;
    this.getNode("lbl_unlock").active = false;
    this.getNode("node_yijibai").active = false;
    this.getNode("node_times").active = false;
    if (TrainModule.data.realmsMessage.maxConquerId > data.id) {
      this.getNode("node_yijibai").active = true;
    } else if (TrainModule.data.realmsMessage.maxConquerId == data.id) {
      this.getNode("btn_fight").active = true;
      this.getNode("node_times").active = true;
      this.getNode("lbl_times").getComponent(Label).string = `${data.dayTime - realmsMessage.sweepCount}/${
        data.dayTime
      }`;
      if (realmsMessage.sweepCount > 0) {
        this.getNode("btn_fight").getComponent(StateButton).state = StateButtonState.SWEEP;
        this.getNode("ItemCost").active = true;
        this.getNode("ItemCost")
          .getComponent(ItemCost)
          .setItemId(ItemEnum.仙玉_6, data.cost[0][realmsMessage.sweepCount - 1]);
      } else {
        this.getNode("btn_fight").getComponent(StateButton).state = StateButtonState.FREE;
      }
    } else if (
      TrainModule.data.realmsMessage.maxUnlockId >= data.id &&
      TrainModule.data.realmsMessage.maxConquerId + 1 == data.id
    ) {
      this.getNode("btn_fight").active = true;
      this.getNode("btn_fight").getComponent(StateButton).state = StateButtonState.FIGHT;
    } else {
      this.getNode("lbl_unlock").active = true;
    }
  }
  private onClickFight() {
    if (this.getNode("btn_fight").getComponent(StateButton).state == StateButtonState.FIGHT) {
      TrainModule.api.conquer((data: RealmsFightResponse) => {
        // this.bindData(this._data);
        TipMgr.showTip("挑战成功");
        this.node.parent.getComponent(AdapterView).getAdapter().notifyDataSetChanged();
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage?.rewardList });
      });
    } else {
      TrainModule.api.sweep((data: RealmsFightResponse) => {
        // this.bindData(this._data);
        TipMgr.showTip("扫荡成功");
        this.node.parent.getComponent(AdapterView).getAdapter().notifyDataSetChanged();
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardMessage?.rewardList });
      });
      return;
    }
  }
}
export class TrainAdapter extends ListAdapter {
  private _item: Node;
  private _data: IConfigTrain[];
  constructor(item: Node) {
    super();
    this._item = item;
  }
  setData(data: IConfigTrain[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let node = instantiate(this._item);
    node.active = true;
    return node;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(TrainViewHolder).bindData(this._data[position]);
  }
  getCount(): number {
    return this._data.length;
  }
}
