// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: Mail.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "sim";

/**  */
export interface MailMessage {
  id: number;
  /** 发件人名称 */
  sender: string;
  /** 标题 */
  title: string;
  /** 副标题 */
  subTitle: string;
  /** 邮件内容 */
  content: string;
  /** 物品列表 */
  rewardList: number[];
  /** 发送时间 */
  sendTs: number;
  /** 过期时间 */
  overDateTs: number;
  /** 创建时间 */
  createTime: number;
  /** 已读/已领取奖励 */
  read: boolean;
  /** 读取/领取时间 */
  readTs: number;
}

/**  */
export interface ReadResponse {
  /** 已读邮件ID */
  idList: number[];
  /** 奖励列表 */
  rewardList: number[];
}

function createBaseMailMessage(): MailMessage {
  return {
    id: 0,
    sender: "",
    title: "",
    subTitle: "",
    content: "",
    rewardList: [],
    sendTs: 0,
    overDateTs: 0,
    createTime: 0,
    read: false,
    readTs: 0,
  };
}

export const MailMessage: MessageFns<MailMessage> = {
  encode(message: MailMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== 0) {
      writer.uint32(8).int64(message.id);
    }
    if (message.sender !== "") {
      writer.uint32(18).string(message.sender);
    }
    if (message.title !== "") {
      writer.uint32(26).string(message.title);
    }
    if (message.subTitle !== "") {
      writer.uint32(34).string(message.subTitle);
    }
    if (message.content !== "") {
      writer.uint32(42).string(message.content);
    }
    writer.uint32(50).fork();
    for (const v of message.rewardList) {
      writer.int64(v);
    }
    writer.join();
    if (message.sendTs !== 0) {
      writer.uint32(56).int64(message.sendTs);
    }
    if (message.overDateTs !== 0) {
      writer.uint32(64).int64(message.overDateTs);
    }
    if (message.createTime !== 0) {
      writer.uint32(72).int64(message.createTime);
    }
    if (message.read !== false) {
      writer.uint32(80).bool(message.read);
    }
    if (message.readTs !== 0) {
      writer.uint32(88).int64(message.readTs);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MailMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMailMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sender = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.subTitle = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 6: {
          if (tag === 48) {
            message.rewardList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 50) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.sendTs = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.overDateTs = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.createTime = longToNumber(reader.int64());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.read = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.readTs = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<MailMessage>, I>>(base?: I): MailMessage {
    return MailMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MailMessage>, I>>(object: I): MailMessage {
    const message = createBaseMailMessage();
    message.id = object.id ?? 0;
    message.sender = object.sender ?? "";
    message.title = object.title ?? "";
    message.subTitle = object.subTitle ?? "";
    message.content = object.content ?? "";
    message.rewardList = object.rewardList?.map((e) => e) || [];
    message.sendTs = object.sendTs ?? 0;
    message.overDateTs = object.overDateTs ?? 0;
    message.createTime = object.createTime ?? 0;
    message.read = object.read ?? false;
    message.readTs = object.readTs ?? 0;
    return message;
  },
};

function createBaseReadResponse(): ReadResponse {
  return { idList: [], rewardList: [] };
}

export const ReadResponse: MessageFns<ReadResponse> = {
  encode(message: ReadResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.idList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReadResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReadResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.idList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.idList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 17) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<ReadResponse>, I>>(base?: I): ReadResponse {
    return ReadResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReadResponse>, I>>(object: I): ReadResponse {
    const message = createBaseReadResponse();
    message.idList = object.idList?.map((e) => e) || [];
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
