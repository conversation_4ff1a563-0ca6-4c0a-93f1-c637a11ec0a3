import {
  BattleActionDTO,
  BattleReplayDTO,
  BuffDTO,
  ChangeDTO,
  CharacterDTO,
  RoundDTO,
  SceneDTO,
  SkillDTO,
} from "../FightConstant";

function parseList(data: any, func: Function, toList: any[]) {
  let skillDataList: any[] = data || [];
  for (let idx in skillDataList) {
    let skillData = skillDataList[idx];
    toList.push(func(skillData));
  }
}

/**
 * 解析场景信息
 * @param data 原数据
 * @returns 场景信息
 */
function parseSceneDTO(data: any): SceneDTO {
  data = data || {};

  let rs: SceneDTO = {
    fromType: data.a || 0,
    artId: data.b || 0,
    roundMax: data.c || 0,
    gvsj连击概率衰减List: data.d || [],
    暴击最大倍率: data.e || 0,
    暴击最小倍率: data.f || 0,
    最大吸血概率: data.g || 0,
  };

  return rs;
}
function parseSkillDTO(data: any): SkillDTO {
  data = data || {};
  let rs: SkillDTO = {
    id: data.a || 0,
    level: data.b || 0,
    cdMax: data.c || 0,
    currentCd: data.d || 0,
    args: data.e || [],
  };

  return rs;
}

function parseBuffDTO(data: any): BuffDTO {
  data = data || {};
  let rs: BuffDTO = {
    id: data.a || 0,
    fromId: data.b || 0,
    targetId: data.c || 0,
    skillInterId: data.d || 0,
    life: data.e || 0,
    lifeType: data.f || 0,
    changeValueList: data.g || [],
  };

  return rs;
}

function parseCharacterDTO(data: any): CharacterDTO {
  data = data || {};
  let rs: CharacterDTO = {
    characterId: data.a || 0,
    fightCharacterId: data.b || 0,
    name: data.c || "",
    mountsArtId: data.d || 0,
    level: data.e || 0,
    attrMap: data.f || {},
    skillList: [],
    buffList: [],
    petList: [],
    id: 0,
  };

  parseList(data.g, parseSkillDTO, rs.skillList);
  parseList(data.h, parseBuffDTO, rs.buffList);
  parseList(data.i, parseCharacterDTO, rs.petList);
  return rs;
}

function parseChangeDTO(data: any): ChangeDTO {
  data = data || {};
  let rs: ChangeDTO = {
    targetId: data.a || 0,
    attrChangeMap: data.b || {},
    newBuffList: [],
    removeBuffList: data.d || [],
    otherChangeList: [],
  };

  parseList(data.c, parseBuffDTO, rs.newBuffList);
  parseList(data.e, parseChangeDTO, rs.otherChangeList);

  return rs;
}

function parseBattleActionDTO(data: any): BattleActionDTO {
  data = data || {};
  let rs: BattleActionDTO = {
    characterId: data.a || 0,
    actionType: data.b || 0,
    targetId: data.c || 0,
    数值变动List: [],
    子行为List: [],
    skillInterId: data.f || 0,
    attrMapMap: data.g || {},
  };

  parseList(data.d, parseChangeDTO, rs.数值变动List);
  parseList(data.e, parseBattleActionDTO, rs.子行为List);

  return rs;
}

function parseRoundDTO(data: any): RoundDTO {
  data = data || {};
  let rs: RoundDTO = {
    roundIdx: data.a || 0,
    actionList: [],
    teamList: [],
  };

  parseList(data.b, parseBattleActionDTO, rs.actionList);
  parseList(data.c, parseCharacterDTO, rs.teamList);
  return rs;
}

export function getBattleReport(battleData: any): BattleReplayDTO {
  let rs: BattleReplayDTO = {
    scene: parseSceneDTO(battleData.a),
    teamList: [[]],
    beforeRoundActionList: [],
    roundList: [],
    teamListFinally: [],
    winLoseMap: battleData.f || {},
  };

  let teamList: [][] = battleData.b || [];

  for (let idx in teamList) {
    let team = [];
    parseList(teamList[idx], parseCharacterDTO, team);
    rs.teamList.push(team);
  }

  parseList(battleData.c, parseCharacterDTO, rs.beforeRoundActionList);
  parseList(battleData.d, parseRoundDTO, rs.roundList);
  parseList(battleData.e, parseCharacterDTO, rs.teamListFinally);

  return rs;
}
