import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { _decorator, UITransform, Node, CCInteger, tween, v3 } from "cc";
import { LayoutManager } from "../LayoutManager";
import { Rect } from "../../../../lib/utils/Rect";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);

const { ccclass, property } = _decorator;
@ccclass("LinearLayoutManager")
export class LinearLayoutManager extends LayoutManager {
  //
  @property({ type: CCInteger, tooltip: "间距" })
  private spaceY: number = 0;

  private _frameLoad = false;
  // 该变量用于标识是否启用分帧加载
  private _isFrameLoadEnabled = true;
  protected update(dt: number): void {
    if (this._frameLoad) {
      let last = this.children.peekRear();
      let isFreeScreen = this.getNodeBottom(last) - this.spaceY > this.getBorderBottom();
      if (isFreeScreen) {
        let newNode = this.addNodeToTail();
        this._isFrameLoadEnabled = false;

        if (newNode) {
          let width = newNode.getComponent(UITransform).width;
          let height = newNode.getComponent(UITransform).height;
          let anchorX = newNode.getComponent(UITransform).anchorX;
          let anchorY = newNode.getComponent(UITransform).anchorY;
          let x = this.getBorderLeft() + anchorX * width;

          let y = this.getBorderTop() - (1 - anchorY) * height;
          if (last) {
            y = this.getNodeBottom(last) - (1 - anchorY) * height - this.spaceY;
          }
          // if (!this.lastRect.isZero) {
          //   y = this.lastRect.top - (1 - anchorY) * height;
          // }
          // log.log(`x[${x}],y[${y}]`, lastRect);
          this.updateChildPosition(newNode, x, y);

          // 后续需要封装成接口
          tween(newNode)
            .set({ scale: v3(0.1, 0.1, 1) })
            .to(0.3, { scale: v3(1, 1, 1) }, { easing: "sineOut" })
            .start();

          this.lastRect = new Rect(
            this.getBorderLeft(),
            this.lastRect.top,
            this.getBorderRight(),
            this.getNodeBottom(this.children.peekRear())
          );
        } else {
          this._frameLoad = false;
          return;
        }
      } else {
        this._frameLoad = false;
        return;
      }
    }
  }

  protected onLayout(changed: boolean, lastRect: Rect, offsetX: number, offsetY: number, isFling: boolean): Rect {
    if (!isFling) {
      if (offsetY < 0) {
        if (this.viewholders.firstVisiblePosition == 0) {
          this.getNodeTop(this.children.peekFront()) + offsetY < this.getBorderTop() && (offsetY /= 3);
        }
      } else {
        if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
          this.getNodeBottom(this.children.peekRear()) + offsetY > this.getBorderBottom() && (offsetY /= 3);
        }
      }
    }
    for (let i = 0; i < this.children.size(); i++) {
      let child = this.children.get(i);
      let width = child.getComponent(UITransform).width;
      let anchorX = child.getComponent(UITransform).anchorX;
      let x = this.getBorderLeft() + anchorX * width;
      let childY = child.position.y + offsetY;
      this.updateChildPosition(child, x, childY);
    }
    // 如果正在分帧加载则返回
    if (this._frameLoad) {
      return lastRect;
    }
    if (this.children.size() == 0 && this._isFrameLoadEnabled) {
      this._frameLoad = true;
      return lastRect;
    } else if (this.children.size() == 0) {
      let newNode = this.addNodeToTail();
      if (newNode) {
        let width = newNode.getComponent(UITransform).width;
        let height = newNode.getComponent(UITransform).height;
        let anchorX = newNode.getComponent(UITransform).anchorX;
        let anchorY = newNode.getComponent(UITransform).anchorY;
        let x = this.getBorderLeft() + anchorX * width;
        let y = this.getBorderTop() - (1 - anchorY) * height;
        if (changed && lastRect.top < 0) {
          y = lastRect.top - (1 - anchorY) * height;
        }
        log.log(`x[${x}],y[${y}]`, lastRect);
        this.updateChildPosition(newNode, x, y);
      } else {
        return lastRect;
      }
    }
    if (offsetY < 0) {
      // 向下滚动
      for (
        let first = this.children.peekFront();
        this.getNodeTop(first) + this.spaceY < this.getBorderTop();
        first = this.children.peekFront()
      ) {
        let newNode = this.addNodeToHeader();
        if (newNode) {
          let width = newNode.getComponent(UITransform).width;
          let height = newNode.getComponent(UITransform).height;
          let anchorX = newNode.getComponent(UITransform).anchorX;
          let anchorY = newNode.getComponent(UITransform).anchorY;
          let x = this.getBorderLeft() + anchorX * width;
          let y = this.getNodeTop(first) + anchorY * height + this.spaceY;
          this.updateChildPosition(newNode, x, y);
        } else {
          break;
        }
      }
    } else {
      // 向上滚动
      for (
        let last = this.children.peekRear();
        this.getNodeBottom(last) - this.spaceY > this.getBorderBottom();
        last = this.children.peekRear()
      ) {
        let newNode = this.addNodeToTail();
        if (newNode) {
          let width = newNode.getComponent(UITransform).width;
          let height = newNode.getComponent(UITransform).height;
          let anchorX = newNode.getComponent(UITransform).anchorX;
          let anchorY = newNode.getComponent(UITransform).anchorY;
          let x = this.getBorderLeft() + anchorX * width;
          let y = this.getNodeBottom(last) - (1 - anchorY) * height - this.spaceY;
          this.updateChildPosition(newNode, x, y);
        } else {
          break;
        }
      }
    }
    return new Rect(
      this.getBorderLeft(),
      this.getNodeTop(this.children.peekFront()),
      this.getBorderRight(),
      this.getNodeBottom(this.children.peekRear())
    );
  }

  protected onChildRecycled(child: Node, isFromHeader: boolean): boolean {
    let recycle = super.onChildRecycled(child, isFromHeader);
    let recycle2 = false;
    if (isFromHeader) {
      recycle2 = this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1;
    } else {
      recycle2 = this.viewholders.firstVisiblePosition == 0;
    }
    return recycle && !recycle2;
  }
  protected onFling(xoffset: number, yoffset: number): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    let isFirst = this.viewholders.firstVisiblePosition == 0;
    let isLast = this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1;
    if (
      (isFirst && this.getNodeTop(front) < this.getBorderTop()) ||
      (isLast && this.getNodeBottom(rear) > this.getBorderBottom())
    ) {
      this.scroller.setQuickStop();
      // log.log("fling complete");
    }
    // log.log("fling", xoffset, yoffset);
    super.onFling(xoffset, yoffset);
  }
  protected onScrollComplete(): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    let overStart = 0;
    let overEnd = 0;
    if (this.viewholders.firstVisiblePosition == 0) {
      overStart = this.getBorderTop() - this.getNodeTop(front);
    }
    if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
      overEnd = this.getBorderBottom() - this.getNodeBottom(rear);
    }
    // log.log(`overStart[${overStart}], overEnd[${overEnd}], first[${this.viewholders.firstVisiblePosition}]`);
    if (
      this.viewholders.firstVisiblePosition == 0 &&
      this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1
    ) {
      if (overStart > 0) {
        this.layoutChildrenOffset(0, overStart);
      } else if (overStart < 0 && overEnd < 0) {
        let deltaS = Math.abs(overStart) < Math.abs(overEnd) ? overStart : overEnd;
        this.layoutChildrenOffset(0, deltaS);
      }
    } else if (overStart > 0) {
      this.layoutChildrenOffset(0, overStart);
    } else if (overEnd < 0) {
      this.layoutChildrenOffset(0, overEnd);
    }
  }
  protected onFlingComplete(): void {
    let front = this.children.peekFront();
    let rear = this.children.peekRear();
    let overStart = 0;
    let overEnd = 0;
    if (this.viewholders.firstVisiblePosition == 0) {
      overStart = this.getBorderTop() - this.getNodeTop(front);
    }
    if (this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1) {
      overEnd = this.getBorderBottom() - this.getNodeBottom(rear);
    }
    // log.log(`overStart[${overStart}], overEnd[${overEnd}], first[${this.viewholders.firstVisiblePosition}]`);
    if (
      this.viewholders.firstVisiblePosition == 0 &&
      this.viewholders.lastVisiblePosition == this.viewholders.dataCount - 1
    ) {
      if (overStart > 0) {
        this.scroller.scrollBy(0, overStart);
      } else if (overStart < 0 && overEnd < 0) {
        let deltaS = Math.abs(overStart) < Math.abs(overEnd) ? overStart : overEnd;
        this.scroller.scrollBy(0, deltaS);
      }
    } else if (overStart > 0) {
      this.scroller.scrollBy(0, overStart);
    } else if (overEnd < 0) {
      this.scroller.scrollBy(0, overEnd);
    }
  }
}
