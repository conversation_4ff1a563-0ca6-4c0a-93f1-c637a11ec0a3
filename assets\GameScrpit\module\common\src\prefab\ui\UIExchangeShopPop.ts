import { _decorator, Component, EventTouch, instantiate, Label, Node } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { SystemOpenEnum } from "db://assets/GameScrpit/game/GameDefine";
import { GameDirector } from "db://assets/GameScrpit/game/GameDirector";
import TipMgr from "db://assets/GameScrpit/lib/tips/TipMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
const { ccclass, property } = _decorator;

@ccclass("UIExchangeShopPop")
@routeConfig({
  bundle: BundleEnum.BUNDLE_COMMON,
  url: "prefab/ui/UIExchangeShopPop",
  nextHop: [],
  exit: "",
  transparent: true,
})
export class UIExchangeShopPop extends BaseCtrl {
  _list: { name: string; systemOpen: SystemOpenEnum }[];
  _position: number;
  _layoutIndex: number = 0;
  init(args: RouteShowArgs): void {
    //
    this._list = args.payload.list;
    this._position = args.payload.position;
  }
  //
  protected start(): void {
    super.start();
    this.getNode("node_shop_list");
  }
  protected update(dt: number): void {
    if (this._layoutIndex < this._list.length) {
      let item = this.getNode("node_shop_list").children[this._layoutIndex];
      if (!item) {
        item = instantiate(this.getNode("node_shop_list").children[0]);
        this.getNode("node_shop_list").addChild(item);
      }
      item.active = true;
      item.getChildByName("lbl_name").getComponent(Label).string = this._list[this._layoutIndex].name;
      if (this._layoutIndex == this._position) {
        item.getChildByName("node_select").active = true;
      } else {
        item.getChildByName("node_select").active = false;
      }
      if (GameDirector.instance.isSystemOpen(this._list[this._layoutIndex].systemOpen)) {
        item.getChildByName("node_lock").active = false;
      } else {
        item.getChildByName("node_lock").active = true;
      }
      this._layoutIndex++;
    }
  }
  private onClickItem(event: EventTouch) {
    let target: Node = event.target;
    let index = target.getSiblingIndex();
    if (GameDirector.instance.isSystemOpen(this._list[index].systemOpen)) {
      this.getNode("node_shop_list").children.forEach((item) => {
        item.getChildByName("node_select").active = false;
      });
      target.getChildByName("node_select").active = true;
      this.closeBack({
        position: index,
      });
    } else {
      TipMgr.showTip("模块未解锁");
    }
  }
}
