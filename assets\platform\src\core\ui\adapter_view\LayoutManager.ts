import { _decorator, Node, UITransform, Vec2, Component } from "cc";
import { <PERSON><PERSON><PERSON> } from "../../../lib/ui/Scroller";
import { ViewHolderManager } from "./ViewHolderManager";
import { Deque } from "../../../lib/utils/Deque";
import { ListAdapter, ViewHolder } from "./ListAdapter";
import { Rect } from "../../../lib/utils/Rect";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

const log = Logger.getLoger(LOG_LEVEL.STOP);
export enum LayoutDirection {
  Start = -1,
  End = 1,
}
export abstract class LayoutManager extends Component {
  private _parent: Node;
  private _scroller: Scroller;
  private _viewHolders: ViewHolderManager;
  private _needLayout: boolean = true;

  private _changed: boolean = false;
  private _lastRect: Rect = new Rect(0, 0, 0, 0);

  public get children(): Deque<Node> {
    return this._viewHolders?.visibleViews;
  }
  public get scroller(): Scroller {
    return this._scroller;
  }
  public get viewholders() {
    return this._viewHolders;
  }
  public set lastRect(lastRect: Rect) {
    this._lastRect = lastRect;
  }
  public get lastRect(): Rect {
    return this._lastRect;
  }
  public get parent() {
    return this._parent;
  }

  protected getNodeLeft(node: Node): number {
    if (!node) {
      return 0;
    }
    let width = node.getComponent(UITransform).width;
    let anchorX = node.getComponent(UITransform).anchorX;
    // log.log(width, anchorX, node.position.x);
    return node.position.x - width * anchorX;
  }
  protected getNodeTop(node: Node): number {
    if (!node) {
      return 0;
    }
    let height = node.getComponent(UITransform).height;
    let anchorY = node.getComponent(UITransform).anchorY;
    return node.position.y + height * (1 - anchorY);
  }
  protected getNodeRight(node: Node): number {
    if (!node) {
      return 0;
    }
    let width = node.getComponent(UITransform).width;
    return this.getNodeLeft(node) + width;
  }

  protected getNodeBottom(node: Node): number {
    if (!node) {
      return 0;
    }
    let height = node.getComponent(UITransform).height;
    return this.getNodeTop(node) - height;
  }
  protected getBorderLeft() {
    let width = this._parent.getComponent(UITransform).width;
    let anchorX = this._parent.getComponent(UITransform).anchorX;
    return 0 - width * anchorX;
  }
  protected getBorderTop() {
    let height = this._parent.getComponent(UITransform).height;
    let anchorY = this._parent.getComponent(UITransform).anchorY;
    return height * (1 - anchorY);
  }
  protected getBorderRight() {
    let width = this._parent.getComponent(UITransform).width;
    return this.getBorderLeft() + width;
  }
  protected getBorderBottom() {
    let height = this._parent.getComponent(UITransform).height;
    let anchorY = this._parent.getComponent(UITransform).anchorY;
    return 0 - height * anchorY;
  }

  //判断node是否在显示区域
  protected isNodeOutofScreen(node: Node): boolean {
    if (!node) {
      return true;
    }
    let left = this.getNodeLeft(node);
    let top = this.getNodeTop(node);
    let right = this.getNodeRight(node);
    let bottom = this.getNodeBottom(node);
    if (
      left > this.getBorderRight() ||
      right < this.getBorderLeft() ||
      top < this.getBorderBottom() ||
      bottom > this.getBorderTop()
    ) {
      return true;
    }
    return false;
  }

  protected onEnable(): void {
    this.scroller?.scrollBy(0, 0);
  }
  protected onDisable(): void {
    log.log("onDisable");
    this.scroller?.reset();
  }
  protected isRectOutofScreen(rect: Rect): boolean {
    if (!rect || rect.isZero) {
      return true;
    }
    if (
      rect.right < this.getBorderLeft() ||
      rect.left > this.getBorderRight() ||
      rect.top < this.getBorderBottom() ||
      rect.bottom > this.getBorderTop()
    ) {
      return true;
    }
    return false;
  }
  public removeAllChildren() {
    this._viewHolders.resetPosition();
    this._parent.removeAllChildren();
  }

  public requestLayout(clear: boolean = false) {
    if (clear) {
      this._changed = true;
      this._parent.removeAllChildren();
      this._viewHolders.calcPosition();
      // this._viewHolders.resetPosition();
    }
    log.warn("request layout");
    this.layoutChildrenOffset(0, 0);
    this.onScrollComplete();
  }
  public layoutChildrenOffset(x: number, y: number, isFling: boolean = false) {
    this.layoutChildren(x, y, isFling);
  }
  public attachTo(parent: Node, scroller: Scroller, adapter: ListAdapter) {
    this._parent = parent;
    this._scroller = scroller;
    this._scroller.onFlingComplete = this.onFlingComplete.bind(this);
    this._scroller.onFling = this.onFling.bind(this);
    this._scroller.onScrollComplete = this.onScrollComplete.bind(this);
    this._viewHolders = new ViewHolderManager(adapter);
    this.onAttach();
  }
  protected onFling(xoffset: number, yoffset: number) {
    this.layoutChildrenOffset(xoffset, yoffset, true);
  }
  protected onAttach() {}
  protected onFlingComplete() {
    console.log("LayoutManager onFlingComplete");
  }
  protected onScrollComplete() {
    console.log("LayoutManager onScrollComplete");
  }

  //new
  private layoutChildren(offsetX: number, offsetY: number, isFling: boolean) {
    this._lastRect = this.onLayout(this._changed, this._lastRect, offsetX, offsetY, isFling);
    log.log(this.lastRect);
    this._changed = false;
    // 检查等待回收池;
    while (this.onChildRecycled(this.children.peekRear(), false)) {
      let node = this._viewHolders.recyclerTail(); //加入回收池
      this._parent.removeChild(node);
    }

    while (this.onChildRecycled(this.children.peekFront(), true)) {
      let node = this._viewHolders.recyclerHeader(); //加入回收池
      this._parent.removeChild(node);
    }
  }

  protected updateChildPosition(child: Node, x: number, y: number) {
    // let position = child.getComponent(ViewHolder).position;
    // 如果在屏外并且position等于firstVisiblePosition或lastVisiblePosition则回收
    // 设置位置
    this.onUpdateChildPosition(child, x, y);

    // child.getComponent(ViewHolder).isOutOfScreen = this.isNodeOutofScreen(child);
  }
  /**
   * 不可调用@function updateChildPosition() 否则会死循环
   * @param child
   * @param x
   * @param y
   */
  protected onUpdateChildPosition(child: Node, x: number, y: number) {
    child.setPosition(x, y);
  }

  /**
   * 添加一个节点到队列的尾部
   * @returns 新添加的节点
   */
  protected addNodeToTail(): Node {
    let newNode = null;
    newNode = this._viewHolders.addToTail();
    if (newNode) {
      this._parent.addChild(newNode);
      this.viewholders.bindData(newNode);
    }
    return newNode;
  }

  /**
   * 添加一个节点到队列的头部
   * @returns 新添加的节点
   */
  protected addNodeToHeader(): Node {
    let newNode = null;
    newNode = this._viewHolders.addToHeader();
    if (newNode) {
      this._parent.addChild(newNode);
      this.viewholders.bindData(newNode);
    }
    return newNode;
  }

  /**
   * 添加一个节点到指定位置，该操作会重置startIndex和endIndex
   * @param position 数据集的位置
   * @returns
   */
  protected addNode(position: number) {
    let newNode = null;
    newNode = this._viewHolders.addNode(position);
    if (newNode) {
      this._parent.addChild(newNode);
      this.viewholders.bindData(newNode);
    }
    return newNode;
  }

  /**
   * 每个节点回收前的回调
   * @param child
   * @param isFromHeader 如果为true则是回收头部的数据，对应于addNodeToHeader加入的节点；否则是回收尾部的数据，对应于addNodeToTail加入的节点
   * @returns true则回收，否则不回收
   */
  protected onChildRecycled(child: Node, isFromHeader: boolean): boolean {
    if (this.viewholders.firstVisiblePosition == this.viewholders.lastVisiblePosition) {
      return false;
    }
    if (child && this.isNodeOutofScreen(child)) {
      return true;
    }
    return false;
  }
  /**
   *    子类实现
   *  1.先遍历children并根据offsetX和offsetY设置新位置
   *  2.根据最终屏幕上是否有空间，决定是否调用makeNewNode生成新的节点
   * @param changed TRUE 说明数据发生改变
   * @param lastRect 上一帧布局的rect
   * @param offsetX
   * @param offsetY
   * @param isFling
   */
  protected abstract onLayout(
    changed: boolean,
    lastRect: Rect,
    offsetX: number,
    offsetY: number,
    isFling: boolean
  ): Rect;

  protected onDestroy(): void {
    log.log(this.constructor.name, "onDestroy");
    this.viewholders?.onDestroy();
  }
}
