// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: City.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RewardMessage } from "./Comm";

export const protobufPackage = "sim";

/**  */
export interface CityAchieveResponse {
  resAddList: number[];
  /** 下一个领取过建筑升级奖励的进度 */
  nextCityLevelRewardIndex: number;
}

/**  */
export interface CityAggregateMessage {
  /** 下一个领取过建筑升级奖励的进度 */
  nextCityLevelRewardIndex: number;
  /** 已经领取过的城池通关奖励 */
  rewardCityList: number[];
  /** 已经打开过迷雾的城池 */
  openFogCityList: number[];
  /** 额外需要建造的建筑 */
  otherCreateCityList: number[];
  /** 解锁最大的五族荣耀建筑预览ID */
  unlockRaceShowId: number;
  /** 已经解锁的五族荣耀装饰ID结合 */
  decorationIdList: number[];
  /** 已经领取过五族荣耀种族装饰攒齐的奖励ID集合 */
  raceRewardIdList: number[];
  /** 已建三界小家 ID：等级 ID为一级时的各建筑ID 只包括1xx */
  smallHomeLevelMap: { [key: number]: number };
  /** 已经领取的三界小家升级奖励的集合 元素为配置表ID 包含1xx,2xx,3xx */
  smallHomeRewardList: number[];
  /** 所有建筑达到某个等级的奖励领取情况 如果已经领取完1,2级的奖励元素为{1,2} */
  smallHomeAllLevelReward: number[];
  /** 号召宝箱的等级 从1开始 */
  boxLevel: number;
  /** 下一次自动召唤的冷却时间 */
  nextAutoTimeStamp: number;
  /** 号召宝箱的总数量 */
  boxTotalCount: number;
  /** 满足条件但需要手动解锁的装饰集合 */
  activeLockIdList: number[];
}

export interface CityAggregateMessage_SmallHomeLevelMapEntry {
  key: number;
  value: number;
}

/**  */
export interface CityChapterResponse {
  resAddList: number[];
  /** 已经领取过的城池通关奖励 */
  rewardCityList: number[];
}

/**  */
export interface CityCreateResponse {
  /**  */
  cityMessage:
    | CityMessage
    | undefined;
  /**  */
  cityAggregateMessage: CityAggregateMessage | undefined;
  rewardList: number[];
}

/**  */
export interface CityHireResponse {
  energyHire: number;
  /** 总的号召宝箱的数量 */
  totalBoxCount: number;
  /** 下一次自动召唤的冷却时间 */
  nextAutoTimeStamp: number;
}

/**  */
export interface CityHireWorkerListMessage {
  cityList: CityMessage[];
}

/**  */
export interface CityHireWorkerMessage {
  /** 据点id */
  cityId: number;
  /** 招募员工的人数 */
  num: number;
}

/**  */
export interface CityMessage {
  cityId: number;
  level: number;
  energyHire: number;
  itemHire: number;
}

/**  */
export interface DecorationManualReward {
  /**  */
  rewardMessage: RewardMessage | undefined;
  decorationIdList: number[];
}

/**  */
export interface EnergyFactoryMessage {
  /** 族运水晶等级 */
  level: number;
  /** 外观 */
  look: string;
  /** 是否已经领取过了自动点击奖励 领取完奖励后才算上自动点击 */
  autoTake: boolean;
  /** 自动点击解锁进度条 */
  autoStateCount: number;
}

/**  */
export interface HeroPictureMessage {
  /** 已经激活的图鉴集合 key:图鉴ID val:等级 */
  pictureMap: { [key: number]: number };
}

export interface HeroPictureMessage_PictureMapEntry {
  key: number;
  value: number;
}

function createBaseCityAchieveResponse(): CityAchieveResponse {
  return { resAddList: [], nextCityLevelRewardIndex: 0 };
}

export const CityAchieveResponse: MessageFns<CityAchieveResponse> = {
  encode(message: CityAchieveResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    if (message.nextCityLevelRewardIndex !== 0) {
      writer.uint32(16).int32(message.nextCityLevelRewardIndex);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityAchieveResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityAchieveResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nextCityLevelRewardIndex = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityAchieveResponse>, I>>(base?: I): CityAchieveResponse {
    return CityAchieveResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityAchieveResponse>, I>>(object: I): CityAchieveResponse {
    const message = createBaseCityAchieveResponse();
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.nextCityLevelRewardIndex = object.nextCityLevelRewardIndex ?? 0;
    return message;
  },
};

function createBaseCityAggregateMessage(): CityAggregateMessage {
  return {
    nextCityLevelRewardIndex: 0,
    rewardCityList: [],
    openFogCityList: [],
    otherCreateCityList: [],
    unlockRaceShowId: 0,
    decorationIdList: [],
    raceRewardIdList: [],
    smallHomeLevelMap: {},
    smallHomeRewardList: [],
    smallHomeAllLevelReward: [],
    boxLevel: 0,
    nextAutoTimeStamp: 0,
    boxTotalCount: 0,
    activeLockIdList: [],
  };
}

export const CityAggregateMessage: MessageFns<CityAggregateMessage> = {
  encode(message: CityAggregateMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nextCityLevelRewardIndex !== 0) {
      writer.uint32(8).int32(message.nextCityLevelRewardIndex);
    }
    writer.uint32(18).fork();
    for (const v of message.rewardCityList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.openFogCityList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(34).fork();
    for (const v of message.otherCreateCityList) {
      writer.int64(v);
    }
    writer.join();
    if (message.unlockRaceShowId !== 0) {
      writer.uint32(40).int32(message.unlockRaceShowId);
    }
    writer.uint32(50).fork();
    for (const v of message.decorationIdList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(58).fork();
    for (const v of message.raceRewardIdList) {
      writer.int32(v);
    }
    writer.join();
    Object.entries(message.smallHomeLevelMap).forEach(([key, value]) => {
      CityAggregateMessage_SmallHomeLevelMapEntry.encode({ key: key as any, value }, writer.uint32(66).fork()).join();
    });
    writer.uint32(74).fork();
    for (const v of message.smallHomeRewardList) {
      writer.int64(v);
    }
    writer.join();
    writer.uint32(82).fork();
    for (const v of message.smallHomeAllLevelReward) {
      writer.int32(v);
    }
    writer.join();
    if (message.boxLevel !== 0) {
      writer.uint32(88).int32(message.boxLevel);
    }
    if (message.nextAutoTimeStamp !== 0) {
      writer.uint32(96).int64(message.nextAutoTimeStamp);
    }
    if (message.boxTotalCount !== 0) {
      writer.uint32(104).int32(message.boxTotalCount);
    }
    writer.uint32(114).fork();
    for (const v of message.activeLockIdList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityAggregateMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityAggregateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.nextCityLevelRewardIndex = reader.int32();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.rewardCityList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardCityList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.openFogCityList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.openFogCityList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 4: {
          if (tag === 32) {
            message.otherCreateCityList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.otherCreateCityList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.unlockRaceShowId = reader.int32();
          continue;
        }
        case 6: {
          if (tag === 48) {
            message.decorationIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 50) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.decorationIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 7: {
          if (tag === 56) {
            message.raceRewardIdList.push(reader.int32());

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.raceRewardIdList.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          const entry8 = CityAggregateMessage_SmallHomeLevelMapEntry.decode(reader, reader.uint32());
          if (entry8.value !== undefined) {
            message.smallHomeLevelMap[entry8.key] = entry8.value;
          }
          continue;
        }
        case 9: {
          if (tag === 72) {
            message.smallHomeRewardList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 74) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.smallHomeRewardList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
        case 10: {
          if (tag === 80) {
            message.smallHomeAllLevelReward.push(reader.int32());

            continue;
          }

          if (tag === 82) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.smallHomeAllLevelReward.push(reader.int32());
            }

            continue;
          }

          break;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.boxLevel = reader.int32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.nextAutoTimeStamp = longToNumber(reader.int64());
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.boxTotalCount = reader.int32();
          continue;
        }
        case 14: {
          if (tag === 112) {
            message.activeLockIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 114) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.activeLockIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityAggregateMessage>, I>>(base?: I): CityAggregateMessage {
    return CityAggregateMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityAggregateMessage>, I>>(object: I): CityAggregateMessage {
    const message = createBaseCityAggregateMessage();
    message.nextCityLevelRewardIndex = object.nextCityLevelRewardIndex ?? 0;
    message.rewardCityList = object.rewardCityList?.map((e) => e) || [];
    message.openFogCityList = object.openFogCityList?.map((e) => e) || [];
    message.otherCreateCityList = object.otherCreateCityList?.map((e) => e) || [];
    message.unlockRaceShowId = object.unlockRaceShowId ?? 0;
    message.decorationIdList = object.decorationIdList?.map((e) => e) || [];
    message.raceRewardIdList = object.raceRewardIdList?.map((e) => e) || [];
    message.smallHomeLevelMap = Object.entries(object.smallHomeLevelMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    message.smallHomeRewardList = object.smallHomeRewardList?.map((e) => e) || [];
    message.smallHomeAllLevelReward = object.smallHomeAllLevelReward?.map((e) => e) || [];
    message.boxLevel = object.boxLevel ?? 0;
    message.nextAutoTimeStamp = object.nextAutoTimeStamp ?? 0;
    message.boxTotalCount = object.boxTotalCount ?? 0;
    message.activeLockIdList = object.activeLockIdList?.map((e) => e) || [];
    return message;
  },
};

function createBaseCityAggregateMessage_SmallHomeLevelMapEntry(): CityAggregateMessage_SmallHomeLevelMapEntry {
  return { key: 0, value: 0 };
}

export const CityAggregateMessage_SmallHomeLevelMapEntry: MessageFns<CityAggregateMessage_SmallHomeLevelMapEntry> = {
  encode(
    message: CityAggregateMessage_SmallHomeLevelMapEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityAggregateMessage_SmallHomeLevelMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityAggregateMessage_SmallHomeLevelMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityAggregateMessage_SmallHomeLevelMapEntry>, I>>(
    base?: I,
  ): CityAggregateMessage_SmallHomeLevelMapEntry {
    return CityAggregateMessage_SmallHomeLevelMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityAggregateMessage_SmallHomeLevelMapEntry>, I>>(
    object: I,
  ): CityAggregateMessage_SmallHomeLevelMapEntry {
    const message = createBaseCityAggregateMessage_SmallHomeLevelMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseCityChapterResponse(): CityChapterResponse {
  return { resAddList: [], rewardCityList: [] };
}

export const CityChapterResponse: MessageFns<CityChapterResponse> = {
  encode(message: CityChapterResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.resAddList) {
      writer.double(v);
    }
    writer.join();
    writer.uint32(18).fork();
    for (const v of message.rewardCityList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityChapterResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityChapterResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 9) {
            message.resAddList.push(reader.double());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.resAddList.push(reader.double());
            }

            continue;
          }

          break;
        }
        case 2: {
          if (tag === 16) {
            message.rewardCityList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardCityList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityChapterResponse>, I>>(base?: I): CityChapterResponse {
    return CityChapterResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityChapterResponse>, I>>(object: I): CityChapterResponse {
    const message = createBaseCityChapterResponse();
    message.resAddList = object.resAddList?.map((e) => e) || [];
    message.rewardCityList = object.rewardCityList?.map((e) => e) || [];
    return message;
  },
};

function createBaseCityCreateResponse(): CityCreateResponse {
  return { cityMessage: undefined, cityAggregateMessage: undefined, rewardList: [] };
}

export const CityCreateResponse: MessageFns<CityCreateResponse> = {
  encode(message: CityCreateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cityMessage !== undefined) {
      CityMessage.encode(message.cityMessage, writer.uint32(10).fork()).join();
    }
    if (message.cityAggregateMessage !== undefined) {
      CityAggregateMessage.encode(message.cityAggregateMessage, writer.uint32(18).fork()).join();
    }
    writer.uint32(26).fork();
    for (const v of message.rewardList) {
      writer.double(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityCreateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityCreateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cityMessage = CityMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cityAggregateMessage = CityAggregateMessage.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag === 25) {
            message.rewardList.push(reader.double());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.rewardList.push(reader.double());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityCreateResponse>, I>>(base?: I): CityCreateResponse {
    return CityCreateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityCreateResponse>, I>>(object: I): CityCreateResponse {
    const message = createBaseCityCreateResponse();
    message.cityMessage = (object.cityMessage !== undefined && object.cityMessage !== null)
      ? CityMessage.fromPartial(object.cityMessage)
      : undefined;
    message.cityAggregateMessage = (object.cityAggregateMessage !== undefined && object.cityAggregateMessage !== null)
      ? CityAggregateMessage.fromPartial(object.cityAggregateMessage)
      : undefined;
    message.rewardList = object.rewardList?.map((e) => e) || [];
    return message;
  },
};

function createBaseCityHireResponse(): CityHireResponse {
  return { energyHire: 0, totalBoxCount: 0, nextAutoTimeStamp: 0 };
}

export const CityHireResponse: MessageFns<CityHireResponse> = {
  encode(message: CityHireResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.energyHire !== 0) {
      writer.uint32(8).int32(message.energyHire);
    }
    if (message.totalBoxCount !== 0) {
      writer.uint32(16).int32(message.totalBoxCount);
    }
    if (message.nextAutoTimeStamp !== 0) {
      writer.uint32(24).int64(message.nextAutoTimeStamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityHireResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityHireResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.energyHire = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.totalBoxCount = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.nextAutoTimeStamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityHireResponse>, I>>(base?: I): CityHireResponse {
    return CityHireResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityHireResponse>, I>>(object: I): CityHireResponse {
    const message = createBaseCityHireResponse();
    message.energyHire = object.energyHire ?? 0;
    message.totalBoxCount = object.totalBoxCount ?? 0;
    message.nextAutoTimeStamp = object.nextAutoTimeStamp ?? 0;
    return message;
  },
};

function createBaseCityHireWorkerListMessage(): CityHireWorkerListMessage {
  return { cityList: [] };
}

export const CityHireWorkerListMessage: MessageFns<CityHireWorkerListMessage> = {
  encode(message: CityHireWorkerListMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cityList) {
      CityMessage.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityHireWorkerListMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityHireWorkerListMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cityList.push(CityMessage.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityHireWorkerListMessage>, I>>(base?: I): CityHireWorkerListMessage {
    return CityHireWorkerListMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityHireWorkerListMessage>, I>>(object: I): CityHireWorkerListMessage {
    const message = createBaseCityHireWorkerListMessage();
    message.cityList = object.cityList?.map((e) => CityMessage.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCityHireWorkerMessage(): CityHireWorkerMessage {
  return { cityId: 0, num: 0 };
}

export const CityHireWorkerMessage: MessageFns<CityHireWorkerMessage> = {
  encode(message: CityHireWorkerMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cityId !== 0) {
      writer.uint32(8).int64(message.cityId);
    }
    if (message.num !== 0) {
      writer.uint32(16).int32(message.num);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityHireWorkerMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityHireWorkerMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.cityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.num = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityHireWorkerMessage>, I>>(base?: I): CityHireWorkerMessage {
    return CityHireWorkerMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityHireWorkerMessage>, I>>(object: I): CityHireWorkerMessage {
    const message = createBaseCityHireWorkerMessage();
    message.cityId = object.cityId ?? 0;
    message.num = object.num ?? 0;
    return message;
  },
};

function createBaseCityMessage(): CityMessage {
  return { cityId: 0, level: 0, energyHire: 0, itemHire: 0 };
}

export const CityMessage: MessageFns<CityMessage> = {
  encode(message: CityMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cityId !== 0) {
      writer.uint32(8).int64(message.cityId);
    }
    if (message.level !== 0) {
      writer.uint32(16).int32(message.level);
    }
    if (message.energyHire !== 0) {
      writer.uint32(24).int32(message.energyHire);
    }
    if (message.itemHire !== 0) {
      writer.uint32(32).int32(message.itemHire);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.cityId = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.energyHire = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.itemHire = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<CityMessage>, I>>(base?: I): CityMessage {
    return CityMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityMessage>, I>>(object: I): CityMessage {
    const message = createBaseCityMessage();
    message.cityId = object.cityId ?? 0;
    message.level = object.level ?? 0;
    message.energyHire = object.energyHire ?? 0;
    message.itemHire = object.itemHire ?? 0;
    return message;
  },
};

function createBaseDecorationManualReward(): DecorationManualReward {
  return { rewardMessage: undefined, decorationIdList: [] };
}

export const DecorationManualReward: MessageFns<DecorationManualReward> = {
  encode(message: DecorationManualReward, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rewardMessage !== undefined) {
      RewardMessage.encode(message.rewardMessage, writer.uint32(10).fork()).join();
    }
    writer.uint32(18).fork();
    for (const v of message.decorationIdList) {
      writer.int64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DecorationManualReward {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDecorationManualReward();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rewardMessage = RewardMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.decorationIdList.push(longToNumber(reader.int64()));

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.decorationIdList.push(longToNumber(reader.int64()));
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<DecorationManualReward>, I>>(base?: I): DecorationManualReward {
    return DecorationManualReward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DecorationManualReward>, I>>(object: I): DecorationManualReward {
    const message = createBaseDecorationManualReward();
    message.rewardMessage = (object.rewardMessage !== undefined && object.rewardMessage !== null)
      ? RewardMessage.fromPartial(object.rewardMessage)
      : undefined;
    message.decorationIdList = object.decorationIdList?.map((e) => e) || [];
    return message;
  },
};

function createBaseEnergyFactoryMessage(): EnergyFactoryMessage {
  return { level: 0, look: "", autoTake: false, autoStateCount: 0 };
}

export const EnergyFactoryMessage: MessageFns<EnergyFactoryMessage> = {
  encode(message: EnergyFactoryMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.level !== 0) {
      writer.uint32(8).int32(message.level);
    }
    if (message.look !== "") {
      writer.uint32(18).string(message.look);
    }
    if (message.autoTake !== false) {
      writer.uint32(24).bool(message.autoTake);
    }
    if (message.autoStateCount !== 0) {
      writer.uint32(32).int32(message.autoStateCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EnergyFactoryMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEnergyFactoryMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.level = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.look = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.autoTake = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.autoStateCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<EnergyFactoryMessage>, I>>(base?: I): EnergyFactoryMessage {
    return EnergyFactoryMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnergyFactoryMessage>, I>>(object: I): EnergyFactoryMessage {
    const message = createBaseEnergyFactoryMessage();
    message.level = object.level ?? 0;
    message.look = object.look ?? "";
    message.autoTake = object.autoTake ?? false;
    message.autoStateCount = object.autoStateCount ?? 0;
    return message;
  },
};

function createBaseHeroPictureMessage(): HeroPictureMessage {
  return { pictureMap: {} };
}

export const HeroPictureMessage: MessageFns<HeroPictureMessage> = {
  encode(message: HeroPictureMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.pictureMap).forEach(([key, value]) => {
      HeroPictureMessage_PictureMapEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeroPictureMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeroPictureMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = HeroPictureMessage_PictureMapEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.pictureMap[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeroPictureMessage>, I>>(base?: I): HeroPictureMessage {
    return HeroPictureMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeroPictureMessage>, I>>(object: I): HeroPictureMessage {
    const message = createBaseHeroPictureMessage();
    message.pictureMap = Object.entries(object.pictureMap ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseHeroPictureMessage_PictureMapEntry(): HeroPictureMessage_PictureMapEntry {
  return { key: 0, value: 0 };
}

export const HeroPictureMessage_PictureMapEntry: MessageFns<HeroPictureMessage_PictureMapEntry> = {
  encode(message: HeroPictureMessage_PictureMapEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== 0) {
      writer.uint32(8).int64(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).int32(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeroPictureMessage_PictureMapEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeroPictureMessage_PictureMapEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.key = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  create<I extends Exact<DeepPartial<HeroPictureMessage_PictureMapEntry>, I>>(
    base?: I,
  ): HeroPictureMessage_PictureMapEntry {
    return HeroPictureMessage_PictureMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeroPictureMessage_PictureMapEntry>, I>>(
    object: I,
  ): HeroPictureMessage_PictureMapEntry {
    const message = createBaseHeroPictureMessage_PictureMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
