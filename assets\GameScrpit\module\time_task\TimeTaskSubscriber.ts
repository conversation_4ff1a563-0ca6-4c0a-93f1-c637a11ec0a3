import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../game/mgr/ApiHandler";
import { ActivityCmd } from "../../game/net/cmd/CmdData";
import { TimeTaskResponse } from "../../game/net/protocol/Activity";
import { TimeTaskModule } from "./TimeTaskModule";

export class TimeTaskSubscriber {
  private onTimeTaskNotice(resp: TimeTaskResponse) {
    //
    TimeTaskModule.data.updateTask(resp.taskId, resp.targetVal);
  }
  public register() {
    //订阅服务器消息
    ApiHandler.instance.subscribe(TimeTaskResponse, ActivityCmd.TimeTaskNotice, this.onTimeTaskNotice);
  }
  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(ActivityCmd.TimeTaskNotice, this.onTimeTaskNotice);
  }
}
