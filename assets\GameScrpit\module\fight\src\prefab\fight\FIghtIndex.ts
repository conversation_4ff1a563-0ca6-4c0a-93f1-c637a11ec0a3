import { _decorator, instantiate, Label, Node, Prefab, tween, UITransform, v3, Vec3 } from "cc";
import { Sleep } from "db://assets/GameScrpit/game/GameDefine";
import {
  ActionEnum,
  AttrKeyEnum,
  BaseSkillType,
  BattleActionDTO,
  ChangeValueType,
  FaceToEnum,
  HpStatusType,
} from "../../FightConstant";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { FightManager } from "./FightManager";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { NumberJump } from "./NumberJumpCtrl";
import { PoolMgr } from "db://assets/platform/src/PoolHelper";
import { CharacterBase, CharacterState } from "./character/CharacterBase";
import { TaskSync } from "db://assets/GameScrpit/lib/utils/TaskSync";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { ActionType } from "db://assets/GameScrpit/game/fight/FightDefine";
const { ccclass, property } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

@routeConfig({
  bundle: BundleEnum.BUNDLE_G_FIGHT, //BUNDLE分包
  url: "prefab/fight/FIghtIndex",
  nextHop: [],
  exit: "",
})
@ccclass("FIghtIndex")
export class FIghtIndex extends BaseCtrl {
  /** ======== 界面元素相关 ======== */
  // 站位
  posMap: { [key: number]: Vec3 } = {
    11: v3(-200, 0, 0),
    21: v3(200, 0, 0),
  };
  // 数字跳动层
  node_number_layer: Node;
  // 站位线
  node_line1: Node;
  // 回合进度
  lbl_round: Label;

  /** ======== 状态相关 ======== */
  // 界面准备完毕
  loadReady = false;
  // 加载
  readyMap: { [key: number]: boolean } = {};
  // 行动已过时间长
  actionTimePass = 0;
  // 行动所需时长
  actionTimeNeed = 0;
  // 是否正在行动
  isActioning = false;
  // update间隔
  updateSpaceMax = 0.25;
  updateSpaceNow = 0;
  // 战斗结束
  finish = false;

  /** ======== 脚本控制 ======== */
  args: { battleData: any };
  // 战斗管理
  fightMgr: FightManager;
  // 漂字管理池
  numPool: PoolMgr<NumberJump>;
  // 战斗角色控制脚本
  characterCtrlMap: { [key: number]: CharacterBase } = {};

  init(args: { battleData: any }) {
    // 解析录像成为可读样式，不改格式
    this.fightMgr = FightManager.getInstance();
    this.fightMgr.init(args.battleData);
    this.args = args;
  }

  async start() {
    log.info("战斗加载中");
    super.start();

    this.node_number_layer = this.getNode("node_number_layer");
    this.node_line1 = this.getNode("node_line1");
    this.lbl_round = this.getNode("lbl_round").getComponent(Label);
    log.info("转换为世界坐标");
    const uiTrans = this.node_line1.getComponent(UITransform);
    this.posMap[11] = uiTrans.convertToWorldSpaceAR(this.posMap[11]);
    this.posMap[21] = uiTrans.convertToWorldSpaceAR(this.posMap[21]);

    const taskSync = new TaskSync(
      this,
      { name: "初始化完成", task: this.loadComplete },
      { name: "初始化错误", task: () => {} },
      10
    );
    taskSync.addTask({ name: "加载数字池子", task: this.loadNumPool });

    const 加载角色11 = "加载角色11";
    taskSync.addTask({ name: 加载角色11, task: this.loadingCharactor, args: [11] });

    const 加载角色21 = "加载角色21";
    taskSync.addTask({ name: "加载角色21", task: this.loadingCharactor, args: [21] });

    taskSync.addTask({
      name: "加载角色11血条",
      task: this.loadHp,
      args: [HpStatusType.green, 11],
      依赖任务列表: [加载角色11],
    });

    taskSync.addTask({
      name: "加载角色21血条",
      task: this.loadHp,
      args: [HpStatusType.red, 21],
      依赖任务列表: [加载角色21],
    });

    taskSync.addTask({
      name: "加载角色11特效",
      task: this.loadCharacterRes,
      args: [11],
      依赖任务列表: [加载角色11],
    });

    taskSync.addTask({
      name: "加载角色21特效",
      task: this.loadCharacterRes,
      args: [21],
      依赖任务列表: [加载角色21],
    });

    this.fightMgr.roundIdxNow = -1;
    this.fightMgr.actionIdx = 0;
    this.lbl_round.string = `战斗准备`;

    TipsMgr.showTip("播放战斗开始动画");

    taskSync.start();
    // 加载界面
    await Sleep(1);
  }

  /** 初始化完成 */
  private loadComplete() {
    this.loadReady = true;
  }

  /** 加载数字池 */
  private async loadNumPool() {
    const pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_FIGHT, "prefab/fight/NumberJump");

    // 初始化数字池子
    this.numPool = new PoolMgr<NumberJump>(
      async () => {
        return instantiate(pb).getComponent(NumberJump);
      },
      100,
      []
    );
  }

  /** 加载血条 */
  private async loadHp(type: HpStatusType, posId: number) {
    await this.characterCtrlMap[posId].loadHp(type, this.fightMgr.getCharacterById(posId).attrMap[AttrKeyEnum.血量Max]);
  }

  /** 加载特效 */
  private async loadCharacterRes(posId: number) {
    await this.characterCtrlMap[posId].loadFightRes();
  }

  /**
   * 加载角色
   * @param pbCharacter
   * @param posId
   */
  private async loadingCharactor(posId: number) {
    return new Promise(async (resolve, reject) => {
      const characterDTO = this.fightMgr.getCharacterById(posId);

      let pbCharacter = await this.assetMgr.loadPrefabSync(
        BundleEnum.BUNDLE_G_FIGHT,
        `prefab/fight/character/Character${characterDTO.fightCharacterId}`
      );

      const nodeCharacterNew = instantiate(pbCharacter);
      nodeCharacterNew.parent = this.node_line1;
      const characterNewCtrl = nodeCharacterNew.getComponent(CharacterBase);
      this.characterCtrlMap[posId] = characterNewCtrl;
      characterNewCtrl.init({ fightCharacterId: characterDTO.fightCharacterId, posId: posId });
      nodeCharacterNew.setWorldPosition(this.posMap[posId]);

      characterNewCtrl.runAfterStart(() => {
        if (posId == 11) {
          this.characterCtrlMap[posId].setFaceTo(FaceToEnum.right);
        } else {
          this.characterCtrlMap[posId].setFaceTo(FaceToEnum.left);
        }
        resolve(true);
      });
    });
  }

  protected update(dt: number): void {
    if (this.finish || !this.loadReady) {
      return;
    }

    if (this.isActioning == true) {
      return;
    }

    // if (this.actionTimeNeed <= this.actionTimePass || this.isActioning == false) {
    //   this.actionTimePass = 0;
    //   this.actionTimeNeed = 1;
    // } else {
    //   this.actionTimePass += dt;
    //   return;
    // }

    let actionDTO: BattleActionDTO = null;

    // 战前动作
    if (this.fightMgr.roundIdxNow == -1) {
      if (this.fightMgr.battleReport.beforeRoundActionList.length > this.fightMgr.actionIdx) {
        actionDTO = this.fightMgr.battleReport.beforeRoundActionList[this.fightMgr.actionIdx];
      } else {
        this.roundNext();
      }
    }

    // 回合动作
    const roundList = this.fightMgr.battleReport.roundList;
    if (this.fightMgr.roundIdxNow >= 0) {
      // 下一回合
      if (roundList[this.fightMgr.roundIdxNow].actionList.length <= this.fightMgr.actionIdx) {
        this.roundNext();
      }

      // 下一个动作
      if (roundList[this.fightMgr.roundIdxNow]) {
        actionDTO = roundList[this.fightMgr.roundIdxNow].actionList[this.fightMgr.actionIdx];
      }
    }

    // 取到动作
    if (actionDTO) {
      this.doAction(actionDTO);
      this.fightMgr.actionIdx++;
    } else {
      this.goFinish();
    }
  }

  private goFinish() {
    TipsMgr.showTip("战斗结束");
    this.finish = true;
  }

  /** 下一回合 */
  private roundNext() {
    this.fightMgr.roundIdxNow++;
    this.fightMgr.actionIdx = 0;
    this.lbl_round.string = `第${this.fightMgr.roundIdxNow + 1}/${this.fightMgr.battleReport.scene.roundMax}回合`;
  }

  /** 执行动作 */
  private async doAction(action: BattleActionDTO, isActioning: boolean = false) {
    log.info("doAction ", action.characterId + " " + action.targetId);
    this.isActioning = true || isActioning;

    if (action.actionType == BaseSkillType.静止) {
      await this.playNumBuffAni(action);
      this.isActioning = false || isActioning;
      return;
    }

    let actionCtrl = this.characterCtrlMap[action.characterId];
    let targetCtrl = this.characterCtrlMap[action.targetId];

    // 行动角色要在最前面
    actionCtrl.node.setSiblingIndex(this.node_line1.children.length - 1);

    // 随机一个可用的普攻动作
    const attackConfig = actionCtrl.randomNormalAttack();
    attackConfig.critical = action.actionType == BaseSkillType.暴击;

    // 判断距离/位移
    const nodeAttachment = actionCtrl.getAttachmentByAction(attackConfig.action);
    const worldX1 = nodeAttachment.worldPosition.x;
    const worldX2 = targetCtrl.getAttachmentByAction(ActionEnum.hurt).worldPosition.x;
    let deltaX = worldX2 - worldX1;

    // 在范围内，不用移动
    if (actionCtrl.getFaceTo() == FaceToEnum.right) {
      if (deltaX < 0) {
        deltaX = 0;
      }
    } else {
      if (deltaX > 0) {
        deltaX = 0;
      }
    }

    const modeDt = 0.15;
    // 移动
    const xTo = actionCtrl.node.worldPosition.x + deltaX;
    if (xTo > 0) {
      actionCtrl.clearQueueAndSetStaus(CharacterState.JUMP1);

      tween(actionCtrl.node)
        .to(modeDt, { worldPosition: v3(xTo, actionCtrl.node.worldPosition.y, 0) })
        .start();

      await Sleep(modeDt);
      if (!this.isValid) {
        return;
      }
    }

    // 攻击动作
    actionCtrl.clearQueueAndSetStaus(CharacterState.ATTACK, attackConfig);
    await Sleep(attackConfig.eventTime);
    if (!this.isValid) {
      return;
    }

    // 行动方血变动
    if (action.attrMapMap[action.characterId]) {
      const characterDTO = this.fightMgr.getCharacterById(action.characterId);
      const hpStart = characterDTO.attrMap[AttrKeyEnum.血量];
      const hpEnd = action.attrMapMap[action.characterId][AttrKeyEnum.血量];
      if (hpEnd || hpEnd === 0) {
        actionCtrl.changeHp(hpEnd - hpStart);
        characterDTO.attrMap[AttrKeyEnum.血量] = hpEnd;
      }
    }

    // 被动方血变动
    if (action.attrMapMap[action.targetId]) {
      const targetDTO = this.fightMgr.getCharacterById(action.targetId);
      const hpStart2 = targetDTO.attrMap[AttrKeyEnum.血量];
      const hpEnd2 = action.attrMapMap[action.targetId][AttrKeyEnum.血量];
      if (hpEnd2 || hpEnd2 === 0) {
        targetCtrl.changeHp(hpEnd2 - hpStart2);
        targetDTO.attrMap[AttrKeyEnum.血量] = hpEnd2;
      }
    }

    // 闪避
    if (action.actionType == BaseSkillType.闪避) {
      targetCtrl.clearQueueAndSetStaus(CharacterState.DODGE);
    } else {
      // 受击 或 阵亡
      if (targetCtrl.isDie) {
        targetCtrl.clearQueueAndSetStaus(CharacterState.DIE);
      } else {
        targetCtrl.clearQueueAndSetStaus(CharacterState.HURT);
      }
    }

    this.playNumBuffAni(action);
    await Sleep(0.3);

    // 子行为
    if (action.子行为List && action.子行为List.length > 0) {
      for (let i = 0; i < action.子行为List.length; i++) {
        let 子行为 = action.子行为List[i];
        await this.doAction(子行为, true);
      }
    }

    // 返回原位逻辑
    if (deltaX != 0 && !actionCtrl.isDie) {
      tween(actionCtrl.node)
        .call(() => {
          actionCtrl.clearQueueAndSetStaus(CharacterState.JUMP2);
        })
        .to(modeDt, { worldPosition: this.posMap[action.characterId] })
        .start();
      await Sleep(modeDt + 0.1);
    }

    this.isActioning = false || isActioning;
  }

  async playNumBuffAni(action: BattleActionDTO) {
    // 飘数值
    for (let i = 0; i < action.数值变动List.length; i++) {
      let 变动 = action.数值变动List[i];
      for (let key in 变动.attrChangeMap) {
        let numCtrl = await this.numPool.getOne();

        numCtrl.node.parent = this.node_number_layer;
        // 设置父级
        let nodeAttachment = this.characterCtrlMap[变动.targetId].getAttachmentByAction(ActionEnum.hurt);
        if (nodeAttachment) {
          numCtrl.node.worldPosition = nodeAttachment.worldPosition;
        } else {
          log.error("受击节点未找到");
          numCtrl.node.worldPosition = this.characterCtrlMap[变动.targetId].node.worldPosition;
        }

        numCtrl.init({ value: 变动.attrChangeMap[key][0], type: 变动.attrChangeMap[key][1] });
        numCtrl.runAfterStart(() => {
          numCtrl.show();
        });

        if (变动.attrChangeMap[key][1] == ChangeValueType.吸血) {
          this.characterCtrlMap[变动.targetId].playLep();
        }

        // 回收
        tween(numCtrl.node)
          .delay(1)
          .call(() => {
            this.numPool.recycle(numCtrl);
          })
          .start();
        await Sleep(0.1);
      }
    }
  }

  on_click_btn_restart() {
    this.init(this.args);

    for (let key in this.characterCtrlMap) {
      let ctrl = this.characterCtrlMap[key];
      ctrl.hpStatusCtrl.initHp(this.fightMgr.getCharacterById(ctrl.posId).attrMap[AttrKeyEnum.血量Max]);
    }

    this.fightMgr.roundIdxNow = -1;
    this.fightMgr.actionIdx = 0;
    this.lbl_round.string = `战斗准备`;
    this.finish = false;
  }
}
